<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.ybm100.app.crm"
    android:installLocation="auto">

    <uses-permission android:name="android.permission.INTERNET" /> <!-- Android8.0安装apk权限 大于等于26 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 危险权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" /> <!-- Bugly权限Start -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- Bugly权限End -->
    <!-- 获取手机录音机使用权限，听写、识别、语义理解需要用到此权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 读取手机信息权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 读取联系人权限，上传联系人需要用到此权限 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" /> <!-- 通话记录权限 -->
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" /> <!-- CALL_PHONE -->
    <uses-permission android:name="android.permission.CALL_PHONE" /> <!-- 百度地图 start -->
    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" /> &lt;!&ndash; 获取CUID，用于鉴权，请求签名等 &ndash;&gt; -->
    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" /> &lt;!&ndash; 这个权限用于进行网络定位 &ndash;&gt; -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 百度地图 end -->
    <!-- 腾讯tbs浏览器 start -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />


    <!--  读取药帮忙app中的deviceid权限  -->
    <uses-permission android:name="com.ybmmarket20.permission.READ_DID" />

    <application
        android:name=".global.MyApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="${app_name}"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="label,allowBackup">

        <meta-data
            android:name="com.xyy.config.UploadLimit"
            android:value="10" />
        <!-- =============================屏幕适配 start ============================= -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
        <!-- 设计图宽度 -->
        <meta-data
            android:name="design_width_in_dp"
            android:value="375" /> <!-- 设计图高度 -->
        <meta-data
            android:name="design_height_in_dp"
            android:value="667" /> <!-- 适配全面屏 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" /> <!-- 适配华为 刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" /> <!-- 适配小米 刘海屏 -->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <!-- =============================屏幕适配  end  ============================= -->

        <!-- HUAWEI厂商通道 start -->
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="appid=${um_hauwei_app_id}" />
        <!-- HUAWEI厂商通道 end -->

        <activity
            android:name="com.ybm100.app.crm.ui.activity.ManufacturerPushActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.Translucent" />

        <activity
            android:name=".ui.activity.FlashActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FlashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.login.LoginActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-mt.ybm100.com"
                    android:path="/login/home"
                    android:scheme="crmlogin" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.home.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:scheme="crmpage" />

            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/main"
                    android:scheme="xyy" />

            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.drugstore.MineDrugstoreDetailItemActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/drugstore/detail/item"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.schedule.ScheduleDetailActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/schedule/detail"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.message.MessageDetailActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/msg/detail"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>

        </activity>
        <activity android:name=".ui.activity.CommonH5Activity" android:exported="false"/>
        <activity android:name=".ui.activity.lbs.LBSMapActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/customer_map"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.lbs.LBSChangeActivity" android:exported="false"/>
        <activity android:name=".ui.activity.lbs.LBSReMarksActivity" android:exported="false"/>
        <activity
            android:name=".ui.activity.drugstore.DrugstoreSearchActivity" android:exported="false"
            android:launchMode="singleTop" />
        <activity
            android:name=".ui.activity.drugstore.FilterPrivateCustomerActivity" android:exported="false"
            android:launchMode="singleTop" />
        <activity android:name=".ui.activity.drugstore.PrivateCustomerDetailActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/private/detail"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>

        </activity>
        <activity android:name=".ui.activity.share.ShareSearchActivityActivity" android:exported="false"/>
        <activity android:name=".ui.fragment.share.ShareRootActivity" android:exported="false"/>
        <activity
            android:name=".ui.activity.schedule.CreateScheduleTargetConditionActivity"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/schedule/object_status"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>


        <!-- 新建联系人 -->
        <activity
            android:name=".ui.activity.message.ContactActivity" android:exported="true"
            android:windowSoftInputMode="adjustPan|stateHidden" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/add_contact_page"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <!-- 选择执行人 -->
        <activity
            android:name=".task.activity.TaskSelectExecutorActivity" android:exported="false"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 选择执行人 -->
        <activity
            android:name=".order.activity.OrderExecutorActivity"
            android:configChanges="orientation">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/executor"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <!-- 搜索执行人 -->
        <activity
            android:name=".task.activity.TaskSearchExecutorActivity"
            android:configChanges="orientation" android:exported="false"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" /> <!-- 搜索执行对象 -->
        <!-- 搜索执行人 -->
        <activity
            android:name=".order.activity.OrderExecutorSearchActivity"
            android:configChanges="orientation" android:exported="false"
            android:windowSoftInputMode="adjustPan|stateAlwaysVisible" /> <!-- 搜索执行对象 -->
        <activity android:name=".order.activity.AlbumActivity" android:exported="false"/>
        <activity android:name=".utils.router.SchemeFilterActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="ybm100.com"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.personal.AboutActivity" android:exported="false"/>
        <activity android:name=".ui.activity.personal.ShareActivity" android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/share_qr_code"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.ApplyDetailActivity" android:exported="false"/>
        <activity android:name=".ui.activity.InvoiceApplyActivity" android:exported="false"/>
        <activity
            android:name=".order.activity.ShareImageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/AppTheme.TranslucentTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <!-- =============================== 百度地图 开始 ===================================== -->
        <!-- 百度地图key -->
        <meta-data
            android:name="com.baidu.lbsapi.API_KEY"
            android:value="${baidulbs_key}" /> <!-- 百度地图service -->
        <service
            android:name="com.baidu.location.f"
            android:enabled="true"
            android:process=":remote" />
        <service android:name=".utils.debug.DebugService" />
        <!-- =============================== 百度地图 结束 ===================================== -->
        <!-- ============================= 适配Android7.0  Start ====================================== -->
        <!-- 7.0以后的相册uri问题 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${file_provider}"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_path"
                tools:replace="android:resource" />
        </provider> <!-- ============================= 适配Android7.0  end   ====================================== -->
        <!-- bugly hotfix -->
        <provider
            android:name=".widget.HotfixProvider"
            android:authorities="${file_provider}"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

        <activity
            android:name=".ui.activity.EditActivity"
            android:exported="false"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTop" />
        <activity android:name=".ui.activity.TempActivity" android:exported="false"/>
        <activity android:name=".doraemon.SimulatedLoginActivity" android:exported="false">
            <intent-filter>
                <action android:name="com.ybm100.app.crm.doraemon.QRCodeActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".doraemon.QRCodeActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/qr_code"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".doraemon.SettingUrlActivity" android:exported="false">
            <intent-filter>
                <action android:name="com.ybm100.app.crm.doraemon.SettingUrlActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".doraemon.ynb.YNBHybridActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/crm/web_view"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>

        <activity android:name=".doraemon.ynb.YNBEditHostActivity" android:exported="false">
            <intent-filter>
                <action android:name="com.ybm100.app.crm.doraemon.ynb.YNBEditHostActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.drugstore.AreaSelectActivity" android:exported="false"/>
        <activity android:name=".task.activity.SingleSelectExecutorActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/alloc_people"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.personal.NetworkDiagnosticActivity" android:exported="false"/>
        <activity
            android:name=".task.activity.TaskGoodsDetailActivity"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/task/goods/detail"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity android:name=".task.activity.TeamTaskActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/task/goods/team"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity android:name=".task.activity.SelectCustomersActivity" android:exported="false"/>
        <activity android:name=".task.activity.SelectGoodsActivity" android:exported="false"/>
        <activity android:name=".ui.fragment.lzcustomer.LzCustomerSearchActivity" android:exported="false"/>
        <activity android:name=".ui.fragment.lzcustomer.LzPrivateDetailActivity" android:exported="false" />
        <activity android:name=".ui.fragment.lzcustomer.LzPublicDetailActivity" android:exported="false"/>
        <activity
            android:name=".license.activity.SelectAreaActivity"
            android:theme="@style/custom_transparent">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/select_area"
                    android:scheme="xyy" />
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity
            android:name=".goods.ui.GoodsDetailActivity"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/good_detail"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".task.activity.BaseTaskSearchActivity" android:exported="false"/>
        <activity android:name=".goods.ui.PromotionCustomListActivity" android:exported="false"/>
        <activity android:name=".home.sales.ui.SalesDataConfigActivity" android:exported="false"/>
        <activity android:name=".home.sales.ui.SalesDataDictActivity" android:exported="false"/>
        <activity android:name=".ui.fragment.drugstore.minedrug.LicensePhotoViewActivity" android:exported="false"/>
        <activity
            android:name=".goodsmanagement.activity.GoodsManagementActivity"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/goods/management"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity
            android:name=".goodsmanagement.activity.PrivateCustomerGoodsManagementActivity"
            android:exported="true"
            android:launchMode="singleTask" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/goodcontrol"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".goodsmanagement.activity.GoodsManagementSelectGoodsActivity" android:exported="false"/>
        <activity android:name=".goodsmanagement.activity.BaseSearchActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/goods_search_history"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".goodsmanagement.activity.BaseSearchResultActivity" android:exported="false" />
        <activity android:name=".goods.ui.AddSellPointActivity" android:exported="false"/>
        <activity android:name=".goods.ui.AddTagActivity" android:exported="false"/>
        <activity
            android:name="com.tencent.bugly.beta.ui.BetaActivity"
            android:exported="false"
            android:configChanges="keyboardHidden|orientation|screenSize|locale"
            android:theme="@android:style/Theme.Translucent" />

        <receiver android:name=".schedule.service.PhoneStateReceiver">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.PHONE_STATE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".flutter.CustomFlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:exported="false"
            android:windowSoftInputMode="adjustResize">


        </activity>
        <activity
            android:name=".ui.activity.SettingActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/mine/setting"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".flutter.debug.FlutterJumpActivity" android:exported="false"/>
        <activity android:name=".ui.activity.drugstore.AreaSelectActivityV2">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/select_area_v2"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <!--  荷叶健康客户搜索      -->
        <activity android:name=".ui.fragment.hycustomer.HyCustomerSearchActivity" android:exported="false"/>
        <!--  荷叶健康公海客户详情      -->
        <activity android:name=".ui.fragment.hycustomer.HyPublicDetailActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/hy_public_detail"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <!--  荷叶健康私海客户详情      -->
        <activity android:name=".ui.fragment.hycustomer.HyPrivateDetailActivity" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/customer/hy_private_detail"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <!--  荷叶健康客户详情item      -->
        <activity android:name=".ui.fragment.hycustomer.HyMineDrugstoreDetailItemActivity" android:exported="false" />
        <!-- 荷叶健康联系人 -->
        <activity
            android:name=".ui.fragment.hycontact.HyContactActivity" android:exported="false"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <!-- 荷叶健康选择联系人 -->
        <activity android:name=".ui.fragment.hycontact.HySelectContactsActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/schedule/hy_select_contacts"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".goodsmanagement.activity.PublicCustomerGoodsManagementActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/public_customer/good_manager"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
        <activity android:name=".ui.activity.pdf.PdfDisplayActivity">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="crm-app.ybm100.com"
                    android:path="/crm/tbspdfdisplay"
                    android:scheme="xyy" />
            </intent-filter>
        </activity>
    </application>

</manifest>