package com.ybm100.app.crm.utils;

import android.content.Context;

import java.util.HashMap;
import java.util.Map;

/**
 * Quick Tracking SDK使用示例
 * 展示如何在实际业务中使用统计功能
 */
public class QuickTrackingExample {
    
    /**
     * 示例：用户登录事件统计
     */
    public static void trackUserLogin(Context context, String userId, String loginMethod) {
        // 记录用户登录
        QuickTrackingUtils.onUserLogin(context, userId);
        
        // 记录登录方式等额外信息
        Map<String, Object> properties = new HashMap<>();
        properties.put("user_id", userId);
        properties.put("login_method", loginMethod); // 如：'phone', 'wechat', 'password'
        properties.put("login_time", System.currentTimeMillis());
        
        QuickTrackingUtils.trackEvent(context, "user_login", properties);
    }
    
    /**
     * 示例：页面访问统计
     */
    public static void trackPageView(Context context, String pageName) {
        // 记录页面开始
        QuickTrackingUtils.onPageStart(context, pageName);
        
        // 记录页面访问事件
        Map<String, Object> properties = new HashMap<>();
        properties.put("page_name", pageName);
        properties.put("visit_time", System.currentTimeMillis());
        
        QuickTrackingUtils.trackEvent(context, "page_view", properties);
    }
    
    /**
     * 示例：按钮点击事件统计
     */
    public static void trackButtonClick(Context context, String buttonName, String pageName) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("button_name", buttonName);
        properties.put("click_time", System.currentTimeMillis());
        
        QuickTrackingUtils.trackEventWithPage(context, "button_click", pageName, properties);
    }
    
    /**
     * 示例：商品查看事件统计
     */
    public static void trackProductView(Context context, String productId, String productName, String pageName) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("product_id", productId);
        properties.put("product_name", productName);
        properties.put("event_time", System.currentTimeMillis());
        
        QuickTrackingUtils.trackEventWithPage(context, "product_view", pageName, properties);
    }
    
    /**
     * 示例：搜索事件统计
     */
    public static void trackSearch(Context context, String keyword, String searchType, String pageName) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("keyword", keyword);
        properties.put("search_type", searchType); // 如：'product', 'customer', 'order'
        properties.put("search_time", System.currentTimeMillis());
        
        QuickTrackingUtils.trackEventWithPage(context, "search", pageName, properties);
    }
}
