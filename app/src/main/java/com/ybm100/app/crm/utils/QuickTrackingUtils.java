package com.ybm100.app.crm.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.config.AppNetConfig;
import com.ybm100.app.crm.platform.RuntimeEnv;

import java.util.HashMap;
import java.util.Map;

/**
 * Quick Tracking SDK工具类
 * 用于统一管理阿里云Quick Tracking统计分析SDK的初始化和使用
 */
public class QuickTrackingUtils {
    private static final String TAG = "QuickTrackingSDK";
    private static boolean isInitialized = false;
    
    /**
     * 初始化Quick Tracking SDK
     * 注意：只有在用户同意隐私政策后才能调用此方法
     * 
     * @param context 应用上下文
     */
    public static void initSDK(Context context) {
        if (isInitialized) {
            Log.i(TAG, "SDK already initialized");
            return;
        }
        
        try {
            // 获取当前环境信息来决定使用哪个AppKey
            String appKey = getAppKey();
            String channel = getChannel();
            
            if (TextUtils.isEmpty(appKey)) {
                Log.w(TAG, "AppKey is empty, skipping SDK initialization");
                return;
            }
            
            // 使用反射调用SDK初始化方法
            Class<?> qtConfigureClass = Class.forName("com.quick.qt.analytics.QtConfigure");
            java.lang.reflect.Method initMethod = qtConfigureClass.getMethod("init", 
                Context.class, String.class, String.class, int.class, String.class);
            
            // DEVICE_TYPE_PHONE = 1
            initMethod.invoke(null, context, appKey, channel, 1, null);
            
            isInitialized = true;
            Log.i(TAG, "SDK initialized successfully - AppKey: " + appKey + ", Channel: " + channel);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize SDK", e);
        }
    }
    
    /**
     * 自定义事件统计
     * 
     * @param context 上下文
     * @param eventName 事件名称
     * @param properties 事件属性
     */
    public static void trackEvent(Context context, String eventName, Map<String, Object> properties) {
        if (!isInitialized) {
            Log.w(TAG, "SDK not initialized, skipping event: " + eventName);
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onEventMethod = qtTrackAgentClass.getMethod("onEventObject", 
                Context.class, String.class, Map.class);
            
            onEventMethod.invoke(null, context, eventName, properties != null ? properties : new HashMap<>());
            Log.d(TAG, "Event tracked: " + eventName + " with properties: " + properties);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track event: " + eventName, e);
        }
    }
    
    /**
     * 带页面信息的自定义事件统计
     * 
     * @param context 上下文
     * @param eventName 事件名称
     * @param pageName 页面名称
     * @param properties 事件属性
     */
    public static void trackEventWithPage(Context context, String eventName, String pageName, Map<String, Object> properties) {
        if (!isInitialized) {
            Log.w(TAG, "SDK not initialized, skipping event: " + eventName);
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onEventMethod = qtTrackAgentClass.getMethod("onEventObjectWithPage", 
                Context.class, String.class, String.class, Map.class);
            
            onEventMethod.invoke(null, context, eventName, pageName, properties != null ? properties : new HashMap<>());
            Log.d(TAG, "Event tracked: " + eventName + " on page: " + pageName + " with properties: " + properties);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track event: " + eventName + " on page: " + pageName, e);
        }
    }
    
    /**
     * 页面开始统计
     * 
     * @param context 上下文
     * @param pageName 页面名称
     */
    public static void onPageStart(Context context, String pageName) {
        if (!isInitialized) {
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onPageStartMethod = qtTrackAgentClass.getMethod("onPageStart", 
                Context.class, String.class);
            
            onPageStartMethod.invoke(null, context, pageName);
            Log.d(TAG, "Page started: " + pageName);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track page start: " + pageName, e);
        }
    }
    
    /**
     * 页面结束统计
     * 
     * @param context 上下文
     * @param pageName 页面名称
     */
    public static void onPageEnd(Context context, String pageName) {
        if (!isInitialized) {
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onPageEndMethod = qtTrackAgentClass.getMethod("onPageEnd", 
                Context.class, String.class);
            
            onPageEndMethod.invoke(null, context, pageName);
            Log.d(TAG, "Page ended: " + pageName);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track page end: " + pageName, e);
        }
    }
    
    /**
     * 用户登录统计
     * 
     * @param context 上下文
     * @param userId 用户ID
     */
    public static void onUserLogin(Context context, String userId) {
        if (!isInitialized) {
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onProfileSignInMethod = qtTrackAgentClass.getMethod("onProfileSignIn", 
                Context.class, String.class);
            
            onProfileSignInMethod.invoke(null, context, userId);
            Log.d(TAG, "User login tracked: " + userId);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track user login: " + userId, e);
        }
    }
    
    /**
     * 用户登出统计
     * 
     * @param context 上下文
     */
    public static void onUserLogout(Context context) {
        if (!isInitialized) {
            return;
        }
        
        try {
            Class<?> qtTrackAgentClass = Class.forName("com.quick.qt.analytics.QtTrackAgent");
            java.lang.reflect.Method onProfileSignOffMethod = qtTrackAgentClass.getMethod("onProfileSignOff", 
                Context.class);
            
            onProfileSignOffMethod.invoke(null, context);
            Log.d(TAG, "User logout tracked");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to track user logout", e);
        }
    }
    
    /**
     * 获取Quick Tracking AppKey
     * 根据当前环境返回对应的AppKey
     */
    private static String getAppKey() {
        if (isAppProd()) {
            // 生产环境AppKey - 请替换为您在阿里云控制台申请的生产环境AppKey
            return "your_production_appkey_here";
        } else {
            // 测试环境AppKey - 请替换为您在阿里云控制台申请的测试环境AppKey  
            return "your_test_appkey_here";
        }
    }

    /**
     * 获取Quick Tracking渠道
     */
    private static String getChannel() {
        if (isAppProd()) {
            return "official"; // 生产环境渠道
        } else {
            return "test"; // 测试环境渠道
        }
    }

    /**
     * 判断是否为生产环境
     */
    private static boolean isAppProd() {
        String currFlavor = SharedPrefManager.getInstance().getCurrFlavor();
        if (TextUtils.isEmpty(currFlavor) || AppNetConfig.FlavorType.CUSTOM.equals(currFlavor)) {
            currFlavor = RuntimeEnv.INSTANCE.getEnv();
        }
        return AppNetConfig.FlavorType.PROD.equalsIgnoreCase(currFlavor);
    }
    
    /**
     * 检查SDK是否已初始化
     */
    public static boolean isInitialized() {
        return isInitialized;
    }
}
