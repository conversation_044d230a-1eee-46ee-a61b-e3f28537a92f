Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter attach --app-id com.xyycrm

## exception

SentinelException: [Sentinel type: Sentinel, kind: Collected, valueAsString: <collected>] from _flutter.setAssetBundlePath()

```
```

## flutter doctor

```
[✓] Flutter (Channel stable, 2.0.6, on macOS 12.3 21E230 darwin-arm, locale zh-Hans-CN)
    • Flutter version 2.0.6 at /Users/<USER>/.flutter
    • Framework revision 1d9032c7e1 (1 year, 4 months ago), 2021-04-29 17:37:58 -0700
    • Engine revision 05e680e202
    • Dart version 2.12.3
    • Pub download mirror https://pub.flutter-io.cn
    • Flutter download mirror https://storage.flutter-io.cn

[!] Android toolchain - develop for Android devices (Android SDK version 33.0.0)
    • Android SDK at /Users/<USER>/Library/Android/sdk
    • Platform android-33, build-tools 33.0.0
    ✗ Android SDK file not found: /Users/<USER>/Library/Android/sdk/platforms/android-33/android.jar.
    • Try re-installing or updating your Android SDK,
      visit https://flutter.dev/docs/get-started/install/macos#android-setup for detailed instructions.

[✓] Xcode - develop for iOS and macOS
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Xcode 13.4.1, Build version 13F100
    • CocoaPods version 1.11.3

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[!] Android Studio (version 2021.2)
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    ✗ Unable to find bundled Java version.
    • Try updating or re-installing Android Studio.

[✓] IntelliJ IDEA Ultimate Edition (version 2022.1.4)
    • IntelliJ at /Applications/IntelliJ IDEA.app
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart

[✓] VS Code (version 1.70.2)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.46.0

[✓] Connected device (2 available)
    • iPhone (mobile) • d08e334c2ad12c2d3623127f0242f1946fe73c29 • ios            • iOS 14.0
    • Chrome (web)    • chrome                                   • web-javascript • Google Chrome 104.0.5112.101

! Doctor found issues in 2 categories.
```
