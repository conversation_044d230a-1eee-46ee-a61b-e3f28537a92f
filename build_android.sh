#!/usr/bin/env bash


#set -e

echo "run shell file:$0"
echo "params count:$#"
echo "all params:$*"


while getopts v:n:p:t:b: opt
do
  case $opt in
    v)
      newVersion="$OPTARG"
      ;;
    n)
      userName="$OPTARG"
      ;;
    p)
      userPwd="$OPTARG"
      ;;
    t)
      token="$OPTARG"
      ;;
    b)
      branchName="$OPTARG"
      ;;
    *)
      shift
      break
      ;;
  esac
done

if [ -z $newVersion ];
then
  newVersion=''
fi

if [ -z $branchName ];
then
  branchName='origin/master'
fi

# 获取的参数
echo "newVersion：$newVersion"
echo "dingdingToken：$dingdingToken"
echo "branchName：$branchName"

branchName=${branchName#*/}

echo "new branchName：$branchName"



declare -a pluginNameList
declare -a pluginVersionList




# Flutter项目版本号自增或者固定版本号
IncreaseVersion() {
  echo "------------------------------处理版本号------------------------------"
	PUBSPEC_PATH='./pubspec.yaml'
	LINE=$(sed -n '/version:/=' $PUBSPEC_PATH)
	LINE_CONTENT=$(sed -n ${LINE}p $PUBSPEC_PATH)
	echo '获取的版本信息内容: '$LINE_CONTENT
	VERSION=${LINE_CONTENT//version:/}
	echo '编译前版本号:'$VERSION

	if [ -z "$newVersion" ];
	then
    echo "newVersion is empty"
    echo "需要自增版本号"
    VERSION_ARRAY=(${VERSION//./ })
    LAST_NUMBER=${VERSION_ARRAY[${#VERSION_ARRAY[@]} - 1]}
    #提取LAST_NUMBER中的数字，因为在window环境下换行符为\r\n，在linux、mac环境下打包时，换行会忽略\r，
    #这样版本号字符串提取时会多带出来一个\r，从而导致版本号自增失败。
    LAST_NUMBER=$(echo $LAST_NUMBER | tr -cd "[0-9]")
    LAST_NUMBER=$(($LAST_NUMBER + 1))
    VERSION_ARRAY[${#VERSION_ARRAY[@]} - 1]=$LAST_NUMBER
    for var in ${VERSION_ARRAY[@]}
    do
      if [ -n "$newVersion" ]
      then
        newVersion=$newVersion.$var
      else
        newVersion=$var
      fi
    done
    echo '新版本号：'$newVersion
  else
    echo "不需要自增版本号"
  fi

	NEW_VERSION_STR="version: $newVersion"
	sed -i "" "${LINE}s/${LINE_CONTENT}/${NEW_VERSION_STR}/g" $PUBSPEC_PATH
	newVersion="${branchName}-${newVersion}"
	echo "新版本号为${newVersion}"
	git add .
	git commit -m "更新了Flutter版本号：$newVersion"
	git push origin HEAD:$branchName
}




CheckResult(){
  if [ $? -ne 0 ]; then
      echo "failed"
      if [ $# -gt 0 ]; then
        exit 1
      fi
  else
      echo "succeed"
      if [ $# -gt 0 ]; then
        exit 0
      fi
  fi
}

#修改aar文件名
RenameAARFile() {
  echo "------------------------------修改aar文件名------------------------------"
  fileName="-${newVersion}.aar"
  newfileName=".aar"

  while read line
  do
      echo $line
      newPath=${line//$fileName/$newfileName}
      echo $newPath
      mv $line $newPath
  done< <(find build -name "*.aar")
}

#加载flutter plugins
LoadFlutterPlugins() {
  echo "------------------------------加载flutter plugins------------------------------"
  i=0
  while read -r line
  do
    firstChar=${line:0:1}
    if [ $firstChar != '#' ]; then
        echo $i
        echo $line
        pluginName=${line%=*}
        subVersionStr=${line##*-}
        subVersion=${subVersionStr%/*}
        pluginNameList[i]=$pluginName
        pluginVersionList[i]=$subVersion
        i=$[$i+1]
    fi
  done < .flutter-plugins
  echo "------load flutter plugin result------"

  for(( i=0;i<${#pluginNameList[@]};i++)) do
    echo "${pluginNameList[i]}-${pluginVersionList[i]}"
  done;
}


#修改pom内容
ModifyPomFile() {
  echo "------------------------------遍历pom文件，修改文件名及对应版本号------------------------------"
  fileName="-${newVersion}.pom"
  newfileName=".pom"

  while read line
  do
      newPath=${line//$fileName/$newfileName}
      mv $line $newPath
#      echo "修改文件名从${line}为${newPath}"

      #遍历加载的plugins，替换版本号
      for(( i=0;i<${#pluginNameList[@]};i++)) do
        echo "${pluginNameList[i]}-${pluginVersionList[i]}"
        target="${pluginNameList[i]}_release"
#        sed -n ${target} ${newPath}
        lineResult=$(sed -n "/${target}/=" $newPath)
        echo  "result:$lineResult"
        if [ $lineResult ]; then
          lineResult=$[$lineResult+1]
          echo "---------start:${lineResult}s/${newVersion}/${pluginVersionList[i]}/g---------"
          sed -i .bak "${lineResult}s/${newVersion}/${pluginVersionList[i]}/g" $newPath
        echo "---------end---------"
        fi
      done;

  done< <(find build -name "*.pom")
}


#上传所有aar和pom
UploadALLProduct() {


  while read line
  do
      echo $line
      RELEASE_NEXUS_URL='http://mvn.int.ybm100.com/repository/maven-releases/'


      AAR_FILE_PATH=$(find $line -name "*release.aar")
      POM_FILE_PATH=$(find $line -name "*release.pom")

       echo 'aar path:'$AAR_FILE_PATH
      echo 'pom path:'$POM_FILE_PATH


      groupIdLine=$(cat  $POM_FILE_PATH | grep '<groupId>' | head -1)
      artifactIdLine=$(cat  $POM_FILE_PATH | grep '<artifactId>' | head -1)
      versionLine=$(cat  $POM_FILE_PATH | grep '<version>' | head -1)


      groupId=$(echo "${groupIdLine}" | sed 's/<groupId>//g' | sed 's/<\/groupId>//g' | sed 's/[[:space:]]//g')
      artifactId=$(echo "${artifactIdLine}" | sed 's/<artifactId>//g' | sed 's/<\/artifactId>//g' | sed 's/[[:space:]]//g')
      version=$(echo "${versionLine}" | sed 's/<version>//g' | sed 's/<\/version>//g' | sed 's/[[:space:]]//g')


      echo $groupId
      echo $artifactId
      echo $version

      if [[ "$(mvn dependency:get -DremoteRepositories=${RELEASE_NEXUS_URL}\
       -DgroupId=${groupId} -DartifactId=${artifactId} -Dversion=${version} -Dtransitive=false\
        -Dpackaging=aar -DrepositoryId=nexus)" =~ 'BUILD SUCCESS' ]]; then
        echo "已存在，跳过"
      else
        echo "不存在，上传"
        mvn deploy:deploy-file\
        -DpomFile=$POM_FILE_PATH\
        -Dfile=$AAR_FILE_PATH\
        -Durl=$RELEASE_NEXUS_URL\
        -DrepositoryId="nexus"\
        -Dinternal.username=${userName}\
        -Dinternal.userpwd=${userPwd}\
        -s="./mvn-setting.xml"
      fi

  done< <(find build -name ${newVersion})
}

sendMessageToDingDing() {
  /usr/bin/curl "$1" -H 'Content-Type: application/json' -d "
  {
      \"actionCard\": {
          \"title\": \"$2\",
          \"text\": \"$3\",
          \"hideAvatar\": \"0\",
          \"btnOrientation\": \"0\",
          \"btns\": [
              {
                  \"title\": \"构建job\",
                  \"actionURL\": \"$4\"
              }
          ]
      },
      \"msgtype\": \"actionCard\"
  }"
}


#处理版本号
IncreaseVersion
#clean构建文件
flutter clean
flutter pub get
#执行aar构建
flutter build aar --no-debug --no-profile --build-number=${newVersion} --verbose
#加载flutter插件信息
LoadFlutterPlugins
#重命名aar文件
RenameAARFile
#修改pom文件信息
ModifyPomFile
#上传aar
UploadALLProduct

echo '---------FBI WARNING----------'
echo "开始发送消息"
echo "版本号：$newVersion"
echo "发起人：$userName"
echo '---------FBI WARNING----------'



if [ -z "$token" ]; then
  echo "token is empty"
  exit 0
fi


# 机器人 webhook 地址  根据情况修改  重点
WEIXIN_TOKEN_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=$token&debug=1"

sendImageMessage() {
   /usr/bin/curl "$1" -H 'Content-Type: application/json' -d "
  {
      \"image\": {
          \"base64\": \"$2\",
          \"md5\": \"$3\"
      },
      \"msgtype\": \"image\"
  }"
}

sendTextMessage() {
  /usr/bin/curl "$1" -H 'Content-Type: application/json' -d "
  {
      \"markdown\": {
          \"content\": \"$2\"
      },
      \"msgtype\": \"markdown\"
  }"
}

# 构建后通知消息 - 企业微信
SendMessageToEnterpriseWX() {
	/usr/bin/curl "$1" -H "Content-Type: application/json" -d "{\"markdown\": {\"content\": \"$2\"},\"msgtype\": \"markdown\"}"
}

SendMessageToEnterpriseWX ${WEIXIN_TOKEN_URL} "# ‼️‼️‼️<font color=\\\"warning\\\">FBI WARNING</font>‼️‼️‼️\n
><font color=\\\"comment\\\">豆芽Flutter构建结果</font>
><font color=\\\"comment\\\">·项目：${JOB_NAME}${BUILD_DISPLAY_NAME}</font>
><font color=\\\"info\\\">·版本号：$newVersion</font>
><font color=\\\"comment\\\">·构建人：$userName</font>
"







