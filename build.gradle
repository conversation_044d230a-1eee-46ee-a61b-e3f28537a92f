group 'com.qt.qt_common_sdk'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/central' }

    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/central' }

    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    api 'com.lydaas.qtsdk:qt-px-common:1.6.1.PX' //必选
}
