#!/usr/bin/env bash

set -e


while getopts b: opt
do
	case $opt in
		b) 
			build_type="$OPTARG" 
			;;
		*) 
			shift
			break
			;;
	esac
done

# 如果未传入构建类型，默认为Release
if [ -z $build_type ]; 
then
	build_type="Release"
fi

WORKSPACE_PATH=`pwd`

BuildFlutter() {
	echo 'build flutter start'
	ruby --version
	rm -rf ./build
	flutter clean
	flutter pub get


# 修改Podfile内容
cat > .ios/Podfile << EOF
source 'https://github.com/CocoaPods/Specs.git'
# 私有pod源地址
source '**********************:mobile/native-common/iOS-Common/CRM-PodSpecs.git'
source '**********************:mobile/ios/common/YBMRepos.git'
source '**********************:mobile/canary-ios-repo.git'
source '**********************:mobile/apm-cly/ios-sdk-repo.git'

platform :ios, '11.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure \"flutter pub get\" is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  pod 'FlutterPluginRegistrant', :path => File.join('Flutter', 'FlutterPluginRegistrant'), :inhibit_warnings => true
end

post_install do |installer|
  installer.generated_projects.each do |project|
    installer.pods_project.targets.each do |target|
        target.build_configurations.each do |config|
            config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '11.0'
        end
    end
  end
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
  end
end
EOF


	case $build_type in
		Debug)
			flutter build ios-framework --xcframework --debug --no-profile --no-release --verbose
			echo 'flutter build ios-framework --xcframework --debug --no-profile --no-release --verbose'
			;;
		Profile)
			flutter build ios-framework --xcframework --no-debug --profile --no-release --verbose
			echo 'flutter build ios-framework --xcframework --no-debug --profile --no-release --verbose'
			;;
		Release)
			flutter build ios-framework --xcframework --no-debug --no-profile --release --verbose
			echo 'flutter build ios-framework --xcframework --no-debug --no-profile --release --verbose'
			;;
		*)
			flutter build ios-framework --xcframework --no-debug --no-profile --release --verbose
			echo 'not found build_type'
			;;
	esac

	echo "build flutter end for build-type:"$build_type
}

BuildFlutter
