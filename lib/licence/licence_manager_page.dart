import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'bean/licence_filter_bean.dart';
import 'bean/licence_item_bean.dart';
import 'bean/tabInfo.dart';
import 'licence_tab_page.dart';
import 'widget/fix_tab_bar_view.dart';
import 'widget/licence_appbar.dart';

///资质列表页
class LicenceManagerPage extends BasePage {
  final String? tabCode;
  final String? statusType;

  LicenceManagerPage({this.tabCode, this.statusType});

  @override
  BaseState<StatefulWidget> initState() {
    return LicenceListState();
  }
}

class LicenceListState extends BaseState<LicenceManagerPage>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  PageController? _pageController;
  late StatusModel _statusModel;
  late LicenceListModel _licenceListModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();
  int currTab = 0;

  @override
  void onCreate() {
    _licenceListModel = LicenceListModel(_refreshController, _scrollController);
    _statusModel = StatusModel(
        _refreshController, _scrollController, _licenceListModel,
        defaultTabCode: widget.tabCode, defaultStatusStr: widget.statusType);
    _statusModel.requestFilterData();
    _pageController = PageController();
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<StatusModel>(create: (context) => _statusModel),
      ChangeNotifierProvider<LicenceListModel>(
          create: (context) => _licenceListModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    if (_tabController == null && _statusModel.tabs.length != 0) {
      _tabController =
          TabController(length: _statusModel.tabs.length, vsync: this);
    }
    return Consumer<StatusModel>(builder: (context, model, child) {
      return DefaultTabController(
          length: _statusModel.tabs.length,
          child: Scaffold(
            appBar: LicenceAppBar(_statusModel, _tabController, (index) {
              _statusModel.getFilterList(index);
              _statusModel.refreshPage(
                  _statusModel.dateType,
                  _statusModel.statusType,
                  _statusModel.filterList![index].code.toString(),
                  _statusModel.licenseType,
                  true);
              _pageController!.jumpToPage(index);
            }),
            body: FixTabBarView(
                tabController: _tabController,
                pageController: _pageController,
                physics: NeverScrollableScrollPhysics(),
                children: _statusModel.tabs.map((e) {
                  return Container(
                    child: e.widget,
                  );
                }).toList()),
          ));
    });
  }

  @override
  String getTitleName() {
    return '';
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}

class StatusModel extends ChangeNotifier {
  var _isDisposed = false;
  int? dateType = 0; //创建时间1:今日,5.本周 2.当月,3.上周, 4.上月
  int? statusType = -1; //单据状态
  int? licenseType = 0; //资质类型
  String? timeStr = "创建时间";
  String? statusStr = "单据状态";
  String? typeStr = "资质类型";

  String code = "1"; // tab对应code
  List<LicenceFilterBean>? filterList = [];
  List<LicenceFilterBean>? statueList = [];
  List<TabInfo> tabs = [];
  var _refreshController;
  var _scrollController;
  LicenceListModel? _licenceModel;

  String? defaultTabCode;
  String? defaultStatusStr;

  StatusModel(
      this._refreshController, this._scrollController, this._licenceModel,
      {this.defaultTabCode, this.defaultStatusStr});

  void requestFilterData() async {
    Network<LicenceFilterBean>(LicenceFilterBean()).requestListData(
        'licenseOptimize/licenseStatus',
        method: RequestMethod.GET,
        parameters: {}).then((value) {
      handleStatusResult(value.isSuccess, value.data);
    });
  }

  void handleStatusResult(bool isSuccess, List<LicenceFilterBean>? data) {
    if (!_isDisposed && isSuccess) {
      filterList = data;
      filterList!.forEach((element) {
        TabInfo info = TabInfo(
            element.name,
            LicenceTabPage(
                _refreshController, _scrollController, _licenceModel));
        tabs.add(info);
      });
      var tabIndex = getDefaultTabIndex(filterList);
      if (defaultTabCode != null) {
        statusType = getDefaultStatusType();
      }
      refreshPage(dateType, statusType, filterList![tabIndex].code.toString(),
          licenseType, true);
      getFilterList(tabIndex);
      defaultTabCode = null;
      defaultStatusStr = null;
      notifyListeners();
    }
  }

  int getDefaultTabCode() {
    var tabCode = 1;
    try {
      tabCode = int.tryParse(defaultTabCode ?? "0") ?? 0;
    } catch (e) {}
    return tabCode;
  }

  int getDefaultTabIndex(List<LicenceFilterBean>? filterList) {
    if (defaultTabCode == null) {
      return 0;
    }
    var tabCode = getDefaultTabCode();
    var tabIndex = 0;
    if (filterList != null) {
      for (int i = 0; i < filterList.length; i++) {
        if (filterList[i].code == tabCode) {
          tabIndex = i;
          break;
        }
      }
    }
    return tabIndex;
  }

  int getDefaultStatusType() {
    var tabCode = 0;
    try {
      tabCode = int.tryParse(defaultStatusStr ?? "0") ?? 0;
    } catch (e) {}
    return tabCode;
  }

  void refreshPage(int? dateType, int? statusStr, String code, int? licenseType,
      bool reSetFilter) {
    _licenceModel!.dateType = dateType;
    _licenceModel!.statusStr = statusStr;
    _licenceModel!.code = code;
    _licenceModel!.licenseType = licenseType;
    this.code = code;
    if (reSetFilter) {
      //筛选项进行重置
      this.dateType = 0;
      if (defaultTabCode != null) {
        statusType = getDefaultStatusType();
      } else {
        this.statusType = -1;
      }
      this.licenseType = 0;
      this.timeStr = "创建时间";
      if (defaultStatusStr == null) {
        this.statusStr = "单据状态";
      } else {
        try {
          this.statusStr = filterList![getDefaultTabIndex(filterList)]
                  .licenseStatusList
                  ?.firstWhere((element) => element.code == statusType)
                  .name ??
              "";
        } catch (e) {}
      }
      this.typeStr = "资质类型";
    }
    _licenceModel!.requestListData(true);
  }

  void getFilterList(int index) {
    statueList = filterList![index].licenseStatusList;
    dateType = 0;
    if (defaultTabCode != null) {
      statusType = getDefaultStatusType();
    } else {
      statusType = -1;
    }
    licenseType = 0;
    notifyListeners();
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}

class LicenceListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool isLastPage = false;
  int pageNum = 0;
  bool forceRefresh = false;
  List<LicenceItemBean>? list;
  bool? isSuccess;
  String? keyword;
  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;
  int? dateType = 0;
  int? statusStr = 0;
  String? code;
  int? licenseType = 0;

  LicenceListModel(this._refreshController, this._scrollController);

  void requestListData(bool refresh) async {
    if (refresh) {
      pageNum = 0;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    Network<LicenceItemBean>(LicenceItemBean())
        .requestListData('licenseOptimize/queryLicenseAuditList',
            method: RequestMethod.POST,
            contentType: RequestContentType.FORM,
            parameters: buildParamsMap())
        .then((value) {
      handleResult(value.isSuccess, value.data);
    });
  }

  Map<String, String?> buildParamsMap() {
    Map<String, String?> params = Map();
    if (dateType != 0) {
      params["dateType"] = dateType.toString();
    }
    if (statusStr != null) {
      params["statusStr"] = statusStr.toString();
    }
    if (code!.isNotEmpty) {
      params["code"] = code;
    }
    if (licenseType != 0) {
      params["licenseType"] = licenseType.toString();
    }
    params["limit"] = "10";
    params["offset"] = pageNum.toString();
    return params;
  }

  void handleResult(bool isSuccess, List<LicenceItemBean>? data) {
    EasyLoading.dismiss();
    if (!_isDisposed && isSuccess) {
      this.isSuccess = isSuccess;
      if (data != null) {
        var tempList = data;
        if (forceRefresh) {
          list = tempList;
          _scrollController.jumpTo(0);
        } else {
          list!.addAll(tempList);
        }
        isLastPage = false;
      } else {
        isLastPage = true;
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage);
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
