
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-investment-visit-page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-operation-visit-page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-visit-detail-page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/main/business-main-page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/loading_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/page/ybm_customer_map_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/page/customer_search_tab_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/page/ybm_customer_search_history.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/detail/customer_private_detail_page_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/schedule/customer_schedule_record_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/page/customer_basic_info_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/coupons/page/customer_coupon_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/recommendation/page/goods_recommend_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/refund/page/customer_refund_order_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/page/customer_search_word.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/basic_info/customer_public_basic_info.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/claim_record/customer_claim_record_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/detail/customer_public_detail_page_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/order_record/customer_public_order_record.dart';
import 'package:XyyBeanSproutsFlutter/customer/statistics/active_customer_today_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/goods_already_buy_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/goods_no_buy_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/commodity_detail_list_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/commodity_rank_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_authorize_goods_parent_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_authorize_merchant_parent_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goods_result.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goods_search_history.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goodset_detail.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_manager_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/goods_management_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/task_detail_page.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/page/sales_result_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/licence_examine_log_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/licence_manager_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/licence_search.dart';
import 'package:XyyBeanSproutsFlutter/licence/license_detail_page_v2.dart';
import 'package:XyyBeanSproutsFlutter/licence/license_edit_base_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/photo_list_view_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/photo_view_page.dart';
import 'package:XyyBeanSproutsFlutter/main/OpenMainBridgetHandler.dart';
import 'package:XyyBeanSproutsFlutter/main/page/main_tab_page.dart';
import 'package:XyyBeanSproutsFlutter/message/page/message_root_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/page/coupons_detail_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/page/coupons_group_select_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/funnel_cumulative_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/funnel_cumulative_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/customer_funnel_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/funnel_level_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/funnel_level_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/funnel_potential_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/funnel_potential_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_no_pin_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_no_pin_nearly_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_no_pin_nearly_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_on_pin_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_pin_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_pin_nearly_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_pin_nearly_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/funnel_pin_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/funnel_quality_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/funnel_quality_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/personal_info/personal_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/pop_data_merchant_list.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/pop_data_statistics.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/pop_merchant_statistics.dart';
import 'package:XyyBeanSproutsFlutter/order/logistics_info_page.dart';
import 'package:XyyBeanSproutsFlutter/order/order_detail_refund_page.dart';
import 'package:XyyBeanSproutsFlutter/order/order_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/order/order_search_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/team_performance_ka_or_bd_list_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/team_performance_list_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/team_performance_rank_list_page.dart';
import 'package:XyyBeanSproutsFlutter/price/page/product_search_history.dart';
import 'package:XyyBeanSproutsFlutter/price/page/provice_price_health_list.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_accompany_visit_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_visit_recommendation_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_visit_Planning_roadmap_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_perfect_visit_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_visit_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/schedule_relevance_task_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/add_contact_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/visit_select_contact.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/visit_select_object.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/visit_select_object_search.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/map_test.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/schedule_analysis_bd_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/schedule_analysis_bd_today_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/schedule_analysis_bdm_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/schedule_no_visit_customer_bd_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/schedule_no_visit_customer_bdm_page.dart';
import 'package:XyyBeanSproutsFlutter/statistics/custom_statistics_page.dart';
import 'package:XyyBeanSproutsFlutter/statistics/custom_statistics_result_page.dart';
import 'package:XyyBeanSproutsFlutter/statistics/statistics_order_page.dart';
import 'package:XyyBeanSproutsFlutter/task/task_association_visit.dart';
import 'package:XyyBeanSproutsFlutter/task/task_child_list_page.dart';
import 'package:XyyBeanSproutsFlutter/task/task_child_result_page.dart';
import 'package:XyyBeanSproutsFlutter/task/task_child_search_page.dart';
import 'package:XyyBeanSproutsFlutter/task/task_dotask_page.dart';
import 'package:XyyBeanSproutsFlutter/visit/visit_detail_page.dart';
import 'package:XyyBeanSproutsFlutter/visit/visit_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/visit/vistPlan/add_visit_plan_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:XyyBeanSproutsFlutter/apply/super_worry_free_application.dart';
import 'package:XyyBeanSproutsFlutter/apply/order/order_list_page.dart';
import 'package:XyyBeanSproutsFlutter/apply/history/order_manage_page.dart';
import 'package:XyyBeanSproutsFlutter/apply/history/order_search_page.dart';
import 'package:XyyBeanSproutsFlutter/apply/application_request_successfull.dart';

import 'businessOperation/home/<USER>/business-investment-rank-page.dart';
import 'businessOperation/home/<USER>/business-operation-rank-page.dart';
import 'common/header_footer/header_footer_helper.dart';
import 'message/page/message_list_page.dart';
import 'mine/coupons/page/coupons_list_page.dart';
import 'mine/setting/change_password.dart';
import 'order/order_detail_page.dart';
import 'order/order_detail_refund_cwy_page.dart';
import 'order/order_list_cwy_page.dart';
import 'order/order_manage_page.dart';
import 'order/order_moderation_cwy_page.dart';
import 'order/widgets/order_detail_exceptionreason_item.dart';
import 'visit/visit_manage_page.dart';
import 'visit/vistPlan/visit_plan_page.dart';
import 'visit/addPlan/add_plan_page.dart';
import 'price/page/price_health_home_page.dart';
import 'price/page/price_health_ecspu_list.dart';
import 'price/page/price_health_sku_list.dart';
import 'price/page/provice_price_health_list.dart';
import 'price/page/product_search_history.dart';


void main() {
  print("guan call flutter main ${window.defaultRouteName}");
  XYYContainer.init(
    routes,
    appBuilder: (context, navigatorKey, initialRoutePath, customObserver,
        onGenerateRoute, onGenerateInitialRoutes) {
      print('appbuilder:$initialRoutePath');
      return MaterialApp(
        navigatorKey: navigatorKey,
        initialRoute: initialRoutePath,
        debugShowCheckedModeBanner: false,
        onGenerateRoute: onGenerateRoute,
        onGenerateInitialRoutes: onGenerateInitialRoutes,
        navigatorObservers: [customObserver],
        builder: EasyLoading.init(),
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
        ],
        supportedLocales: [
          const Locale('zh'),
        ],
      );
    },
  );
  injectCustomBridge();
  compatTranslucentStatusBar();
  configRefreshHeaderFooter();
  configEasyLoading();
  initStatusPadding();
}

int statusPadding = -1;

void initStatusPadding() {
  if (Platform.isAndroid) {
    SharedPreferences.getInstance().then((value) {
      var padding = value.get("status_padding");
      if (padding == null || (padding is String && padding.isEmpty)) {
        //未适配
        statusPadding = 0;
      } else {
        //适配
        statusPadding = 50;
      }
    });
  }
}

/// 全局设置刷新加载样式
void configRefreshHeaderFooter() {
  EasyRefresh.defaultHeader = HeaderFooterHelp().getHeader();
  EasyRefresh.defaultFooter = HeaderFooterHelp().getFooter();
}

/// 设置全局Loading样式
void configEasyLoading() {
  EasyLoading.instance.indicatorType = EasyLoadingIndicatorType.ring;
}

Future<String>initMianPage() async {
  
  String? mianType = await XYYContainer.storageChannel.getValue('mianType_shop',space:'douya_shop');
    try {
      if (mianType == null) {
        var result = await XYYContainer.requestChannel.request('/mop/account/query',
            method: RequestMethod.GET,
            contentType: RequestContentType.FORM,
            parameters: {});
        var data = json.decode(result);
        if (data['status'] == 'success' && data['data']['permissionLevelCode'] == 2 && data['data']['shouldRedirectToMop'] == true) {
              XYYContainer.storageChannel.put('mianType_shop', 'shop',space:'douya_shop');
              return 'shop';
        }
        XYYContainer.storageChannel.put('mianType_shop', 'douya',space:'douya_shop');
        return 'douya';
      }
      
      return mianType;
    } catch (e) {
      return 'douya';
    }
    

}

final routes = {
  '/': (context, arguments) => ReplacePage(),
  '/main': (context, arguments) => FutureBuilder(
        future: initMianPage(),
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done) {
            return LoadingPage();
          }
          if (snapshot.data == 'shop') {
            return BusinessMainPage();
          } else{
            return MainTabPage();
          }
        },
      ),
'/change_password': (context, arguments) => ChangePassword(),
  '/personal_data': (context, arguments) => PersonPage(),
  '/custom_statistics': (context, arguments) => CustomStatisticsPage(),
  '/custom_statistics_result': (context, arguments) =>
      CustomStatisticsResultPage(arguments["result"]),
  '/licence_manager_page': (context, arguments) => LicenceManagerPage(
      tabCode: arguments["tabCode"], statusType: arguments["statusType"]),
  '/licence_search': (context, arguments) => LicenceSearch(),
  '/licence_examine_log': (context, arguments) =>
      LicenceExamineLogPage(arguments["licenseAuditId"], arguments["type"]),
  '/licence_edit_base_page': (context, arguments) => LicenseEditBasePage(
        qualificationNo: arguments["qualificationNo"],
        isDraft: arguments["isDraft"],
        mIsAddType: arguments["mIsAddType"],
        merchantId: arguments["merchantId"],
        from: arguments["from"],
        ecOrgCode: arguments["ecOrgCode"],
        type: arguments["type"],
        licenseInitDataJson: arguments["licenseInitDataJson"],
      ),
  '/task_child_list_page': (context, arguments) =>
      TaskChildListPage(arguments["mainTaskId"]?.toString() ?? ""),
  '/task_child_search_page': (context, arguments) =>
      TaskChildSearchPage(arguments["mainTaskId"]),
  '/task_child_result_page': (context, arguments) =>
      TaskChildResultPage(arguments["subTaskId"]?.toString() ?? ""),
  '/schedule_relevance_task_page': (context, arguments) =>
      ScheduleRelevanceTaskPage(arguments["customerId"] ?? ""),
  '/task_dotask': (context, arguments) => TaskDoTaskPage(
        taskId: arguments['taskId']?.toString() ?? "",
        isFromVisit: arguments['isFromVisit'] ?? false,
      ),
  '/task_association': (context, arguments) => TaskAssociationVisitPage(
        taskId: arguments['taskId'] ?? "",
      ),
  '/order_detail_page': (context, arguments) => OrderDetailPage(
        orderId: arguments["orderId"]?.toString() ?? "",
        merchantId: arguments["merchantId"]?.toString() ?? "",
        isParent:arguments["isParent"]
      ),
  '/order_filter_page': (context, arguments) => OrderFilterPage(
        sourceJSON: arguments['sourceJSON'],
        isRefund: arguments['isRefund'],
        currentParams: arguments['currentParams'],
      ),
  //   商品价格力
  '/price_health_home_page': (context, arguments) => PriceHealthHomePage(),
  '/price_health_ecspu_list': (context, arguments) => PriceHealthEcspuList(
      provinceCode: arguments['provinceCode'],
  ),

  '/price_health_sku_list': (context, arguments) => PriceHealthSkuList(
      spuId: arguments["spuId"]?.toString() ?? "",
      businessFormatType: arguments['businessFormatType'],
      platformType: arguments['platformType'],
      provinceCode: arguments['provinceCode'],
  ),
  '/provice_price_health_list': (context, arguments) => ProvicePriceHealthList(),
  '/product_search_history': (context, arguments) => ProductSearchHistory(),
  '/Logistics': (context, arguments) => arguments == null
      ? LogisticsInfoPage()
      : LogisticsInfoPage(arguments: arguments['arguments']),
  '/OrderDetailReasonPage': (context, arguments) => OrderDetailReasonPage(
        refuseReason: arguments['refuseReason'],
      ),
  '/OrderDetailRefundPage': (context, arguments) => OrderDetailRefundPage(
        orderId: arguments["orderId"]?.toString() ?? "",
        merchantId: arguments["merchantId"]?.toString() ?? "",
      ),
  '/GoodsManagement': (context, arguments) => GoodsManagement(
        roleType: arguments["roleType"],
        selectedTab: arguments['selectedTab'],
      ),
  '/order_manage_page': (context, arguments) => OrderManagePage(
        arguments["pageType"],
        dateType: arguments['DATE_TYPE'],
        id: arguments["id"],
        isGroup: arguments["isGroup"],
        areaName: arguments["areaName"],
        statusList: arguments['statusList'],
        exceptionTypeList: arguments['exceptionTypeList'],
        startCreateTime: arguments['startCreateTime'],
        endCreateTime: arguments['endCreateTime'],
        sceneType: arguments['sceneType'],
      ),
  '/order_search_page': (context, arguments) => OrderSearchPage(
        arguments["pageType"],
        filterParams: arguments['filterParams'],
      ),
  '/TaskDetailPage': (context, arguments) => TaskDetailPage(
        taskId: arguments["taskId"],
      ),
  '/license_detail_page': (context, arguments) => LicenseDetailPageV2(
      arguments["licenseAuditId"]?.toString(), arguments["type"]?.toString()),
  '/photo_view_page': (context, arguments) => PhotoViewPage(
      arguments["urlPath"],
      delete: arguments["delete"],
      callback: arguments["callback"]),
  '/photo_list_view_page': (context, arguments) => PhotoListViewPage(
      urlPathListJson: arguments["urlPathListJson"],
      titleListJson: arguments["titleListJson"],
      isWaterMark: arguments["isWaterMark"],
      position: arguments["position"],
      subTitleListJson: arguments["subTitleListJson"]),
  '/visit_manage_page': (context, arguments) => VisitManagePage(
        userId: arguments["userId"],
        userName: arguments["userName"],
      ),
  '/visit_detail_page': (context, arguments) =>
      VisitDetailPage(arguments['scheduleId']),
  '/visit_filter_page': (context, arguments) => VisitFilterPage(
      sourceJSON: arguments['sourceJSON'],
      currentParams: arguments['currentParams']),
  '/message_list_page': (context, arguments) => MessageListPage(
        messageTitle: arguments["messageTitle"],
        messageType: arguments["messageType"],
        subPage: arguments["subPage"],
      ),
  '/add_visit_page': (context, arguments) => AddVisitPage(
        externalJson: arguments["externalJson"],
        rolesJSON: arguments["rolesJSON"],
        isHeyeVisit: arguments["isHeyeVisit"],
      ),
  '/add_plan_page': (context, arguments) => AddPlanPage(
      // lat: arguments?['latitude'],
      // lng: arguments?['longitude'],
      ),
  '/visit_plan_page': (context, arguments) => VisitPlanPage(
        lat: arguments?['latitude'],
        lng: arguments?['longitude'],
      ),
  '/add_visit_plan_page': (context, arguments) => AddVisitPlanPage(),
  '/add_accompany_visit_page': (context, arguments) => AddAccompanyVisitPage(
        externalJson: arguments["externalJson"],
        rolesJSON: arguments["rolesJSON"],
        isHeyeVisit: arguments["isHeyeVisit"],
      ),
  '/add_visit_recommendation_page': (context, arguments) =>
      AddVisitRecommendationPage(
        lat: arguments?['lat'],
        lng: arguments?['lng'],
      ),
  '/add_visit_Planning_roadmap_page': (context, arguments) =>
      AddVisitPlanningRoadmap(
        planIdStr: arguments["planIdStr"],
      ),
  '/add_perfect_visit_page': (context, arguments) => AddPerfectVisitPage(
        perfectJSON: arguments["perfectJSON"],
      ),
  '/schedule_select_object': (context, arguments) =>
      VisitSelectObject(rolesJSON: arguments["rolesJSON"]),
  '/select_object_filter': (context, arguments) => ObjectFilterPage(
        filterParams: arguments['filterParams'],
        cacheParams: arguments['cacheParams'],
      ),
  '/select_object_search': (context, arguments) =>
      VisitSelectObjectSearchPage(roleCode: arguments['roleCode']),
  '/visit_select_contact': (context, arguments) => VisitSelectContactPage(
        customerId: arguments["customerId"],
        customerName: arguments["customerName"],
        selectId: arguments["selectId"],
      ),
  '/add_contact_page': (context, arguments) =>
      AddContactPage(contactJSON: arguments["contactJSON"]),
  '/customer_funnel': (context, arguments) => CustomerFunnelPage(
        oaId: arguments["oaId"],
      ),
  '/funnel_cumulative_page': (context, arguments) => FunnelCumulativePage(
        totalNum: arguments["totalNum"],
        deptCode: arguments["deptCode"],
        oaId: arguments["oaId"],
      ),
  '/funnel_cumulative_merchant_page': (context, arguments) =>
      FunnelCumulativeMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        totalNum: arguments["totalNum"],
      ),
  '/funnel_pin_nearly_page': (context, arguments) => FunnelPinNearlyPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
        moveSaleTotalCustNum: arguments["moveSaleTotalCustNum"],
      ),
  '/funnel_pin_nearly_merchant_page': (context, arguments) =>
      FunnelPinNearlyMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        moveSaleTotalCustNum: arguments["moveSaleTotalCustNum"],
      ),
  '/funnel_no_pin_nearly_page': (context, arguments) => FunnelNoPinNearlyPage(
        noMoveSaleTotalCustNum: arguments["noMoveSaleTotalCustNum"],
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
      ),
  '/funnel_no_pin_nearly_merchant_page': (context, arguments) =>
      FunnelNoPinNearlyMerchantPage(
        noMoveSaleTotalCustNum: arguments["noMoveSaleTotalCustNum"],
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
      ),
  '/funnel_pin_page': (context, arguments) => FunnelPinPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
        moveSaleTotalCustNum: arguments["moveSaleTotalCustNum"],
      ),
  '/funnel_pin_merchant_page': (context, arguments) => FunnelPinMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        totalCustNum: arguments["totalCustNum"],
      ),
  '/funnel_no_pin_page': (context, arguments) => FunnelNoPinPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
        noMoveSaleTotalCustNum: arguments["noMoveSaleTotalCustNum"],
      ),
  '/funnel_no_pin_merchant_page': (context, arguments) =>
      FunnelNoPinMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        nonMoveSaleTotalCustNum: arguments["nonMoveSaleTotalCustNum"],
      ),
  '/funnel_quality_page': (context, arguments) => FunnelQualityPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
        totalCustNum: arguments["totalCustNum"],
      ),
  '/funnel_quality_merchant_page': (context, arguments) =>
      FunnelQualityMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        totalCustNum: arguments["totalCustNum"],
      ),
  '/funnel_level_page': (context, arguments) => FunnelLevelPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
        sLevelNumber: arguments["sLevelNumber"],
      ),
  '/funnel_level_merchant_page': (context, arguments) =>
      FunnelLevelMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        sLevelNumber: arguments["sLevelNumber"],
      ),
  '/funnel_potential_page': (context, arguments) => FunnelPotentialPage(
        oaId: arguments["oaId"],
        deptCode: arguments["deptCode"],
      ),
  '/funnel_potential_merchant_page': (context, arguments) =>
      FunnelPotentialMerchantPage(
        oaId: arguments["oaId"],
        isBd: arguments["isBd"],
        potentialNumber: arguments["potentialNumber"],
      ),
  '/customer_public_detail': (context, arguments) =>
      CustomerPublicDetailPageV2(openSeaId: arguments['openSeaId']),
  '/customer_public_basic_info': (context, arguments) =>
      CustomerPublicBasicInfoPage(shopId: arguments["shopId"]),
  '/customer_public_order_record': (context, arguments) =>
      CustomerPublicOrderRecordPage(shopId: arguments["shopId"]),
  '/customer_schedule_record_page': (context, arguments) =>
      ScheduleRecordListPage(arguments["type"], arguments["customerId"]),
  '/customer_claim_record_page': (context, arguments) =>
      ScheduleClaimListPage(arguments["merchantId"], arguments["customerId"]),
  '/goods_no_buy_page': (context, arguments) => GoodsNoBuyPage(
        skuId: arguments['skuId'],
        sceneType: arguments['type'],
      ),
  '/goods_already_buy_page': (context, arguments) => GoodsAlreadyBuyPage(
        skuId: arguments['skuId'],
        type: arguments['type'],
      ),
  '/commodity_rank_page': (context, arguments) => CommodityRankPage(
        merchantId: arguments?["merchantId"],
        customerId: arguments?["customerId"],
      ),
  '/commodity_detail_list': (context, arguments) => CommodityDetailListPage(
        goodsName: arguments["goodsName"],
        merchantId: arguments["merchantId"],
        customerId: arguments["customerId"],
        productType: arguments["productType"],
        provinceCode: arguments["provinceCode"],
        rankType: arguments["rankType"],
        sortType: arguments["sortType"],
        searchSkuId: arguments["searchSkuId"],
        searchType: arguments["searchType"],
      ),
  '/pop_data_statistics': (context, arguments) => PopDataStatisticsPage(),
  '/pop_data_merchant_list': (context, arguments) => PopDataMerchantList(
      isSearch: arguments['isSearch'],
      period: arguments['period'],
      searchUserId: arguments['searchUserId'],
      groupId: arguments['groupId']),
  '/pop_merchant_statistics': (context, arguments) => PopMerchantStatistics(
      merchantName: arguments['merchantName'],
      merchantId: arguments['merchantId']),
  '/control_manager_page': (context, arguments) => ControlManagerPage(
        merchantId: arguments?['merchantId'],
        customerId: arguments?['customerId'],
      ),
  '/control_search_history': (context, arguments) =>
      ControlGoodsSearchHistoryPage(
        searchType: arguments['searchType'],
        keyword: arguments['keyword'],
        skuCollectCode: arguments['skuCollectCode'],
        branchCode: arguments['branchCode'],
        branchName: arguments['branchName'],
      ),
  '/control_goods_result': (context, arguments) => ControlGoodsResultPage(
        keyword: arguments['keyword'],
        skuCollectCode: arguments['skuCollectCode'],
        branchCode: arguments['branchCode'],
        branchName: arguments['branchName'],
      ),
  '/control_goodset_detail': (context, arguments) => ControlGoodSetDetailPage(
        skuCollectCode: arguments['skuCollectCode'],
        skuCollectName: arguments['skuCollectName'],
        customerId: arguments['merchantId'],
      ),
  '/control_authorize_merchant_page': (context, arguments) =>
      ControlAuthorizeMerchantParentPage(
        arguments['skuId'],
        arguments['skuCollectCode'],
      ),
  '/control_authorize_goods_page': (context, arguments) =>
      ControlAuthorizeGoodsParentPage(
        arguments['skuCollectCode'],
        arguments['merchantId'],
        arguments['customerId'],
      ),
  '/active_customer_today_page': (context, arguments) =>
      ActiveCustomerTodayPage(arguments['groupId'], arguments['searchUserId']),
  '/schedule_analysis_bdm_page': (context, arguments) =>
      ScheduleAnalysisBDMPage(
        arguments['groupId'],
        arguments['searchUserId'],
        arguments['name'],
        arguments['isToday'],
      ),
  '/schedule_no_visit_customer_bdm_page': (context, arguments) =>
      ScheduleNoVisitCustomerBDMPage(
        arguments['groupId'],
        arguments['searchUserId'],
        arguments['name'],
      ),
  '/schedule_no_visit_customer_bd_page': (context, arguments) =>
      ScheduleNoVisitCustomerBDPage(
        arguments['searchUserId'],
      ),
  '/schedule_analysis_bd_page': (context, arguments) => ScheduleAnalysisBDPage(
        arguments['searchUserId'],
      ),
  '/sales_result_page': (context, arguments) => SalesResultPage(
        period: arguments['period'],
      ),
  '/message_root_page': (context, arguments) =>
      MessageRootPage(index: arguments?['index']),
  '/map_test': (context, arguments) => MapTest(),
  '/schedule_analysis_bd_today_page': (context, arguments) =>
      ScheduleAnalysisBDTodayPage(
        arguments?['searchUserId'],
        arguments?['searchUserName'],
      ),
  '/customer_search_tab_page': (context, arguments) => CustomerSearchTabPage(
        keyword: arguments['keyword'],
        roleCode: arguments['roleCode'],
        selectIndex: arguments['selectIndex'],
      ),
  '/customer_private_detail_page': (context, arguments) =>
      CustomerPrivateDetailPageV2(
        customerId: arguments['customerId'],
        distributable: arguments['distributable'],
        licenseValidateMust: arguments['licenseValidateMust'],
        licenseValidateIssue: arguments['licenseValidateIssue'],
        merchantStatusName: arguments['merchantStatusName'],
      ),
  '/customer_basic_info_page': (context, arguments) => CustomerBasicInfoPage(
        customerId: arguments['customerId'],
        registerFlag: arguments['registerFlag'],
      ),
  '/customer_refund_order_page': (context, arguments) =>
      CustomerRefundOrderPage(
        merchantId: arguments['merchantId'],
      ),
  '/ybm_search_history': (context, arguments) => YBMCustomerSearchHistoryPage(
        keyword: arguments['keyword'],
        searchType: arguments['searchType'],
      ),
  '/coupon_group_select_page': (context, arguments) =>
      CouponGroupSelectPage(groupId: arguments?['groupId']),
  '/coupons_list': (context, arguments) =>
      CouponsListPage(searchUserId: arguments['searchUserId']),
  '/coupons_detail': (context, arguments) => CouponsDetailPage(
      couponId: arguments['couponId'], searchUserId: arguments['searchUserId']),
  '/customer_coupons_page': (context, arguments) => CustomerCouponsPage(
        merchantId: arguments['merchantId'],
      ),
  '/ybm_customer_map_page': (context, arguments) => YBMCustomerMapPage(
        customerId: arguments?['customerId'],
        latitude: arguments?['latitude'],
        longitude: arguments?['longitude'],
      ),
  '/customer_search_word_page': (context, arguments) => CustomerSearchWordPage(
        merchantId: arguments["merchantId"],
      ),
  '/goods_recommend_page': (context, arguments) => GoodsRecommendPage(
        merchantId: arguments["merchantId"],
        customerId: arguments["customerId"],
        selectedIndex: arguments["selectedIndex"],
      ),
  '/statistics_order_page': (context, arguments) => StatisticsOrderPage(
        period: arguments["period"],
        drugstoreType: arguments["drugstoreType"],
        targetId: arguments["targetId"],
        gmvType: arguments["gmvType"],
        total: arguments["total"],
      ),
  '/team_performance_list_page': (context, arguments) =>
      TeamPerformanceListPage(
          displayName: arguments['displayName'],
          paramsModelJson: arguments['paramsModelJson']),
  '/team_performance_ka_or_bd_list_page': (context, arguments) =>
      TeamPerformanceKAOrBDListPage(
        listType: arguments['listType'],
        paramsModelJson: arguments['paramsModelJson'],
      ),
  '/team_performance_rank_list_page': (context, arguments) =>
      TeamPerformanceRankListPage(
          userLevel: arguments['userLevel'],
          rankUserType: arguments['rankUserType']),
  '/business_operation_rank_page': (context, arguments) =>
      BusinessOperationRankPage(),
  '/business_investment_rank_page': (context, arguments) =>
      BusinessInvestmentRankPage(timePeriod: arguments['timePeriod']),
  '/business_operation_visit_page': (context, arguments) =>
      BusinessOperationVisitPage(),
  '/business_investment_visit_page': (context, arguments) =>
      BusinessInvestmentVisitPage(),
  '/business_visit_detail_page': (context, arguments) =>
      BusinessVisitDetailPage(id: arguments['id']),
  '/OrderDetailRefundCwyPage':(context, arguments) =>
      OrderDetailRefundCwyPage(),
  '/super_worry_free_application':(context, arguments) => SuperWorryFreeApplication(),
  '/apply_order_list_page':(context, arguments) => OrderListPage(arguments["selectedCountMap"],arguments["preKeywordSave"]),
  '/apply_history_order_manage_page': (context, arguments) => ApplyHistoryOrderManagePage(),
  '/apply_history_order_search_page': (context, arguments) => ApplyHistoryOrderSearchPage(arguments["status"]),
  '/application_request_successfull': (context, arguments) => ApplicationRequestSuccessfull(),
  '/order_detail_refund_cwy_page':(context, arguments) =>
      OrderDetailRefundCwyPage( orderNo:arguments['orderNo'] ?? ""),
  '/order_moderation_cwy_page':(context, arguments) =>
      OrderModerationCwyPage( orderNo:arguments['orderNo'] ?? ""),
  '/order_list_cwy_page':(context, arguments) =>
      OrderListCwyPage(),
};

/// 自定义桥注入
void injectCustomBridge() {
  XYYContainer.registerBridge('app_back');
  XYYContainer.registerBridge('user_info');
  XYYContainer.registerBridge('app_host');
  XYYContainer.registerBridge('copy_text');
  XYYContainer.registerBridge('event_bus');
  XYYContainer.registerBridge('areaSelect');
  XYYContainer.registerBridge('event_track');
  XYYContainer.registerBridge('private_list_jump');
  XYYContainer.registerBridge('schedule_select_image');
  XYYContainer.registerBridge('voice_input');
  XYYContainer.registerBridge('public_list_jump');
  XYYContainer.registerBridge('call_phone');
  XYYContainer.registerBridge('open_share');
  XYYContainer.registerBridge('search_driving_line');
  XYYContainer.registerBridge('check_notification_setting');
  XYYContainer.registerBridge('open_notification_setting');
  XYYContainer.registerBridge('handle_call_record');
  XYYContainer.registerBridge('open_main',
      bridgeHandler: OpenMainBridgeHandler());
  XYYContainer.registerBridge('map_navigation');
}

void compatTranslucentStatusBar() {
  if (Platform.isAndroid) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
      systemNavigationBarDividerColor: null,
      statusBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ));
  } else {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
  }

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

class ReplacePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('未知页面'),
      ),
      body: Container(
        child: Center(
          child: Text('访问的页面不存在'),
        ),
      ),
    );
  }
}
