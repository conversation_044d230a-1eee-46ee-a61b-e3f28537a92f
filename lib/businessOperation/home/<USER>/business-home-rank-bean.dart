import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'business-home-rank-bean.g.dart';
@JsonSerializable()
class BusinessHomeRankTabOptionsBean extends BaseModelV2<BusinessHomeRankTabOptionsBean> {
  String? dictLabel;
  String? dictValue;
  int? dictSort;
  String? dictType;


  BusinessHomeRankTabOptionsBean({this.dictLabel, this.dictValue, this.dictSort, this.dictType});

  @override
  BusinessHomeRankTabOptionsBean fromJsonMap(Map<String, dynamic> json) => _$BusinessHomeRankTabOptionsBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessHomeRankTabOptionsBeanToJson(this);

  factory BusinessHomeRankTabOptionsBean.fromJson(Map<String, dynamic> json) => _$BusinessHomeRankTabOptionsBeanFromJson(json);

}


@JsonSerializable()
class BusinessHomeRankDataBean extends BaseModelV2<BusinessHomeRankDataBean> {
  String? accountId;
  String? storeName;
  String? regions;
  String? manager;
  List<BusinessHomeCardBean>? metrics;


  BusinessHomeRankDataBean({this.accountId, this.storeName, this.regions, this.manager, this.metrics});

  @override
  BusinessHomeRankDataBean fromJsonMap(Map<String, dynamic> json) => _$BusinessHomeRankDataBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessHomeRankDataBeanToJson(this);

  factory BusinessHomeRankDataBean.fromJson(Map<String, dynamic> json) => _$BusinessHomeRankDataBeanFromJson(json);

}

@JsonSerializable()
class BusinessVisitDetailBean extends BaseModelV2<BusinessVisitDetailBean> {
  int? totalCount;
  List<BusinessVisitDetailListBean>? result;
  int? pageNo;
  int? pageSize;
  String? scrollId;


  BusinessVisitDetailBean({this.totalCount, this.result, this.pageNo, this.pageSize, this.scrollId});

  @override
  BusinessVisitDetailBean fromJsonMap(Map<String, dynamic> json) => _$BusinessVisitDetailBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessVisitDetailBeanToJson(this);

  factory BusinessVisitDetailBean.fromJson(Map<String, dynamic> json) => _$BusinessVisitDetailBeanFromJson(json);
}

@JsonSerializable()
class BusinessVisitDetailListBean extends BaseModelV2<BusinessVisitDetailListBean> {
  int? id;
  int? merchantManagerId;
  String? createTime;
  String? createTimeStr;      //创建时间
  String? callingStartTime;
  String? callingStartTimeStr;   //拜/陪访开始时间
  String? callingEndTime;
  String? callingEndTimeStr;   //拜/陪访结束时间
  String? accountName;       //拜/陪访人姓名
  String? accountId;         //拜/陪访人id
  String? followVisitAccountId;    //陪访对象(工号)
  String? followVisitAccountName;   //陪访对象
  String? callingType;
  String? callingTypeStr;    //拜/陪访类型
  String? callingMethod;
  String? callingMethodStr;   //拜/陪访方式
  int? isValid;       //是否有效
  dynamic callingStatus;       //是否草稿
  int? poiId;
  String? poiName;   //
  String? orgId;    //商户编码
  String? orgName;    //商户名称
  String? storeName;   //店铺名称
  int? provId;    //企业省份id
  String? provName; //商业注册省份
  int? cityId;      //企业城市id
  String? cityName;     //企业城市
  String? contactName;    //联系人
  String? contactPhone;   //联系人电话
  String? callingPurpose;
  String? callingPurposeStr;   //拜/陪访目的
  String? callingSummary;   //拜/陪访总结
  String? callingPicture;  //拜访图片
  String? deptName;
  int? isKp;   //是否是KP
  String? visitFollowTag;

  BusinessVisitDetailListBean({
      this.id,
      this.merchantManagerId,
      this.createTime,
      this.createTimeStr,
      this.callingStartTime,
      this.callingStartTimeStr,
      this.callingEndTime,
      this.callingEndTimeStr,
      this.accountName,
      this.accountId,
      this.followVisitAccountId,
      this.followVisitAccountName,
      this.callingType,
      this.callingTypeStr,
      this.callingMethod,
      this.callingMethodStr,
      this.isValid,
      this.callingStatus,
      this.poiId,
      this.poiName,
      this.orgId,
      this.orgName,
      this.storeName,
      this.provId,
      this.provName,
      this.cityId,
      this.cityName,
      this.contactName,
      this.contactPhone,
      this.callingPurpose,
      this.callingPurposeStr,
      this.callingSummary,
      this.callingPicture,
      this.deptName,
      this.isKp,
      this.visitFollowTag
  }); //所属部门



  @override
  BusinessVisitDetailListBean fromJsonMap(Map<String, dynamic> json) => _$BusinessVisitDetailListBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessVisitDetailListBeanToJson(this);

  factory BusinessVisitDetailListBean.fromJson(Map<String, dynamic> json) => _$BusinessVisitDetailListBeanFromJson(json);
}