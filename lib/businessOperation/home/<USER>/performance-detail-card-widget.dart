import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/flow-layout.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/common/shop_filter_list.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PerformanceDetailCardWidget extends StatelessWidget {
  List<BusinessHomeCardBean>? dataList;
  int? colCount;
  Color? itemBgColor;
  bool? isClick;
  String? timePeriod;

  PerformanceDetailCardWidget({
    this.dataList = const [],
    this.colCount = 4,
    this.itemBgColor = const Color(0xFFF9F9F9),
    this.isClick = false,
    this.timePeriod = ''
  });

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      alignment: Alignment.centerLeft,
      child: GridView.count(
        shrinkWrap: true,
        mainAxisSpacing: 7,
        crossAxisSpacing: 7,
        primary: false,
        childAspectRatio: 1.3,
        crossAxisCount: colCount ?? 2,
        children: [
          ...getDataWidget(dataList,context)
        ],
      )
    );
  }

  List<Widget> getDataWidget(
      List<BusinessHomeCardBean>? data, BuildContext context) {
    if (data == null) return [];
    List<Widget> tempList = [];
    for (int curIndex = 0; curIndex < data.length; curIndex++) {
      tempList.add(GestureDetector(
          onTap: () {
            if (isClick ?? false) {
              Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => ShopFilterListPage(
                title: data[curIndex].title,
                indexCardTypeCode: data[curIndex].type,
                timePeriod: this.timePeriod,
              ),
              fullscreenDialog: true,
            ));
            }
          },
          child: Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.all(5),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: itemBgColor
            ),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(data[curIndex].title ?? '--',
                    maxLines: 2,
                    style: TextStyle(
                      fontSize: 11,
                      color: GlobalData.unSelectedColor,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
                SizedBox(height: 5),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(data[curIndex].valueFormat ?? '--',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: data[curIndex].color != null && data[curIndex].color != '' ?  Color(int.parse(data[curIndex].color!)) : GlobalData.textNormalColor
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
                if (data[curIndex].rate != '' && data[curIndex].rate != null)
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Text(data[curIndex].rateStr ?? '--',
                          style: TextStyle(
                              fontSize: 10,
                              color: GlobalData.unSelectedColor,
                              fontWeight: FontWeight.w400
                          ),
                          textAlign: TextAlign.start,
                        ),
                        SizedBox(width: 5),
                        Text(data[curIndex].rate ?? '--',
                          maxLines: 2,
                          style: TextStyle(
                              fontSize: 10,
                              color: data[curIndex].rateColor != null && data[curIndex].rateColor != '' ?  Color(int.parse(data[curIndex].rateColor!)) : GlobalData.textNormalColor
                          ),
                          textAlign: TextAlign.start,
                        ),
                        if (data[curIndex].isUp == 1)
                          Icon(
                            Icons.keyboard_arrow_up_rounded,
                            color: data[curIndex].rateColor != null && data[curIndex].rateColor != '' ?  Color(int.parse(data[curIndex].rateColor!)) : GlobalData.textNormalColor,
                            size: 12,
                          ),
                        if (data[curIndex].isUp == 2)
                          Icon(
                            Icons.keyboard_arrow_down_rounded,
                            color: data[curIndex].rateColor != null && data[curIndex].rateColor != '' ?  Color(int.parse(data[curIndex].rateColor!)) : GlobalData.textNormalColor,
                            size: 12,
                          )
                      ],
                    ),
                  )
              ],
            ),
          )
      ));

    }
    return tempList;

  }
}