import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'business-home-userInfo-bean.g.dart';

@JsonSerializable()
class BusinessHomeQrcodeBean extends BaseModelV2<BusinessHomeQrcodeBean> {
  String? accountName;
  String? qrcodeImage;
  String? createTime;
  String? createBy;
  String? accountId;
  String? updateTime;
  String? updateBy;
  String? dataSource;

  BusinessHomeQrcodeBean({this.accountName, this.qrcodeImage, this.createTime, this.createBy, this.accountId, this.updateTime, this.updateBy, this.dataSource});

  @override
  BusinessHomeQrcodeBean fromJsonMap(Map<String, dynamic> json) => _$BusinessHomeQrcodeBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessHomeQrcodeBeanToJson(this);

  factory BusinessHomeQrcodeBean.fromJson(Map<String, dynamic> json) => _$BusinessHomeQrcodeBeanFromJson(json);
}