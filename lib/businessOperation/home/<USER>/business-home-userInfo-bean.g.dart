// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business-home-userInfo-bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessHomeQrcodeBean _$BusinessHomeQrcodeBeanFromJson(
    Map<String, dynamic> json) {
  return BusinessHomeQrcodeBean(
    accountName: json['accountName'] as String?,
    qrcodeImage: json['qrcodeImage'] as String?,
    createTime: json['createTime'] as String?,
    createBy: json['createBy'] as String?,
    accountId: json['accountId'] as String?,
    updateTime: json['updateTime'] as String?,
    updateBy: json['updateBy'] as String?,
    dataSource: json['dataSource'] as String?,
  );
}

Map<String, dynamic> _$BusinessHomeQrcodeBeanToJson(
        BusinessHomeQrcodeBean instance) =>
    <String, dynamic>{
      'accountName': instance.accountName,
      'qrcodeImage': instance.qrcodeImage,
      'createTime': instance.createTime,
      'createBy': instance.createBy,
      'accountId': instance.accountId,
      'updateTime': instance.updateTime,
      'updateBy': instance.updateBy,
      'dataSource': instance.dataSource,
    };
