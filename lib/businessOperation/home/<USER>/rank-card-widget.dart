import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
class StatusTagType{
  const StatusTagType._(this.index);
  final int index;
  static const StatusTagType SUCCESS = StatusTagType._(1);
  static const StatusTagType ERROR = StatusTagType._(2);
  static const StatusTagType NORMAL = StatusTagType._(0);
}
class StatusTag extends StatelessWidget {
  String text;
  double size;
  StatusTagType type;
  StatusTag({
    this.text = "",
    this.size = 20,
    this.type = StatusTagType.NORMAL
  });
  IconData getIcon(StatusTagType type) {
    switch (type) {
      case StatusTagType.ERROR: {
        return Icons.cancel_rounded;
      }
      case StatusTagType.SUCCESS: {
        return Icons.check_circle_rounded;
      }
      case StatusTagType.NORMAL: {
        return Icons.info_rounded;
      }
      default: {
        return Icons.info_rounded;
      }
    }
  }
  List<Color> getColors(StatusTagType type) {
    Color borderColor;
    Color bgColor;
    Color textColor;
    switch (type) {
      case StatusTagType.NORMAL: {
        borderColor = Color.fromRGBO(230, 230, 200, 1.0);
        bgColor = Color.fromRGBO(240, 240, 240, 1.0);
        textColor = Color.fromRGBO(195, 195, 195, 1.0);
        break;
      }
      case StatusTagType.ERROR: {
        borderColor = Color.fromRGBO(251, 84, 84, 1);
        bgColor = Color.fromRGBO(246, 202, 202, 1.0);
        textColor = Color.fromRGBO(251, 84, 84, 1);
        break;
      }
      case StatusTagType.SUCCESS: {
        borderColor = Color.fromRGBO(152, 218, 137, 1.0);
        bgColor = Color.fromRGBO(205, 243, 194, 1.0);
        textColor = Color.fromRGBO(25, 185, 90, 1);
        break;
      }
      default: {
        borderColor = Color.fromRGBO(200, 200, 200, 1.0);
        bgColor = Color.fromRGBO(227, 227, 227, 1.0);
        textColor = Color.fromRGBO(135, 135, 135, 1.0);
        break;
      }
    }
    return [borderColor, bgColor, textColor];
  }
  @override
  Widget build(BuildContext context) {
    return Container(
      height: this.size,
      padding: EdgeInsets.only(left: 2, right: 3),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size / 2),
        color: getColors(type)[1],
        border: Border.all(
          color: getColors(type)[0],
          style: BorderStyle.solid,
          width: 1
        )
      ),
      child: Row(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Icon(
              getIcon(type),
              color: getColors(type)[2],
              size: size / 5 * 4,
            ),
          ),
          SizedBox(width: 3),
          Align(
            alignment: Alignment.center,
            child: Text(
              text,
              style: TextStyle(
                fontSize: size / 5 * 3,
                color: getColors(type)[2]
              ),
            ),
          )
        ],
      ),
    );
  }
}