// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business-home-card-bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessHomeCardBean _$BusinessHomeCardBeanFromJson(Map<String, dynamic> json) {
  return BusinessHomeCardBean(
    title: json['title'] as String?,
    value: json['value'] as String?,
    type: json['type'] as String?,
    isUp: json['isUp'] as int?,
    color: json['color'] as String?,
    rate: json['rate'] as String?,
    rateStr: json['rateStr'] as String?,
    previousValue: json['previousValue'] as String?,
    rateColor: json['rateColor'] as String?,
    valueFormat: json['valueFormat'] as String?
  );
}

Map<String, dynamic> _$BusinessHomeCardBeanToJson(
        BusinessHomeCardBean instance) =>
    <String, dynamic>{
      'title': instance.title,
      'value': instance.value,
      'previousValue': instance.previousValue,
      'valueFormat': instance.valueFormat,
      'rate': instance.rate,
      'rateStr': instance.rateStr,
      'type': instance.type,
      'color': instance.color,
      'rateColor': instance.rateColor,
      'isUp': instance.isUp,

    };

CardVisibleBean _$CardVisibleBeanFromJson(Map<String, dynamic> json) {
  return CardVisibleBean(
    totalPerformanceVisible: json['totalPerformanceVisible'] as bool?,
    totalNewSignVisible: json['totalNewSignVisible'] as bool?,
    storeActivityVisible: json['storeActivityVisible'] as bool?,
    alertInfoVisible: json['alertInfoVisible'] as bool?,
    operationVisitsVisible: json['operationVisitsVisible'] as bool?,
    recruitmentVisitsVisible: json['recruitmentVisitsVisible'] as bool?,
    operationRankingVisible: json['operationRankingVisible'] as bool?,
    recruitmentRankingVisible: json['recruitmentRankingVisible'] as bool?,
    totalNewSignDescription: json['totalNewSignDescription'] as String?,
    storeActivityDescription: json['storeActivityDescription'] as String?,
    alertInfoDescription: json['alertInfoDescription'] as String?,
    operationVisitsDescription: json['operationVisitsDescription'] as String?,
    recruitmentVisitsDescription: json['recruitmentVisitsDescription'] as String?,
  );
}

Map<String, dynamic> _$CardVisibleBeanToJson(CardVisibleBean instance) =>
    <String, dynamic>{
      'totalPerformanceVisible': instance.totalPerformanceVisible,
      'totalNewSignVisible': instance.totalNewSignVisible,
      'storeActivityVisible': instance.storeActivityVisible,
      'alertInfoVisible': instance.alertInfoVisible,
      'operationVisitsVisible': instance.operationVisitsVisible,
      'recruitmentVisitsVisible': instance.recruitmentVisitsVisible,
      'operationRankingVisible': instance.operationRankingVisible,
      'recruitmentRankingVisible': instance.recruitmentRankingVisible,

      'totalNewSignDescription': instance.totalNewSignDescription,
      'storeActivityDescription': instance.storeActivityDescription,
      'alertInfoDescription': instance.alertInfoDescription,
      'operationVisitsDescription': instance.operationVisitsDescription,
      'recruitmentVisitsDescription': instance.recruitmentVisitsDescription,

    };
