import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class SearchWidget extends StatelessWidget {
  final bool autoFocus;
  final FocusNode? focusNode;
  final TextEditingController? controller;

  // 输入框背景颜色
  final Color backgroundColor;
  // 输入框圆角大小
  BorderRadius radius;

  // 默认值
  final String? value;


  final List<Widget> actions;

  // 提示文字
  final String? hintText;
  final TextStyle hintTextStyle;

  // 输入框点击
  final VoidCallback? onTap;

  // 单独清除输入框内容
  final VoidCallback? onClear;

  // 清除输入框内容并取消输入
  final VoidCallback? onCancel;

  // 输入框内容改变
  final ValueChanged? onChanged;

  // 点击键盘搜索
  final ValueChanged? onSearch;

  // 取消按钮样式
  final TextStyle cancleStyle;
  // 取消按钮文案
  final String cancleText;

  // 是否显示清除按钮
  final bool? showClear;
  final Widget? suffix;
  SearchWidget({
    Key? key,
    this.autoFocus = false,
    this.suffix,
    this.focusNode,
    this.controller,
    this.value,
    this.backgroundColor = const Color(0xFFF5F5F5),
    this.radius = const BorderRadius.all(Radius.circular(2)),
    this.showClear = true,
    this.actions = const [],
    this.hintText,
    this.hintTextStyle =
    const TextStyle(fontSize: 14, color: Color(0xFFb3b3c2)),
    this.onTap,
    this.onClear,
    this.onCancel,
    this.onChanged,
    this.onSearch,
    this.cancleText = '取消',
    this.cancleStyle = const TextStyle(color: Color(0xFF292933), fontSize: 16),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 5, left: 5),
      padding: EdgeInsets.only(left: 10),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: radius,
      ),
      child: Row(
        children: [
          SizedBox(
            width: 22,
            height: 22,
            child: Image.asset("assets/images/titlebar/icon_search_bar.png"),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 3),
              child: TextField(
                autofocus: autoFocus,
                focusNode: focusNode,
                controller: controller,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  isDense: true,
                  hintText: hintText ?? '',
                  hintStyle: hintTextStyle,
                ),
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF292933),
                ),
                textInputAction: TextInputAction.search,
                onTap: onTap,
                onChanged: onChanged,
                onSubmitted: onSearch,
                keyboardAppearance: Brightness.light,
              ),
            ),
          ),
          _suffix()
        ],
      ),
    );
  }

  Widget _suffix() {
    return this.suffix ?? SizedBox();
  }
}