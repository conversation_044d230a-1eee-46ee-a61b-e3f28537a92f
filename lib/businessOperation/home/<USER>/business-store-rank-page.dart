import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/search-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/rank-icon.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

class BusinessStoreRankData extends ChangeNotifier {
  //数据
  List<BusinessHomeRankDataBean> _dataList = [];
  dynamic _dictValue = "";
  int pageNo = 1;
  bool isEnd = false;
  updatePagination(int? pageNo, bool? isEnd) {
    this.pageNo = pageNo ?? this.pageNo;
    this.isEnd = isEnd ?? this.isEnd;
  }
  updateDictValue(dynamic value) {
    this._dictValue = value;
  }
  updateDataList(List<BusinessHomeRankDataBean> list) {
    this._dataList = list;
    notify();
  }
  List<BusinessHomeRankDataBean> get dataList => this._dataList;
  dynamic get dictValue => this._dictValue;
  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class BusinessStoreRankState extends BaseState<BusinessStoreRankPage> with SingleTickerProviderStateMixin {
  List<List<String>> tabList = [['昨天', 'yesterday'], ['今日', 'today'], ['上周', 'last_week'], ['本周', 'this_week'], ['上月', 'last_month'],['本月', 'this_month']];
  BusinessStoreRankData businessOperationRankData = new BusinessStoreRankData();
  TextEditingController _textController = TextEditingController();
  late TabController _controller;
  late ScrollController _scrollController;
  bool loading = false;
  String search = "";
  bool showSearch = false;
  String accountId;

  BusinessStoreRankState({ required this.accountId });

  @override
  void initState() {
    this._controller = TabController(
      length: tabList.length,
      vsync: this,
      initialIndex: 1,
    );
    this._scrollController = new ScrollController();
    this._scrollController.addListener(() {
      if (this._scrollController.position.pixels == this._scrollController.position.maxScrollExtent
          && loading == false
          && businessOperationRankData.isEnd == false
      ) {
        businessOperationRankData.updatePagination(businessOperationRankData.pageNo + 1, false);
        this.getDataList(this.tabList[this._controller.index][1], businessOperationRankData.dataList);
      }
    });
    getDataList(this.tabList[this._controller.index][1], []);
  }
  getDataList(String timePeriod, List<BusinessHomeRankDataBean> list) async {
    EasyLoading.show(status: '加载中...');
    this.loading = true;
    var dataList = await NetworkV2<BusinessHomeRankDataBean>(BusinessHomeRankDataBean())
        .requestDataV2('/mop/index/listOrgTotalPerformanceData', method: RequestMethod.GET, parameters: {
      "timePeriod": timePeriod,
      "pageNo": businessOperationRankData.pageNo,
      "pageSize": 20,
      "accountId": this.accountId,
      "search": this.search
    });
    this.loading = false;
    var data = dataList.getListData() ?? [];
    this.businessOperationRankData.updateDataList([...list, ...data]);
    EasyLoading.dismiss();
  }
  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget
    return BaseContainer(
      color: GlobalData.normalViewBgColor,
      child: ChangeNotifierProvider(
        create: (_) {
          this._controller.addListener(() async {
            businessOperationRankData.updatePagination(1, false);
            this.getDataList(this.tabList[this._controller.index][1], []);
          });
          return businessOperationRankData;
        },
        child: Container(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: getTabBar(),
                padding: EdgeInsets.all(5),
              ),
              Expanded(
                child: SingleChildScrollView(
                  physics: BouncingScrollPhysics(),
                  controller: _scrollController,
                  padding: EdgeInsets.only(left: 7,right: 7, top: 5,bottom: 5),
                  child: Consumer<BusinessStoreRankData>(
                    builder: (context, model, child) {
                      return Column(
                        children: [
                          ...getRank(model.dataList)
                        ],
                      );
                    },
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      titleWidget: Container(
        child: this.showSearch == false ? Text(
          '店铺',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ) : SearchWidget(
          radius: BorderRadius.circular(15),
          onChanged: (value) {
            this.search = value;
          },
          suffix: buildButton(),
        ),
      ),
      rightButtons: [
        if (this.showSearch)
          Container(),
        if (this.showSearch == false)
          TextButton(
            onPressed: () {
              this.showSearch = true;
              setState(() {});
            },
            child: Image.asset(
              'assets/images/customer/customer_tab_search.png',
              width: 22,
              height: 22,
            ),
            style: ButtonStyle(
              overlayColor:
              MaterialStateProperty.all<Color>(Colors.transparent),
              padding:
              MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
      ],
    );
    /*if (showSearch == true) {
      return CommonSearchBar(
        focusNode: this._focusNode,
        showLeading: true,
        showClear: false,
        hideCancel: true,
        autoFocus: true,
        controller: this._textController,
        radius: BorderRadius.circular(22),
        cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
        suffix: buildButton(),
        onSearch: (value) {
          String inputValue = value.trim();
          if (inputValue.isEmpty) {
            this._textController.text = inputValue;
            return;
          }
        },
      );
    } */
    /*return CommonSearchBar(
      focusNode: this._focusNode,
      showLeading: true,
      showClear: false,
      hideCancel: true,
      autoFocus: true,
      controller: this._textController,
      radius: BorderRadius.circular(22),
      cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
      suffix: buildButton(),
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._textController.text = inputValue;
          return;
        }
      },
    );*/
  }

  Widget buildButton() {
    return Container(
      margin: EdgeInsets.only(left: 5),
      child: TextButton(
          onPressed: () {
            String inputValue = this._textController.text.trim();
            this.getDataList(this.tabList[this._controller.index][1], []);
          },
          child: Container(
            width: 56,
            height: 28,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Color(0xFF00B955),
            ),
            child: Center(
              child: Text(
                '搜索',
                style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
              ),
            ),
          ),
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          )),
    );
  }
  Widget getTabBar() {
    return XTabBar(
      controller: this._controller,
      unselectedLabelColor: GlobalData.unSelectedColor,
      labelColor: GlobalData.selectedColor,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.all(5),
      onTap: (index) {
        //model.updateDataPerformanceList('{ "data": [{"title": "啊哈哈哈哈", "tValue": "25,235,368", "tTipsList": [{"title": "啊哈哈哈哈", "tValue": "25,235,368"}]}], "code": 0, "msg": "success" }');
        //model.updateDataPerformanceList();
      },
      indicator: CustomUnderlineTabIndicator(
        borderSide: BorderSide(
            color: GlobalData.selectedColor,
            width: 1,
            style: BorderStyle.solid
        ),
      ),
      tabs: [
        ...this.tabList.asMap().keys.map((i) {
          return XTab(
            notHaveIconHeight: 30,
            child: Text(
              this.tabList[i][0],
              style: TextStyle(
                  fontSize: 14
              ),
            ),
          );
        })
      ],
    );
  }

  //排行榜
  List<Widget> getRank(List<BusinessHomeRankDataBean> list) {
    List<Widget> result = [];
    for (int index = 0; index < list.length; index++) {
      result.add(Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(bottom: index < list.length - 1 ? 10 : 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white
        ),
        child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(7),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                        child: Consumer<BusinessStoreRankData>(
                          builder: (context, model, child) {
                            return Row(
                              children: [
                                RankIcon(level: index, size: 14),
                                SizedBox(width: 5),
                                Transform.translate(
                                  offset: Offset(0, -2),
                                  child: Text(
                                    list[index].storeName ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        )
                    ),
                    Container(
                      child: Text(
                        list[index].regions ?? '',
                        style: TextStyle(
                            fontSize: 12
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Divider(height: 1, color: Color.fromRGBO(235, 235, 235, 1.0)),
              Container(
                  padding: EdgeInsets.all(7),
                  child: Consumer<BusinessStoreRankData>(
                    builder: (context, model, child) {
                      return PerformanceDetailWidget(
                        dataList:  list[index].metrics,
                      );
                    },
                  )
              )
            ]
        ),
      ));
    }
    return result;
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    throw "店铺";
  }

//获取筛选枚举
}
class BusinessStoreRankPage extends BasePage {
  String accountId;
  BusinessStoreRankPage({required this.accountId});

  @override
  BaseState<StatefulWidget> initState() {
    return BusinessStoreRankState(accountId: this.accountId);

  }
}

