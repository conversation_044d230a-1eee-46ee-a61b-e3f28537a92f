import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/horizontal-tabs-select.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/rank-card-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_accompany_visit.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_visit.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/rank-icon.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';

class BusinessInvestmentVisitData extends ChangeNotifier {
  //tab筛选  BusinessHomeRankTabOptionsBean
  List<HorizontalTabsOptions> _tabList = [
  ];
  //到底了
  bool isEnd = false;
  //数据
  List<BusinessHomeRankDataBean> _dataList = [];
  //今日拜访明细
  List<BusinessVisitDetailListBean> _list = [];

  dynamic _dictValue = "";
  dynamic _barValue = "mop_app_today_visit_option";
  int _pageNo = 1;
  updatePageNo(int pageNo) {
    this._pageNo = pageNo;
  }
  updateBarValue(dynamic value) {
    this._barValue = value;
    notify();
  }
  updateDictValue(dynamic value) {
    this._dictValue = value;
    notify();
  }
  updateDataList(List<BusinessHomeRankDataBean> list) {
    this._dataList = list;
    notify();
  }
  updateTabList(List<BusinessHomeRankTabOptionsBean> list) {
    this._tabList = this._tabList = List.generate(list.length,
            (index) =>
            HorizontalTabsOptions(list[index].dictLabel ?? '', list[index].dictValue));;
    notify();
  }
  updateTodayVisitDetailList(List<BusinessVisitDetailListBean> list) {
    this._list = list;
    notify();
  }
  updateIsEnd(bool status) {
    this.isEnd = status;
    notify();
  }
  dynamic get barValue => this._barValue;
  int get pageNo => this._pageNo;
  List<BusinessHomeRankDataBean> get totalDataList => this._dataList;
  List<HorizontalTabsOptions> get tabList => this._tabList;
  List<BusinessVisitDetailListBean> get todayVisitDetailList => this._list;
  dynamic get dictValue => this._dictValue;
  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class BusinessInvestmentVisitState extends BaseState<BusinessInvestmentVisitPage> with SingleTickerProviderStateMixin {
  List<List<String>> tabList = [
    ['拜访明细', 'mop_app_today_visit_option'],
    ['拜访总汇', 'all']
  ];
  BusinessInvestmentVisitData businessOperationVisitData = new BusinessInvestmentVisitData();
  late TabController _controller;
  late ScrollController _scrollController;
  bool loading = false;

  // 修改控制器声明，提供初始值
  Map<String, DropButtonController> controllers = {};

  // 添加筛选配置
  List<CommonDropConfigModel> get filterConfig {
    // 确保在访问 filterConfig 时 controllers 已经初始化
    if (controllers.isEmpty) {
      initControllers();
    }
    return [
      CommonDropConfigModel(
        defaultTitle: "拜访时间",
        paramKey: "visitTime",
        controller: controllers['visitTime'],
      ),
      CommonDropConfigModel(
        defaultTitle: "拜访状态",
        paramKey: "visitState",
        controller: controllers['visitState'],
      ),
      CommonDropConfigModel(
        defaultTitle: "拜访类型",
        paramKey: "visitTypes",
        controller: controllers['visitTypes'],
      ),
    ];
  }
  void initControllers() {
    // 先销毁已存在的控制器
    disposeControllers();

    // 使用 setState 来触发重建
    setState(() {
      controllers = {
        'visitTime': DropButtonController(model: DropButtonModel(normalText: "拜访时间")),
        'visitState': DropButtonController(model: DropButtonModel(normalText: "拜访状态")),
        'visitTypes': DropButtonController(model: DropButtonModel(normalText: "拜访类型")),
      };
    });
  }

  void disposeControllers() {
    controllers.forEach((key, controller) {
      controller.dispose();
    });
    controllers.clear();
  }

  // 添加选项数据
  final List<CustomerConditionModel> visitTimeList = [
    CustomerConditionModel(text: '今日', code: "1"),
    CustomerConditionModel(text: '近7日', code: "2"),
    CustomerConditionModel(text: '近30日', code: "3"),
  ];

  final List<CustomerConditionModel> visitStatesList = [
    CustomerConditionModel(text: '全部', code: "all"),
    CustomerConditionModel(text: '有效', code: "1"),
    CustomerConditionModel(text: '无效', code: "2"),
    CustomerConditionModel(text: '草稿', code: "0"),
  ];

  final List<CustomerConditionModel> visitTypesList = [
    CustomerConditionModel(text: '全部', code: "all"),
    CustomerConditionModel(text: '拜访', code: "visit"),
    CustomerConditionModel(text: '陪访', code: "follow"),
  ];

  // 存储选中的值
  Map<String, String> selectedValues = {
    'visitTime': '1',  // 设置默认选中今日
    'visitState': 'all',
    'visitTypes': 'all',
  };

  @override
  void initState() {
    super.initState();
    this._controller = TabController(
      length: tabList.length,
      vsync: this,
      initialIndex: 0,
    );
    this._scrollController = new ScrollController();
    this._scrollController.addListener(() {
      if (this._scrollController.position.pixels == this._scrollController.position.maxScrollExtent
          && businessOperationVisitData.barValue == 'mop_app_today_visit_option'
          && loading == false
          && businessOperationVisitData.isEnd == false
      ) {
        businessOperationVisitData.updatePageNo(businessOperationVisitData.pageNo + 1);
        this.getTodayVisitDetail(businessOperationVisitData.todayVisitDetailList);
      }
    });
    // 添加tab切换监听
    this._controller.addListener(() {
      if (loading) return;
      var newBarValue = this.tabList[this._controller.index][1];
      var oldBarValue = businessOperationVisitData.barValue;

      businessOperationVisitData.updateBarValue(newBarValue);
      businessOperationVisitData.updateIsEnd(false);

      // 切换到今日拜访明细
      if (newBarValue == 'mop_app_today_visit_option') {
        businessOperationVisitData.updatePageNo(1);
        // 如果是从其他tab切回来，重新初始化控制器
        if (oldBarValue != 'mop_app_today_visit_option') {
          Future.microtask(() => initControllers());
        }
        getTodayVisitDetail([]);
      } else {
        // 切换到其他tab时销毁控制器
        disposeControllers();
        getTotalMenu();
      }
    });

    // 初始化控制器
    initControllers();
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    disposeControllers();
    super.dispose();
  }
  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget\
    return BaseContainer(
      color: GlobalData.normalViewBgColor,
      child: ChangeNotifierProvider(
        create: (_) {
          getTodayVisitDetail([]);
          // this._controller.addListener(() {
          //   if (loading) return;
          //   businessOperationVisitData.updateIsEnd(false);
          //   businessOperationVisitData.updateBarValue(this.tabList[this._controller.index][1]);
          //   if (businessOperationVisitData.barValue == 'mop_app_today_visit_option') {
          //     businessOperationVisitData.updatePageNo(1);
          //     getMenu();
          //     //getTodayVisitDetail([]);
          //   } else {
          //     getTotalMenu();
          //     //getTotalVisit();
          //   }
          // });
          // getMenu();
          return businessOperationVisitData;
        },
        child: Container(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: getTabBar(),
                padding: EdgeInsets.all(5),
              ),
              Consumer<BusinessInvestmentVisitData>(
                  builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      child: model.barValue == 'mop_app_today_visit_option'
                          ? CommonDropFilterBar(
                        configs: filterConfig,
                        action: showDropPopupView,
                      )
                          : Container(),
                    );
                  }
              ),
              Consumer<BusinessInvestmentVisitData>(
                  builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      child: model.barValue != 'mop_app_today_visit_option' ? HorizontalTabsSelect(
                        onTap: (value) {
                          businessOperationVisitData.updateDictValue(value);
                          if (businessOperationVisitData.barValue == 'mop_app_today_visit_option') {
                            businessOperationVisitData.updateIsEnd(false);
                            businessOperationVisitData.updatePageNo(1);
                            getTodayVisitDetail([]);
                          } else {
                            getTotalVisit();
                          }
                        },
                        size: businessOperationVisitData.tabList.length == 3 ? 110 : 80,
                        options: businessOperationVisitData.tabList,
                      ) : Container(),
                    );
                  }
              ),
              Expanded(
                child:EasyRefresh(
                  // controller: _controller,
                  onRefresh: () async {
                    if (businessOperationVisitData.barValue == 'mop_app_today_visit_option') {
                      businessOperationVisitData.updatePageNo(1);
                      businessOperationVisitData.updateIsEnd(false);
                      getTodayVisitDetail([]);
                    } else {
                      getTotalVisit();
                    }
                  },
                  onLoad: null,
                  emptyWidget: null,
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    controller: _scrollController,
                    padding: EdgeInsets.only(
                        left: 7, right: 7, top: 5, bottom: 5),
                    child: Consumer<BusinessInvestmentVisitData>(
                      builder: (context, model, child) {
                        return Column(
                          children: [
                            ...(businessOperationVisitData.barValue ==
                                'mop_app_today_visit_option'
                                ? getTodayRank(businessOperationVisitData.todayVisitDetailList)
                                : getRank(businessOperationVisitData.totalDataList)),
                          ],
                        );
                      },
                    ),
                  ),
                )

              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '招商拜访';
  }

  Widget getTabBar() {
    return XTabBar(
      controller: this._controller,
      unselectedLabelColor: GlobalData.unSelectedColor,
      labelColor: GlobalData.selectedColor,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.all(5),
      indicator: CustomUnderlineTabIndicator(
        borderSide: BorderSide(
            color: GlobalData.selectedColor,
            width: 1,
            style: BorderStyle.solid
        ),
      ),
      tabs: [
        ...this.tabList
            .asMap()
            .keys
            .map((i) {
          return XTab(
            notHaveIconHeight: 30,
            child: Text(
              this.tabList[i][0],
              style: TextStyle(
                  fontSize: 14
              ),
            ),
          );
        })
      ],
    );
  }

  //获取枚举
  void getMenu() async {
    loading = true;
    EasyLoading.show(status: '加载中...');
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(
        BusinessHomeRankTabOptionsBean())
        .requestDataV2(
        '/mop/dict/list', method: RequestMethod.GET, parameters: {
      "dictType": "mop_app_today_visit_option"
    });
    loading = false;
    var list = tabListResult.getListData() ?? [];
    businessOperationVisitData.updateTabList(list);
    businessOperationVisitData.updateDictValue(
        list.length > 0 ? list[0].dictValue : '');
    this.getTodayVisitDetail([]);
    EasyLoading.dismiss();
  }
  //获取汇总枚举
  void getTotalMenu() async {
    loading = true;
    EasyLoading.show(status: '加载中...');
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(BusinessHomeRankTabOptionsBean())
        .requestDataV2('/mop/dict/regions', method: RequestMethod.GET);
    loading = false;
    var list = tabListResult.getListData() ?? [];
    businessOperationVisitData.updateTabList(list);
    businessOperationVisitData.updateDictValue(
        list.length > 0 ? list[0].dictValue : '');
    this.getTotalVisit();
    EasyLoading.dismiss();
  }
  //获取今日拜访明细
  void getTodayVisitDetail(List<BusinessVisitDetailListBean> dataList) async {
    loading = true;
    EasyLoading.show(status: '加载中...');
    var listResult = await NetworkV2<BusinessVisitDetailBean>(
        BusinessVisitDetailBean())
        .requestDataV2(
        '/mop/calling/log/today/page', method: RequestMethod.POST, parameters: {
      // 'todayVisitOptionCode': businessOperationVisitData.dictValue,
      'appQueryType': 'RECRUITMENT_VISITS',
      'pageNo': businessOperationVisitData.pageNo,
      'pageSize': 20,
      'timeSelect': selectedValues['visitTime'],
      'callingStatus':selectedValues['visitState'] == 'all' ? '' : selectedValues['visitState'],
      'todayVisitOptionCode': selectedValues['visitTypes'] == '' ? 'all' : selectedValues['visitTypes'],
    });
    loading = false;
    if (listResult.getData()!.result == null || listResult.getData()!.result!.length == 0) {
      businessOperationVisitData.updateIsEnd(true);
    }
    var list = listResult.getData()!.result ?? [];
    list = [...dataList, ...list];
    businessOperationVisitData.updateTodayVisitDetailList(list);
    EasyLoading.dismiss();
  }
  //获取拜访总汇
  void getTotalVisit() async {
    loading = true;
    EasyLoading.show(status: '加载中...');
    var listResult = await NetworkV2<BusinessHomeRankDataBean>(
        BusinessHomeRankDataBean())
        .requestDataV2(
        '/mop/index/recruitmentVisitSummary', method: RequestMethod.GET, parameters: {
      'positionLevel': businessOperationVisitData.dictValue,
    });
    loading = false;
    var list = listResult.getListData();
    businessOperationVisitData.updateDataList(list ?? []);
    EasyLoading.dismiss();
  }
  //拜访总汇
  List<Widget> getRank(List<BusinessHomeRankDataBean> list) {
    List<Widget> result = [];
    for (int index = 0; index < list.length; index++) {
      result.add(Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(bottom: index < list.length - 1 ? 10 : 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white
        ),
        child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(7),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                        child: Consumer<BusinessInvestmentVisitData>(
                          builder: (context, model, child) {
                            return Row(
                              children: [
                                RankIcon(level: index, size: 14),
                                SizedBox(width: 5),
                                Transform.translate(
                                  offset: Offset(0, -2),
                                  child: Text(
                                    list[index].manager ?? '--',
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        )
                    ),
                    Container(
                      child: Text(
                        list[index].regions ?? '--',
                        style: TextStyle(
                            fontSize: 12
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Divider(height: 1, color: Color.fromRGBO(235, 235, 235, 1.0)),
              Container(
                  padding: EdgeInsets.all(7),
                  child: Consumer<BusinessInvestmentVisitData>(
                    builder: (context, model, child) {
                      return PerformanceDetailWidget(
                        dataList: list[index].metrics,
                      );
                    },
                  )
              )
            ]
        ),
      ));
    }
    return result;
  }

  //拜访明细
  List<Widget> getTodayRank(List<BusinessVisitDetailListBean> list) {
    List<Widget> result = [];
    for (int index = 0; index < list.length; index++) {
      result.add(GestureDetector(
        onTap: () {
          if (list[index].callingStatus == 0) { //草稿
            if (list[index].callingType == 'operationVisit' || list[index].callingType == 'businessVisit') {
              Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => ShopVisit(shopInfo: ShopListDataItemModel(id:list[index].merchantManagerId,orgId:list[index].orgId,companyName:list[index].orgName, callingLogId: list[index].id),isPhoneVisit: list[index].callingMethod == 'phoneVisit'),
                    fullscreenDialog: true,
                  )).then((value) {
                    if (value == 'success') {
                      getMenu();
                      setState(() {});
                    }
                  });
            }else {
              Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => ShopAccompanyVisit(shopInfo: ShopListDataItemModel(id:list[index].merchantManagerId,orgId:list[index].orgId,companyName:list[index].orgName, callingLogId: list[index].id)),
                      fullscreenDialog: true,
                    )).then((value) {
                    if (value == 'success') {
                      getMenu();
                      setState(() {});
                    }
                  });
            }
            
            return;
          } else {
            Navigator.of(context).pushNamed('/business_visit_detail_page', arguments: {
              'id': list[index].id.toString()
            });
          }
        },
        child: Container(
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: index < list.length - 1 ? 10 : 0),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: Colors.white
          ),
          child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(7),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                          child: Consumer<BusinessInvestmentVisitData>(
                            builder: (context, model, child) {
                              return Row(
                                children: [
                                  Transform.translate(
                                    offset: Offset(0, -2),
                                    child: Text(
                                      (list[index].accountName ?? '') + " - " +
                                          (list[index].callingMethodStr ?? ''),
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          )
                      ),
                      Container(
                          child: StatusTag(
                              text: list[index].callingStatus == 0 ? '草稿' : list[index].isValid == 1 ? '有效' : '无效',
                              size: 18,
                              type: list[index].callingStatus == 0 ? StatusTagType.NORMAL : list[index].isValid == 1 ? StatusTagType.SUCCESS : StatusTagType.ERROR
                          )
                      )
                    ],
                  ),
                ),
                Divider(height: 1, color: Color.fromRGBO(235, 235, 235, 1.0)),
                Container(
                    padding: EdgeInsets.all(7),
                    child: Consumer<BusinessInvestmentVisitData>(
                      builder: (context, model, child) {
                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  '所属部门',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  list[index].deptName ?? '',
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  (list[index].visitFollowTag == 'visit' ? '拜访' : '陪访') + '时间',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  (list[index].callingStartTimeStr ?? '') + ' - ' + (list[index].callingEndTimeStr ?? ''),
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  (list[index].visitFollowTag == 'visit' ? '拜访' : '陪访') + '商业',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  list[index].storeName ?? '--',
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 5),
                            if (list[index].visitFollowTag == 'follow')
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    '陪访对象',
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: GlobalData.unSelectedColor
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  Text(
                                    (list[index].followVisitAccountName ?? ''),
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            if (list[index].visitFollowTag == 'follow')
                              SizedBox(height: 5),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  (list[index].visitFollowTag == 'visit' ? '拜访' : '陪访') + '目的',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  (list[index].callingPurposeStr ?? ''),
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    )
                )
              ]
          ),
        ),
      ));
    }
    return result;
  }
  // 修改筛选弹窗方法
  void showDropPopupView(GlobalKey authKey, CommonDropConfigModel model, DropButtonController controller) async {
    if (!mounted) return;

    switch (model.paramKey) {
      case "visitTime":
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: visitTimeList,
              selectedCode: selectedValues[model.paramKey]?.isEmpty == true ? null : selectedValues[model.paramKey],
              selectAction: (value) {
                if (!mounted) return;
                // 如果点击的是已选中的选项，则取消选中
                if (selectedValues[model.paramKey] == value.code) {
                  selectedValues[model.paramKey] = '';
                } else {
                  selectedValues[model.paramKey] = value.code;
                }
                refreshList();
                controller.setIsOpen(false);
              },
              distance: distance,
            );
          },
        ).then((_) {
          // 弹窗关闭时（包括点击空白处）重置下拉按钮状态
          if (mounted && controller.value != null) {
            controller.setIsOpen(false);
          }
        });
        break;
      case "visitState":
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: visitStatesList,
              selectedCode: selectedValues[model.paramKey]?.isEmpty == true ? null : selectedValues[model.paramKey],
              selectAction: (value) {
                if (!mounted) return;
                // 如果点击的是已选中的选项，则取消选中
                if (selectedValues[model.paramKey] == value.code) {
                  selectedValues[model.paramKey] = '';
                } else {
                  selectedValues[model.paramKey] = value.code;
                }
                refreshList();
                controller.setIsOpen(false);
              },
              distance: distance,
            );
          },
        ).then((_) {
          // 弹窗关闭时（包括点击空白处）重置下拉按钮状态
          if (mounted && controller.value != null) {
            controller.setIsOpen(false);
          }
        });
        break;
      case "visitTypes":
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: visitTypesList,
              selectedCode: selectedValues[model.paramKey]?.isEmpty == true ? null : selectedValues[model.paramKey],
              selectAction: (value) {
                if (!mounted) return;
                // 如果点击的是已选中的选项，则取消选中
                if (selectedValues[model.paramKey] == value.code) {
                  selectedValues[model.paramKey] = '';
                } else {
                  selectedValues[model.paramKey] = value.code;
                }
                refreshList();
                controller.setIsOpen(false);
              },
              distance: distance,
            );
          },
        ).then((_) {
          // 弹窗关闭时（包括点击空白处）重置下拉按钮状态
          if (mounted && controller.value != null) {
            controller.setIsOpen(false);
          }
        });
        break;
    }
  }

  // 修改刷新方法，加入筛选参数
  Future<void> refreshList() async {
    businessOperationVisitData.updateIsEnd(false);
    businessOperationVisitData.updatePageNo(1);
    getTodayVisitDetail([]);
  }
}

class BusinessInvestmentVisitPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return BusinessInvestmentVisitState();

  }
}

