import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/common/shop_filter_list.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PerformanceDetailWidget extends StatelessWidget {
  List<BusinessHomeCardBean>? dataList;
  bool? isClick;
  PerformanceDetailWidget({
    this.dataList = const [],
    this.isClick = false,
  });

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(top: 3, bottom: 3),
      child: IntrinsicHeight(
        child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ...renderList(dataList, context)
            ]
        ),
      ),
      /*child: SingleChildScrollView(
        physics: BouncingScrollPhysics(),
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...renderList(dataList, context)
          ],
        ),
      ),*/
    );
  }
  List<Widget> renderList(List<BusinessHomeCardBean>? dataList, BuildContext context) {
    if (dataList == null) return [];
    List<Widget> result = [];
    for (int index = 0; index < dataList.length; index++) {
      result.add(
          Expanded(
            flex: index == 0 && dataList.length == 3 ? 4 : 3,
              child: GestureDetector(
                onTap: () {
                  BusinessHomeCardBean? item = dataList[index];
                  if (isClick ?? false) {
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => ShopFilterListPage(title: item.title,indexCardTypeCode: item.type,),
                    fullscreenDialog: true,
                  ));
                  }
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ConstrainedBox(
                      constraints: BoxConstraints(minHeight: 30),
                      child: Text(dataList[index].title ?? '',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: TextStyle(
                            fontSize: 11,
                            color: GlobalData.unSelectedColor,
                            fontWeight: FontWeight.w400
                        ),
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(dataList[index].valueFormat ?? '',
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: dataList[index].color != null && dataList[index].color != '' ? Color(int.parse(dataList[index].color!)) : GlobalData.textNormalColor
                      ),
                    ),
                    SizedBox(height: 5),
                    if (dataList[index].rate != '' && dataList[index].rate != null)
                      Align(
                        alignment: Alignment.center,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(dataList[index].rateStr ?? '',
                              style: TextStyle(
                                  color: GlobalData.unSelectedColor,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400
                              ),
                              textAlign: TextAlign.start,
                            ),
                            Text(dataList[index].rate ?? '',
                              style: TextStyle(
                                fontSize: 10,
                                color: dataList[index].rateColor != null && dataList[index].rateColor != '' ?
                                Color(int.parse(dataList[index].rateColor!)) :
                                GlobalData.textNormalColor,
                              ),
                              textAlign: TextAlign.start,
                            ),
                            if (dataList[index].isUp == 1)
                              Icon(
                                Icons.north_rounded,
                                color: dataList[index].rateColor != null && dataList[index].rateColor != '' ?
                                Color(int.parse(dataList[index].rateColor!)) :
                                GlobalData.textNormalColor,
                                size: 10,
                              ),
                            if (dataList[index].isUp == 2)
                              Icon(
                                Icons.south_rounded,
                                color: dataList[index].rateColor != null && dataList[index].rateColor != '' ?
                                Color(int.parse(dataList[index].rateColor!)) :
                                GlobalData.textNormalColor,
                                size: 10,
                              )
                          ],
                        ),
                      )
                  ],
                ),
              )
            /*child: Text(dataList[index].title!),*/
          )
      );
      if (index < dataList.length - 1)
        result.add(
            Container(
              alignment: Alignment.center,
              child: Container(
                height: 30,
                width: 1,
                color: Color.fromRGBO(0, 0, 0, 0.05),
                margin: EdgeInsets.only(left: 9, right: 9),
              ),
            )
        );
    }
    return result;
  }
}
