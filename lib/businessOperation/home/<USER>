import 'dart:ui';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/show_upertino_ialog_v2.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/buildLocaRequest.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/single_child_widget.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/customer-switch.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-card-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/horizontal-tabs-select.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/total-performance-widget.dart';
import './data/business-home-card-bean.dart';
import 'package:provider/provider.dart';

import 'data/business-home-rank-bean.dart';

class BusinessHomePage extends BasePage {
  BusinessHomePage();

  @override
  BaseState<StatefulWidget> initState() {
    // TODO: implement initState
    return BusinessHomeState();
  }
}

class DataState extends ChangeNotifier {
  //首页卡片数据
  List<BusinessHomeCardBean> _cardDataList = [];
  //细项数据
  List<BusinessHomeCardBean> _totalPerformanceDetail = [];
  //运营拜访
  List<BusinessHomeCardBean> _operationDataList = [];
  //总新签
  List<BusinessHomeCardBean> _newSignUpsList = [];
  //店铺动销
  List<BusinessHomeCardBean> _shopSales = [];
  //预警信息
  List<BusinessHomeCardBean> _merchantWarnings = [];
  //招商拜访
  List<BusinessHomeCardBean> _recruitmentVisit = [];
  //横向选择框
  List<HorizontalTabsOptions> _menuList = [
    // HorizontalTabsOptions('KA', 'ka'),
    // HorizontalTabsOptions('非KA', 'notKa'),
    // HorizontalTabsOptions('优选', 'highMargin'),
    // HorizontalTabsOptions('甄选', 'selected'),
    // HorizontalTabsOptions('批购', 'batchPurchase'),
    // HorizontalTabsOptions('中药', 'traditionalChineseMedicine'),
    // HorizontalTabsOptions('非药', 'nonMedicine')
  ];
  //卡片权限
  CardVisibleBean cardVisible = new CardVisibleBean();
  dynamic _curValue = 'ka';

  updateCardVisibleBean(CardVisibleBean cardVisibleBean) {
    this.cardVisible = cardVisibleBean;
    notify();
  }

  updateRecruitmentVisit(List<BusinessHomeCardBean> list) {
    this._recruitmentVisit = list;
    notify();
  }

  updateMerchantWarnings(List<BusinessHomeCardBean> list) {
    this._merchantWarnings = list;
    notify();
  }

  updateShopSales(List<BusinessHomeCardBean> list) {
    this._shopSales = list;
    notify();
  }

  updateOperationDataList(List<BusinessHomeCardBean> list) {
    this._operationDataList = list;
    notify();
  }

  updateTotalPerformanceDetail(List<BusinessHomeCardBean> list) {
    this._totalPerformanceDetail = list;
    notify();
  }

  updateDataPerformance(List<BusinessHomeCardBean> cardDataList) {
    this._cardDataList = cardDataList;
    notify();
  }

  updateCurValue(dynamic curValue) {
    this._curValue = curValue;
    notify();
  }

  updateNewSignUpsList(List<BusinessHomeCardBean> list) {
    this._newSignUpsList = list;
    notify();
  }
  updateMenuList(List<dynamic> list) {
    this._menuList = list.map((item) {
      // 假设 item 包含可以用于构造 BusinessHomeCardBean 的信息
      // 如果 item 是 Map 或其他结构，请根据实际情况调整
      return HorizontalTabsOptions(item.dictLabel,item.dictValue);
    }).toList();
    this._menuList.removeAt(0);
    notify();
  }

  List<HorizontalTabsOptions> get menuList => _menuList;
  List<BusinessHomeCardBean> get cardDataList => _cardDataList;
  List<BusinessHomeCardBean> get totalPerformanceDetail =>
      _totalPerformanceDetail;
  List<BusinessHomeCardBean> get shopSales => _shopSales;
  List<BusinessHomeCardBean> get newSignUpsList => _newSignUpsList;
  List<BusinessHomeCardBean> get operationDataList => _operationDataList;
  List<BusinessHomeCardBean> get merchantWarnings => _merchantWarnings;
  List<BusinessHomeCardBean> get recruitmentVisit => _recruitmentVisit;
  dynamic get curValue => _curValue;

  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}

class BusinessHomeState extends BaseState<BusinessHomePage>
    with SingleTickerProviderStateMixin {
  List<List<String>> tabList = [];
  late DataState dataState = DataState();
  late TabController _controller;
  late ScrollController _scrollController;
  bool loading = false;
  bool customerSwitch = true;
  @override
  void initState() {
    this._controller = TabController(
      length: 6,
      vsync: this,
      initialIndex: 1,
    );
    this.getCardVisible();
    this.getMenuList();
    this._scrollController = new ScrollController();
    this._controller.addListener(() {
      if (loading) return;
      request(1);
    });
    this._scrollController.addListener(() {
      if (this._scrollController.position.pixels < -50 && !loading) {
        setState(() {
          this.customerSwitch = true;
        });
        request(4);
      }
    });
    Future.delayed(Duration(milliseconds: 1000), () {
       BuildLocaRequest.locaRequest();
    });
  }

  request(int status) async {
    EasyLoading.show(status: '加载中...');
    loading = true;
    //新总签
    if ((status == 3 || status == 4) &&
        dataState.cardVisible.totalNewSignVisible == true) {
      var newSignUpsResult =
          await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
              .requestDataV2('/mop/index/newSignUps',
                  method: RequestMethod.GET,
                  parameters: {
            "timePeriod": 'today',
          });
      dataState.updateNewSignUpsList(newSignUpsResult.getListData() ?? []);
    }
    if ((status == 1 || status == 4) &&
        dataState.cardVisible.totalPerformanceVisible == true) {
      //总业绩
      var totalPerformanceResult =
          await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
              .requestDataV2('/mop/index/totalPerformance',
                  method: RequestMethod.GET,
                  parameters: {
            "type": "all",
            "timePeriod": this.tabList[this._controller.index][1],
          });
      //总业绩下明细
      var totalPerformanceDetailResult =
          await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
              .requestDataV2('/mop/index/totalPerformance',
                  method: RequestMethod.GET,
                  parameters: {
            "type": this.dataState.curValue,
            "timePeriod": this.tabList[this._controller.index][1],
          });
      dataState.updateTotalPerformanceDetail(
          totalPerformanceDetailResult.getListData() ?? []);
      dataState
          .updateDataPerformance(totalPerformanceResult.getListData() ?? []);
    }
    if ((status == 2 || status == 4) &&
        dataState.cardVisible.totalPerformanceVisible == true) {
      //总业绩下明细
      var totalPerformanceDetailResult =
          await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
              .requestDataV2('/mop/index/totalPerformance',
                  method: RequestMethod.GET,
                  parameters: {
            "type": this.dataState.curValue,
            "timePeriod": this.tabList[this._controller.index][1],
          });
      dataState.updateTotalPerformanceDetail(
          totalPerformanceDetailResult.getListData() ?? []);
    }
    if (status == 4) {
      //店铺动销
      if (dataState.cardVisible.storeActivityVisible == true) {
        var shopSalesResult =
            await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
                .requestDataV2('/mop/index/shopSales',
                    method: RequestMethod.GET);
        dataState.updateShopSales(shopSalesResult.getListData() ?? []);
      }
      //运营拜访
      if (dataState.cardVisible.operationVisitsVisible == true) {
        var operationVisitResult =
            await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
                .requestDataV2('/mop/index/operationVisit',
                    method: RequestMethod.GET);
        dataState
            .updateOperationDataList(operationVisitResult.getListData() ?? []);
      }

      //招商拜访
      if (dataState.cardVisible.recruitmentVisitsVisible == true) {
        var recruitmentVisitResult =
            await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
                .requestDataV2('/mop/index/recruitmentVisit',
                    method: RequestMethod.GET);
        dataState
            .updateRecruitmentVisit(recruitmentVisitResult.getListData() ?? []);
      }

      //预警信息
      if (dataState.cardVisible.alertInfoVisible == true) {
        var merchantWarningsResult =
            await NetworkV2<BusinessHomeCardBean>(BusinessHomeCardBean())
                .requestDataV2('/mop/index/merchantWarnings',
                    method: RequestMethod.GET);
        dataState
            .updateMerchantWarnings(merchantWarningsResult.getListData() ?? []);
      }
    }
    loading = false;
    EasyLoading.dismiss();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [ChangeNotifierProvider<DataState>(create: (context) => dataState)];
  }

  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget
    return BaseContainer(
      color: GlobalData.normalViewBgColor,
      child: SliverTabBarOnTop(
        tabBar: dataState.cardVisible.totalPerformanceVisible == true
            ? getTabBar()
            : null,
        controller: _scrollController,
        tabBarColor: Colors.white,
        body: [
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.totalPerformanceVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(
                      children: [
                        Consumer<DataState>(
                          builder: (context, model, child) {
                            return TotalPerformanceWidget(
                                dataList: model.cardDataList);
                          },
                        ),
                        SizedBox(height: 15),
                        Consumer<DataState>(
                          builder: (context, model, child) {
                            return HorizontalTabsSelect(
                              onTap: (value) async {
                                dataState.updateCurValue(value);
                                request(2);
                              },
                              options: model._menuList,
                            );
                          },
                        ),
                        SizedBox(height: 5),
                        Consumer<DataState>(
                          builder: (context, model, child) {
                            return PerformanceDetailWidget(
                              dataList: model.totalPerformanceDetail,
                            );
                          },
                        ),
                      ],
                    ),
                  )
                : Container();
          }),
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.totalNewSignVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            child: Row(
                              children: [
                                Text(
                                  '总新签',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600),
                                ),
                                tips(dataState
                                        .cardVisible.totalNewSignDescription ??
                                    '--'),
                                SizedBox(width: 5),
                                Row(
                                  children: [
                                    //开关
                                    CustomerSwitch(
                                      onChange: (bool status) async {
                                        setState(() {
                                          this.customerSwitch = status;
                                        });
                                        EasyLoading.show(status: '查询中...');
                                        var newSignUpsResult = await NetworkV2<
                                                    BusinessHomeCardBean>(
                                                BusinessHomeCardBean())
                                            .requestDataV2(
                                                '/mop/index/newSignUps',
                                                method: RequestMethod.GET,
                                                parameters: {
                                              "timePeriod": status
                                                  ? 'today'
                                                  : 'this_month',
                                            });
                                        dataState.updateNewSignUpsList(
                                            newSignUpsResult.getListData() ??
                                                []);
                                        EasyLoading.dismiss();
                                      },
                                      status: customerSwitch,
                                      openLabel: '今日',
                                      closeLabel: '本月',
                                      bgColorWhenSelectedOpen:
                                          Color.fromRGBO(241, 241, 241, 1.0),
                                      closeColorWhenSelectedOpen:
                                          Color.fromRGBO(170, 170, 170, 1.0),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Align(
                              child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).pushNamed(
                                  '/business_investment_rank_page',
                                  arguments: {
                                    "timePeriod":
                                        customerSwitch ? 'today' : 'this_month'
                                  });
                            },
                            child: Row(
                              children: [
                                Text(
                                  '团队排行',
                                  style: TextStyle(
                                      fontSize: 11,
                                      color: GlobalData.unSelectedColor),
                                ),
                                Icon(Icons.navigate_next_rounded,
                                    size: 12, color: GlobalData.unSelectedColor)
                              ],
                            ),
                          ))
                        ],
                      ),
                      SizedBox(height: 10),
                      Consumer<DataState>(
                        builder: (context, model, child) {
                          return PerformanceDetailCardWidget(
                            dataList: model.newSignUpsList,
                            colCount: 4,
                            isClick: true,
                            timePeriod:
                                this.customerSwitch ? 'TODAY' : 'THIS_MONTH',
                          );
                        },
                      )
                    ]))
                : //总新签
                Container();
          }),
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.storeActivityVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            child: Row(
                              children: [
                                Text(
                                  '店铺动销',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600),
                                ),
                                tips(dataState
                                        .cardVisible.storeActivityDescription ??
                                    '--')
                              ],
                            ),
                          ),
                          /*Align(
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).pushNamed('/business_operation_rank_page');
                            },
                            child: Row(
                              children: [
                                Text(
                                  '查看更多',
                                  style: TextStyle(
                                      fontSize: 10,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                Icon(
                                    Icons.navigate_next_rounded,
                                    size: 12,
                                    color: GlobalData.unSelectedColor
                                )
                              ],
                            ),
                          )
                        )*/
                        ],
                      ),
                      SizedBox(height: 10),
                      Consumer<DataState>(
                        builder: (context, model, child) {
                          return PerformanceDetailCardWidget(
                            dataList: model.shopSales,
                            colCount: 4,
                            isClick: true,
                          );
                        },
                      )
                    ]))
                : //店铺动销
                Container();
          }),
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.operationVisitsVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            child: Row(
                              children: [
                                Text(
                                  '运营拜访',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600),
                                ),
                                tips(dataState.cardVisible
                                        .operationVisitsDescription ??
                                    '--'),
                              ],
                            ),
                          ),
                          Align(
                              child: GestureDetector(
                            onTap: () {
                              Navigator.of(context)
                                  .pushNamed('/business_operation_visit_page');
                            },
                            child: Row(
                              children: [
                                Text(
                                  '查看更多',
                                  style: TextStyle(
                                      fontSize: 11,
                                      color: GlobalData.unSelectedColor),
                                ),
                                Icon(Icons.navigate_next_rounded,
                                    size: 12, color: GlobalData.unSelectedColor)
                              ],
                            ),
                          ))
                        ],
                      ),
                      SizedBox(height: 10),
                      Consumer<DataState>(
                        builder: (context, model, child) {
                          return PerformanceDetailWidget(
                            dataList: model.operationDataList,
                          );
                        },
                      )
                    ]))
                : //运营拜访
                Container();
          }),
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.recruitmentVisitsVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            child: Row(
                              children: [
                                Text(
                                  '招商拜访',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600),
                                ),
                                tips(dataState.cardVisible
                                        .recruitmentVisitsDescription ??
                                    '--'),
                              ],
                            ),
                          ),
                          Align(
                              child: GestureDetector(
                            onTap: () {
                              Navigator.of(context)
                                  .pushNamed('/business_investment_visit_page');
                            },
                            child: Row(
                              children: [
                                Text(
                                  '查看更多',
                                  style: TextStyle(
                                      fontSize: 11,
                                      color: GlobalData.unSelectedColor),
                                ),
                                Icon(Icons.navigate_next_rounded,
                                    size: 12, color: GlobalData.unSelectedColor)
                              ],
                            ),
                          ))
                        ],
                      ),
                      SizedBox(height: 10),
                      Consumer<DataState>(
                        builder: (context, model, child) {
                          return PerformanceDetailWidget(
                            dataList: model.recruitmentVisit,
                          );
                        },
                      )
                    ]))
                : //招商拜访
                Container();
          }),
          Consumer<DataState>(builder: (context, modal, child) {
            return dataState.cardVisible.alertInfoVisible == true
                ? Container(
                    alignment: Alignment.centerLeft,
                    padding: GlobalData.normalCardPadding,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    child: Column(children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Align(
                            child: Row(
                              children: [
                                Text(
                                  '预警信息',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600),
                                ),
                                tips(dataState
                                        .cardVisible.alertInfoDescription ??
                                    '--'),
                              ],
                            ),
                          )
                        ],
                      ),
                      SizedBox(height: 10),
                      Consumer<DataState>(
                        builder: (context, model, child) {
                          return PerformanceDetailWidget(
                            dataList: model.merchantWarnings,
                            isClick: true,
                          );
                        },
                      )
                    ]))
                : //预警信息
                Container();
          }),
        ],
      ),
    );
  }

  /*getTimeMenu() async {
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(
        BusinessHomeRankTabOptionsBean())
        .requestDataV2(
        '/mop/dict/list', method: RequestMethod.GET, parameters: {
      "dictType": "mop_time_period"
    });
    var list = tabListResult.getListData() ?? [];
    list.forEach((element) {
      tabList.add([element.dictLabel!, element.dictValue!]);
    });
    setState(() {});
  }*/
  Widget tips(String content) {
    // print("xwh");
    // print(dataState.cardVisible.totalNewSignDescription);
    return Container(
      padding: EdgeInsets.only(left: 3),
      child: GestureDetector(
        onTap: () => {
          showCommonAlertV2(
            context: this.context,
            title: "提示",
            contentTextAlign: TextAlign.left,
            content: content,
            actions: [
              CommonAlertActionV2(
                title: "好的",
              ),
            ],
          )
        },
        child: Image.asset(
          "assets/images/home/<USER>",
          width: 14,
          height: 14,
        ),
      ),
    );
  }

  //首页tab
  Widget getTabBar() {
    return dataState.cardVisible.totalPerformanceVisible == true
        ? XTabBar(
            controller: this._controller,
            unselectedLabelColor: GlobalData.unSelectedColor,
            labelColor: GlobalData.selectedColor,
            indicatorSize: TabBarIndicatorSize.label,
            labelPadding: EdgeInsets.all(5),
            indicator: CustomUnderlineTabIndicator(
              borderSide: BorderSide(
                  color: GlobalData.selectedColor,
                  width: 2,
                  style: BorderStyle.solid),
            ),
            tabs: [
              ...this.tabList.asMap().keys.map((i) {
                return XTab(
                  notHaveIconHeight: 35,
                  child: Text(
                    this.tabList[i][0],
                    style: TextStyle(fontSize: 14),
                  ),
                );
              })
            ],
          )
        : Container();
  }
  getMenuList() async{
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(
        BusinessHomeRankTabOptionsBean())
        .requestDataV2('/mop/dict/list',
        method: RequestMethod.GET,
        parameters: {"dictType": "mop_gmv_type"});
    dataState.updateMenuList(tabListResult.getListData()!);
  }
  //卡片权限以及枚举
  getCardVisible() async {
    var result = await NetworkV2<CardVisibleBean>(CardVisibleBean())
        .requestDataV2('/mop/index/visible', method: RequestMethod.GET);
    dataState.updateCardVisibleBean(result.getData() ?? new CardVisibleBean());
    print("jtt");
    print(result.getData());
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(
            BusinessHomeRankTabOptionsBean())
        .requestDataV2('/mop/dict/list',
            method: RequestMethod.GET,
            parameters: {"dictType": "mop_time_period"});
    var list = tabListResult.getListData() ?? [];
    list.forEach((element) {
      this.tabList.add([element.dictLabel!, element.dictValue!]);
    });
    this.request(4);
    setState(() {});
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    // TODO: implement getTitleName
    return CommonTitleBar(
      '商家运营',
      backgroundColor: Colors.white,
      leftType: LeftButtonType.none,
      leftButton: null,
      leadingWidth: 0,
      rightButtons: [
        SizedBox(
          width: 30,
        )
      ],
    );
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '';
  }

  @override
  void dispose() {
    // 释放资源
    this._controller.dispose();
    super.dispose();
  }
}
