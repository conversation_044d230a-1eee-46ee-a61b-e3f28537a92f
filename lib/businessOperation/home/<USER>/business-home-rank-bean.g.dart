// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business-home-rank-bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessHomeRankTabOptionsBean _$BusinessHomeRankTabOptionsBeanFromJson(
    Map<String, dynamic> json) {
  return BusinessHomeRankTabOptionsBean(
    dictLabel: json['dictLabel'] as String?,
    dictValue: json['dictValue'] as String?,
    dictSort: json['dictSort'] as int?,
    dictType: json['dictType'] as String?,
  );
}

Map<String, dynamic> _$BusinessHomeRankTabOptionsBeanToJson(
        BusinessHomeRankTabOptionsBean instance) =>
    <String, dynamic>{
      'dictLabel': instance.dictLabel,
      'dictValue': instance.dictValue,
      'dictSort': instance.dictSort,
      'dictType': instance.dictType,
    };

BusinessHomeRankDataBean _$BusinessHomeRankDataBeanFromJson(
    Map<String, dynamic> json) {
  return BusinessHomeRankDataBean(
    accountId: json['accountId'] as String?,
    storeName: json['storeName'] as String?,
    regions: json['regions'] as String?,
    manager: json['manager'] as String?,
    metrics: (json['metrics'] as List<dynamic>?)
        ?.map((e) => BusinessHomeCardBean.fromJson(e as Map<String, dynamic>))
        .toList(),
  );
}

Map<String, dynamic> _$BusinessHomeRankDataBeanToJson(
        BusinessHomeRankDataBean instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      'storeName': instance.storeName,
      'regions': instance.regions,
      'manager': instance.manager,
      'metrics': instance.metrics,
    };

BusinessVisitDetailBean _$BusinessVisitDetailBeanFromJson(
    Map<String, dynamic> json) {
  return BusinessVisitDetailBean(
    totalCount: json['totalCount'] as int?,
    result: (json['result'] as List<dynamic>?)
        ?.map((e) =>
            BusinessVisitDetailListBean.fromJson(e as Map<String, dynamic>))
        .toList(),
    pageNo: json['pageNo'] as int?,
    pageSize: json['pageSize'] as int?,
    scrollId: json['scrollId'] as String?,
  );
}

Map<String, dynamic> _$BusinessVisitDetailBeanToJson(
        BusinessVisitDetailBean instance) =>
    <String, dynamic>{
      'totalCount': instance.totalCount,
      'result': instance.result,
      'pageNo': instance.pageNo,
      'pageSize': instance.pageSize,
      'scrollId': instance.scrollId,
    };

BusinessVisitDetailListBean _$BusinessVisitDetailListBeanFromJson(
    Map<String, dynamic> json) {
  return BusinessVisitDetailListBean(
    id: json['id'] as int?,
    merchantManagerId: json['merchantManagerId'] as int?,
    createTime: json['createTime'] as String?,
    createTimeStr: json['createTimeStr'] as String?,
    callingStartTime: json['callingStartTime'] as String?,
    callingStartTimeStr: json['callingStartTimeStr'] as String?,
    callingEndTime: json['callingEndTime'] as String?,
    callingEndTimeStr: json['callingEndTimeStr'] as String?,
    accountName: json['accountName'] as String?,
    accountId: json['accountId'] as String?,
    followVisitAccountId: json['followVisitAccountId'] as String?,
    followVisitAccountName: json['followVisitAccountName'] as String?,
    callingType: json['callingType'] as String?,
    callingTypeStr: json['callingTypeStr'] as String?,
    callingMethod: json['callingMethod'] as String?,
    callingMethodStr: json['callingMethodStr'] as String?,
    isValid: json['isValid'] as int?,
    callingStatus: json['callingStatus'],
    poiId: json['poiId'] as int?,
    poiName: json['poiName'] as String?,
    orgId: json['orgId'] as String?,
    orgName: json['orgName'] as String?,
    storeName: json['storeName'] as String?,
    provId: json['provId'] as int?,
    provName: json['provName'] as String?,
    cityId: json['cityId'] as int?,
    cityName: json['cityName'] as String?,
    contactName: json['contactName'] as String?,
    contactPhone: json['contactPhone'] as String?,
    callingPurpose: json['callingPurpose'] as String?,
    callingPurposeStr: json['callingPurposeStr'] as String?,
    callingSummary: json['callingSummary'] as String?,
    callingPicture: json['callingPicture'] as String?,
    deptName: json['deptName'] as String?,
    isKp: json['isKp'] as int?,
    visitFollowTag: json['visitFollowTag'] as String?
  );
}

Map<String, dynamic> _$BusinessVisitDetailListBeanToJson(
        BusinessVisitDetailListBean instance) =>
    <String, dynamic>{
      'id': instance.id,
      'merchantManagerId': instance.merchantManagerId,
      'createTime': instance.createTime,
      'createTimeStr': instance.createTimeStr,
      'callingStartTime': instance.callingStartTime,
      'callingStartTimeStr': instance.callingStartTimeStr,
      'callingEndTime': instance.callingEndTime,
      'callingEndTimeStr': instance.callingEndTimeStr,
      'accountName': instance.accountName,
      'accountId': instance.accountId,
      'followVisitAccountId': instance.followVisitAccountId,
      'followVisitAccountName': instance.followVisitAccountName,
      'callingType': instance.callingType,
      'callingTypeStr': instance.callingTypeStr,
      'callingMethod': instance.callingMethod,
      'callingMethodStr': instance.callingMethodStr,
      'isValid': instance.isValid,
      'callingStatus': instance.callingStatus,
      'poiId': instance.poiId,
      'poiName': instance.poiName,
      'orgId': instance.orgId,
      'orgName': instance.orgName,
      'storeName': instance.storeName,
      'provId': instance.provId,
      'provName': instance.provName,
      'cityId': instance.cityId,
      'cityName': instance.cityName,
      'contactName': instance.contactName,
      'contactPhone': instance.contactPhone,
      'callingPurpose': instance.callingPurpose,
      'callingPurposeStr': instance.callingPurposeStr,
      'callingSummary': instance.callingSummary,
      'callingPicture': instance.callingPicture,
      'deptName': instance.deptName,
      'isKp': instance.isKp,
      'visitFollowTag': instance.visitFollowTag
    };
