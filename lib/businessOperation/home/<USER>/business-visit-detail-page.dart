import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/horizontal-tabs-select.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/rank-card-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/rank-icon.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';

class BusinessVisitData extends ChangeNotifier {
  BusinessVisitDetailListBean data = new BusinessVisitDetailListBean();

  updateBusinessVisitDetailListBean(BusinessVisitDetailListBean data) {
    this.data = data;
  }
  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class BusinessVisitDetailState extends BaseState<BusinessVisitDetailPage> with SingleTickerProviderStateMixin {
  String id;
  BusinessVisitData businessVisitData = new BusinessVisitData();
  bool loading = false;
  BusinessVisitDetailState({required this.id});
  @override
  void initState() {
    this.getData();
  }
  getData() async {
    EasyLoading.show(status: '加载中...');
    var dataResult = await NetworkV2<BusinessVisitDetailListBean>(BusinessVisitDetailListBean())
        .requestDataV2('/mop/calling/log/detail', method: RequestMethod.GET, parameters: {
          "id": this.id
    });
    EasyLoading.dismiss();
    businessVisitData.updateBusinessVisitDetailListBean(dataResult.getData() ?? BusinessVisitDetailListBean());
    setState(() {});
  }
  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget\
    return BaseContainer(
      color: GlobalData.normalViewBgColor,
      child: ChangeNotifierProvider(
        create: (_) {
          return businessVisitData;
        },
        child: Container(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 8, right: 8),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Consumer<BusinessVisitData>(builder: (context, modal, child) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            (businessVisitData.data.accountName ?? '') + ' - ' + (businessVisitData.data.callingMethodStr ?? ''),
                            style: TextStyle(
                                fontWeight: FontWeight.w600
                            ),
                          ),
                          StatusTag(
                              text: businessVisitData.data.callingStatus == 0 ? '草稿' : businessVisitData.data.isValid == 1 ? '有效' : '无效',
                              size: 18,
                              type: businessVisitData.data.callingStatus == 0 ? StatusTagType.NORMAL : businessVisitData.data.isValid == 1 ? StatusTagType.SUCCESS : StatusTagType.ERROR
                          ),
                        ],
                      );
                    }),
                    SizedBox(height: 5),
                    Text(
                      businessVisitData.data.deptName ?? '',
                      style: TextStyle(
                        fontSize: 12
                      ),
                    ),
                    SizedBox(height: 5),
                  ],
                ),
              ),
              SizedBox(height: 5),
              Container(
                  padding: EdgeInsets.all(7),
                  color: Colors.white,
                  child: Consumer<BusinessVisitData>(
                    builder: (context, model, child) {
                      return Column(
                        children: [
                          SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                (businessVisitData.data.visitFollowTag == 'visit' ? '拜访' : '陪访') + '时间',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                              ),
                              SizedBox(width: 10),
                              Text(
                                (businessVisitData.data.callingStartTimeStr ?? '') + ' 至 ' + (businessVisitData.data.callingEndTimeStr ?? ''),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                (businessVisitData.data.visitFollowTag == 'visit' ? '拜访' : '陪访') + '商业',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                              ),
                              SizedBox(width: 10),
                              Text(
                                (businessVisitData.data.orgName ?? ''),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          if (businessVisitData.data.visitFollowTag == 'follow')
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  '陪访对象',
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: GlobalData.unSelectedColor
                                  ),
                                ),
                                SizedBox(width: 10),
                                Text(
                                  (businessVisitData.data.followVisitAccountName ?? ''),
                                  style: TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          if (businessVisitData.data.visitFollowTag == 'follow')
                            SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                businessVisitData.data.visitFollowTag == 'visit' ? '联  系  人' : '陪  访  人',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                              ),
                              SizedBox(width: 10),
                              Text(
                                ((businessVisitData.data.visitFollowTag == 'visit' ? businessVisitData.data.contactName ?? '' : businessVisitData.data.accountName ?? '')
                                    + ' '
                                    + (businessVisitData.data.isKp == 1 ? 'KP' : '')),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          if(businessVisitData.data.visitFollowTag == 'visit')
                            Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                '联系方式',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                              ),
                              SizedBox(width: 10),
                              Text(
                                (businessVisitData.data.contactPhone ?? ''),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          if(businessVisitData.data.visitFollowTag == 'visit')
                            SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                (businessVisitData.data.visitFollowTag == 'visit' ? '拜访' : '陪访') + '目的',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                              ),
                              SizedBox(width: 10),
                              Text(
                                (businessVisitData.data.callingPurposeStr ?? ''),
                                style: TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                (businessVisitData.data.visitFollowTag == 'visit' ? '拜访' : '陪访') + '总结',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                                textAlign: TextAlign.start,
                              ),
                              SizedBox(width: 10),
                              Container(
                                child: Column(
                                  children: [
                                    Text(
                                      (businessVisitData.data.callingSummary ?? ''),
                                      style: TextStyle(
                                        fontSize: 12,
                                      ),
                                      textAlign: TextAlign.start,
                                      maxLines: 100,
                                    ),
                                    /*SizedBox(height: 5),
                                    //图片
                                    getImageWidget(businessVisitData.data.callingPicture ?? '')*/
                                  ],
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 5),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '               ',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: GlobalData.unSelectedColor
                                ),
                                textAlign: TextAlign.start,
                              ),
                              SizedBox(width: 10),
                              Container(
                                child: getImageWidget(businessVisitData.data.callingPicture ?? ''),
                              )
                            ],
                          ),
                        ],
                      );
                    },
                  )
              )
            ],
          ),
        ),
      ),
    );
  }
  Widget getImageWidget(String urls) {
    if (urls == '') return Container();
    var result = urls.split(",").map((e) => GestureDetector(
        onTap: () {
          //预览
          Navigator.of(context).pushNamed('/photo_view_page',
              arguments: {"urlPath": e, "delete": false, "callback": () {}});
        },
        child: Container(
          width: 70,
          height: 70,
          padding: EdgeInsets.all(5),
          child: ImageWidget(
            w: 50,
            h: 50,
            url: e,
            fit: BoxFit.fill,
          ),
        )
    )).toList();
    var list = [];
    var col = [];
    for (var i = 0; i < result.length; i++) {
      list.add(result[i]);
      list.add(SizedBox(width: 5));
      if ((i + 1) % 3 == 0) {
        col.add(Row(
          children: [...list],
        ));
        col.add(SizedBox(height: 5));
        list = [];
      }
    }
    if (list.length > 0) {
      col.add(Row(
        children: [...list],
      ));
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [...col],
    );
  }
  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '拜访详情';
  }


}
class BusinessVisitDetailPage extends BasePage {
  String id;
  BusinessVisitDetailPage({
      required this.id
  });
  @override
  BaseState<StatefulWidget> initState() {
    return BusinessVisitDetailState(id: this.id);

  }
}

