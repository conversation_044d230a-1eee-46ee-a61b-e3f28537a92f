import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'business-home-card-bean.g.dart';

@JsonSerializable()
class BusinessHomeCardBean extends BaseModelV2<BusinessHomeCardBean> {
  String? title;
  String? value; //原始值
  String? previousValue; //不知道啥用，但有
  String? valueFormat; //展示值
  String? rate;
  String? rateStr;
  String? type;
  String? color;
  String? rateColor; //环比颜色
  int? isUp; //0没有  1上升   2下降

  BusinessHomeCardBean(
      {this.title,
      this.value,
      this.type,
      this.isUp,
      this.color,
      this.rate,
      this.rateStr,
      this.previousValue,
      this.rateColor,
      this.valueFormat});
  @override
  BusinessHomeCardBean fromJsonMap(Map<String, dynamic> json) =>
      _$BusinessHomeCardBeanFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessHomeCardBeanToJson(this);

  factory BusinessHomeCardBean.fromJson(Map<String, dynamic> json) =>
      _$BusinessHomeCardBeanFromJson(json);
}

/*@JsonSerializable()
class BusinessHomeCardBeanList extends BaseModelV2<BusinessHomeCardBeanList> {
  int? code;
  String? message;
  List<BusinessHomeCardBean>? data;


  BusinessHomeCardBeanList();

  @override
  BusinessHomeCardBeanList fromJsonMap(Map<String, dynamic> json) => _$BusinessHomeCardBeanListFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BusinessHomeCardBeanListToJson(this);

  factory BusinessHomeCardBeanList.fromJson(Map<String, dynamic> json) => _$BusinessHomeCardBeanListFromJson(json);
}*/

@JsonSerializable()
class CardVisibleBean extends BaseModelV2<CardVisibleBean> {
  bool? totalPerformanceVisible; //总业绩
  bool? totalNewSignVisible; //总新签
  bool? storeActivityVisible; //店铺动销
  bool? alertInfoVisible; //预警信息
  bool? operationVisitsVisible; //运营拜访
  bool? recruitmentVisitsVisible; //招商拜访
  bool? operationRankingVisible; //运营拜访
  bool? recruitmentRankingVisible; //招商拜访
  dynamic? totalNewSignDescription; //总新签区域说明
  dynamic? storeActivityDescription; //店铺动销区域说明
  dynamic? alertInfoDescription; //预警信息区域说明
  dynamic? operationVisitsDescription; //运营拜访区域说明
  dynamic? recruitmentVisitsDescription; //招商拜访区域说明
  CardVisibleBean(
      {this.totalPerformanceVisible,
      this.totalNewSignVisible,
      this.storeActivityVisible,
      this.alertInfoVisible,
      this.operationVisitsVisible,
      this.recruitmentVisitsVisible,
      this.operationRankingVisible,
      this.recruitmentRankingVisible,
      this.totalNewSignDescription,
      this.storeActivityDescription,
      this.alertInfoDescription,
      this.operationVisitsDescription,
      this.recruitmentVisitsDescription});
  @override
  CardVisibleBean fromJsonMap(Map<String, dynamic> json) =>
      _$CardVisibleBeanFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CardVisibleBeanToJson(this); //数据
  factory CardVisibleBean.fromJson(Map<String, dynamic> json) =>
      _$CardVisibleBeanFromJson(json);
}
