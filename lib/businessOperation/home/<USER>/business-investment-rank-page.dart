import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/horizontal-tabs-select.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-card-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/rank-icon.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';

class BusinessInvestmentRankData extends ChangeNotifier {
  //tab筛选  BusinessHomeRankTabOptionsBean
  List<BusinessHomeRankTabOptionsBean> _tabList = [];
  //数据
  List<BusinessHomeRankDataBean> _dataList = [];
  dynamic _dictValue = "";
  updateDictValue(dynamic value) {
    this._dictValue = value;
  }
  updateDataList(List<BusinessHomeRankDataBean> list) {
    this._dataList = list;
    notify();
  }
  updateTabList(List<BusinessHomeRankTabOptionsBean> list) {
    this._tabList = list;
    notify();
  }

  List<BusinessHomeRankDataBean> get dataList => this._dataList;
  List<BusinessHomeRankTabOptionsBean> get tabList => this._tabList;
  dynamic get dictValue => this._dictValue;
  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class BusinessInvestmentRankState extends BaseState<BusinessInvestmentRankPage> with SingleTickerProviderStateMixin {
  String timePeriod;
  List<List<String>> tabList = [['今日', 'today'], ['本月', 'this_month']];
  bool isLoading = false;
  BusinessInvestmentRankData businessInvestmentRankData = new BusinessInvestmentRankData();
  late TabController _controller;
  BusinessInvestmentRankState({ this.timePeriod = "" });
  @override
  void initState() {

    this._controller = TabController(
      length: tabList.length,
      vsync: this,
      initialIndex: this.timePeriod == "today" ? 0 : this.timePeriod == 'this_month' ? 1 : 1,
    );
    this.getTabList();
  }
  getTabList() async {
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(BusinessHomeRankTabOptionsBean())
        .requestDataV2('/mop/dict/regions', method: RequestMethod.GET);
    businessInvestmentRankData.updateTabList(tabListResult.getListData() ?? []);
    businessInvestmentRankData.updateDictValue(businessInvestmentRankData.tabList.length > 0 ? businessInvestmentRankData.tabList[0].dictValue! : '');
    this.getDataList(this.tabList[this._controller.index][1], businessInvestmentRankData.dictValue);
  }
  getDataList(String timePeriod, String positionLevel) async {
    isLoading = true;
    EasyLoading.show(status: '加载中...');
    var dataList = await NetworkV2<BusinessHomeRankDataBean>(BusinessHomeRankDataBean())
        .requestDataV2('/mop/index/listNewSignUps', method: RequestMethod.GET, parameters: {
      "timePeriod": timePeriod,
      "positionLevel": positionLevel
    });
    isLoading = false;
    this.businessInvestmentRankData.updateDataList(dataList.getListData() ?? []);
    EasyLoading.dismiss();
  }
  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget
    return BaseContainer(
      color: GlobalData.normalViewBgColor,
      child: ChangeNotifierProvider(
        create: (_) {
          this._controller.addListener(() async {
            if (isLoading) return;
            this.getDataList(this.tabList[this._controller.index][1], businessInvestmentRankData.dictValue);
          });
          return businessInvestmentRankData;
        },
        child: Container(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: getTabBar(),
                padding: EdgeInsets.all(5),
              ),
              Consumer<BusinessInvestmentRankData>(
                  builder: (context, model, child) {
                    return Container(
                        color: Colors.white,
                        child: businessInvestmentRankData.tabList.length > 0 ? HorizontalTabsSelect(
                          onTap: (value) {
                            businessInvestmentRankData.updateDictValue(value);
                            this.getDataList(this.tabList[this._controller.index][1], value);
                          },
                          size: 80,
                          options: [...businessInvestmentRankData.tabList.map((item) => HorizontalTabsOptions(item.dictLabel!, item.dictValue))],
                        ) : Container()
                    );
                  }
              ),
              Expanded(
                    child:EasyRefresh(
                      // controller: _controller,
                      onRefresh: () async {
                        return await this.getDataList(this.tabList[this._controller.index][1], businessInvestmentRankData.dictValue);
                      },
                      onLoad: null,
                      emptyWidget: null,
                      child : SingleChildScrollView(
                        physics: BouncingScrollPhysics(),
                        padding: EdgeInsets.only(left: 7,right: 7, top: 5,bottom: 5),
                        child: Consumer<BusinessInvestmentRankData>(
                          builder: (context, model, child) {
                        return Column(
                          children: [
                            ...getRank(model.dataList)
                          ],
                        );
                      },
                    ),
                  ),
                )
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '招商排行榜';
  }
  Widget getTabBar() {
    return XTabBar(
      controller: this._controller,
      unselectedLabelColor: GlobalData.unSelectedColor,
      labelColor: GlobalData.selectedColor,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.all(5),
      onTap: (index) {
        //model.updateDataPerformanceList('{ "data": [{"title": "啊哈哈哈哈", "tValue": "25,235,368", "tTipsList": [{"title": "啊哈哈哈哈", "tValue": "25,235,368"}]}], "code": 0, "msg": "success" }');
        //model.updateDataPerformanceList();
      },
      indicator: CustomUnderlineTabIndicator(
        borderSide: BorderSide(
            color: GlobalData.selectedColor,
            width: 1,
            style: BorderStyle.solid
        ),
      ),
      tabs: [
        ...this.tabList.asMap().keys.map((i) {
          return XTab(
            notHaveIconHeight: 30,
            child: Text(
              this.tabList[i][0],
              style: TextStyle(
                  fontSize: 14
              ),
            ),
          );
        })
      ],
    );
  }

  //排行榜
  List<Widget> getRank(List<BusinessHomeRankDataBean> list) {
    List<Widget> result = [];
    for (int index = 0; index < list.length; index++) {
      result.add(Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(bottom: index < list.length - 1 ? 10 : 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: Colors.white
        ),
        child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(7),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                        child: Consumer<BusinessInvestmentRankData>(
                          builder: (context, model, child) {
                            return Row(
                              children: [
                                RankIcon(level: index, size: 14),
                                SizedBox(width: 5),
                                Transform.translate(
                                  offset: Offset(0, -2),
                                  child: Text(
                                    list[index].manager ?? '',
                                    style: TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        )
                    ),
                    Container(
                      child: Text(
                        list[index].regions ?? '',
                        style: TextStyle(
                            fontSize: 12
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Divider(height: 1, color: Color.fromRGBO(235, 235, 235, 1.0)),
              Container(
                  padding: EdgeInsets.all(7),
                  child: Consumer<BusinessInvestmentRankData>(
                    builder: (context, model, child) {
                      return PerformanceDetailCardWidget(
                        dataList:  list[index].metrics,
                      );
                    },
                  )
              )
            ]
        ),
      ));
    }
    return result;
  }

//获取筛选枚举
}
class BusinessInvestmentRankPage extends BasePage {
  late String timePeriod;
  BusinessInvestmentRankPage({this.timePeriod = "today"});
  @override
  BaseState<StatefulWidget> initState() {
    return BusinessInvestmentRankState(timePeriod: timePeriod);

  }
}

