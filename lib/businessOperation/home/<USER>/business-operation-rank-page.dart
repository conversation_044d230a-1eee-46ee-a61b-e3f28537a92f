import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/CustomerTabBar.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/horizontal-tabs-select.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-store-rank-page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/performance-detail-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/rank-icon.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/search-widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';

class BusinessOperationRankData extends ChangeNotifier {
  //tab筛选  BusinessHomeRankTabOptionsBean
  List<BusinessHomeRankTabOptionsBean> _tabList = [];
  //数据
  List<BusinessHomeRankDataBean> _dataList = [];
  dynamic _dictValue = "";
  updateDictValue(dynamic value) {
    this._dictValue = value;
  }
  updateDataList(List<BusinessHomeRankDataBean> list) {
    this._dataList = list;
    notify();
  }
  updateTabList(List<BusinessHomeRankTabOptionsBean> list) {
    this._tabList = list;
    notify();
  }

  List<BusinessHomeRankDataBean> get dataList => this._dataList;
  List<BusinessHomeRankTabOptionsBean> get tabList => this._tabList;
  dynamic get dictValue => this._dictValue;
  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class BusinessOperationRankState extends BaseState<BusinessOperationRankPage> with SingleTickerProviderStateMixin {
  List<List<String>> tabList = [];
  String search = '';
  bool showSearch = false;
  BusinessOperationRankData businessOperationRankData = new BusinessOperationRankData();
  TextEditingController _textController = TextEditingController();
  FocusNode _focusNode = FocusNode();
  late TabController _controller;
  bool isLoading = false;

  @override
  void initState() {
    this._controller = TabController(
      length: 6,
      vsync: this,
      initialIndex: 1,
    );
    this.getTabList();
  }
  getTabList() async {
    var tabListResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(BusinessHomeRankTabOptionsBean())
        .requestDataV2('/mop/dict/regions', method: RequestMethod.GET);
    businessOperationRankData.updateTabList(tabListResult.getListData() ?? []);
    businessOperationRankData.updateDictValue(businessOperationRankData.tabList.length > 0 ? businessOperationRankData.tabList[0].dictValue! : '');
    var tabResult = await NetworkV2<BusinessHomeRankTabOptionsBean>(
        BusinessHomeRankTabOptionsBean())
        .requestDataV2(
        '/mop/dict/list', method: RequestMethod.GET, parameters: {
      "dictType": "mop_time_period"
    });
    var list = tabResult.getListData() ?? [];
    list.forEach((element) {
      tabList.add([element.dictLabel!, element.dictValue!]);
    });
    setState(() {});
    this.getDataList(this.tabList[this._controller.index][1], businessOperationRankData.dictValue);
  }
  getDataList(String timePeriod, String positionLevel) async {
    EasyLoading.show(status: '加载中...');
    isLoading = true;
    var dataList = await NetworkV2<BusinessHomeRankDataBean>(BusinessHomeRankDataBean())
        .requestDataV2('/mop/index/listTotalPerformance', method: RequestMethod.GET, parameters: businessOperationRankData.dictValue == "personnel" ? {
      "timePeriod": timePeriod,
      "positionLevel": positionLevel,
      "search": this.search
    } : {
      "timePeriod": timePeriod,
      "positionLevel": positionLevel
    });
    isLoading = false;
    this.businessOperationRankData.updateDataList(dataList.getListData() ?? []);
    EasyLoading.dismiss();
  }
  @override
  Widget buildWidget(BuildContext context) {
    // TODO: implement buildWidget
    return BaseContainer(
        color: GlobalData.normalViewBgColor,
        child: ChangeNotifierProvider(
          create: (_) {
            this._controller.addListener(() async {
              if (isLoading) return;
              this.getDataList(this.tabList[this._controller.index][1], businessOperationRankData.dictValue);
            });
            return businessOperationRankData;
          },
          child: Container(
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  child: tabList.length > 0 ? getTabBar() : Container(),
                  padding: EdgeInsets.all(5),
                ),
                Consumer<BusinessOperationRankData>(
                  builder: (context, model, child) {
                    return Container(
                      color: Colors.white,
                      child: businessOperationRankData.tabList.length > 0 ? HorizontalTabsSelect(
                        onTap: (value) {
                          businessOperationRankData.updateDictValue(value);
                          if (value != "personnel") {
                            showSearch = false;
                          }
                          setState(() {});
                          this.getDataList(this.tabList[this._controller.index][1], value);
                        },
                        size: 80,
                        options: [...businessOperationRankData.tabList.map((item) => HorizontalTabsOptions(item.dictLabel!, item.dictValue))],
                      ) : Container()
                    );
                  }
                ),
                Expanded(
                  child:EasyRefresh(
                    // controller: _controller,
                    onRefresh: () async {
                      return await this.getDataList(this.tabList[this._controller.index][1], businessOperationRankData.dictValue);
                    },
                    onLoad: null,
                    emptyWidget: null,
                    child:SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.only(left: 7,right: 7, top: 5,bottom: 5),
                      child: Consumer<BusinessOperationRankData>(
                        builder: (context, model, child) {
                          return Column(
                            children: [
                              ...getRank(model.dataList)
                            ],
                          );
                        },
                      ),
                    ),
                  )
                )
              ],
            ),
          ),
        ),
    );
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '运营排行榜';
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      titleWidget: Container(
        child: this.showSearch == false ? Text(
          '运营排行榜',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ) : SearchWidget(
          radius: BorderRadius.circular(15),
          onChanged: (value) {
            this.search = value;
          },
          suffix: buildButton(),
        ),
      ),
      rightButtons: [
        if (this.showSearch || businessOperationRankData.dictValue != 'personnel')
          Container(),
        if (this.showSearch == false && businessOperationRankData.dictValue == 'personnel')
          TextButton(
            onPressed: () {
              this.showSearch = true;
              setState(() {});
            },
            child: Image.asset(
              'assets/images/customer/customer_tab_search.png',
              width: 22,
              height: 22,
            ),
            style: ButtonStyle(
              overlayColor:
              MaterialStateProperty.all<Color>(Colors.transparent),
              padding:
              MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
      ],
    );
    /*if (showSearch == true) {
      return CommonSearchBar(
        focusNode: this._focusNode,
        showLeading: true,
        showClear: false,
        hideCancel: true,
        autoFocus: true,
        controller: this._textController,
        radius: BorderRadius.circular(22),
        cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
        suffix: buildButton(),
        onSearch: (value) {
          String inputValue = value.trim();
          if (inputValue.isEmpty) {
            this._textController.text = inputValue;
            return;
          }
        },
      );
    } */
    /*return CommonSearchBar(
      focusNode: this._focusNode,
      showLeading: true,
      showClear: false,
      hideCancel: true,
      autoFocus: true,
      controller: this._textController,
      radius: BorderRadius.circular(22),
      cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
      suffix: buildButton(),
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._textController.text = inputValue;
          return;
        }
      },
    );*/
  }

  Widget buildButton() {
    return Container(
      margin: EdgeInsets.only(left: 5),
      child: TextButton(
          onPressed: () {
            String inputValue = this._textController.text.trim();
            this.getDataList(this.tabList[this._controller.index][1], businessOperationRankData.dictValue);
          },
          child: Container(
            width: 56,
            height: 28,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Color(0xFF00B955),
            ),
            child: Center(
              child: Text(
                '搜索',
                style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
              ),
            ),
          ),
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          )),
    );
  }
  Widget getTabBar() {
    return XTabBar(
      controller: this._controller,
      unselectedLabelColor: GlobalData.unSelectedColor,
      labelColor: GlobalData.selectedColor,
      indicatorSize: TabBarIndicatorSize.label,
      labelPadding: EdgeInsets.all(5),
      onTap: (index) {
        //model.updateDataPerformanceList('{ "data": [{"title": "啊哈哈哈哈", "tValue": "25,235,368", "tTipsList": [{"title": "啊哈哈哈哈", "tValue": "25,235,368"}]}], "code": 0, "msg": "success" }');
        //model.updateDataPerformanceList();
      },
      indicator: CustomUnderlineTabIndicator(
        borderSide: BorderSide(
            color: GlobalData.selectedColor,
            width: 1,
            style: BorderStyle.solid
        ),
      ),
      tabs: [
        ...this.tabList.asMap().keys.map((i) {
          return XTab(
            notHaveIconHeight: 30,
            child: Text(
              this.tabList[i][0],
              style: TextStyle(
                  fontSize: 14
              ),
            ),
          );
        })
      ],
    );
  }


  //排行榜
  List<Widget> getRank(List<BusinessHomeRankDataBean> list) {
    List<Widget> result = [];
    for (int index = 0; index < list.length; index++) {
      result.add(GestureDetector(
        onTap: () {
          if (businessOperationRankData.dictValue == 'personnel') {
            Navigator.of(context).push(MaterialPageRoute(
              builder: (context) => BusinessStoreRankPage(accountId: list[index].accountId!),
              fullscreenDialog: true
            ));
          }
        },
        child: Container(
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.only(bottom: index < list.length - 1 ? 10 : 0),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: Colors.white
          ),
          child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(7),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                          child: Consumer<BusinessOperationRankData>(
                            builder: (context, model, child) {
                              return Row(
                                children: [
                                  RankIcon(level: index, size: 14),
                                  SizedBox(width: 5),
                                  Transform.translate(
                                    offset: Offset(0, -2),
                                    child: Text(
                                      list[index].manager ?? '',
                                      style: TextStyle(
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          )
                      ),
                      Container(
                        child: Text(
                          list[index].regions ?? '',
                          style: TextStyle(
                              fontSize: 12
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Divider(height: 1, color: Color.fromRGBO(235, 235, 235, 1.0)),
                Container(
                    padding: EdgeInsets.all(7),
                    child: Consumer<BusinessOperationRankData>(
                      builder: (context, model, child) {
                        return PerformanceDetailWidget(
                          dataList:  list[index].metrics,
                        );
                      },
                    )
                )
              ]
          ),
        ),
      ));
    }
    return result;
  }

  //获取筛选枚举
}
class BusinessOperationRankPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return BusinessOperationRankState();

  }
}

