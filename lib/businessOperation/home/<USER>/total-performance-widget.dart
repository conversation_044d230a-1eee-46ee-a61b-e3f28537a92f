import 'dart:ui';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';

import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
class TotalPerformanceWidget extends StatelessWidget {
  final List<BusinessHomeCardBean>? dataList;
  TotalPerformanceWidget({this.dataList});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7),
        image: DecorationImage(
          image: AssetImage('assets/images/business/business-home-totalPerformance-bg.png'),
          fit: BoxFit.cover,
          alignment: Alignment.topCenter
        )
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '总业绩',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white
                ),
              ),
              SizedBox(width: 5),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pushNamed('/business_operation_rank_page');
                },
                child: Container(
                  height: 22,
                  padding: EdgeInsets.only(left: 6, right: 6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(11)),
                    color: Color.fromRGBO(0, 0, 1, 0.15),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '团队排行',
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 10
                        ),
                      ),
                      Icon(
                        Icons.navigate_next_rounded,
                        size: 12,
                        color: Colors.white,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          ...getDataWidget(dataList, 2)
        ],
      ),
    );
  }
  List<Widget> getDataWidget(List<BusinessHomeCardBean>? data, int colCount) {
    if (data == null) return [];
    List<Widget> tempList = [];
    List<Widget> result = [];
    int curIndex = 0;
    for (curIndex; curIndex < data.length; curIndex++) {
      tempList.add(
        Expanded(
          child: Container(
            alignment: Alignment.centerLeft,
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(data[curIndex].title ?? '--',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
                SizedBox(height: 5),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(data[curIndex].valueFormat ?? '--',
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w600
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
                SizedBox(height: 5),
                if (data[curIndex].rate != '' && data[curIndex].rate != null)
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Text(data[curIndex].rateStr ?? '',
                          style: TextStyle(
                              fontSize: 10,
                              color: Colors.white70,
                              fontWeight: FontWeight.w400
                          ),
                          textAlign: TextAlign.start,
                        ),
                        SizedBox(width: 5),
                        Text(data[curIndex].rate ?? '',
                          style: TextStyle(
                              fontSize: 10,
                              color: Colors.white
                          ),
                          textAlign: TextAlign.start,
                        ),
                        if (data[curIndex].isUp == 1)
                          Icon(
                            Icons.north_rounded,
                            color: Colors.white,
                            size: 10,
                          ),
                        if (data[curIndex].isUp == 2)
                          Icon(
                            Icons.south_rounded,
                            color: Colors.white,
                            size: 10,
                          )
                      ],
                    ),
                  )
              ],
            ),
          )
        )
      );
      if ((curIndex + 1) % colCount == 0) {
        result.add(Row(
          children: [...tempList],
        ));
        result.add(SizedBox(height: 16));
        tempList = [];
      }
    }
    result.add(Row(
      children: [...tempList],
    ));
    return result;
    /*return [
      *//*...data.map((item) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(item.title,
              style: TextStyle(
                  fontSize: 14,
                  color: Colors.white
              ),
            ),
            SizedBox(width: 5),
            Text(item.tValue,
              style: TextStyle(
                  fontSize: 14,
                  color: Colors.white
              ),
            ),
          ],
        );
      })*//*

    ];*/
  }
}