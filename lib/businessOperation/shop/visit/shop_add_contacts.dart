import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/picker/string_value_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_select_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class ShopAddContacts extends BasePage {
  final ShopListDataItemModel? shopInfo;
  ShopAddContacts({this.shopInfo});

  @override
  BaseState<StatefulWidget> initState() {
    return ShopAddContactsState();
  }
}

class ShopAddContactsState extends BaseState<ShopAddContacts> {
  String isKp = '1';
  String name = '';
  String phone = '';
  String role = '';
  String roleStr = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return super.build(context);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF5F5F5),
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(4)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.fromLTRB(10, 16, 10, 16),
                  decoration: BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(4)),
                  child: Row(
                    children: [
                      Text('商业',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF333333),
                          )),
                      Expanded(child: SizedBox()),
                      Text(widget.shopInfo?.companyName ?? '--',
                          style: TextStyle(
                            fontSize: 15,
                            color: Color(0xFF0222222),
                          )),
                    ],
                  ),
                ),
                ScheduleInputItem(
                  title: '联系人姓名',
                  placeHolder: '请输入',
                  itemKey: '000',
                  isCanEdit: true,
                  showBorder: false,
                  content: name,
                  valueChange: (itemKey, content) {
                    name = content;
                  },
                ),
                ScheduleInputItem(
                  title: '联系人电话',
                  placeHolder: '请输入',
                  itemKey: '001',
                  isCanEdit: true,
                  showBorder: false,
                  content: phone,
                  valueChange: (itemKey, content) {
                    phone = content;
                  },
                ),
                ScheduleSelectItem(
                  title: '联系人角色',
                  placeHolder: '请选择',
                  placeHolderColor: 0xFF222222,
                  itemKey: '002',
                  content: roleStr,
                  showBorder: false,
                  selectAction: (itemKey, controller) {
                    selectTypeAction(controller);
                  },
                ),
                Container(
                  padding: EdgeInsets.fromLTRB(10, 16, 10, 16),
                  decoration: BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(4)),
                  child: Row(
                    children: [
                      Text('是否KP',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF222222),
                          )),
                      Expanded(child: SizedBox()),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isKp = '1';
                              });
                            },
                            child: Row(
                              children: [
                                Image.asset(
                                  isKp == '1'
                                      ? 'assets/images/schedule/select_object_selected.png'
                                      : 'assets/images/schedule/select_object_normal.png',
                                  width: 20,
                                  height: 20,
                                ),
                                SizedBox(width: 4),
                                Text('是',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF888888),
                                    )),
                              ],
                            ),
                          ),
                          SizedBox(width: 32),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                isKp = '0';
                              });
                            },
                            child: Row(
                              children: [
                                Image.asset(
                                  isKp == '0'
                                      ? 'assets/images/schedule/select_object_selected.png'
                                      : 'assets/images/schedule/select_object_normal.png',
                                  width: 20,
                                  height: 20,
                                ),
                                SizedBox(width: 4),
                                Text('否',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF888888),
                                    )),
                              ],
                            ),
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SizedBox(),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
            child: TextButton(
                onPressed: () {
                  submitFn();
                },
                child: Container(
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21),
                    color: Color(0xFF00B955),
                  ),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Center(
                    child: Text(
                      '添加',
                      style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          )
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.custom,
      leftButton: TextButton(
        child: Image.asset(
          'assets/images/business/business-shop-visit-back.png',
          width: 24,
        ),
        onPressed: () {
          if (Navigator.canPop(context)) {
            Navigator.maybePop(context);
          } else {
            XYYContainer.close(context);
          }
        },
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }

  @override
  String getTitleName() {
    return '新建联系人';
  }

  /// 选择拜访方式
  void selectTypeAction(ValueNotifier<String?> controller) {
    List<Map<String, String>> typeValues = [
      {"老板/总经理": "bossOrGeneralManager"},
      {"运营负责人": "operateHead"},
      {"运营": "operate"},
      {"采购": "purchase"},
      {"客服": "customerService"},
      {"其他": "other"},
    ];
    showStringValuePickerView<Map<String, String>>(
      context: context,
      source: typeValues,
      defaultValue: '',
      middleTitle: "选择角色",
      sourceBuilder: (value) {
        return value.keys.first;
      },
      selectedAction: (value) {
        roleStr = value.keys.first;
        controller.value = value.keys.first;
        role = value.values.first;
        print('----bug 选择角色 $value');
      },
    );
  }

  Future<void> submitFn() async {
    var body = {
      "orgId": widget.shopInfo?.orgId,
      "orgName": widget.shopInfo?.companyName,
      "contactName": name,
      "contactPhone": phone,
      "contactTypeCode": role,
      "isKp": isKp,
      "merchantManagerId": widget.shopInfo?.id,
    };
    try {
      var result = await NetworkV2<ShopContactModel>(ShopContactModel())
          .requestDataV2('/mop/calling/contacts/add',
              parameters: body, method: RequestMethod.POST);
      if (result.isSuccess == true) {
        XYYContainer.toastChannel.toast('新增成功');
        Navigator.pop(context, 'submit');
      } else {
        XYYContainer.toastChannel.toast(result.errorMsg ?? '新增失败');
      }
      dismissLoadingDialog();
    } catch (e) {
      dismissLoadingDialog();
      XYYContainer.toastChannel.toast('新增失败');
    }
  }
}
