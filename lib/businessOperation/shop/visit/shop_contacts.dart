import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_add_contacts.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class ShopContacts extends BasePage {
  final ShopListDataItemModel? data;
  ShopContacts({this.data});

  @override
  BaseState<StatefulWidget> initState() {
    return ShopContactsState();
  }
}

class ShopContactsState extends BaseState<ShopContacts> {
  List<ShopContactModel> contacts = [];
  ShopContactModel? contact;
  @override
  void initState() {
    requestAllData();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return super.build(context);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF5F5F5),
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ...contacts
                      .map((e) => GestureDetector(
                            onTap: () {
                              setState(() {
                                contact = e;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 10),
                              padding: EdgeInsets.fromLTRB(10, 16, 10, 16),
                              decoration: BoxDecoration(
                                  color: Color(0xFFFFFFFF),
                                  borderRadius: BorderRadius.circular(4)),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('${e.contactName ?? '--'}',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Color(0xFF222222),
                                          )),
                                      SizedBox(height: 5),
                                      Text('${e.contactPhone ?? '--'}',
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFF888888),
                                          )),
                                    ],
                                  ),
                                  Expanded(child: SizedBox()),
                                  Image.asset(
                                    e.id == contact?.id
                                        ? 'assets/images/schedule/select_object_selected.png'
                                        : 'assets/images/schedule/select_object_normal.png',
                                    width: 20,
                                    height: 20,
                                  ),
                                ],
                              ),
                            ),
                          ))
                      .toList(),
                ],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
            child: TextButton(
                onPressed: () {
                  Navigator.pop(context, contact);
                },
                child: Container(
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21),
                    color: Color(0xFF00B955),
                  ),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Center(
                    child: Text(
                      '提交',
                      style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          )
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.custom,
      leftButton: TextButton(
        child: Image.asset(
          'assets/images/business/business-shop-visit-back.png',
          width: 24,
        ),
        onPressed: () {
          if (Navigator.canPop(context)) {
            Navigator.maybePop(context);
          } else {
            XYYContainer.close(context);
          }
        },
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
      rightButtons: [
        TextButton(
          child: Text(
            '新建联系人',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 15,
              fontWeight: FontWeight.normal,
            ),
          ),
          onPressed: () {
            Navigator.of(context)
                .push(MaterialPageRoute(
                  builder: (context) => ShopAddContacts(shopInfo:widget.data),
                  fullscreenDialog: true,
                ))
                .then((value) => {requestAllData()});
          },
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  @override
  String getTitleName() {
    return '选择联系人';
  }

  /// 请求客户详情数据
  Future<void> requestAllData() async {
    var result = await NetworkV2<ShopContactModel>(ShopContactModel())
        .requestDataV2('/mop/calling/contacts/list',
            parameters: {'merchantManagerId': widget.data?.id},
            method: RequestMethod.POST);
    if (mounted && result.isSuccess == true) {
      // detailDataNotifier.value = result.getData();
      setState(() {
        contacts = result.getListData() ?? [];
      });
    }
  }
}
