import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/common/shop_text_item.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_add_object_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_visit_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_add_object.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/picker/string_value_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_config_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_show_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_image_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_location_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_select_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_text_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ShopAccompanyVisit extends BasePage {
  final ShopListDataItemModel? shopInfo;
  ShopAccompanyVisit({this.shopInfo});

  @override
  BaseState<StatefulWidget> initState() {
    return ShopAccompanyVisitState();
  }
}

class ShopAccompanyVisitState extends BaseState<ShopAccompanyVisit> {
  List<ScheduleConfigSectionData> configList = [];

  /// 上传接口使用的模型
  SchedulePostDataModel postModel = SchedulePostDataModel();

  /// 页面展示数据
  ScheduleShowData showModel = ScheduleShowData();
  // 编辑id
  dynamic id;
  // 陪访对象
  ShopAddObjectModel? contacts;
  // 拜访目的
  String objective = '';
  String objectiveStr = '';
  // 拜访开始时间
  String callingStartTime = '';
  // 拜访结束时间
  String callingEndTime = '';
  // 拜访总结
  String visitDesc = '';
  // 拜访照片
  List<String> visitImages = [];
  // 拜访方式
  String? visitWay = 'homeVisit';
  bool isCreate = true;
  GlobalKey imgWidgetKey = GlobalKey();
  @override
  void initState() {
    initRequest();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initRequest() async {
    showLoadingDialog();
    try {
      var result = await NetworkV2<ShopVisitModel>(ShopVisitModel())
          .requestDataV2('/mop/calling/log/latest/follow/draft',
              parameters: {"merchantManagerId": widget.shopInfo?.id, 'callingLogId': widget.shopInfo?.callingLogId},
              method: RequestMethod.POST);
      var data = result.getData();
      if (data?.id != null) {
        id = data?.id ?? '';
        callingStartTime = data?.callingStartTimeStr ?? '';
        callingEndTime = data?.callingEndTimeStr ?? '';
        objective = data?.callingPurpose ?? '';
        objectiveStr = data?.callingPurposeStr ?? '';
        var ii = data?.callingPicture;
        visitImages = (ii != null && ii.isNotEmpty) ? ii.split(',') : [];
        visitDesc = data?.callingSummary ?? '';
        visitWay = data?.callingMethod ?? 'homeVisit';
        contacts = ShopAddObjectModel(
            accountId: data?.followVisitAccountId,
            accountName: data?.followVisitAccountName);
        imgWidgetKey = GlobalKey();
      } else {
        var result = await XYYContainer.requestChannel.request(
            '/mop/calling/log/merchant/default/follow',
            method: RequestMethod.POST,
            contentType: RequestContentType.FORM,
            parameters: {"merchantId": widget.shopInfo?.id});
        var data = json.decode(result);
        if (data['status'] == 'success' &&
            data['data']['followVisitAccountId'] != null) {
          contacts = ShopAddObjectModel(
              accountId: data['data']['followVisitAccountId'] ?? '',
              accountName: data['data']['followVisitAccountName'] ?? '');
        }
      }
      isCreate = false;
      dismissLoadingDialog();

      setState(() {});
    } catch (e) {
      dismissLoadingDialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    return super.build(context);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        color: Color(0xFFF5F5F5),
        width: double.infinity,
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              color: Color(0xFFEFEFF4),
              height: 0.5,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(10, 16, 10, 16),
                      decoration: BoxDecoration(
                          color: Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(4)),
                      child: Row(
                        children: [
                          Text('商业',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF333333),
                              )),
                          Expanded(child: SizedBox()),
                          Text(widget.shopInfo?.companyName ?? '--',
                              style: TextStyle(
                                fontSize: 15,
                                color: Color(0xFF0222222),
                              )),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(bottom: 10),
                      margin: EdgeInsets.only(bottom: 10, top: 10),
                      decoration: BoxDecoration(
                          color: Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(4)),
                      child: Column(
                        children: [
                          ScheduleSelectItem(
                            title: '陪访对象',
                            placeHolder: '请选择',
                            placeHolderColor: 0xFF999999,
                            itemKey: '002',
                            content: contacts?.accountName,
                            showBorder: false,
                            selectAction: (itemKey, controller) {
                              Navigator.of(context)
                                  .push(MaterialPageRoute(
                                builder: (context) =>
                                    ShopAddObject(data: widget.shopInfo),
                                fullscreenDialog: true,
                              ))
                                  .then((value) {
                                if (value != null) {
                                  setState(() {
                                    contacts = value;
                                  });
                                }
                              });
                            },
                          ),
                          ScheduleSelectItem(
                            title: '陪访目的',
                            placeHolder: '请选择',
                            placeHolderColor: 0xFF999999,
                            itemKey: '003',
                            content: objectiveStr,
                            showBorder: false,
                            selectAction: (itemKey, controller) {
                              selectPurposeAction(controller);
                            },
                          ),
                          Container(
                            decoration: BoxDecoration(
                                color: Color(0xFFFFFFFF),
                                borderRadius: BorderRadius.circular(4)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
                                  child: Text('陪访时间',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Color(0xFF333333),
                                      )),
                                ),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        margin: EdgeInsets.only(left: 10),
                                        padding:
                                            EdgeInsets.fromLTRB(8, 12, 8, 12),
                                        decoration: BoxDecoration(
                                          color: Color(0xFFF8F8F8),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          callingStartTime,
                                          style: TextStyle(
                                              color: Color(0xFF999999)),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      alignment: Alignment.center,
                                      height: 38,
                                      margin: EdgeInsets.fromLTRB(6, 0, 6, 0),
                                      child: Text('至'),
                                    ),
                                    Expanded(
                                      child: Container(
                                        padding:
                                            EdgeInsets.fromLTRB(8, 12, 8, 12),
                                        margin: EdgeInsets.only(right: 10),
                                        decoration: BoxDecoration(
                                          color: Color(0xFFF8F8F8),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          callingEndTime,
                                          style: TextStyle(
                                              color: Color(0xFF999999)),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                          color: Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(4)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ShopTextItem(
                            title: '陪访总结',
                            showTitleColor: false,
                            placehold: '1、陪访人机会点\n2、陪访人发展点\n3、陪访总结',
                            itemKey: '004',
                            content: visitDesc,
                            maxLenght: 500,
                            valueChange: (itemKey, text) {
                              visitDesc = text;
                            },
                          ),
                          SizedBox(height: 10),
                          ScheduleImageItem(
                            key:imgWidgetKey,
                            isPerfectVisit: false,
                            hasTalkTime: false,
                            address: this.postModel.merchantVisit,
                            currentImages: visitImages,
                            limitCount: 5,
                            itemKey: '005',
                            imageCahnge: (imageList, itemKey) {
                              if (callingStartTime.isEmpty) {
                                callingStartTime = getTime();
                              }
                              if ((imageList ?? []).length > visitImages.length) {
                                callingEndTime = getTime();
                              }
                              visitImages = imageList ?? [];
                              draftSubmit();
                              setState(() {});
                            },
                          ),
                        ],
                      ),
                    ),
                    ScheduleLocationItem(
                      locationChange: (value) {
                        if (value.isSuccess == true) {
                          this.postModel.merchantVisit.lng = value.longitude;
                          this.postModel.merchantVisit.lat = value.latitude;
                          this.postModel.merchantVisit.address = value.address;
                          setState(() {});
                        }
                      },
                    )
                  ],
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
              decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
              child: Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            height: 42,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(21),
                              border: Border.all(
                                  color: Color(0xFFCACACA), width: 1),
                            ),
                            padding: EdgeInsets.only(left: 10, right: 10),
                            child: Center(
                              child: Text(
                                '取消',
                                style: TextStyle(
                                    color: Color(0xFF222222), fontSize: 14),
                              ),
                            ),
                          ),
                          style: ButtonStyle(
                            overlayColor: MaterialStateProperty.all<Color>(
                                Colors.transparent),
                            padding: MaterialStateProperty.all<EdgeInsets>(
                                EdgeInsets.only(right: 10)),
                            minimumSize:
                                MaterialStateProperty.all<Size>(Size.zero),
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ))),
                  SizedBox(width: 10),
                  Expanded(
                      flex: 1,
                      child: TextButton(
                          onPressed: () {
                            submitVisit();
                          },
                          child: Container(
                            height: 42,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(21),
                              color: Color(0xFF00B955),
                            ),
                            padding: EdgeInsets.only(left: 10, right: 10),
                            child: Center(
                              child: Text(
                                '提交',
                                style: TextStyle(
                                    color: Color(0xFFFFFFFF), fontSize: 14),
                              ),
                            ),
                          ),
                          style: ButtonStyle(
                            overlayColor: MaterialStateProperty.all<Color>(
                                Colors.transparent),
                            padding: MaterialStateProperty.all<EdgeInsets>(
                                EdgeInsets.only(right: 10)),
                            minimumSize:
                                MaterialStateProperty.all<Size>(Size.zero),
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          ))),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.custom,
      leftButton: TextButton(
        child: Image.asset(
          'assets/images/business/business-shop-visit-back.png',
          width: 24,
        ),
        onPressed: () {
          if (Navigator.canPop(context)) {
            Navigator.maybePop(context);
          } else {
            XYYContainer.close(context);
          }
        },
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
      rightButtons: [
        TextButton(
          child: Text(
            '保存草稿',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 15,
              fontWeight: FontWeight.normal,
            ),
          ),
          onPressed: () {
            draftSubmit();
          },
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }

  @override
  String getTitleName() {
    return '添加陪访';
  }

  /// 选择对象
  void selectObjectAction(ValueNotifier<String?> controller) {
    var router = '/schedule_select_object?rolesJSON=${""}';
    router = Uri.encodeFull(router);
    Navigator.of(context).pushNamed(router).then((value) {
      if (value is Map<String, dynamic>?) {
        Map<String, dynamic>? result = value;
        if (result != null) {
          if (result.containsKey("isHeyeVisit")) {
            this.showModel.isHeyeVisit = result["isHeyeVisit"];
          } else {
            this.showModel.isHeyeVisit = false;
          }
          ScheduleExternalModel allinfoModel =
              ScheduleExternalModel.fromJson(result);
          controller.value = allinfoModel.customer?.name ?? "";
          this.showModel.poiId = allinfoModel.customer?.poiId;

        }
      }
    });
  }

  String getTime() {
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    return formattedDate;
  }

  /// 选择陪访目的
  void selectPurposeAction(ValueNotifier<String?> controller) {
    List<Map<String, String>> purposeValues = [
      {"尾部人员陪访赋能": "followTailManEmpower"},
      {"新人陪访赋能": "followNewcomerEmpower"},
      {"重点商业陪访": "followKeyBusiness"},
      {"新商家陪访": "followNewMerchant"},
      {"活动大促陪访": "followActivityPromo"},
      {"其他": "followOther"},
    ];

    showStringValuePickerView<Map<String, String>>(
      context: context,
      source: purposeValues,
      defaultValue: objective,
      middleTitle: "请选择陪访目的",
      sourceBuilder: (value) {
        return value.keys.first;
      },
      selectedAction: (value) {
        objective = value.values.first;
        objectiveStr = value.keys.first;
        controller.value = value.keys.first;
      },
    );
  }

  Future<void> submitVisit() async {
    if (postModel.merchantVisit.lat == null || postModel.merchantVisit.lng == null) {
      showToast("请定位成功后，再提交陪访");
      return;
    }
    if (objective == '') {
      XYYContainer.toastChannel.toast('请选择陪访目的');
      return;
    }
    if (contacts == null) {
      XYYContainer.toastChannel.toast('请选择陪访对象');
      return;
    }
    if (visitDesc == '') {
      XYYContainer.toastChannel.toast('请填写拜访总结');
      return;
    }
    if (visitWay == 'homeVisit' || visitWay == 'tripVisit') {
      if (visitImages.isEmpty) {
        XYYContainer.toastChannel.toast('请上传图片');
        return;
      }
    }
    showLoadingDialog();
    var body = {
      "id": id,
      "merchantManagerId": widget.shopInfo?.id,
      "callingStatus": "1",
      "callingStartTime": callingStartTime,
      "callingEndTime": callingEndTime,
      "callingPurposeCode": objective,
      "callingSummary": visitDesc,
      "callingPicture": visitImages.join(','),
      "followVisitAccountId": contacts?.accountId,
      "callingMethodCode":"homeVisit",
      "curLatitudeStr": postModel.merchantVisit.lat,
      "curLongitudeStr": postModel.merchantVisit.lng,
    };
    try {
      var result = await NetworkV2<ShopContactModel>(ShopContactModel())
          .requestDataV2('/mop/calling/log/company/add',
              parameters: body, method: RequestMethod.POST);
      if (result.isSuccess == true) {
        XYYContainer.toastChannel.toast('陪访成功');
        Navigator.pop(context,'success');
      } else {
        XYYContainer.toastChannel.toast(result.errorMsg ?? '添加陪访失败');
      }
      dismissLoadingDialog();
    } catch (e) {
      dismissLoadingDialog();
      XYYContainer.toastChannel.toast('添加陪访失败');
    }
  }

  Future<void> draftSubmit() async {
    showLoadingDialog();
    var body = {
      "id": id,
      "merchantManagerId": widget.shopInfo?.id,
      "callingStatus": "0",
      "callingStartTime": callingStartTime,
      "callingEndTime": callingEndTime,
      "callingPurposeCode": objective,
      "callingSummary": visitDesc,
      "callingPicture": visitImages.join(','),
      "followVisitAccountId": contacts?.accountId,
      "callingMethodCode":visitWay,
    };
    try {
      var result = await NetworkV2<ShopContactModel>(ShopContactModel())
          .requestDataV2('/mop/calling/log/company/add',
              parameters: body, method: RequestMethod.POST);
      if (result.isSuccess == true) {
        id = result.data?.id;
        XYYContainer.toastChannel.toast('保存成功');
      } else {
        XYYContainer.toastChannel.toast(result.errorMsg ?? '保存失败');
      }
      dismissLoadingDialog();
    } catch (e) {
      dismissLoadingDialog();
      XYYContainer.toastChannel.toast('保存失败');
    }
  }
}
