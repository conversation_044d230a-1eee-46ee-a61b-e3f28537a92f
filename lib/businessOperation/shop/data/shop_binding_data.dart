import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';


@JsonSerializable()
class ShopBinDingModel extends BaseModelV2<ShopBinDingModel> {
  dynamic id;
  dynamic staffNum;
  dynamic realname;
  dynamic displayName;

  ShopBinDingModel(
      {this.id, this.staffNum,this.realname, this.displayName});

  @override
  ShopBinDingModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopFilterModelToJson(this);
  }

  factory ShopBinDingModel.fromJson(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }
}


ShopBinDingModel _$ShopFilterModelFromJson(Map<String, dynamic> json) {
  return ShopBinDingModel(
    id: json['id'],
    staffNum: json['staffNum'],
    realname: json['realname'],
    displayName: json['displayName'],
  );
}

Map<String, dynamic> _$ShopFilterModelToJson(ShopBinDingModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'staffNum': instance.staffNum,
      'realname': instance.realname,
      'displayName': instance.displayName,
    };
