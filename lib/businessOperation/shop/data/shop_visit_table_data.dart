import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';


@JsonSerializable()
class ShopVisitTableModel extends BaseModelV2<ShopVisitTableModel> {

  dynamic callingStartTimeStr;
  dynamic callingEndTimeStr;
  dynamic visitOaUser;
  dynamic visitPurpose;
  dynamic visitTypeDesc;
  dynamic visitSummary;
  dynamic customerId;
  dynamic merchantId;

  ShopVisitTableModel();


  factory ShopVisitTableModel.fromJson(Map<String, dynamic> json) =>
      _$ShopVisitTableModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopVisitTableModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopVisitTableModelToJson(this);
  }
}


ShopVisitTableModel _$ShopVisitTableModelFromJson(
    Map<String, dynamic> json) {
  return ShopVisitTableModel()
    ..callingStartTimeStr = json['callingStartTimeStr']
    ..callingEndTimeStr = json['callingEndTimeStr']
    ..visitOaUser = json['visitOaUser']
    ..visitPurpose = json['visitPurpose']
    ..visitTypeDesc = json['visitTypeDesc']
    ..visitSummary = json['visitSummary']
    ..customerId = json['customerId']
    ..merchantId = json['merchantId'];
}

Map<String, dynamic> _$ShopVisitTableModelToJson(
        ShopVisitTableModel instance) =>
    <String, dynamic>{
      'callingStartTimeStr': instance.callingStartTimeStr,
      'callingEndTimeStr': instance.callingEndTimeStr,
      'visitOaUser': instance.visitOaUser,
      'visitPurpose': instance.visitPurpose,
      'visitTypeDesc': instance.visitTypeDesc,
      'visitSummary': instance.visitSummary,
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
    };

