import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';


@JsonSerializable()
class ShopAddObjectModel extends BaseModelV2<ShopAddObjectModel> {
  dynamic accountName;

  dynamic accountId;

  ShopAddObjectModel(
      {this.accountName, this.accountId});

  @override
  ShopAddObjectModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopFilterModelToJson(this);
  }

  factory ShopAddObjectModel.fromJson(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }
}


ShopAddObjectModel _$ShopFilterModelFromJson(Map<String, dynamic> json) {
  return ShopAddObjectModel(
    accountName: json['accountName'],
    accountId: json['accountId'],
  );
}

Map<String, dynamic> _$ShopFilterModelToJson(ShopAddObjectModel instance) =>
    <String, dynamic>{
      'accountName': instance.accountName,
      'accountId': instance.accountId,
    };
