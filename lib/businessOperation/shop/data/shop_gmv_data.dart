import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopGmvItem extends BaseModelV2<ShopGmvItem> {
  dynamic dateStr;
  dynamic payGmv;
  dynamic highGrossGmv;
  dynamic kaGMV;

  dynamic selectionGrossGmv; //甄选GMV

  ShopGmvItem();

  factory ShopGmvItem.fromJson(Map<String, dynamic> json) =>
      _$ShopGmvItemFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopGmvItemFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopGmvItemToJson(this);
  }
}

ShopGmvItem _$ShopGmvItemFromJson(Map<String, dynamic> json) {
  return ShopGmvItem()
    ..dateStr = json['dateStr']
    ..payGmv = json['payGmv']
    ..highGrossGmv = json['highGrossGmv']
    ..kaGMV = json['kaGMV']
    ..selectionGrossGmv = json['selectionGrossGmv'];
}

Map<String, dynamic> _$ShopGmvItemToJson(ShopGmvItem instance) =>
    <String, dynamic>{
      'dateStr': instance.dateStr,
      'payGmv': instance.payGmv,
      'highGrossGmv': instance.highGrossGmv,
      'kaGMV': instance.kaGMV,
      'selectionGrossGmv': instance.selectionGrossGmv,
    };
