import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopDetailDataShopCardData extends BaseModelV2<ShopDetailDataShopCardData> {
  dynamic metricsName;
  dynamic metricsVal;
  dynamic sort;

  // dynamic dateStr;
  // dynamic payGmv;
  // dynamic highGrossGmv;
  // dynamic kaGMV;


  ShopDetailDataShopCardData();

  factory ShopDetailDataShopCardData.fromJson(Map<String, dynamic> json) =>
      _$ShopDetailDataShopCardDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopDetailDataShopCardDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopDetailDataShopCardDataToJson(this);
  }
}

ShopDetailDataShopCardData _$ShopDetailDataShopCardDataFromJson(Map<String, dynamic> json) {
  return ShopDetailDataShopCardData()
    ..metricsName = json['metricsName']
    ..metricsVal = json['metricsVal']
    ..sort = json['sort'];
  //   ..dateStr = json['dateStr']
  // ..payGmv = json['metricsName']
  // ..highGrossGmv = json['metricsVal']
  // ..kaGMV = json['sort'];

}

Map<String, dynamic> _$ShopDetailDataShopCardDataToJson(ShopDetailDataShopCardData instance) =>
    <String, dynamic>{
      'metricsName': instance.metricsName,
      'metricsVal': instance.metricsVal,
      'sort': instance.sort,
      // 'dateStr': instance.dateStr,
      // 'payGmv': instance.payGmv,
      // 'highGrossGmv': instance.highGrossGmv,
      // 'highGrossGmv': instance.kaGMV,


    };
