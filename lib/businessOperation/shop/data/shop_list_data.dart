
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';


@JsonSerializable()
class ShopListDataModel
    extends BaseModelV2<ShopListDataModel> {
  @JsonKey(defaultValue: [])
  List<ShopListDataItemModel>? result;

  dynamic totalPage;
  dynamic lastPage;

  ShopListDataModel();

  @override
  ShopListDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopListDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopListDataModelToJson(this);
  }

  factory ShopListDataModel.fromJson(Map<String, dynamic> json) {
    return _$ShopListDataModelFromJson(json);
  }
}

@JsonSerializable()
class ShopListDataItemModel
    extends BaseModelV2<ShopListDataItemModel> {
  /// 主键
  dynamic id;
  /// 商户编码
  dynamic orgId;
  /// 店铺编码
  dynamic shopCode;
  /// 店铺状态汉字
  dynamic appShopStatusDisplay;
  /// 店铺状态
  dynamic shopStatus;
  /// 店铺状态
  dynamic shopStatusStr;
  /// 商户名称
  dynamic companyName;
  /// 店铺名称
  dynamic storeName;
  /// 地址
  dynamic addr;
  /// 企业省份id
  dynamic provId;
  /// 企业省份
  dynamic prov;
  /// 企业城市id
  dynamic cityId;
  /// 企业城市
  dynamic city;
  /// 企业区域id
  dynamic areaId;
  /// 企业区域
  dynamic area;
  /// 经度
  dynamic longitude;
  /// 纬度
  dynamic latitude;
  /// 店铺经营状态
  dynamic shopManageStatus;
  /// 店铺经营状态
  dynamic shopManageStatusStr;
  /// 店铺分类
  dynamic businessAttribute;
  /// 店铺分类
  dynamic businessAttributeStr;
  /// 店铺等级K级
  dynamic shopKLevel;
  /// 店铺等级S级
  dynamic shopSLevel;
  /// erp对接状态
  dynamic erpJointStatus;
  /// erp对接状态
  dynamic erpJointStatusStr;
  /// 合同
  dynamic fddEnterpriseAgreementStatus;
  /// 合同
  dynamic fddEnterpriseAgreementStatusStr;
  /// 保证金
  dynamic bondStatus;
  /// 保证金
  dynamic bondStatusStr;
  /// 主键
  dynamic latestCallingTime;
  /// 主键
  dynamic sinceLastVisitDays;
  /// 招商id
  dynamic bindZsAccountId;
  /// 运营id
  dynamic bindYyAccountId;
  /// 招商
  dynamic bindZsAccount;
  /// 运营
  dynamic bindYyAccount;
  /// 拜访陪访按钮 1 拜访 2 陪访
  dynamic visitButton;
  /// 认领按钮 0不展示 1认领
  dynamic claimButton;
  /// 编辑招商按钮 0不可编辑 1可编辑
  dynamic updateZsButton;
  /// 编辑运营按钮 0不可编辑 1可编辑
  dynamic updateYyButton;
  /// 编辑运营按钮 0不可编辑 1可编辑
  dynamic distance;
  /// 查询草稿要用
  dynamic callingLogId;
  /// gmv
  List<ShopListGmvModel>? indexCardList;


  ShopListDataItemModel({this.id,this.orgId,this.companyName, this.callingLogId});

  @override
  ShopListDataItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopListDataItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopListDataItemModellToJson(this);
  }

  factory ShopListDataItemModel.fromJson(Map<String, dynamic> json) {
    return _$ShopListDataItemModelFromJson(json);
  }
}

ShopListDataModel _$ShopListDataModelFromJson(
    Map<String, dynamic> json) {
  return ShopListDataModel()
    ..result = (json['result'] as List<dynamic>?)
            ?.map((e) =>
                ShopListDataItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        []
    ..totalPage = json['totalPage']
    ..lastPage = json['lastPage'];
}

Map<String, dynamic> _$ShopListDataModelToJson(
        ShopListDataModel instance) =>
    <String, dynamic>{
      'result': instance.result,
      'totalPage': instance.totalPage,
      'lastPage': instance.lastPage,
    };

ShopListDataItemModel _$ShopListDataItemModelFromJson(
    Map<String, dynamic> json) {
  return ShopListDataItemModel()
    ..id = json['id']
    ..orgId = json['orgId']
    ..shopCode = json['shopCode'] ?? 0
    ..appShopStatusDisplay = json['appShopStatusDisplay']
    ..shopStatus = json['shopStatus']
    ..shopStatusStr = json['shopStatusStr']
    ..companyName = json['companyName']
    ..storeName = json['storeName']
    ..addr = json['addr']
    ..provId = json['provId']
    ..prov = json['prov']
    ..cityId = json['cityId']
    ..city = json['city']
    ..areaId = json['areaId']
    ..area = json['area']
    ..longitude = json['longitude']
    ..latitude = json['latitude']
    ..shopManageStatus = json['shopManageStatus']
    ..shopManageStatusStr = json['shopManageStatusStr']
    ..businessAttribute = json['businessAttribute']
    ..businessAttributeStr = json['businessAttributeStr'] ?? '-'
    ..shopKLevel = json['shopKLevel']
    ..shopSLevel = json['shopSLevel']
    ..erpJointStatus = json['erpJointStatus']
    ..erpJointStatusStr = json['erpJointStatusStr'] ?? ''
    ..fddEnterpriseAgreementStatus = json['fddEnterpriseAgreementStatus'] ?? ''
    ..fddEnterpriseAgreementStatusStr = json['fddEnterpriseAgreementStatusStr'] ?? ''
    ..bondStatus = json['bondStatus'] ?? ''
    ..bondStatusStr = json['bondStatusStr'] ?? ''
    ..latestCallingTime = json['latestCallingTime'] ?? ''
    ..sinceLastVisitDays = json['sinceLastVisitDays']
    ..bindZsAccountId = json['bindZsAccountId'] ?? ''
    ..bindYyAccountId = json['bindYyAccountId']
    ..bindZsAccount = json['bindZsAccount'] ?? ''
    ..bindYyAccount = json['bindYyAccount'] ?? ''
    ..visitButton = json['visitButton'] ?? ''
    ..claimButton = json['claimButton'] ?? ''
    ..updateZsButton = json['updateZsButton'] ?? ''
    ..updateYyButton = json['updateYyButton'] ?? ''
    ..distance = json['distance'] ?? ''
    ..callingLogId = json['callingLogId'] ?? ''
    ..indexCardList = (json['indexCardList'] as List<dynamic>?)
        ?.map(
            (e) => ShopListGmvModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ShopListDataItemModellToJson(
        ShopListDataItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orgId': instance.orgId,
      'shopCode': instance.shopCode,
      'appShopStatusDisplay': instance.appShopStatusDisplay,
      'shopStatus': instance.shopStatus,
      'shopStatusStr': instance.shopStatusStr,
      'companyName': instance.companyName,
      'storeName': instance.storeName,
      'addr': instance.addr,
      'provId': instance.provId,
      'prov': instance.prov,
      'cityId': instance.cityId,
      'city': instance.city,
      'areaId': instance.areaId,
      'area': instance.area,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'shopManageStatus': instance.shopManageStatus,
      'shopManageStatusStr': instance.shopManageStatusStr,
      'businessAttribute': instance.businessAttribute,
      'businessAttributeStr': instance.businessAttributeStr,
      'shopKLevel': instance.shopKLevel,
      'shopSLevel': instance.shopSLevel,
      'erpJointStatus': instance.erpJointStatus,
      'erpJointStatusStr': instance.erpJointStatusStr,
      'fddEnterpriseAgreementStatus': instance.fddEnterpriseAgreementStatus,
      'fddEnterpriseAgreementStatusStr': instance.fddEnterpriseAgreementStatusStr,
      'bondStatus': instance.bondStatus,
      'bondStatusStr': instance.bondStatusStr,
      'latestCallingTime': instance.latestCallingTime,
      'sinceLastVisitDays': instance.sinceLastVisitDays,
      'bindZsAccountId': instance.bindZsAccountId,
      'bindYyAccountId': instance.bindYyAccountId,
      'bindZsAccount': instance.bindZsAccount,
      'bindYyAccount': instance.bindYyAccount,
      'visitButton': instance.visitButton,
      'claimButton': instance.claimButton,
      'updateZsButton': instance.updateZsButton,
      'updateYyButton': instance.updateYyButton,
      'distance': instance.distance,
      'indexCardList': instance.indexCardList,
      'callingLogId': instance.callingLogId,
    };


@JsonSerializable()
class ShopListGmvModel extends BaseModelV2 {
  dynamic title;
  dynamic value;
  dynamic rate;
  dynamic rateStr;
  dynamic type;
  dynamic color;
  dynamic isUp;

  ShopListGmvModel({this.title,this.value,this.rate,this.rateStr,this.color,this.isUp});

  factory ShopListGmvModel.fromJson(Map<String, dynamic> json) =>
      _$ShopListGmvModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopListGmvModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopListGmvModelToJson(this);
  }
}

ShopListGmvModel _$ShopListGmvModelFromJson(Map<String, dynamic> json) {
  return ShopListGmvModel()
    ..title = json['title']
    ..value = json['value']
    ..rate = json['rate']
    ..rateStr = json['rateStr']
    ..type = json['type']
    ..color = json['color']
    ..isUp = json['isUp'];
}

Map<String, dynamic> _$ShopListGmvModelToJson(ShopListGmvModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'value': instance.value,
      'rate': instance.rate,
      'rateStr': instance.rateStr,
      'type': instance.type,
      'color': instance.color,
      'isUp': instance.isUp,
    };

