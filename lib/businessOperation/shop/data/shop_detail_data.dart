import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopDetailModel extends BaseModelV2 {
  dynamic id;

  /// 店铺名称
  dynamic storeName;

  /// 经营状态
  dynamic shopManageStatus;

  /// 经营状态
  dynamic shopManageStatusStr;

  /// 商户名称
  dynamic companyName;

  /// 店铺注册地
  dynamic regAddr;

  /// 商户编码
  dynamic orgId;

  /// 店铺分类
  dynamic businessAttribute;

  /// 店铺分类
  dynamic businessAttributeStr;

  /// 店铺等级 K级
  dynamic shopKLevel;

  /// 店铺等级S级
  dynamic shopSLevel;

  /// 保证金
  dynamic bondMoney;
  dynamic bondMoneyStr;

  /// 保证金余额比例
  dynamic bondBalanceRatio;

  /// 协议(合同)状态
  dynamic fddEnterpriseAgreementStatus;

  /// 协议(合同)状态
  dynamic fddEnterpriseAgreementStatusStr;

  /// 资质状态
  dynamic qualificationStatus;

  /// 资质状态
  dynamic qualificationStatusStr;

  /// 店铺成功入驻时间
  dynamic shopSuccessEnterTime;

  /// 店铺有效入驻时间
  dynamic shopEffectEnterTime;

  /// 地址
  dynamic addr;

  /// 企业省份id
  dynamic provId;

  /// 企业省份
  dynamic prov;

  /// 企业城市id
  dynamic cityId;

  /// 企业城市
  dynamic city;

  /// 企业区域id
  dynamic areaId;

  /// 企业区域
  dynamic area;

  /// 经度
  dynamic longitude;

  /// 纬度
  dynamic latitude;

  /// logo
  dynamic logoUrl;

  /// 联系人
  List<ShopContactModel>? contactsList;

  /// --
  List<dynamic>? callingLogList;

  /// 店铺状态
  String? appShopStatusDisplay;

  ShopDetailModel();

  factory ShopDetailModel.fromJson(Map<String, dynamic> json) =>
      _$ShopDetailModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopDetailModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopDetailModelToJson(this);
  }
}

ShopDetailModel _$ShopDetailModelFromJson(Map<String, dynamic> json) {
  return ShopDetailModel()
    ..id = json['id']
    ..storeName = json['storeName']
    ..shopManageStatus = json['shopManageStatus']
    ..shopManageStatusStr = json['shopManageStatusStr']
    ..companyName = json['companyName']
    ..regAddr = json['regAddr']
    ..orgId = json['orgId']
    ..businessAttribute = json['businessAttribute']
    ..businessAttributeStr = json['businessAttributeStr']
    ..shopKLevel = json['shopKLevel']
    ..shopSLevel = json['shopSLevel']
    ..bondMoney = json['bondMoney']
    ..bondMoneyStr = json['bondMoneyStr']
    ..bondBalanceRatio = json['bondBalanceRatio']
    ..fddEnterpriseAgreementStatus = json['fddEnterpriseAgreementStatus']
    ..fddEnterpriseAgreementStatusStr = json['fddEnterpriseAgreementStatusStr']
    ..qualificationStatus = json['qualificationStatus']
    ..qualificationStatusStr = json['qualificationStatusStr']
    ..shopSuccessEnterTime = json['shopSuccessEnterTime']
    ..shopEffectEnterTime = json['shopEffectEnterTime']
    ..addr = json['addr']
    ..provId = json['provId']
    ..prov = json['prov']
    ..cityId = json['cityId']
    ..city = json['city']
    ..areaId = json['areaId']
    ..area = json['area']
    ..longitude = json['longitude']
    ..latitude = json['latitude']
    ..logoUrl = json['logoUrl']
    ..appShopStatusDisplay = json['appShopStatusDisplay']
    ..contactsList = (json['contactsList'] as List<dynamic>?)
        ?.map(
            (e) => ShopContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..callingLogList = json['callingLogList'];
}

Map<String, dynamic> _$ShopDetailModelToJson(ShopDetailModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'storeName': instance.storeName,
      'shopManageStatus': instance.shopManageStatus,
      'shopManageStatusStr': instance.shopManageStatusStr,
      'companyName': instance.companyName,
      'regAddr': instance.regAddr,
      'orgId': instance.orgId,
      'businessAttribute': instance.businessAttribute,
      'businessAttributeStr': instance.businessAttributeStr,
      'shopKLevel': instance.shopKLevel,
      'shopSLevel': instance.shopSLevel,
      'bondMoney': instance.bondMoney,
      'bondMoneyStr': instance.bondMoneyStr,
      'bondBalanceRatio': instance.bondBalanceRatio,
      'fddEnterpriseAgreementStatus': instance.fddEnterpriseAgreementStatus,
      'fddEnterpriseAgreementStatusStr':
          instance.fddEnterpriseAgreementStatusStr,
      'qualificationStatus': instance.qualificationStatus,
      'qualificationStatusStr': instance.qualificationStatusStr,
      'shopSuccessEnterTime': instance.shopSuccessEnterTime,
      'shopEffectEnterTime': instance.shopEffectEnterTime,
      'addr': instance.addr,
      'provId': instance.provId,
      'prov': instance.prov,
      'cityId': instance.cityId,
      'city': instance.city,
      'areaId': instance.areaId,
      'area': instance.area,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'logoUrl': instance.logoUrl,
      'appShopStatusDisplay': instance.appShopStatusDisplay,
      'contactsList': instance.contactsList,
      'callingLogList': instance.callingLogList,
    };

@JsonSerializable()
class ShopContactModel extends BaseModelV2 {
  dynamic id;
  dynamic orgId;
  dynamic orgName;
  dynamic contactName;
  dynamic contactPhone;
  dynamic contactType;
  dynamic contactTypeStr;
  dynamic isKp;
  dynamic createTime;
  dynamic createBy;
  dynamic updateTime;
  dynamic updateBy;
  dynamic dataSource;
  dynamic remark;
  dynamic yn;
  dynamic merchantManagerId;

  ShopContactModel({this.contactPhone,this.contactName});

  factory ShopContactModel.fromJson(Map<String, dynamic> json) =>
      _$ShopContactModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopContactModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopContactModelToJson(this);
  }
}

ShopContactModel _$ShopContactModelFromJson(Map<String, dynamic> json) {
  return ShopContactModel()
    ..id = json['id']
    ..orgId = json['orgId']
    ..orgName = json['orgName']
    ..contactName = json['contactName']
    ..contactPhone = json['contactPhone']
    ..contactType = json['contactType']
    ..contactTypeStr = json['contactTypeStr']
    ..isKp = json['isKp']
    ..createTime = json['createTime']
    ..createBy = json['createBy']
    ..updateTime = json['updateTime']
    ..updateBy = json['updateBy']
    ..dataSource = json['dataSource']
    ..remark = json['remark']
    ..yn = json['yn']
    ..merchantManagerId = json['merchantManagerId'];
}

Map<String, dynamic> _$ShopContactModelToJson(ShopContactModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orgId': instance.orgId,
      'orgName': instance.orgName,
      'contactName': instance.contactName,
      'contactPhone': instance.contactPhone,
      'contactType': instance.contactType,
      'contactTypeStr': instance.contactTypeStr,
      'isKp': instance.isKp,
      'createTime': instance.createTime,
      'createBy': instance.createBy,
      'updateTime': instance.updateTime,
      'updateBy': instance.updateBy,
      'dataSource': instance.dataSource,
      'remark': instance.remark,
      'yn': instance.yn,
      'merchantManagerId': instance.merchantManagerId,
    };
