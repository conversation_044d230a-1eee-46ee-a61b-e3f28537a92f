import 'package:json_annotation/json_annotation.dart';

@JsonSerializable(createFactory: false)
class ShopListParamData {
  //时间
  String? timePeriod;

  // 关键词 - 搜索页面使用
  String? storeNameOrgId;

  // 认领人员
  String? claimType;

  // 店铺状态
  String? appShopStatusOption;

  // 注册时间
  String? regTimeOption;

  // 店铺类型
  List<String>? businessAttributeList;

  // 合同签署
  List<String>? fddEnterpriseAgreementStatusList;

  // 保证金
  List<String>? bondStatusList;

  // erp对接
  List<String>? erpJointStatusList;

  // 资质
  List<String>? qualificationStatusList;

  /// 筛选页面参数
  @Json<PERSON>ey(ignore: true)
  Map<String, String>? filterParams = {};

  /// 筛选页面缓存
  @JsonKey(ignore: true)
  Map<String, dynamic>? cacheParams = {};

  String? curLatitudeStr;
  String? curLongitudeStr;

  ShopListParamData({ this.timePeriod });

  Map<String, dynamic> toJson() {
    var map = _$ShopListParamDataToJson(this);
    if (this.filterParams != null) {
      map.addAll(this.filterParams!);
    }
    map.removeWhere((key, value) =>
        value == "-1" || value == -1 || value == null || value == "");
    return map;
  }

  Map<String, dynamic> _$ShopListParamDataToJson(
        ShopListParamData instance) =>
    <String, dynamic>{
      'storeNameOrgId': instance.storeNameOrgId,
      'claimType': instance.claimType,
      'appShopStatusOption': instance.appShopStatusOption,
      'regTimeOption': instance.regTimeOption,
      'businessAttributeList': instance.businessAttributeList,
      'fddEnterpriseAgreementStatusList': instance.fddEnterpriseAgreementStatusList,
      'bondStatusList': instance.bondStatusList,
      'erpJointStatusList': instance.erpJointStatusList,
      'qualificationStatusList': instance.qualificationStatusList,
      'curLatitudeStr': instance.curLatitudeStr,
      'curLongitudeStr': instance.curLongitudeStr,
      'timePeriod': instance.timePeriod
    };
}
