import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopFilterListModel extends BaseModelV2<ShopFilterListModel> {
  // 认领人员
  List<ShopFilterModel>? mopClaimType;
  // 店铺状态
  List<ShopFilterModel>? shopStatusOption;
  // 注册时间
  List<ShopFilterModel>? mopRegTimeOption;
  // 店铺分类
  List<ShopFilterModel>? shopBusinessAttribute;
  // 协议(合同)状态
  List<ShopFilterModel>? mopEnterpriseAgreementSignStatus;
  // 保证金状态
  List<ShopFilterModel>? mopBondStatus;
  // erp对接状态
  List<ShopFilterModel>? mopErpJointStatus;

  @override
  ShopFilterListModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopFilterListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopFilterListModelToJson(this);
  }
}

@JsonSerializable()
class ShopFilterModel extends BaseModelV2<ShopFilterModel> {
  dynamic dictSort;

  dynamic dictLabel;

  dynamic dictValue;

  dynamic dictType;

  ShopFilterModel(
      {this.dictSort, this.dictLabel, this.dictValue, this.dictType});

  @override
  ShopFilterModel fromJsonMap(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopFilterModelToJson(this);
  }

  factory ShopFilterModel.fromJson(Map<String, dynamic> json) {
    return _$ShopFilterModelFromJson(json);
  }
}

ShopFilterListModel _$ShopFilterListModelFromJson(Map<String, dynamic> json) {
  return ShopFilterListModel()
    ..mopClaimType = (json['mop_claim_type'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..shopStatusOption = (json['mop_app_shop_status_option'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..mopRegTimeOption = (json['mop_reg_time_option'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..shopBusinessAttribute = (json['mop_shop_business_attribute'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..mopEnterpriseAgreementSignStatus =
        (json['mop_enterprise_agreement_sign_status'] as List<dynamic>?)
            ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
            .toList()
    ..mopBondStatus = (json['mop_bond_status'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..mopErpJointStatus = (json['mop_erp_joint_status'] as List<dynamic>?)
        ?.map((e) => ShopFilterModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ShopFilterListModelToJson(
        ShopFilterListModel instance) =>
    <String, dynamic>{
      'mopClaimType': instance.mopClaimType,
      'shopStatusOption': instance.shopStatusOption,
      'mopRegTimeOption': instance.mopRegTimeOption,
      'shopBusinessAttribute': instance.shopBusinessAttribute,
      'mopEnterpriseAgreementSignStatus': instance.mopEnterpriseAgreementSignStatus,
      'mopBondStatus': instance.mopBondStatus,
      'mopErpJointStatus': instance.mopErpJointStatus,
    };

ShopFilterModel _$ShopFilterModelFromJson(Map<String, dynamic> json) {
  return ShopFilterModel(
    dictSort: json['dictSort'],
    dictLabel: json['dictLabel'],
    dictValue: json['dictValue'],
    dictType: json['dictType'],
  );
}

Map<String, dynamic> _$ShopFilterModelToJson(ShopFilterModel instance) =>
    <String, dynamic>{
      'dictSort': instance.dictSort,
      'dictLabel': instance.dictLabel,
      'dictValue': instance.dictValue,
      'dictType': instance.dictType,
    };
