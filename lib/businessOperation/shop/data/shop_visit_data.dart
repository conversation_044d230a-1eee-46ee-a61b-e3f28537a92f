import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopVisitModel extends BaseModelV2 {
  dynamic id;
  dynamic callingStartTimeStr;
  dynamic callingEndTimeStr;
  dynamic callingPurpose;
  dynamic callingPurposeStr;
  dynamic callingSummary;
  String? callingPicture;
  dynamic followVisitAccountId;
  dynamic followVisitAccountName;
  dynamic contactName;
  dynamic contactPhone;
  dynamic callingMethod;
  dynamic callingMethodStr;

  ShopVisitModel();

  factory ShopVisitModel.fromJson(Map<String, dynamic> json) =>
      _$ShopVisitModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopVisitModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopVisitModelToJson(this);
  }
}

ShopVisitModel _$ShopVisitModelFromJson(Map<String, dynamic> json) {
  return ShopVisitModel()
    ..id = json['id']
    ..callingStartTimeStr = json['callingStartTimeStr']
    ..callingEndTimeStr = json['callingEndTimeStr']
    ..callingPurpose = json['callingPurpose']
    ..callingPurposeStr = json['callingPurposeStr']
    ..callingSummary = json['callingSummary']
    ..callingPicture = json['callingPicture']
    ..followVisitAccountId = json['followVisitAccountId']
    ..contactName = json['contactName']
    ..contactPhone = json['contactPhone']
    ..callingMethod = json['callingMethod']
    ..callingMethodStr = json['callingMethodStr']
    ..followVisitAccountName = json['followVisitAccountName'];
}

Map<String, dynamic> _$ShopVisitModelToJson(ShopVisitModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'callingStartTimeStr': instance.callingStartTimeStr,
      'callingEndTimeStr': instance.callingEndTimeStr,
      'callingPurpose': instance.callingPurpose,
      'callingPurposeStr': instance.callingPurposeStr,
      'callingSummary': instance.callingSummary,
      'callingPicture': instance.callingPicture,
      'followVisitAccountId': instance.followVisitAccountId,
      'contactName': instance.contactName,
      'contactPhone': instance.contactPhone,
      'callingMethod': instance.callingMethod,
      'callingMethodStr': instance.callingMethodStr,
      'followVisitAccountName': instance.followVisitAccountName,
    };