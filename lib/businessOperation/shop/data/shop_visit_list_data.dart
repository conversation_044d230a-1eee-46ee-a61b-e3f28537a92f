import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class ShopVisitListData extends BaseModelV2<ShopVisitListData> {
  bool? lastPage;
  dynamic totalCount;
  List<ShopVisitItemModel?>? result;

  ShopVisitListData();

  factory ShopVisitListData.fromJson(Map<String, dynamic>? json) =>
      _$ShopVisitListDataFromJson(json!);

  @override
  ShopVisitListData fromJsonMap(Map<String, dynamic> json) {
    return ShopVisitListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopVisitListDataToJson(this);
  }
}

ShopVisitListData _$ShopVisitListDataFromJson(Map<String, dynamic> json) {
  return ShopVisitListData()
    ..lastPage = json['lastPage'] as bool?
    ..totalCount = json['totalCount']
    ..result = (json['result'] as List<dynamic>?)
        ?.map((e) => e == null
            ? null
            : ShopVisitItemModel.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$ShopVisitListDataToJson(ShopVisitListData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'totalCount': instance.totalCount,
      'result': instance.result,
    };

@JsonSerializable()
class ShopVisitItemModel extends BaseModelV2<ShopVisitItemModel> {
  dynamic id;
  dynamic merchantManagerId;
  dynamic callingStartTimeStr;
  dynamic callingEndTimeStr;
  dynamic accountName;
  dynamic accountId;
  dynamic callingMethod;
  dynamic callingMethodStr;
  // 1
  dynamic isValid;

  ShopVisitItemModel();

  factory ShopVisitItemModel.fromJson(Map<String, dynamic>? json) =>
      _$ShopVisitItemModelFromJson(json!);

  @override
  ShopVisitItemModel fromJsonMap(Map<String, dynamic> json) {
    return ShopVisitItemModel.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopVisitItemModelToJson(this);
  }
}

ShopVisitItemModel _$ShopVisitItemModelFromJson(Map<String, dynamic> json) {
  return ShopVisitItemModel()
    ..id = json['id']
    ..merchantManagerId = json['merchantManagerId']
    ..callingStartTimeStr = json['callingStartTimeStr']
    ..callingEndTimeStr = json['callingEndTimeStr']
    ..accountName = json['accountName']
    ..accountId = json['accountId']
    ..callingMethod = json['callingMethod']
    ..callingMethodStr = json['callingMethodStr']
    ..isValid = json['isValid'];
}

Map<String, dynamic> _$ShopVisitItemModelToJson(ShopVisitItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'merchantManagerId': instance.merchantManagerId,
      'callingStartTimeStr': instance.callingStartTimeStr,
      'callingEndTimeStr': instance.callingEndTimeStr,
      'accountName': instance.accountName,
      'accountId': instance.accountId,
      'callingMethod': instance.callingMethod,
      'callingMethodStr': instance.callingMethodStr,
      'isValid': instance.isValid,
    };
