import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

@JsonSerializable()
class ShopDetailDataShopTableData extends BaseModelV2<ShopDetailDataShopTableData> {
  List<String>? headerList;
  List<Map<String, dynamic>>? dataList;

  ShopDetailDataShopTableData({this.headerList, this.dataList});

  factory ShopDetailDataShopTableData.fromJson(Map<String, dynamic> json) =>
      _$ShopDetailDataShopTableDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ShopDetailDataShopTableDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShopDetailDataShopTableDataToJson(this);
  }
}

class JsonSerializable {
  const JsonSerializable();
}

// 生成的 JSON 序列化方法
ShopDetailDataShopTableData _$ShopDetailDataShopTableDataFromJson(Map<String, dynamic> json) {
  return ShopDetailDataShopTableData(
    headerList: (json['headerList'] as List?)?.map((e) => e.toString()).toList(),
    dataList: (json['dataList'] as List?)?.cast<Map<String, dynamic>>(),
  );
}

Map<String, dynamic> _$ShopDetailDataShopTableDataToJson(ShopDetailDataShopTableData instance) =>
    <String, dynamic>{
      'headerList': instance.headerList,
      'dataList': instance.dataList,
    };