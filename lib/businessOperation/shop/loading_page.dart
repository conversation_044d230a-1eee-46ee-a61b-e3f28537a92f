import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:flutter/material.dart';

class LoadingPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return LoadingPageState();
  }
}

class LoadingPageState extends BaseState<LoadingPage>
    with SingleTickerProviderStateMixin {
  late CustomTabController _controller;

  @override
  void initState() {
    this._controller = CustomTabController(
      length: 0,
      vsync: this,
      animationDuration: Duration.zero,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      bottomNavigationBar: SafeArea(
        child: Container(
          color: Color(0xFFFFFFFF),
          child: <PERSON><PERSON><PERSON><PERSON>(
            height: 49,
            child: CustomTabBar(
              controller: this._controller,
              isScrollable: false,
              indicatorColor: Colors.transparent,
              indicatorWeight: 0.1,
              // 指示器高度不能设置为0
              indicatorSize: CustomTabBarIndicatorSize.label,
              unselectedLabelColor: Color(0xFF9494A6),
              unselectedLabelStyle: TextStyle(fontSize: 10),
              labelColor: Color(0xFF333333),
              labelStyle: TextStyle(fontSize: 10),
              tabs: [],
              disableClickTabIndex: [],
              onTap: (index) {},
            ),
          ),
        ),
      ),
      body: SizedBox(),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}
