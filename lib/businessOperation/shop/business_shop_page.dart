import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_claim_list.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_search_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_unclaim_list.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class BusinessShopPage extends BasePage {
  BusinessShopPage();

  @override
  BaseState<StatefulWidget> initState() {
    return BusinessShopPageState();
  }
}

class BusinessShopPageState extends BaseState<BusinessShopPage>
    with SingleTickerProviderStateMixin, EventBusObserver {
  // Tab标题数组
  List<String> titles = ["认领店铺", "未合作店铺"];

  // Tab控制器
  late TabController _controller;
UserInfoData? userInfo;
  // 状态保持
  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.initalTabController();
    initInfoFn();
    super.initState();
  }

  initInfoFn() async {
    userInfo = await UserInfoUtil.getUserInfo();
  }

  // 初始化Tab 控制器
  void initalTabController() {
    this._controller = TabController(
      length: this.titles.length,
      initialIndex: 0,
      vsync: this,
    );
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: TabBar(
              controller: this._controller,
              isScrollable: false,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: Color(0xFF00B377),
              indicatorWeight: 3,
              unselectedLabelColor: Color(0xFF666666),
              unselectedLabelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
              labelColor: Color(0xFF333333),
              labelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
              tabs: tabs(),
            ),
          ),
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 1),
          Expanded(
            child: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: this._controller,
              children: [
                ShopClaimListPage(),
                ShopUnClaimListPage(),
              ],
            ),
          )
        ],
      ),
    );
  }

  List<Widget> tabs() {
    return titles
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      leftType: LeftButtonType.none,
      leadingWidth: 100,
      titleWidget: Container(
        child: Text(
          '店铺',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      rightButtons: getRightButtons(),
    );
  }

  List<Widget> getRightButtons() {
    return [
      Container(
        padding: EdgeInsets.only(right: 10),
        width: 100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: searchAction,
              child: Image.asset(
                'assets/images/customer/customer_tab_search.png',
                width: 22,
                height: 22,
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding:
                    MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            TextButton(
              onPressed: addAction,
              child: Image.asset(
                'assets/images/business/business-shop-add.png',
                width: 22,
                height: 22,
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding:
                    MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
      ),
    ];
  }

  void filterAction() {}

  void searchAction() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ShopSearchPage(typeIndex:_controller.index),
        fullscreenDialog: true,
      ),
    );
  }

  void addAction() async {
    var result = await XYYContainer.bridgeCall('app_host');
    String pdcInterface = result['h5_host'] ?? "";
    String pdcUrl = pdcInterface + "/newPoi/shopAdd?source=5&jobNumber=${userInfo?.jobNumber}";
    pdcUrl = Uri.encodeComponent(pdcUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
    XYYContainer.open(router);
  }

  @override
  String getTitleName() {
    return "";
  }
}
