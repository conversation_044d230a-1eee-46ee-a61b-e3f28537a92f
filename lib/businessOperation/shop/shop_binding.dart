import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_add_object_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'dart:convert';

class ShopBinDing extends CustomerBasePage {
  final ShopListDataItemModel? data;
  final int type;
  ShopBinDing({this.data, required this.type});

  @override
  CustomerBasePageState initState() {
    return ShopBinDingState();
  }
}

class ShopBinDingState extends CustomerBasePageState<ShopBinDing> {
  TextEditingController _controller = TextEditingController();

  FocusNode _focusNode = FocusNode();

  List<ShopAddObjectModel> contacts = [];
  ShopAddObjectModel? contact;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    this._focusNode.unfocus();
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this._focusNode.unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  ...contacts
                      .map((e) => GestureDetector(
                            onTap: () {
                              setState(() {
                                contact = e;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 10),
                              padding: EdgeInsets.fromLTRB(20, 16, 20, 16),
                              decoration: BoxDecoration(
                                  color: Color(0xFFFFFFFF),
                                  borderRadius: BorderRadius.circular(4)),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(height: 5),
                                      Text('${e.accountName ?? '--'}',
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: Color(0xFF222222),
                                          )),
                                      SizedBox(height: 5),
                                    ],
                                  ),
                                  Expanded(child: SizedBox()),
                                  Image.asset(
                                    e.accountId == contact?.accountId
                                        ? 'assets/images/schedule/select_object_selected.png'
                                        : 'assets/images/schedule/select_object_normal.png',
                                    width: 20,
                                    height: 20,
                                  ),
                                ],
                              ),
                            ),
                          ))
                      .toList(),
                ],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
            child: TextButton(
                onPressed: () {
                  submit();
                },
                child: Container(
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21),
                    color: Color(0xFF00B955),
                  ),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Center(
                    child: Text(
                      '提交',
                      style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          )
        ],
      ),
    );
  }

  @override
  String getTitleName() {
    return "搜索";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      focusNode: this._focusNode,
      hintText: "请输入对象名称",
      showLeading: true,
      showClear: false,
      hideCancel: true,
      autoFocus: false,
      controller: this._controller,
      radius: BorderRadius.circular(22),
      cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
      suffix: buildButton(),
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._controller.text = inputValue;
          showToast("请输入搜索内容");
          return;
        }
        this.changeKeyword(inputValue);
      },
    );
  }

  Widget buildButton() {
    return Container(
      margin: EdgeInsets.only(left: 5),
      child: TextButton(
          onPressed: () {
            String inputValue = _controller.text.trim();
            if (inputValue.isEmpty) {
              showToast("请输入搜索内容");
              return;
            }
            this.changeKeyword(inputValue);
          },
          child: Container(
            width: 56,
            height: 28,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Color(0xFF00B955),
            ),
            child: Center(
              child: Text(
                '搜索',
                style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
              ),
            ),
          ),
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          )),
    );
  }

  void changeKeyword(String keyword) async {
    requestListData(keyword);
  }

  @override
  Future<void> refreshList() async {}

  @override
  Future<void> loadMoreList() async {}

  /// 请求私海列表数据
  void requestListData(String keyword) async {
    EasyLoading.show();
    var result = await NetworkV2<ShopAddObjectModel>(ShopAddObjectModel())
        .requestDataV2('/mop/account/list',
            parameters: {"accountName": keyword}, method: RequestMethod.POST);
    if (mounted && result.isSuccess == true) {
      setState(() {
        contacts = result.getListData() ?? [];
      });
    }
    EasyLoading.dismiss();
  }

  /// 请求私海列表数据
  void submit() async {
    if (contact == null) {
      showToast("请选择一名人员");
      return;
    }
    var url = '';
    var body = {
      "id": widget.data?.id,
    };
    if (widget.type == 1) {
      url = '/mop/merchant/manager/bind/yy';
      body['bindYyAccountId'] = contact?.accountId;
    } else {
      url = '/mop/merchant/manager/bind/zs';
      body['bindZsAccountId'] = contact?.accountId;
    }
    var result = await XYYContainer.requestChannel.request(url,
        method: RequestMethod.POST,
        contentType: RequestContentType.FORM,
        parameters: body);
    var data = json.decode(result);
    if (data['status'] == 'success') {
      showToast("修改成功");
      Navigator.pop(context, {
        "type": widget.type,
        "name": contact?.accountName,
        "data": data['data']
      });
    } else {
      showToast('操作失败');
    }
  }

  @override
  bool get isSearch => true;

  @override
  bool get isNoMore => true;

  @override
  int get itemCount => 0;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        return SizedBox();
      };

  @override
  List<CommonDropConfigModel> get filterConfig => [];

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) {}
}
