import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../home/<USER>/business-home-card-bean.dart';
import '../../home/<USER>/performance-detail-card-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data_shop_card.dart';

class ShopDataCard extends StatelessWidget {
  Color itemBgColor = const Color(0xFFF9F9F9);
  static Color unSelectedColor = Color(0xFF9494A6);
  final List<ShopDetailDataShopCardData>? list;
  ShopDataCard(this.list);

  List<BusinessHomeCardBean> get _newSignUpsList {
    // 使用空值合并运算符简化逻辑，并确保返回类型正确
    final data = list ?? [];

    // 确保 map 中的每个元素都被转换为 BusinessHomeCardBean
    return data.map((item) {
      // 假设 item 包含可以用于构造 BusinessHomeCardBean 的信息
      // 如果 item 是 Map 或其他结构，请根据实际情况调整
      return BusinessHomeCardBean(
          title: item.metricsName, value: item.metricsVal // 这里可以根据 item 提取实际值
          );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Container(
      width:screenWidth ,
      padding: EdgeInsets.only(left: 6, right: 10, bottom: 20),
      decoration: BoxDecoration(
          // color: Colors.blue,  // 设置背景颜色为蓝色

          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // 左对齐
        mainAxisSize: MainAxisSize.min, // 水平方向的对齐方式
        children: [
          Container(
            padding: EdgeInsets.only(top: 10, bottom: 10,left: 0),
            child: Text(" 店铺数据",style: TextStyle(fontWeight: FontWeight.w600,color: Color(0xff383841),fontSize: 14)),
          ),
          getListData(screenWidth),
        ],
      ),
    );
  }

  Widget getListData(screenWidth) {
    return Container(
      padding: EdgeInsets.only(left: 7),
        child: Wrap(
          spacing: 10, // 水平间距
          runSpacing: 10, // 垂直间距
          alignment: WrapAlignment.start,
          children: _newSignUpsList.map((item) {
            return Container(
              padding: EdgeInsets.all(7),
              width:  (screenWidth-14 - 3 * 10) / 3, // 固定宽度
              height: 70, // 固定高度
              decoration: BoxDecoration(
              color: itemBgColor,
              borderRadius: BorderRadius.circular(6),  // 使容器呈圆形
              ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start, // 左对齐
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      child: Text((item.title ?? '--'),
                          style: TextStyle(
                            fontSize: 11,
                            color: unSelectedColor, //
                          )),
                    ),
                    Container(
                      margin: EdgeInsets.only(top: 1),
                      child: Text(
                        (item.value ?? '--'),
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 17, // 设置字体大小
                        ),
                      ),
                    )
                  ],
                ),
            );
          }).toList(),
        ),

    );
  }
}
// Container(
// height: 10.0,
// padding: EdgeInsets.all(7),
// decoration: BoxDecoration(
// color: itemBgColor,
// borderRadius: BorderRadius.circular(6),  // 使容器呈圆形
// ),
// child:
// Column(
// crossAxisAlignment: CrossAxisAlignment.start,  // 左对齐
// mainAxisSize: MainAxisSize.min,
// children: [
// Container(
// // height: 26.0,
// child:  Text((item.title ?? '--'),style: TextStyle(
// fontSize: 11,
// color:unSelectedColor, //
// )),
// )
// ,
// Container(
// // margin: EdgeInsets.only(top: 1),
// child:  Text((item.value ?? '--'), style: TextStyle(
// fontWeight: FontWeight.w600,
// fontSize: 17, // 设置字体大小
// ),),
// )
// ],
// ),
// );
