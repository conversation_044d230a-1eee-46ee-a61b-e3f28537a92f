import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-rank-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_gmv_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_visit_table_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_detail_card.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_detail_data_table.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_edtail_header.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_visit_table.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_accompany_visit.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_visit.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_visit_list.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_purchase_trend_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_detail_data_shop_card.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data_shop_card.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data_shop_table.dart';
class ShopDeail extends BasePage {
  final ShopListDataItemModel? itemInfo;

  ShopDeail({
    @required this.itemInfo,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return ShopDeailState();
  }
}

class ShopDeailState extends BaseState<ShopDeail> {
  String pdcHost = "";

  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  /// 用来保存NavigationBar右侧按钮
  GlobalKey _anchorKey = GlobalKey();

  ValueNotifier<ShopDetailModel?> detailDataNotifier = ValueNotifier(null);
  ValueNotifier<List<ShopGmvItem>?> purchaseTrendDataNotifier = ValueNotifier(null);

  ValueNotifier<List<ShopDetailDataShopCardData>?> detailDataShopCardNotifier=ValueNotifier([]);
  ValueNotifier<ShopDetailDataShopTableData?> detailDataTableNotifier=ValueNotifier(null);

  ValueNotifier<ShopVisitTableModel?> visitDataNotifier = ValueNotifier(null);

  ShopDetailModel? get detailData => detailDataNotifier.value;

  List<ShopDetailDataShopCardData>? get detailDataCard => detailDataShopCardNotifier.value;
  ShopDetailDataShopTableData? get detailDataTable => detailDataTableNotifier.value;

  ShopVisitTableModel? get visitData => visitDataNotifier.value;

  ScrollController scrollController = ScrollController();

  @override
  void onCreate() {
    super.onCreate();
    requestAllData();
  }

  @override
  void dispose() {
    super.dispose();
    this._controller.dispose();
    detailDataNotifier.dispose();
    purchaseTrendDataNotifier.dispose();
    visitDataNotifier.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    var paddingTop = MediaQuery.of(context).padding.top + 44;
    if (paddingTop <= 44) {
      // 兜底策略，如果状态栏高度不能正常获取，则手动写个高度，防止展示不出来
      paddingTop = 100;
    }
    return Container(
      width: getScreenWidth(),
      height: getScreenHeight(),
      color: Color(0xffffffff),
      child: Container(
        padding: EdgeInsets.only(top: paddingTop),
        child: Column(
          children: [
            Expanded(
              child: Container(
                child: EasyRefresh(
                  controller: _controller,
                  onRefresh: () async {
                    return await requestAllData();
                  },
                  onLoad: null,
                  emptyWidget: null,
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Container(
                      width: getScreenWidth(),
                      child: Column(
                        children: [
                          ValueListenableBuilder<ShopDetailModel?>(
                              valueListenable: detailDataNotifier,
                              builder: (context, value, child) {
                                return ShopDeailHeader(
                                  value,
                                );
                              }),
                          Container(
                            height: 10,
                            color: Color(0xFFF5F5F5),
                          ),
                          ValueListenableBuilder<ShopDetailModel?>(
                              valueListenable: detailDataNotifier,
                              builder: (context, value, child) {
                                return ShopDetailCard(
                                  purchaseTrendNotifier:
                                      purchaseTrendDataNotifier,
                                );
                              }),
                          Container(
                            height: 10,
                            color: Color(0xFFF5F5F5),
                          ),
                          ValueListenableBuilder<List<ShopDetailDataShopCardData>?>(
                              valueListenable: detailDataShopCardNotifier,
                              builder: (context, value, child) {
                                return ShopDataCard(detailDataCard);
                              }),
                          Container(
                            height: 10,
                            color: Color(0xFFF5F5F5),
                          ),
                          ValueListenableBuilder<ShopDetailDataShopTableData?>(
                              valueListenable: detailDataTableNotifier,
                              builder: (context, value, child) {
                                return ShopDataTable(detailDataTable);
                              }),
                          Container(
                            height: 10,
                            color: Color(0xFFF5F5F5),
                          ),
                          ValueListenableBuilder<ShopVisitTableModel?>(
                              valueListenable: visitDataNotifier,
                              builder: (context, value, child) {
                                return ShopVisitTable(visitData, jumpVisitPage);
                              }),

                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            buildVisitButton(),
          ],
        ),
      ),
    );
  }

  /// 请求所有数据
  Future<void> requestAllData() async {
    // showLoadingDialog();
    await Future.wait(
        [requestDetailData(), requestVisitData(), requestPurchasedTrendData(),requestDetailCardData(),requestDetailTableData()]);
    // dismissLoadingDialog();
  }
  ///请求店铺数据
   Future<void> requestDetailCardData() async {
     var result = await NetworkV2<ShopDetailDataShopCardData>(ShopDetailDataShopCardData()).requestDataV2(
         'mop/merchant/shop/metrics',
         parameters: {'id': widget.itemInfo?.id},
         method: RequestMethod.POST);
     if (mounted && result.isSuccess == true) {
       detailDataShopCardNotifier.value = result.getListData();

       // EasyLoading.show(status:result.getListData().toString());
     }
    }
  ///请求在售及动销数据
  Future<void> requestDetailTableData() async {
    var result = await NetworkV2<ShopDetailDataShopTableData>(ShopDetailDataShopTableData()).requestDataV2(
        'mop/merchant//shop/spuTop',
        parameters: {'id': widget.itemInfo?.id},
        method: RequestMethod.POST);
    if (mounted && result.isSuccess == true) {
      detailDataTableNotifier.value= result.getData();
      // EasyLoading.show(status:result.getListData().toString());
    }
  }
  /// 请求客户详情数据
  Future<void> requestDetailData() async {
    var result = await NetworkV2<ShopDetailModel>(ShopDetailModel())
        .requestDataV2('mop/merchant/manager/detail',
            parameters: {'id': widget.itemInfo?.id},
            method: RequestMethod.POST);
    if (mounted && result.isSuccess == true) {
      detailDataNotifier.value = result.getData();
    }
  }

  /// 请求拜访记录数据
  Future<void> requestVisitData() async {
    var result = await NetworkV2<ShopVisitTableModel>(ShopVisitTableModel())
        .requestDataV2('/mop/calling/log/merchant/latest',
            parameters: {'merchantId': widget.itemInfo?.id},
            method: RequestMethod.POST);
    if (mounted && result.isSuccess == true) {
      visitDataNotifier.value = result.getData();
    }
  }

  /// 请求采购趋势数据
  Future<void> requestPurchasedTrendData() async {
    var result = await NetworkV2<ShopGmvItem>(ShopGmvItem()).requestDataV2(
        '/mop/index/org/totalPerformance',
        parameters: {'orgId': widget.itemInfo?.orgId},
        method: RequestMethod.GET);
    if (mounted && result.isSuccess == true) {
      purchaseTrendDataNotifier.value = result.getListData();
    }
  }

  @override
  bool isExtendBodyBehindAppBar() {
    return true;
  }

  @override
  String getTitleName() {
    return "商户详情";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      backgroundColor: Colors.white,
      leftButtonColor: Colors.black,
      titleTextColor: Colors.black,
      rightButtons: [
        IconButton(
          key: this._anchorKey,
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          color: Colors.white,
          splashColor: Colors.transparent,
          icon: ImageIcon(
            AssetImage(
              'assets/images/customer/customer_detail_more.png',
            ),
            color: Colors.white,
            size: 24,
          ),
          onPressed: () {
            // showCustomerMoreMenu(
            //   context: this.context,
            //   targetKey: this._anchorKey,
            //   itemSource: getCustomerMoreList(),
            //   itemTap: this.jumpMoreItem,
            // );
          },
        )
      ],
    );
  }

  /// 跳转地图页
  void jumpAddressPage() {
    if (this.detailData?.id == null) {
      return;
    }
    // String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    // router = router + "id=${this.detailData?.id}&";
    // router = router + "poiLatitude=${this.detailData?.poiLatitude}&";
    // router = router + "poiLongitude=${this.detailData?.poiLongitude}&";
    // router = router + "address=${this.detailData?.address}&";
    // router = router + "customerName=${this.detailData?.customerName}&";
    // router = router + "poiId=${this.detailData?.poiId}&";
    // router = router + "type=false&";
    // router = router + "changePoi=true&";
    // router = router + "changeMap=true";
    // router = Uri.encodeFull(router);
    // XYYContainer.open(router);

    String router = "/ybm_customer_map_page?";
    router = router + "customerId=&";
    router = router + "latitude=&";
    router = router + "longitude=";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
  /// 跳转拜访记录
  void jumpVisitPage() {
    Navigator.of(context).push(MaterialPageRoute(
      builder: (context) => ShopVisitLIstPage(data: widget.itemInfo),
      fullscreenDialog: true,
    ));
  }

  /// 跳转销售趋势
  Widget buildVisitButton() {
    if (widget.itemInfo?.visitButton == 0) {
      return SizedBox();
    }
    return Container(
            padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
            decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
            child: TextButton(
                onPressed: () {
                  if (widget.itemInfo?.visitButton == 2) {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => ShopAccompanyVisit(shopInfo: widget.itemInfo),
                      fullscreenDialog: true,
                    ));
                    return;
                  }
                  Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => ShopVisit(shopInfo: widget.itemInfo),
                    fullscreenDialog: true,
                  ));
                },
                child: Container(
                  height: 42,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21),
                    color: Color(0xFF00B955),
                  ),
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Center(
                    child: Text(
                      widget.itemInfo?.visitButton == 2 ? '陪访' : '拜访',
                      style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          );
  }
}
