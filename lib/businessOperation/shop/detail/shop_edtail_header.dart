import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_add_contacts.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ShopDeailHeader extends StatelessWidget {
  final ShopDetailModel? detailData;

  ShopDeailHeader(
    this.detailData);

  @override
  Widget build(BuildContext context) {
    String address =
        '${detailData?.prov ?? ''}${detailData?.city ?? ''}${detailData?.area ?? ''}${detailData?.addr ?? ''}';
    return ClipRRect(
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8), color: Colors.white),
        padding: EdgeInsets.only(top: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTitleWidget(context),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: buildDetailInfoWidget(context)),
                // buildMapApi(context),
                buildMapAndContactWidget(context)
              ],
            ),
            Container(
              padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
              width: double.infinity,
              decoration: BoxDecoration(
                  border: Border(
                top: BorderSide(
                  width: 0.5,
                  color: Color(0xFFE1E1E5),
                ),
              )),
              child: RichText(
                  textAlign: TextAlign.left,
                  text: TextSpan(children: [
                    TextSpan(
                        text:
                            address,
                        style:
                            TextStyle(color: Color(0xFF999999), fontSize: 12)),
                    WidgetSpan(
                        alignment: PlaceholderAlignment.middle,
                        child: SizedBox(width: 5)),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(text: '${address}'),
                          );
                          XYYContainer.toastChannel.toast('复制成功');
                        },
                        child: Image.asset(
                          "assets/images/customer/customer_private_detail_copy.png",
                          width: 12,
                          height: 12,
                        ),
                      ),
                    ),
                  ]),
                ),
            ),
          ],
        ),
      ),
    );
  }

  /// 详细信息
  Widget buildDetailInfoWidget(BuildContext context) {
    Widget fn(String title, String? content, {bool show = true}) {
      return Row(children: [
        Container(
          width: 100,
          child: Text(title, style: TextStyle(color: Color(0xFF999999))),
        ),
        show
            ? Expanded(
                child: Text(
                content ?? '--',
                softWrap: true,
                style: TextStyle(color: Color(0xFF444444)),
              ))
            : Expanded(
                child: RichText(
                  textAlign: TextAlign.left,
                  text: TextSpan(children: [
                    TextSpan(
                        text: content ?? "--",
                        style: TextStyle(
                            color: const Color(0xff444444),
                            fontWeight: FontWeight.w500)),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          Clipboard.setData(
                            ClipboardData(text: '${this.detailData?.orgId}'),
                          );
                          XYYContainer.toastChannel.toast('复制成功');
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 5),
                          child: Image.asset(
                            "assets/images/customer/customer_private_detail_copy.png",
                            width: 10,
                            height: 10,
                          ),
                        ),
                      ),
                    ),
                  ]),
                ),
              )
      ]);
    }

    String address = '${detailData?.prov ?? ''}-${detailData?.city ?? ''}';
    return Container(
        padding: EdgeInsets.only(right: 10, left: 10),
        child: Column(
          children: [
            SizedBox(height: 10),
            fn('商户', detailData?.companyName ?? '--'),
            SizedBox(height: 10),
            fn('店铺注册地', address),
            SizedBox(height: 10),
            fn('商业编码', detailData?.orgId ?? '--', show: false),
            SizedBox(height: 10),
            fn('店铺等级', detailData?.shopKLevel ?? '--'),
            SizedBox(height: 10),
            fn('保证金余额', detailData?.bondMoneyStr ?? '--'),
            SizedBox(height: 10),
            fn('合同', detailData?.fddEnterpriseAgreementStatusStr ?? '--'),
            SizedBox(height: 10),
            fn('资质', detailData?.qualificationStatusStr ?? '--'),
            SizedBox(height: 10),
            fn('成功入驻时间', detailData?.shopSuccessEnterTime ?? '--'),
            SizedBox(height: 10),
            fn('有效入驻时间', detailData?.shopEffectEnterTime ?? '--'),
            SizedBox(height: 10),
          ],
        ));
  }
  Widget buildMapApi(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(right: 5, left: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(height: 150),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: jumpAddressPage,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    "assets/images/customer/customer_private_detail_map.png",
                    width: 25,
                    height: 35,
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Text(
                    "地图",
                    style: TextStyle(
                        color: const Color(0xff676773),
                        fontSize: 11,
                        fontWeight: FontWeight.normal),
                  )
                ],
              ),
            ),
          ],
        )
    );
  }
  /// 跳转地图页
  void jumpAddressPage() {
    if (this.detailData?.id == null) {
      return;
    }
    // String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    // router = router + "id=${this.detailData?.id}&";
    // router = router + "poiLatitude=${this.detailData?.poiLatitude}&";
    // router = router + "poiLongitude=${this.detailData?.poiLongitude}&";
    // router = router + "address=${this.detailData?.address}&";
    // router = router + "customerName=${this.detailData?.customerName}&";
    // router = router + "poiId=${this.detailData?.poiId}&";
    // router = router + "type=false&";
    // router = router + "changePoi=true&";
    // router = router + "changeMap=true";
    // router = Uri.encodeFull(router);
    // XYYContainer.open(router);
    //${this.detailData?.id}
    //${this.detailData?.latitude}
    //${this.detailData?.longitude}
    String router = "/ybm_customer_map_page?";
    router = router + "customerId=&";
    router = router + "latitude=${this.detailData?.latitude/1000000}&";
    router = router + "longitude=${this.detailData?.longitude/1000000}";
    print("✅ 构建好的路由: $router");
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
  /// 联系人
  Widget buildMapAndContactWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 5, left: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(height: 150),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              EasyLoading.show();
              var result = await NetworkV2<ShopContactModel>(ShopContactModel())
                  .requestDataV2('/mop/calling/contacts/list',
                      parameters: {'merchantManagerId': detailData?.id},
                      method: RequestMethod.POST);
              EasyLoading.dismiss();
              if (result.isSuccess == true) {
                var contacts = result.getListData();
                
                PhoneList.showContactListView(
                    context: context,
                    contactList: contacts ?? [],
                    detailData: detailData,
                    );
              }
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/images/customer/customer_private_detail_contact.png",
                  width: 25,
                  height: 35,
                ),
                SizedBox(
                  height: 10,
                ),
                Text(
                  "联系人",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontSize: 11,
                      fontWeight: FontWeight.normal),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 标题
  Widget buildTitleWidget(BuildContext context) {
    Color textCol = Color(0xFF444444);
    Color bgcCol = Color(0xFFF4F4F4);
    if (detailData?.appShopStatusDisplay == "未经营") {
      textCol = Color(0xFFD38304);
      bgcCol = Color(0xFFFEF7EC);
    } else if (detailData?.appShopStatusDisplay == "经营中") {
      textCol = Color(0xFF1AA665);
      bgcCol = Color(0xFFEBF7ED);
    }
    return Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(children: [
          Container(
              width: 46,
              height: 46,
              margin: EdgeInsets.only(right: 5),
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(5)),
              child:
                  Image.network(detailData?.logoUrl ?? '', fit: BoxFit.cover)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  child: RichText(
                    textAlign: TextAlign.left,
                    text: TextSpan(children: [
                      TextSpan(
                          text: detailData?.storeName?.toString() ?? "--",
                          style: TextStyle(
                              fontSize: 16,
                              color: const Color(0xff333333),
                              fontWeight: FontWeight.w500)),
                      WidgetSpan(
                        alignment: PlaceholderAlignment.middle,
                        child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Clipboard.setData(
                              ClipboardData(text: '${this.detailData?.orgId}'),
                            );
                            XYYContainer.toastChannel.toast('复制成功');
                          },
                          child: Container(
                            padding: const EdgeInsets.only(left: 5),
                            child: Image.asset(
                              "assets/images/customer/customer_private_detail_copy.png",
                              width: 10,
                              height: 10,
                            ),
                          ),
                        ),
                      ),
                    ]),
                  ),
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(4, 2, 4, 2),
                      decoration: BoxDecoration(color: bgcCol),
                      child: Text(
                        detailData?.appShopStatusDisplay ?? '',
                        style:
                            TextStyle(color: textCol, fontSize: 12),
                      ),
                    )
                  ],
                )
              ],
            ),
          )
        ]),
    );
  }
}

class PhoneList {
  static Future<T?> showContactListView<T>({
    required BuildContext context,
    required List<ShopContactModel> contactList,
    required ShopDetailModel? detailData,
  }) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        double maxHeight = MediaQuery.of(context).size.height -
            233 +
            MediaQuery.of(context).viewPadding.bottom;
        double curHeight = paddingBottom(context) + 104;
        curHeight += contactList.length * 70;
        if (curHeight > maxHeight) {
          curHeight = maxHeight;
        }
        return Container(
          height: curHeight,
          decoration: BoxDecoration(
            color: Color(0xFFF2F2F2),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(18),
              topRight: Radius.circular(18),
            ),
          ),
          child: Column(
            children: [
              Container(
                height: 50,
                child: Row(
                  children: [
                    SizedBox(width: 30),
                    Expanded(
                      child: Text(
                        '客户联系人',
                        style: TextStyle(
                            color: Color(0xFF383841),
                            fontSize: 17,
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Image.asset(
                        'assets/images/customer/customer_phone_close.png',
                        width: 20,
                        height: 20,
                      ),
                      style: ButtonStyle(
                        overlayColor: MaterialStateProperty.all<Color>(
                            Colors.transparent),
                        padding: MaterialStateProperty.all<EdgeInsets>(
                            EdgeInsets.only(right: 10)),
                        minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      children: contactList
                          .map((model) => Container(
                                padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFFFFFF),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
                                  height: 60,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  model.contactName ?? '',
                                                  style: TextStyle(
                                                    color: Color(0xFF222222),
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                (model.isKp == 1)
                                                    ? Text(
                                                        "（老板KP）",
                                                        style: TextStyle(
                                                          color:
                                                              Color(0xFF777777),
                                                          fontSize: 14,
                                                        ),
                                                      )
                                                    : SizedBox(),
                                              ],
                                            ),
                                            SizedBox(height: 5),
                                            Text(
                                              '电话：${model.contactPhone ?? '--'}',
                                              style: TextStyle(
                                                  color: Color(0xFF383841),
                                                  fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Spacer(),
                                      TextButton(
                                        onPressed: () {
                                          var body = {
                                            "merchantManagerId": detailData?.id ?? '',
                                            "contactName": model.contactName
                                          };
                                          var bodyStr = json.encode(body);
                                          XYYContainer.bridgeCall('call_phone',
                                              parameters: {
                                                'mobile':
                                                    '${model.contactPhone}',
                                                'merchantId': '-99',
                                                'merchantName': bodyStr,
                                                'addSource': enumToString(
                                                    CallAddSource
                                                        .privateDetail),
                                              });
                                        },
                                        child: Image.asset(
                                          'assets/images/business/business-shop-detail-phone.png',
                                          width: 20,
                                          height: 20,
                                        ),
                                        style: ButtonStyle(
                                          overlayColor:
                                              MaterialStateProperty.all<Color>(
                                                  Colors.transparent),
                                          padding: MaterialStateProperty.all<
                                                  EdgeInsets>(
                                              EdgeInsets.only(right: 15)),
                                          minimumSize:
                                              MaterialStateProperty.all<Size>(
                                                  Size.zero),
                                          tapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ) ,
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                color: Color(0xFFF2F2F2),
                child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context)
                          .push(MaterialPageRoute(
                            builder: (context) => ShopAddContacts(shopInfo: ShopListDataItemModel(id:detailData?.id,orgId:detailData?.orgId,companyName:detailData?.companyName)),
                            fullscreenDialog: true,
                          ))
                          .then((value) => {});
                    },
                    child: Container(
                      width: double.infinity,
                      height: 42,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Color(0xFF00B955),
                      ),
                      child: Center(
                        child: Text(
                          '添加联系人',
                          style:
                              TextStyle(color: Color(0xFFFFFFFF), fontSize: 16,fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    style: ButtonStyle(
                      overlayColor:
                          MaterialStateProperty.all<Color>(Colors.transparent),
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          EdgeInsets.all(0)),
                      minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )),
              )
            ],
          ),
        );
      },
    );
  }

  static double paddingBottom(BuildContext context) {
    var bottom = MediaQuery.of(context).viewPadding.bottom;
    if (bottom == 0) {
      return 20;
    }
    return bottom;
  }
}
