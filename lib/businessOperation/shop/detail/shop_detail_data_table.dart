import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data_shop_table.dart';
class ShopDataTable extends StatelessWidget {
  Color itemBgColor = const Color(0xFFF9F9F9);
  static Color unSelectedColor = Color(0xFF9494A6);
  late final List<String> dataRow;
  late final List<List> tableRow;
  late Map dataTable;
  ShopDetailDataShopTableData? dataTableAll;
  ShopDataTable(this.dataTableAll){
    if(dataTableAll==null){

    }else{
      dataTable=dataTableAll!.toJson();
      dataRow=(dataTable?['dataList'] ?? []).map((item) {
        return item['topName']?.toString() ?? '';
      }).toList().cast<String>();
      dataRow.insert(0, 'TOP分层');
      tableRow=(dataTable?['dataList'] ?? []).map((item) {
        return (item?['metricsDTOS'] ?? []).map((itemV2) {
          return itemV2['metricsVal']?.toString() ?? '';
        }).toList().cast<String>();
      }).toList().cast<List>();
    }

    // print("yz");
    // print(tableRow);
 }
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, bottom: 20),
      child: dataTableAll!=null?Column(
        crossAxisAlignment: CrossAxisAlignment.start,  // 左对齐
        mainAxisSize: MainAxisSize.min,  // 水平方向的对齐方式水平方向的对齐方式
        children: [
          Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(top: 10,bottom: 10),
            child: Text("在售及动销数据",style: TextStyle(fontWeight: FontWeight.w600,color: Color(0xff383841),fontSize: 14)),
          ),
        Row(
            children: <Widget>[
              Container(
                child: Table(
                    children: _buildTableColumnOne()),
                width: numRowWidth, //固定第一列
              ),
              Expanded(
                  child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        child: Table(children: _buildTableRow()),
                        width: (numRowWidth * dataTable?['headerList'].length).toDouble(),
                      )))
            ],
          ),
        ],
      ):Container(),
    );
  }

 //创建第一列行

  double numRowWidth = 120.0;//单个表宽
  double numRowHeight = 48.0;//表格高

  List<TableRow> _buildTableColumnOne() {
    List<TableRow> returnList = [];
    returnList.add(_buildSingleColumnOne(-1));
    for (int i = 0; i < dataTable!["dataList"].length; i++) {
      returnList.add(_buildSingleColumnOne(i));
    }
    return returnList;
  }

  //创建tableRows
  List<TableRow> _buildTableRow() {
    List<TableRow> returnList =  [];
    returnList.add(_buildSingleRow(-1));
    for (int i = 0; i < tableRow.length; i++) {
      returnList.add(_buildSingleRow(i,list:tableRow[i]));
    }
    return returnList;
  }

  //创建第一列tableRow
  TableRow _buildSingleColumnOne(int index) {
    return TableRow(
      //第一行样式 添加背景色
        decoration: BoxDecoration(
          color: Color(0x80F6F7F9), // 第一行背景色
          boxShadow: [
            BoxShadow(
              color:Colors.black.withOpacity(0.1), // 阴影颜色
              offset: Offset(6, 0), // x 轴偏移量为 6，y 为 0，阴影只向右扩散
              blurRadius: 8.0, // 模糊半径
              spreadRadius: 0.0, // 扩散半径，控制阴影范围
            ),
          ],
        ),
        children: [
          //增加行高
          _buildSideBox(dataRow[index+1], index == -1,index:index),
        ]);
  }

  //创建一行tableRow
  TableRow _buildSingleRow(int index,{List<dynamic>? list}) {
    return TableRow(
      //第一行样式 添加背景色
        children:index==-1? [...buildSideMoreBoxes(dataTable?['headerList'])]:
            [...buildSideMoreBoxesContent(list!,index)]);
       // [ _buildSideBoxData(["2676230","2676230"], index == -1,index:index),
       //   _buildSideBoxData(["2676230","2676230"], index == -1,index:index),
       //
       // ]);
  }
  List<Widget> buildSideMoreBoxes(List<String> items) {
    List<Widget> boxes = [];
    for (int i = 1; i < items.length; i++) {
      boxes.add(_buildSideMoreBox(items[i], i == -1));
    }
    return boxes;
  }
  List<Widget> buildSideMoreBoxesContent(List<dynamic> items,index) {
    List<List<String>> result = [];
    for (int i = 0; i < items.length; i += 2) {
      if (i + 1 < items.length) {
        result.add([items[i].toString(), items[i + 1].toString()]);
      } else {
        // 如果最后一个元素没有配对，单独处理（可选）
        result.add([items[i]]);
      }
    }

    List<Widget> boxes = [];
    for (int i = 0; i < result.length; i++) {
      boxes.add(_buildSideBoxData([result[i][0],result[i][1]], i == -1));
    }

    return boxes;
  }

  //创建单个表格
  Widget _buildSideBox(String title, isTitle,{int? index}) {
    return SizedBox(
        height:index == -1?62: numRowHeight,
        width: numRowWidth,
        child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: itemBgColor,
                border: Border(

                    // bottom: BorderSide(width: 0.33, color:Color(0xFFDDDFE1)),
                    right: BorderSide(width: 0.33, color:Color(0xFFDDDFE1)),

                ),

            ),

            child: Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontWeight:isTitle ? FontWeight.normal: FontWeight.w600,
                  fontSize: isTitle ? 12 : 14, color:isTitle?Color(0xFF888888):Colors.black),
            )));
  }
  //创建数据表格
  Widget _buildSideBoxData(List<String> title, isTitle,{int? index}) {
    return SizedBox(
        height:index == -1?62: numRowHeight,
        width: numRowWidth,
        child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: (index ?? 0) % 2 == 1 ? Color(0x80F6F7F9) : Color(0x80FFFFFF),
                border: Border(
                    bottom: BorderSide(width:index==5?0:0.33, color:Color(0xFFDDDFE1)),
                    right: BorderSide(width: 0.33, color:Color(0xFFDDDFE1))
                )),
            child: Flex(
              mainAxisAlignment:MainAxisAlignment.spaceAround,
              direction: Axis.horizontal,  // 或 Axis.vertical
              children: [
                Text(title[0]),
                Text(title[1])
              ],
            )
        ));
  }
  //创建多表头
  Widget _buildSideMoreBox(String title, isTitle) {
    return SizedBox(
        height: 62,
        width: numRowWidth,

        child: Container(

            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: Color(0x80F6F7F9),
                border: Border(
                    bottom: BorderSide(width: 0.33, color:Color(0xFFDDDFE1)),
                    right: BorderSide(width: 0.33, color:Color(0xFFDDDFE1))
                )),
            child: Container(
              child:Column(
                children: [
                  Container(
                    width: double.infinity,  // 宽度填满一整行
                    padding: EdgeInsets.only(top: 5),
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(width: 0.33, color:Color(0xFFDDDFE1)))),
                    child:Center(
                      child:  Text(title,style: TextStyle(fontSize: 12,color:Color(0xFF888888))),
                    ),
                    height: 30,
                  ),
                  Flex(
                    mainAxisAlignment:MainAxisAlignment.spaceAround,
                    direction: Axis.horizontal,  // 或 Axis.vertical
                    children: <Widget>[
                      Container(
                        padding: EdgeInsets.only(top: 5),
                        height: 30,
                        child:Center(
                          child: Text("在售",style: TextStyle(fontSize: 12,color:Color(0xFF888888))),
                        ) ,
                      ),
                      Container(
                        padding: EdgeInsets.only(top: 5),
                        height: 30,
                        child:Center(
                          child: Text("动销",style: TextStyle(fontSize: 12,color:Color(0xFF888888))),
                        )
                      )
                    ],
                  )
                ],
              ) ,
            )
        ));
  }
}

