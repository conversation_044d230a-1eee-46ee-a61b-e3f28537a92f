import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/location/location_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/common/shop_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_param_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_claim_item.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/buildLocaRequest.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'common/shop_filter_box.dart';

class ShopClaimListPage extends CustomerBasePage {
  ShopClaimListPage();

  @override
  CustomerBasePageState initState() {
    return ShopClaimListPageState();
  }
}

class ShopClaimListPageState extends CustomerBasePageState<ShopClaimListPage>{
  // 筛选参数
  ShopFilterListModel? conditionModel;

  // 接口入参存储
  ShopListParamData paramModel = ShopListParamData();

  List<ShopListDataItemModel> dataSource = [];
  String? locationLat;
  String? locationLong;

  /// 用来判断是否是第一次加载
  bool isFirstLoad = true;

  bool isLastPage = true;

  @override
  void initState() {
    refreshList(type: 'init');
    super.initState();
  }
  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        super.buildWidget(context),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 筛选项
  @override
  List<CommonDropConfigModel> get filterConfig {
    return [
      CommonDropConfigModel(
        defaultTitle: "认领人员",
        paramKey: "claimType",
      ),
      CommonDropConfigModel(
        defaultTitle: "店铺状态",
        paramKey: "appShopStatusOption",
      ),
      CommonDropConfigModel(
        defaultTitle: "注册时间",
        paramKey: "regTimeOption",
      ),
      CommonDropConfigModel(
        defaultTitle: "筛选",
        paramKey: "filterLis",
      ),
    ];
  }

  // 筛选弹窗
  @override
  void showDropPopupView(GlobalKey authKey, CommonDropConfigModel model,
      DropButtonController controller) async {
    switch (model.paramKey) {
      case "claimType":
        // 认领人员
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return ShopFilterPopup(
              models: conditionModel?.mopClaimType ?? [],
              selectedCode: paramModel.claimType,
              selectAction: (value) {
                paramModel.claimType = "${value.dictValue}";
                refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "appShopStatusOption":
        // 店铺状态
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return ShopFilterPopup(
              models: conditionModel?.shopStatusOption ?? [],
              selectedCode: paramModel.appShopStatusOption,
              distance: distance,
              selectAction: (value) {
                paramModel.appShopStatusOption = "${value.dictValue}";
                refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "regTimeOption":
        // 注册时间
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return ShopFilterPopup(
              models: conditionModel?.mopRegTimeOption ?? [],
              selectedCode: paramModel.regTimeOption,
              distance: distance,
              selectAction: (value) {
                paramModel.regTimeOption = "${value.dictValue}";
                refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "filterLis":
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            String? val1 = (paramModel.businessAttributeList != null &&
                    paramModel.businessAttributeList!.isNotEmpty)
                ? paramModel.businessAttributeList![0]
                : null;
            String? val2 =
                (paramModel.fddEnterpriseAgreementStatusList != null &&
                        paramModel.fddEnterpriseAgreementStatusList!.isNotEmpty)
                    ? paramModel.fddEnterpriseAgreementStatusList![0]
                    : null;
            String? val3 = (paramModel.bondStatusList != null &&
                    paramModel.bondStatusList!.isNotEmpty)
                ? paramModel.bondStatusList![0]
                : null;
            String? val4 = (paramModel.erpJointStatusList != null &&
                    paramModel.erpJointStatusList!.isNotEmpty)
                ? paramModel.erpJointStatusList![0]
                : null;
            return ShopFilterBox(
              shopBusinessAttributeList:
                  conditionModel?.shopBusinessAttribute ?? [],
              mopEnterpriseAgreementSignStatusList:
                  conditionModel?.mopEnterpriseAgreementSignStatus ?? [],
              mopBondStatusList: conditionModel?.mopBondStatus ?? [],
              mopErpJointStatusList: conditionModel?.mopErpJointStatus ?? [],
              businessAttributeVal: val1,
              fddEnterpriseAgreementStatusVal: val2,
              bondStatusVal: val3,
              erpJointStatusListVal: val4,
              selectAction: (
                val1,
                val2,
                val3,
                val4,
              ) {
                if (val1.dictValue == 'quanbu') {
                  paramModel.businessAttributeList = [];
                } else {
                  paramModel.businessAttributeList = [val1.dictValue];
                }

                if (val2.dictValue == 'quanbu') {
                  paramModel.fddEnterpriseAgreementStatusList = [];
                } else {
                  paramModel.fddEnterpriseAgreementStatusList = [
                    val2.dictValue
                  ];
                }

                if (val3.dictValue == 'quanbu') {
                  paramModel.bondStatusList = [];
                } else {
                  paramModel.bondStatusList = [val3.dictValue];
                }
                if (val4.dictValue == 'quanbu') {
                  paramModel.erpJointStatusList = [];
                } else {
                  paramModel.erpJointStatusList = [val4.dictValue];
                }

                refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      default:
    }
  }

  @override
  Future<void> refreshList({String? type}) async {
    page = 1;
    // 每次刷新时 重新请求定位信息
    await requestLocation(type);
    EasyLoading.show();
    // 如果本地没有筛选条件则先请求筛选
    if (conditionModel == null) {
      await requestCondition();
    }

    // 请求列表数据
    requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    requestListData();
  }

  Future<void> requestLocation(String? type) async {
    if (isFirstLoad) {
      /// 延迟100毫秒 进行定位请求，预防首页跳转过来传参未接收
      await Future.delayed(Duration(milliseconds: 100));
    }

    LocationData result;
    if (BuildLocaRequest.latitude == null || BuildLocaRequest.longitude == '' || type == null) {
      result = await XYYContainer.locationChannel.locate().timeout(
        Duration(seconds: 3),
        onTimeout: () {
          return LocationData();
        },
      );
    }else {
      result = LocationData(
        latitude: BuildLocaRequest.latitude,
        longitude: BuildLocaRequest.longitude,
      );
    }
    paramModel.curLatitudeStr = result.latitude;
    paramModel.curLongitudeStr = result.longitude;
    locationLat = result.latitude;
    locationLong = result.longitude;
    XYYContainer.storageChannel.put("customer_map_longitude", result.longitude);
    XYYContainer.storageChannel.put("customer_map_latitude", result.latitude);
    XYYContainer.storageChannel.put("customer_map_location_timestamp",
        DateTime.now().millisecondsSinceEpoch);
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return paramModel.curLatitudeStr?.isEmpty == false &&
        paramModel.curLongitudeStr?.isEmpty == false;
  }

  /// 请求私海列表数据
  void requestListData() async {

    var params = paramModel.toJson();
    if (paramModel.fddEnterpriseAgreementStatusList?.isEmpty ?? false) {
      params.remove("fddEnterpriseAgreementStatusList");
    }
    if (paramModel.erpJointStatusList?.isEmpty ?? false) {
      params.remove("erpJointStatusList");
    }
    if (paramModel.businessAttributeList?.isEmpty ?? false) {
      params.remove("businessAttributeList");
    }
    if (paramModel.bondStatusList?.isEmpty ?? false) {
      params.remove("bondStatusList");
    }
    // ---- 处理转字符串
    if (paramModel.fddEnterpriseAgreementStatusList?.isNotEmpty ?? false) {
      params['fddEnterpriseAgreementStatusList'] = paramModel.fddEnterpriseAgreementStatusList?.join(',');
    }
    if (paramModel.erpJointStatusList?.isNotEmpty ?? false) {
      params['erpJointStatusList'] = paramModel.erpJointStatusList?.join(',');
    }
    if (paramModel.businessAttributeList?.isNotEmpty ?? false) {
      params['businessAttributeList'] = paramModel.businessAttributeList?.join(',');
    }
    if (paramModel.bondStatusList?.isNotEmpty ?? false) {
      params['bondStatusList'] = paramModel.bondStatusList?.join(',');
    }
    // 移除荷叶使用的参数
    params.remove("sortType");
    params['pageSize'] = 10;
    params['pageNo'] = page;
    var result =
        await NetworkV2<ShopListDataModel>(ShopListDataModel()).requestDataV2(
      '/mop/merchant/manager/claim/page',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
    );
    if (isFirstLoad != true && page == 1) {
      dataSource = [];
    }

    /// 设置第一次加载完成
    isFirstLoad = false;

    EasyLoading.dismiss();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.result ?? [];
        if (page == 1) {
          dataSource = rows;
        } else {
          dataSource.addAll(rows);
        }
        isLastPage = data?.lastPage ?? true;
        page = isLastPage ? page : page + 1;
        setState(() {});
      }
    }
  }

  // 请求筛选条件
  Future<void> requestCondition() async {
    var result = await NetworkV2<ShopFilterListModel>(ShopFilterListModel())
        .requestDataV2(
            '/mop/dict/batch/list?dictTypes=mop_claim_type,mop_app_shop_status_option,mop_reg_time_option,mop_enterprise_agreement_sign_status,mop_bond_status,mop_erp_joint_status,mop_shop_business_attribute');
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (data != null) {
          conditionModel = data;
          setState(() {});
        }
      }
    }
    return;
  }

  @override
  bool get isSearch => false;

  @override
  bool get isNoMore => isLastPage;

  @override
  int get itemCount => dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        ShopListDataItemModel model = dataSource[index];
        return ShopClaimItem(
          model,
          hasLocation: hasLocation,
          serviceInterface: interface,
          locationLat: locationLat,
          locationLong: locationLong,
        );
      };
}
