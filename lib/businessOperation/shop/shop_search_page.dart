import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_param_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_claim_item.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ShopSearchPage extends CustomerBasePage {
  final int typeIndex;
  ShopSearchPage({required this.typeIndex});

  @override
  CustomerBasePageState initState() {
    return ShopSearchPageState();
  }
}

class ShopSearchPageState extends CustomerBasePageState<ShopSearchPage> {
  TextEditingController _controller = TextEditingController();

  FocusNode _focusNode = FocusNode();
  // 接口入参存储
  ShopListParamData paramModel = ShopListParamData();
  List<ShopListDataItemModel> dataSource = [];
  String? locationLat;
  String? locationLong;

  /// 用来判断是否是第一次加载
  bool isFirstLoad = true;

  bool isLastPage = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    this._focusNode.unfocus();
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this._focusNode.unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: Stack(
        children: [
          super.buildWidget(context),
        ],
      ),
    );
  }

  @override
  String getTitleName() {
    return "搜索";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      focusNode: this._focusNode,
      hintText: "请输入店铺名称/SH编码",
      showLeading: true,
      showClear: false,
      hideCancel: true,
      autoFocus: true,
      controller: this._controller,
      radius: BorderRadius.circular(22),
      cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
      suffix: buildButton(),
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._controller.text = inputValue;
          showToast("请输入搜索内容");
          return;
        }
        this.changeKeyword(inputValue);
      },
    );
  }

  Widget buildButton() {
    return Container(
      margin: EdgeInsets.only(left: 5),
      child: TextButton(
          onPressed: () {
            String inputValue = _controller.text.trim();
            if (inputValue.isEmpty) {
              showToast("请输入搜索内容");
              return;
            }
            this.changeKeyword(inputValue);
          },
          child: Container(
            width: 56,
            height: 28,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              color: Color(0xFF00B955),
            ),
            child: Center(
              child: Text(
                '搜索',
                style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
              ),
            ),
          ),
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          )),
    );
  }

  void changeKeyword(String keyword) async {
    paramModel.storeNameOrgId = keyword;
    refreshList();
  }

  @override
  Future<void> refreshList() async {
    page = 1;
    // 没有数据时 刷新增加loading
    if (dataSource.length == 0) {
      showLoadingDialog();
    }

    // 每次刷新时 重新请求定位信息
    await requestLocation();

    // 请求列表数据
    requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    requestListData();
  }

  Future<void> requestLocation() async {
    if (isFirstLoad) {
      /// 延迟100毫秒 进行定位请求，预防首页跳转过来传参未接收
      await Future.delayed(Duration(milliseconds: 100));
    }

    var result = await XYYContainer.locationChannel.locate();
    paramModel.curLatitudeStr = result.latitude;
    paramModel.curLongitudeStr = result.longitude;
    locationLat = result.latitude;
    locationLong = result.longitude;
    XYYContainer.storageChannel.put("customer_map_longitude", result.longitude);
    XYYContainer.storageChannel.put("customer_map_latitude", result.latitude);
    XYYContainer.storageChannel.put("customer_map_location_timestamp",
        DateTime.now().millisecondsSinceEpoch);
    return;
  }

  /// 请求私海列表数据
  void requestListData() async {
    var params = paramModel.toJson();
    // 移除荷叶使用的参数
    params.remove("sortType");
    params['pageSize'] = 10;
    params['pageNo'] = page;
    String url = '/mop/merchant/manager/claim/page';
    if (widget.typeIndex == 1) {
      url = '/mop/merchant/manager/unCooperation/page';
    }
    var result =
        await NetworkV2<ShopListDataModel>(ShopListDataModel()).requestDataV2(
      url,
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
    );
    if (isFirstLoad != true && page == 0) {
      dataSource = [];
    }

    /// 设置第一次加载完成
    isFirstLoad = false;

    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.result ?? [];
        if (page == 1) {
          dataSource = rows;
        } else {
          dataSource.addAll(rows);
        }
        isLastPage = data?.lastPage ?? true;
        page = isLastPage ? page : page + 1;
        setState(() {});
      }
    }
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return paramModel.curLatitudeStr?.isEmpty == false &&
        paramModel.curLongitudeStr?.isEmpty == false;
  }

  @override
  bool get isSearch => true;

  @override
  bool get isNoMore => isLastPage;

  @override
  int get itemCount => dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        ShopListDataItemModel model = dataSource[index];
        return ShopClaimItem(
          model,
          hasLocation: hasLocation,
          serviceInterface: interface,
          locationLat: locationLat,
          locationLong: locationLong,
        );
      };

  @override
  List<CommonDropConfigModel> get filterConfig => [];

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) {}
}
