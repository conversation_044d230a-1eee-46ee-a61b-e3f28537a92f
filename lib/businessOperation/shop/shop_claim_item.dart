import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/detail/shop_detail.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_binding.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_accompany_visit.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_add_contacts.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/visit/shop_visit.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ShopClaimItem extends StatelessWidget {
  final ShopListDataItemModel model;
  final bool hasLocation;
  final String serviceInterface;
  final String? locationLat;
  final String? locationLong;

  ShopClaimItem(
    this.model, {
    this.hasLocation = true,
    this.serviceInterface = '',
    this.locationLat,
    this.locationLong,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: GestureDetector(
        onTap: () {
          if (model.appShopStatusDisplay == '未注册') {
            return;
          }
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => ShopDeail(itemInfo: model),
            fullscreenDialog: true,
          ));
        },
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(7),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              merchantNameWidget(context),
              merchantFullNameWidget(),
              merchantAddressWidget(),
              tagWidget(),
              getVisitTimeWidget(),
              getPurchaseInfoWidget(context),
              getActionWidget(context),
            ],
          ),
        ),
      ),
    );
  }

  // 店铺名称及状态
  Widget merchantNameWidget(context) {
    Color textCol = Color(0xFF444444);
    Color bgcCol = Color(0xFFF4F4F4);
    if (model.appShopStatusDisplay == "未经营") {
      textCol = Color(0xFFD38304);
      bgcCol = Color(0xFFFEF7EC);
    } else if (model.appShopStatusDisplay == "经营中") {
      textCol = Color(0xFF1AA665);
      bgcCol = Color(0xFFEBF7ED);
    }
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10),
      child: Row(
        children: [
          Container(
              constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width - 150),
              child: Text(
                model.storeName ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              )),
          SizedBox(width: 5),
          Container(
            decoration: BoxDecoration(
              color: bgcCol,
              borderRadius: BorderRadius.circular(2),
            ),
            padding: EdgeInsets.only(left: 4, right: 4, top: 2, bottom: 2),
            child: Text(
              model.appShopStatusDisplay ?? '',
              style: TextStyle(color: textCol, fontSize: 12),
            ),
          ),
          Expanded(child: SizedBox()),
          TextButton(
            onPressed: () async {
              EasyLoading.show();
              var result = await NetworkV2<ShopContactModel>(ShopContactModel())
                  .requestDataV2('/mop/calling/contacts/list',
                      parameters: {'merchantManagerId': model.id},
                      method: RequestMethod.POST);
              EasyLoading.dismiss();
              if (result.isSuccess == true) {
                var contacts = result.getListData();

                PhoneList.showContactListView(
                    data: model, context: context, contactList: contacts ?? []);
              }
            },
            child: Image.asset(
              'assets/images/business/business-shop-item-phone.png',
              width: 25,
              height: 25,
            ),
            style: ButtonStyle(
              padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(0)),
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          )
        ],
      ),
    );
  }

  // 店铺全名
  Widget merchantFullNameWidget() {
    if (model.appShopStatusDisplay == '未注册') {
      return SizedBox();
    }
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 2),
      child: Text(
        '商户：${model.companyName ?? '--'}',
        style: TextStyle(color: Color(0xFF666666), fontSize: 12),
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr = this.hasLocation ? "距您${model.distance}" : "获取不到当前位置";
    String address =
        '${model.prov ?? ''}${model.city ?? ''}${model.area ?? ''}${model.addr ?? ''}';
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 6),
      child: Row(
        children: [
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              address,
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 标签行
  Widget tagWidget() {
    if (model.appShopStatusDisplay == '未注册') {
      return SizedBox();
    }
    // 标签
    getTagItem(String tag, Color color) {
      return Container(
        padding: EdgeInsets.fromLTRB(4, 2, 4, 2),
        decoration: BoxDecoration(
          border: Border.all(color: color, width: 0.5),
        ),
        child: Text(
          tag,
          style: TextStyle(color: color, fontSize: 12),
        ),
      );
    }

    List<Widget> tags = [];
    // 店铺分类
    if (this.model.businessAttributeStr?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.businessAttributeStr}", Color(0xFF1AA665)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 店铺等级
    if (this.model.shopKLevel?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.shopKLevel}", Color(0xFF1AA665)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 店铺等级k
    if (this.model.shopSLevel?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.shopSLevel}", Color(0xFF1AA665)),
      );
      tags.add(SizedBox(width: 5));
    }
    // erp对接
    if (this.model.erpJointStatusStr?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem(
            "${this.model.erpJointStatusStr}",
            model.erpJointStatus == 'jointed'
                ? Color(0xFF1AA665)
                : Color(0xFF888888)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 合同状态
    if (this.model.fddEnterpriseAgreementStatusStr?.toString().isNotEmpty ==
        true) {
      tags.add(
        getTagItem(
            "${this.model.fddEnterpriseAgreementStatusStr}",
            model.fddEnterpriseAgreementStatus == 'signed'
                ? Color(0xFF1AA665)
                : Color(0xFF888888)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 保证金状态
    if (this.model.bondStatusStr?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.bondStatusStr}",
            model.bondStatus == 'paid' ? Color(0xFF1AA665) : Color(0xFF888888)),
      );
      tags.add(SizedBox(width: 5));
    }

    return Container(
      padding: EdgeInsets.only(left: 10, top: 8),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: tags,
      ),
    );
  }

  // 拜访时间
  Widget getVisitTimeWidget() {
    Widget buildRowText() {
      if (model.sinceLastVisitDays > 0) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '距上次拜访',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
            Text(
              '${model.sinceLastVisitDays ?? ''}',
              style: TextStyle(color: Color(0xFF333333), fontSize: 12),
            ),
            Text(
              '天',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            )
          ],
        );
      }
      if (model.sinceLastVisitDays == 0) {
        return Text(
          '今日拜访',
          style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
        );
      }
      return Text(
        '未拜访',
        style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
      );
      ;
    }

    return Container(
      height: 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(9),
        ),
      ),
      padding: EdgeInsets.only(left: 10, right: 10, top: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/customer/customer_private_visit_icon.png',
            width: 12,
          ),
          SizedBox(width: 4),
          buildRowText()
        ],
      ),
    );
  }

  /// 采购信息
  Widget getPurchaseInfoWidget(BuildContext context) {
    if (model.appShopStatusDisplay == '未注册') {
      return SizedBox();
    }
    return Container(
      color: Color(0xFFFAFAFA),
      padding: EdgeInsets.only(top: 5, bottom: 5, left: 10, right: 10),
      margin: EdgeInsets.only(top: 10),
      child: Wrap(
        children: [
          ...(model.indexCardList ?? [])
              .map((e) => Column(
                    children: [
                      getPurchaseItem(
                          context, "${e.title ?? ''}：", '${e.value ?? '--'}'),
                      getPurchaseItem(context, "${e.rateStr ?? ''}：", '',
                          box: Row(
                            children: [
                              Text(
                                '${e.rate ?? '--'}',
                                style: TextStyle(
                                  color: Color(
                                      (e.color == null || e.color == '')
                                          ? 0xFF222222
                                          : int.parse(e.color)),
                                  fontSize: 12,
                                ),
                              ),
                              e.isUp == 0
                                  ? SizedBox()
                                  : Image.asset(
                                      e.isUp == 1
                                          ? 'assets/images/business/business-shop-green-top.png'
                                          : 'assets/images/business/business-shop-red-bottom.png',
                                      width: 8,
                                      height: 12,
                                    ),
                            ],
                          )),
                    ],
                  ))
              .toList()
        ],
      ),
    );
  }

  Widget getPurchaseItem(BuildContext context, String title, String content,
      {Widget? box}) {
    return Container(
      width: (MediaQuery.of(context).size.width - 40) / 2,
      height: 25,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              color: Color(0xFF8E8E93),
              fontSize: 12,
            ),
          ),
          box ??
              Expanded(
                  child: Text(
                content,
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 12,
                ),
                overflow: TextOverflow.ellipsis,
              )),
        ],
      ),
    );
  }

  /// 底部按钮
  Widget getActionWidget(BuildContext context) {
    return StatefulBuilder(builder: (context, setState) {
      return Container(
        height: 50,
        child: Row(
          children: [
            SizedBox(width: 10),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          Text(
                            '运营：',
                            style: TextStyle(
                                color: Color(0xFF222222), fontSize: 14),
                          ),
                          Container(
                            constraints: BoxConstraints(maxWidth: 50),
                            child: Text(
                              '${model.bindYyAccount ?? ''}',
                              style: TextStyle(
                                  color: Color(0xFF222222), fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 5),
                          model.updateYyButton == 1
                              ? GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () => {
                                    Navigator.of(context)
                                        .push(MaterialPageRoute(
                                          builder: (context) => ShopBinDing(
                                            data: model,
                                            type: 1,
                                          ),
                                          fullscreenDialog: true,
                                        ))
                                        .then((value) => {
                                              if (value['type'] == 1)
                                                {
                                                  setState(() {
                                                    model.bindYyAccount =
                                                        value['name'];
                                                    model.visitButton =
                                                        value['data'];
                                                  })
                                                }
                                            })
                                  },
                                  child: Image.asset(
                                    'assets/images/business/business-shop-item-edit.png',
                                    width: 16,
                                  ),
                                )
                              : SizedBox(),
                        ],
                      )),
                  Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          Text(
                            '招商：',
                            style: TextStyle(
                                color: Color(0xFF222222), fontSize: 14),
                          ),
                          Container(
                            constraints: BoxConstraints(maxWidth: 50),
                            child: Text(
                              '${model.bindZsAccount ?? ''}',
                              style: TextStyle(
                                  color: Color(0xFF222222), fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          model.claimButton == 1
                              ? TextButton(
                                  onPressed: () async {
                                    var result = await XYYContainer
                                        .requestChannel
                                        .request(
                                            '/mop/merchant/manager/claim/shop',
                                            method: RequestMethod.POST,
                                            contentType:
                                                RequestContentType.FORM,
                                            parameters: {
                                          "id": model.id,
                                        });
                                    var data = json.decode(result);
                                    if (data['status'] == 'success') {
                                      var userInfo =
                                          await UserInfoUtil.getUserInfo();
                                      XYYContainer.toastChannel.toast('认领成功');
                                      model.claimButton = 0;
                                      model.updateZsButton = 1;
                                      model.bindZsAccount =
                                          userInfo?.realName ?? '--';
                                      model.visitButton = data['data'];
                                      setState(() {});
                                    } else {
                                      XYYContainer.toastChannel.toast('操作失败');
                                    }
                                  },
                                  child: Container(
                                    height: 22,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        color: Colors.transparent,
                                        border: Border.all(
                                          color: Color(0xFFBCBCBC),
                                          width: 1,
                                        )),
                                    padding:
                                        EdgeInsets.only(left: 10, right: 10),
                                    child: Center(
                                      child: Text(
                                        '认领',
                                        style: TextStyle(
                                            color: Color(0xFF222222),
                                            fontSize: 12),
                                      ),
                                    ),
                                  ),
                                  style: ButtonStyle(
                                    overlayColor:
                                        MaterialStateProperty.all<Color>(
                                            Colors.transparent),
                                    padding:
                                        MaterialStateProperty.all<EdgeInsets>(
                                            EdgeInsets.only(right: 10)),
                                    minimumSize:
                                        MaterialStateProperty.all<Size>(
                                            Size.zero),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ))
                              : SizedBox(),
                          model.updateZsButton == 1
                              ? GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () => {
                                    Navigator.of(context)
                                        .push(MaterialPageRoute(
                                          builder: (context) => ShopBinDing(
                                            data: model,
                                            type: 2,
                                          ),
                                          fullscreenDialog: true,
                                        ))
                                        .then((value) => {
                                              if (value['type'] == 2)
                                                {
                                                  setState(() {
                                                    model.claimButton = 0;
                                                    model.bindZsAccount =
                                                        value['name'];
                                                    model.visitButton =
                                                        value['data'];
                                                  })
                                                }
                                            })
                                  },
                                  child: Image.asset(
                                    'assets/images/business/business-shop-item-edit.png',
                                    width: 16,
                                  ),
                                )
                              : SizedBox(),
                        ],
                      )),
                ],
              ),
            ),
            model.visitButton != 0
                ? TextButton(
                    onPressed: () {
                      if (model.visitButton == 2) {
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) =>
                              ShopAccompanyVisit(shopInfo: model),
                          fullscreenDialog: true,
                        ));
                        return;
                      }
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => ShopVisit(shopInfo: model),
                        fullscreenDialog: true,
                      ));
                    },
                    child: Container(
                      height: 30,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: Color(0xFF00B955),
                      ),
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child: Center(
                        child: Text(
                          model.visitButton == 2 ? '陪访' : '拜访',
                          style:
                              TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
                        ),
                      ),
                    ),
                    style: ButtonStyle(
                      overlayColor:
                          MaterialStateProperty.all<Color>(Colors.transparent),
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          EdgeInsets.only(right: 10)),
                      minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ))
                : SizedBox(),
          ],
        ),
      );
    });
  }
}

class PhoneList {
  static Future<T?> showContactListView<T>({
    required ShopListDataItemModel? data,
    required BuildContext context,
    required List<ShopContactModel> contactList,
  }) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        double maxHeight = MediaQuery.of(context).size.height -
            233 +
            MediaQuery.of(context).viewPadding.bottom;
        double curHeight = paddingBottom(context) + 104;
        curHeight += contactList.length * 70;
        if (curHeight > maxHeight) {
          curHeight = maxHeight;
        }
        return Container(
          height: curHeight,
          decoration: BoxDecoration(
            color: Color(0xFFF2F2F2),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(18),
              topRight: Radius.circular(18),
            ),
          ),
          child: Column(
            children: [
              Container(
                height: 50,
                child: Row(
                  children: [
                    SizedBox(width: 30),
                    Expanded(
                      child: Text(
                        '客户联系人',
                        style: TextStyle(
                            color: Color(0xFF383841),
                            fontSize: 17,
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Image.asset(
                        'assets/images/customer/customer_phone_close.png',
                        width: 20,
                        height: 20,
                      ),
                      style: ButtonStyle(
                        overlayColor: MaterialStateProperty.all<Color>(
                            Colors.transparent),
                        padding: MaterialStateProperty.all<EdgeInsets>(
                            EdgeInsets.only(right: 10)),
                        minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      children: contactList
                          .map((model) => Container(
                                padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Color(0xFFFFFFFF),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
                                  height: 60,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  model.contactName ?? '',
                                                  style: TextStyle(
                                                    color: Color(0xFF222222),
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                (model.isKp == 1)
                                                    ? Text(
                                                        "（老板KP）",
                                                        style: TextStyle(
                                                          color:
                                                              Color(0xFF777777),
                                                          fontSize: 14,
                                                        ),
                                                      )
                                                    : SizedBox(),
                                              ],
                                            ),
                                            SizedBox(height: 5),
                                            Text(
                                              '电话：${model.contactPhone ?? '--'}',
                                              style: TextStyle(
                                                  color: Color(0xFF383841),
                                                  fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Spacer(),
                                      TextButton(
                                        onPressed: () {
                                          var body = {
                                            "merchantManagerId": data?.id ?? '',
                                            "contactName": model.contactName
                                          };
                                          var bodyStr = json.encode(body);
                                          XYYContainer.bridgeCall('call_phone',
                                              parameters: {
                                                'mobile':
                                                    '${model.contactPhone}',
                                                'merchantId': '-99',
                                                'merchantName': bodyStr,
                                                'addSource': enumToString(
                                                    CallAddSource
                                                        .privateDetail),
                                              });
                                        },
                                        child: Image.asset(
                                          'assets/images/business/business-shop-detail-phone.png',
                                          width: 20,
                                          height: 20,
                                        ),
                                        style: ButtonStyle(
                                          overlayColor:
                                              MaterialStateProperty.all<Color>(
                                                  Colors.transparent),
                                          padding: MaterialStateProperty.all<
                                                  EdgeInsets>(
                                              EdgeInsets.only(right: 15)),
                                          minimumSize:
                                              MaterialStateProperty.all<Size>(
                                                  Size.zero),
                                          tapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                color: Color(0xFFF2F2F2),
                child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      Navigator.of(context)
                          .push(MaterialPageRoute(
                            builder: (context) =>
                                ShopAddContacts(shopInfo: data),
                            fullscreenDialog: true,
                          ))
                          .then((value) => {});
                    },
                    child: Container(
                      width: double.infinity,
                      height: 42,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Color(0xFF00B955),
                      ),
                      child: Center(
                        child: Text(
                          '添加联系人',
                          style: TextStyle(
                              color: Color(0xFFFFFFFF),
                              fontSize: 16,
                              fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    style: ButtonStyle(
                      overlayColor:
                          MaterialStateProperty.all<Color>(Colors.transparent),
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          EdgeInsets.all(0)),
                      minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )),
              )
            ],
          ),
        );
      },
    );
  }

  static double paddingBottom(BuildContext context) {
    var bottom = MediaQuery.of(context).viewPadding.bottom;
    if (bottom == 0) {
      return 20;
    }
    return bottom;
  }
}
