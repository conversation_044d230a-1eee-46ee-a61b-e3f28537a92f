import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_list_param_data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/shop_claim_item.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ShopFilterListPage extends CustomerBasePage {
  final String? title;
  final String? indexCardTypeCode;
  final String? timePeriod;
  ShopFilterListPage({this.title,this.indexCardTypeCode,this.timePeriod = ''});

  @override
  CustomerBasePageState initState() {
    return ShopFilterListPageState();
  }
}

class ShopFilterListPageState extends CustomerBasePageState<ShopFilterListPage> {
  // 筛选参数
  ShopFilterListModel? conditionModel;

  // 接口入参存储
  ShopListParamData paramModel = ShopListParamData();

  List<ShopListDataItemModel> dataSource = [];
  String? locationLat;
  String? locationLong;

  /// 用来判断是否是第一次加载
  bool isFirstLoad = true;

  bool isLastPage = true;

  @override
  void initState() {
    refreshList();
    super.initState();
  }
  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        super.buildWidget(context),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Future<void> refreshList() async {
    EasyLoading.show();
    page = 1;
    // 每次刷新时 重新请求定位信息
    await requestLocation();

    // 请求列表数据
    requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    requestListData();
  }

  Future<void> requestLocation() async {
    if (isFirstLoad) {
      /// 延迟100毫秒 进行定位请求，预防首页跳转过来传参未接收
      await Future.delayed(Duration(milliseconds: 100));
    }

    var result = await XYYContainer.locationChannel.locate();
    paramModel.curLatitudeStr = result.latitude;
    paramModel.curLongitudeStr = result.longitude;
    locationLat = result.latitude;
    locationLong = result.longitude;
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return paramModel.curLatitudeStr?.isEmpty == false &&
        paramModel.curLongitudeStr?.isEmpty == false;
  }

  /// 请求私海列表数据
  void requestListData() async {
    var params = paramModel.toJson();
    if (widget.indexCardTypeCode?.isNotEmpty ?? false) {
      params['indexCardTypeCode'] = widget.indexCardTypeCode ?? null;
    }
    // 移除荷叶使用的参数
    params.remove("sortType");
    params['pageSize'] = 10;
    params['pageNo'] = page;
    if (widget.timePeriod != '') {
      params['timePeriod'] = widget.timePeriod;
    }
    var result =
        await NetworkV2<ShopListDataModel>(ShopListDataModel()).requestDataV2(
      '/mop/merchant/manager/claim/page',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
    );
    if (isFirstLoad != true && page == 1) {
      dataSource = [];
    }

    /// 设置第一次加载完成
    isFirstLoad = false;

    EasyLoading.dismiss();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.result ?? [];
        if (page == 1) {
          dataSource = rows;
        } else {
          dataSource.addAll(rows);
        }
        isLastPage = data?.lastPage ?? true;
        page = isLastPage ? page : page + 1;
        setState(() {});
      }
    }
  }

    @override
  String getTitleName() {
    return widget.title ?? '列表';
  }

    @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      backgroundColor: Colors.white,
      leftButtonColor: Colors.black,
      titleTextColor: Colors.black,
    );
  }

  @override
  bool get isSearch => true;

  @override
  bool get isNoMore => isLastPage;

  @override
  int get itemCount => dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        ShopListDataItemModel model = dataSource[index];
        return ShopClaimItem(
          model,
          hasLocation: hasLocation,
          serviceInterface: interface,
          locationLat: locationLat,
          locationLong: locationLong,
        );
      };

  @override
  List<CommonDropConfigModel> get filterConfig => [];

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) {}
}
