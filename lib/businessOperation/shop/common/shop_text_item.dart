import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

typedef ShopTextItemShowKeyboard = void Function(
    bool isShow, bool isCustomKeyboard);

// ignore: must_be_immutable
class ShopTextItem extends StatefulWidget {
  final String? title;
  final String placehold;
  final String itemKey;
  String? content;
  final int? maxLenght;
  final ScheduleValueChange? valueChange;
  final ShopTextItemShowKeyboard? keyboardShow;
  final bool? showTitleColor;
  

  ShopTextItem({
    this.title,
    required this.placehold,
    required this.itemKey,
    this.maxLenght,
    this.content,
    this.valueChange,
    this.keyboardShow,
    this.showTitleColor,
    Key? key
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ShopTextItemState();
  }
}

class ShopTextItemState extends State<ShopTextItem> {
  FocusNode _focusNode = FocusNode();

  bool isCustomKeyboard = false;

  late TextEditingController _editController;

  @override
  void initState() {
    
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _editController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    _editController = TextEditingController.fromValue(
      TextEditingValue(
        text: widget.content ?? "",
        selection: TextSelection.fromPosition(
          ///用来设置文本的位置
          TextPosition(
            affinity: TextAffinity.downstream,
            // 光标向后移动的长度
            offset: widget.content?.length ?? 0,
          ),
        ),
      ),
    );
    return Container(
        color: Color(0xFFFFFFFF),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.only(left: 10, right: 10),
              height: 30,
              alignment: Alignment.centerLeft,
              color: (widget.showTitleColor ?? true) ? Color(0xFFEFEFF4) : null,
              child: Text(
                widget.title ?? "拜访总结",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            Container(
              height: 88,
              padding: EdgeInsets.only(left: 10, right: 10, bottom: 10, top: 5),
              child: TextFormField(
                  focusNode: _focusNode,
                  showCursor: true,
                  readOnly: this.isCustomKeyboard,
                  inputFormatters: [
                    CustomizedTextInputFormatter(
                        filterPattern: RegExp(
                            r'[\u4e00-\u9fa5，,。.：:（）()?？!！a-zA-Z0-9⼀⼁⼃⼂乛\r\n]*')),
                  ],
                  controller: _editController,
                  onChanged: (text) {
                    widget.content = text;
                    widget.valueChange!(widget.itemKey, text);
                  },
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF292933),
                  ),
                  scrollPadding: EdgeInsets.zero,
                  decoration: InputDecoration(
                    counterStyle: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9494A6),
                    ),
                    hintText: widget.placehold,
                    hintStyle: TextStyle(
                      fontSize: 15,
                      color: Color(0xFF9494A6),
                    ),
                    hintMaxLines: 3,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLength: widget.maxLenght, // 最大长度
                  maxLengthEnforcement: MaxLengthEnforcement
                      .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                  maxLines: 8,
                  keyboardAppearance: Brightness.light,
              ),
            ),
          ],
        ),
    );
  }
}
