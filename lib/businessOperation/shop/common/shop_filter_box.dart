import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

// 单选下拉 菜单
class ShopFilterBox extends CommonFilterPopupBase {
  // 店铺类型
  final List<ShopFilterModel> shopBusinessAttributeList;
  // 合同签署
  final List<ShopFilterModel> mopEnterpriseAgreementSignStatusList;
  // 保证金
  final List<ShopFilterModel> mopBondStatusList;
  // erp对接
  final List<ShopFilterModel> mopErpJointStatusList;
  final String? businessAttributeVal;
  final String? fddEnterpriseAgreementStatusVal;
  final String? bondStatusVal;
  final String? erpJointStatusListVal;
  final Function(
          ShopFilterModel, ShopFilterModel, ShopFilterModel, ShopFilterModel)
      selectAction;

  ShopFilterBox({
    required double distance,
    required this.shopBusinessAttributeList,
    required this.mopEnterpriseAgreementSignStatusList,
    required this.mopBondStatusList,
    required this.mopErpJointStatusList,
    required this.selectAction,
    this.businessAttributeVal,
    this.fddEnterpriseAgreementStatusVal,
    this.bondStatusVal,
    this.erpJointStatusListVal,
  }) : super(distance: distance);

  @override
  CommonFilterPopupBaseState createState() {
    return ShopFilterBoxState();
  }
}

class ShopFilterBoxState extends CommonFilterPopupBaseState<ShopFilterBox> {
  String? val1;
  String? val2;
  String? val3;
  String? val4;
  @override
  void initState() {
    val1 = widget.businessAttributeVal ?? 'quanbu';
    val2 = widget.fddEnterpriseAgreementStatusVal ?? 'quanbu';
    val3 = widget.bondStatusVal ?? 'quanbu';
    val4 = widget.erpJointStatusListVal ?? 'quanbu';
    super.initState();
  }

  @override
  Widget buildContent() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      color: Color(0xFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text(
              '店铺类型',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222)),
            ),
          ),
          Wrap(
            spacing: 8,
            runSpacing: 5,
            children: [
              ...[
                ShopFilterModel(dictLabel: '全部', dictValue: 'quanbu'),
                ...widget.shopBusinessAttributeList
              ]
                  .map((e) => GestureDetector(
                        onTap: () {
                          val1 = e.dictValue;
                          setState(() {});
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFilterItem(
                          title: e.dictLabel,
                          isSelected: e.dictValue == val1,
                        ),
                      ))
                  .toList(),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text(
              '合同签署',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222)),
            ),
          ),
          Wrap(
            spacing: 8,
            runSpacing: 5,
            children: [
              ...[
                ShopFilterModel(dictLabel: '全部', dictValue: 'quanbu'),
                ...widget.mopEnterpriseAgreementSignStatusList
              ]
                  .map((e) => GestureDetector(
                        onTap: () {
                          val2 = e.dictValue;
                          setState(() {});
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFilterItem(
                          title: e.dictLabel,
                          isSelected: e.dictValue == val2,
                        ),
                      ))
                  .toList(),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text(
              '保证金',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222)),
            ),
          ),
          Wrap(
            spacing: 8,
            runSpacing: 5,
            children: [
              ...[
                ShopFilterModel(dictLabel: '全部', dictValue: 'quanbu'),
                ...widget.mopBondStatusList
              ]
                  .map((e) => GestureDetector(
                        onTap: () {
                          val3 = e.dictValue;
                          setState(() {});
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFilterItem(
                          title: e.dictLabel,
                          isSelected: e.dictValue == val3,
                        ),
                      ))
                  .toList(),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Text(
              'ERP对接',
              style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222)),
            ),
          ),
          Wrap(
            spacing: 8,
            runSpacing: 5,
            children: [
              ...[
                ShopFilterModel(dictLabel: '全部', dictValue: 'quanbu'),
                ...widget.mopErpJointStatusList
              ]
                  .map((e) => GestureDetector(
                        onTap: () {
                          val4 = e.dictValue;
                          setState(() {});
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFilterItem(
                          title: e.dictLabel,
                          isSelected: e.dictValue == val4,
                        ),
                      ))
                  .toList()
            ],
          ),
          Container(
            padding: EdgeInsets.only(top: 10),
            decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: TextButton(
                        onPressed: () {
                          widget.selectAction(
                              ShopFilterModel(dictValue: 'quanbu'),
                              ShopFilterModel(dictValue: 'quanbu'),
                              ShopFilterModel(dictValue: 'quanbu'),
                              ShopFilterModel(dictValue: 'quanbu'));
                          EasyPopup.pop(context);
                        },
                        child: Container(
                          height: 42,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(21),
                            border:
                                Border.all(color: Color(0xFFCACACA), width: 1),
                          ),
                          padding: EdgeInsets.only(left: 10, right: 10),
                          child: Center(
                            child: Text(
                              '重置',
                              style: TextStyle(
                                  color: Color(0xFF222222), fontSize: 14),
                            ),
                          ),
                        ),
                        style: ButtonStyle(
                          overlayColor: MaterialStateProperty.all<Color>(
                              Colors.transparent),
                          padding: MaterialStateProperty.all<EdgeInsets>(
                              EdgeInsets.only(right: 10)),
                          minimumSize:
                              MaterialStateProperty.all<Size>(Size.zero),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ))),
                SizedBox(width: 10),
                Expanded(
                    flex: 1,
                    child: TextButton(
                        onPressed: () {
                          widget.selectAction(
                              ShopFilterModel(dictValue: val1),
                              ShopFilterModel(dictValue: val2),
                              ShopFilterModel(dictValue: val3),
                              ShopFilterModel(dictValue: val4));
                          EasyPopup.pop(context);
                        },
                        child: Container(
                          height: 42,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(21),
                            color: Color(0xFF00B955),
                          ),
                          padding: EdgeInsets.only(left: 10, right: 10),
                          child: Center(
                            child: Text(
                              '确认',
                              style: TextStyle(
                                  color: Color(0xFFFFFFFF), fontSize: 14),
                            ),
                          ),
                        ),
                        style: ButtonStyle(
                          overlayColor: MaterialStateProperty.all<Color>(
                              Colors.transparent),
                          padding: MaterialStateProperty.all<EdgeInsets>(
                              EdgeInsets.only(right: 10)),
                          minimumSize:
                              MaterialStateProperty.all<Size>(Size.zero),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ))),
              ],
            ),
          )
        ],
      ),
    );
  }
}

// 选项item
class CustomerFilterItem extends StatelessWidget {
  final String? title;
  final bool isSelected;

  CustomerFilterItem({required this.title, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 76,
        height: 28,
        decoration: BoxDecoration(
            color: isSelected ? Colors.white : Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(17),
            border: Border.all(
              width: 1,
              color: isSelected ? Color(0xFF00B955) : Colors.transparent,
            )),
        child: Center(
          child: Text(
            title ?? '--',
            style: TextStyle(
              color: isSelected ? Color(0xFF00B955) : Color(0xFF444444),
              fontSize: 14,
            ),
          ),
        ));
  }
}
