import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/business_shop_page.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/common/shop_filter_list.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/business-mine-page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class BusinessMainPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return BusinessMainState();
  }
}

class BusinessMainState extends BaseState<BusinessMainPage>
    with SingleTickerProviderStateMixin, EventBusObserver {
  int selectedIndex = 0;

  late CustomTabController _controller;

  DateTime? lastPopTime;

  int customerExtraType = 0;

  @override
  void initState() {
    this._controller = CustomTabController(
      length: 5,
      vsync: this,
      animationDuration: Duration.zero,
    );

    /// 订阅切换tab的通知
    this.eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.CHANGE_TAB_INDEX,
        callback: (index) {
          this._controller.index = index;
          this.selectedIndex = index;
          setState(() {});
        });

    this.eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.CHANGE_FILTER_POI_REGISTER,
        callback: (index) {
          customerExtraType = index;
        });

    this.requestUserAuth();
    super.initState();
    eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.OPEN_MAIN,
        callback: _openMainCallback);
  }

  void _openMainCallback(dynamic arg) {
    if (arg is int) {
      Navigator.of(context).popUntil((route) => route.settings.name == "/main");
      _controller.index = arg;
    }
  }

  @override
  void dispose() {
    this.eventBus.removeListener(observer: this);
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
    ));
    return WillPopScope(
      onWillPop: () async {
        if (lastPopTime == null ||
            DateTime.now().difference(lastPopTime!) > Duration(seconds: 1)) {
          lastPopTime = DateTime.now();

          showToast("再按一次退出");
          return Future.value(false);
        } else {
          lastPopTime = DateTime.now();
          // 退出app
          return Future.value(true);
        }
      },
      child: Scaffold(
        backgroundColor: Color(0xFFFFFFFF),
        bottomNavigationBar: SafeArea(
          child: Container(
            color: Color(0xFFFFFFFF),
            child: SizedBox(
              height: 49,
              child: CustomTabBar(
                controller: this._controller,
                isScrollable: false,
                indicatorColor: Colors.transparent,
                indicatorWeight: 0.1,
                // 指示器高度不能设置为0
                indicatorSize: CustomTabBarIndicatorSize.label,
                unselectedLabelColor: Color(0xFF9494A6),
                unselectedLabelStyle: TextStyle(fontSize: 8),
                labelColor: GlobalData.selectedColor,
                labelStyle: TextStyle(fontSize: 8),
                tabs: this.getTabs(),
                onTap: (index) {
                  this.selectedIndex = index;

                  setState(() {});
                },
              ),
            ),
          ),
        ),
        body: CustomTabBarView(
          physics: new NeverScrollableScrollPhysics(),
          controller: this._controller,
          children: [
            Container(child: BusinessHomePage()),
            Container(child: BusinessShopPage()),
            Container(),
            Container(),
            Container(child: BusinessMinePage()),
          ],
        ),
      ),
    );
  }
  List<Widget> getTabs() {
    return [
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 0
              ? 'assets/images/business/business-index-selected.png'
              : 'assets/images/business/business-index.png'),
          size: 18,
        ),
        iconMargin: EdgeInsets.only(bottom: 4),
        text: "首页",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 1
              ? 'assets/images/business/business-shop-selected.png'
              : 'assets/images/business/business-shop.png'),
          size: 18,
        ),
        iconMargin: EdgeInsets.only(bottom: 4),
        text: "店铺",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 2
              ? 'assets/images/business/business-order-selected.png'
              : 'assets/images/business/business-order.png'),
          size: 18,
        ),
        iconMargin: EdgeInsets.only(bottom: 4),
        text: "订单",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 3
              ? 'assets/images/business/business-product-selected.png'
              : 'assets/images/business/business-product.png'),
          size: 18,
        ),
        iconMargin: EdgeInsets.only(bottom: 4),
        text: "商品",
      ),
      Tab(
        icon: ImageIcon(
          AssetImage(this.selectedIndex == 4
              ? 'assets/images/business/business-mine-selected.png'
              : 'assets/images/business/business-mine.png'),
          size: 18,
        ),
        iconMargin: EdgeInsets.only(bottom: 4),
        text: "我的",
      ),
    ];
  }

  // Request
  // 用户身份 (hy, ybm)
  void requestUserAuth() async {
    var result = await NetworkV2<UserAuthModel>(UserAuthModel())
        .requestDataV2("lz/getRoles");
    if (result.isSuccess == true) {
      // print(result.getListData());
      List<UserAuthModel> data = result.getListData() ?? [];
      List<dynamic> list = data.map((e) => e.toJson()).toList();
      String roleJSON = jsonEncode(list);
      XYYContainer.storageChannel.put('roleJSON', roleJSON);
    }
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}
