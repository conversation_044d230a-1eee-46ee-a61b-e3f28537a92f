import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class RankIcon extends StatelessWidget {
  int level;
  double size;
  List<String> rankImgList = [
    'assets/images/business/rank-icon-one.png',
    'assets/images/business/rank-icon-two.png',
    'assets/images/business/rank-icon-three.png',
    'assets/images/business/rank-icon-normal.png'
  ];
  RankIcon({
    required this.level,
    this.size = 20
  });
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      width: size,
      height: size / 0.84,
      child: Stack(
        children: [
          Center(
            child: Image(
              image: AssetImage(rankImgList[level > 3 ? 3 : level]),
              width: size,
              height: size / 0.84,
            ),
          ),
          Center(
            child: Text(
              (level + 1).toString(),
              style: TextStyle(
                fontSize: size / (level + 1 >= 10 ? 1.4 : 1.2),
                fontWeight: FontWeight.w600,
                color: Colors.white
              ),
            ),
          )
        ],
      ),
    );
  }
}