import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
class CustomerSwitchState extends ChangeNotifier {
  /*bool _status = true;*/
  double _width = 0;
  /*updateStatus(bool status) {
    this._status = status;
    notify();
  }*/
  updateWidth(double width) {
    this._width = width;
    notify();
  }
  /*bool get status => _status;*/
  double get width => _width;
  //通知监听
 notify() {
   if (this.hasListeners) {
     notifyListeners();
   }
 }
}
class CustomerSwitch extends StatelessWidget {
  bool status;
  final String? openLabel;  //左边
  final String? closeLabel;   //右边
  final double? size;
  final double? labelSize;
  final Color? openColorWhenSelectedOpen;
  final Color? closeColorWhenSelectedOpen;
  final Color? openColorWhenSelectedClose;
  final Color? closeColorWhenSelectedClose;
  final Color? bgColorWhenSelectedOpen;
  final Color? bgColorWhenSelectedClose;
  final double? gap;
  final Color? barColor;
  final void Function(bool status) onChange;
  CustomerSwitch({
    required this.status,
    this.closeLabel = '关闭',
    this.openLabel = '开启',
    this.size = 26,
    this.labelSize = 12,
    this.gap = 7,
    required this.onChange,
    this.closeColorWhenSelectedClose = const Color.fromRGBO(0, 185, 85, 1),
    this.openColorWhenSelectedClose = const Color.fromRGBO(170, 170, 170, 1.0),
    this.openColorWhenSelectedOpen = const Color.fromRGBO(0, 185, 85, 1),
    this.closeColorWhenSelectedOpen = const Color.fromRGBO(255, 255, 255, 1),
    this.bgColorWhenSelectedClose = const Color.fromRGBO(241, 241, 241, 1.0),
    this.bgColorWhenSelectedOpen = const Color.fromRGBO(0, 185, 85, 1),
    this.barColor = const Color.fromRGBO(255, 255, 255, 1)
  });

  @override
  Widget build(BuildContext context) {

    return ChangeNotifierProvider(
      create: (_) {
        CustomerSwitchState _dataState = new CustomerSwitchState();
        //_dataState.updateStatus(this.status);
        Future.delayed(Duration(milliseconds: 100), () {
          _dataState.updateWidth(context.size!.width / 2);
        });
        return _dataState;
      },
      child: Consumer<CustomerSwitchState>(
          builder: (context, model, child) {
            return AnimatedContainer(
              padding: EdgeInsets.all(1),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular((size! + 4) / 2),
                color: status ? bgColorWhenSelectedOpen : bgColorWhenSelectedClose,
              ),
              height: size,
              duration: Duration(
                  milliseconds: 200
              ),
              curve: Curves.easeInOut,
              child: Stack(
                children: [
                  Container(
                    width: model.width * 2,
                    child: AnimatedAlign(
                      widthFactor: 1,
                      heightFactor: 1,
                      alignment: status ? Alignment.centerLeft : Alignment.centerRight,
                      duration: Duration(
                          milliseconds: 200
                      ),
                      curve: Curves.easeInOut,
                      child: Container(
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular((size! + 4) / 2),
                          color: barColor,
                        ),
                        width: model.width + 2.5,
                        height: size,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      this.status = this.status == true ? false : true;
                      this.onChange(this.status);
                      //model.updateStatus(status);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(left: gap!, right: gap!),
                      child: Row(
                        children: [
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              openLabel!,
                              style: TextStyle(
                                  fontSize: labelSize,
                                  color: status ? openColorWhenSelectedOpen : openColorWhenSelectedClose
                              ),
                            ),
                          ),
                          SizedBox(width: gap),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Transform.translate(
                              offset: Offset(3, 0),
                              child: Text(
                                closeLabel!,
                                style: TextStyle(
                                    fontSize: labelSize,
                                    color: status ? closeColorWhenSelectedOpen : closeColorWhenSelectedClose
                                ),
                              ),
                            )
                          ),
                        ],
                      )
                    ),
                  ),
                ],
              ),
            );
          }
      ),
    );
  }
}