import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
class FlowLayoutState extends ChangeNotifier {
  double width = 0;
  updateWidth(double width) {
    this.width = width;
    notify();
  }


  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class FlowLayout extends StatelessWidget {
  int colCount;
  double gap;
  List<Widget> children;
  FlowLayout({
    this.colCount = 4,
    this.gap = 5,
    required this.children
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      child: ChangeNotifierProvider(
        create: (_) {
          FlowLayoutState _flowLayoutState = new FlowLayoutState();
          Future.delayed(Duration(milliseconds: 100), () {
            _flowLayoutState.updateWidth(context.size!.width);
          });
          return _flowLayoutState;
        },
        child: Consumer<FlowLayoutState>(
          builder: (context, model, child) {
            return _renderLayout(colCount, children, model);
          },
        ),
      ),
    );
  }

  Widget _renderLayout(int colCount, List<Widget> children, FlowLayoutState model) {
    List<Widget> colList = [];
    //确定item的宽高
    double itemWidth = (model.width - (colCount - 1) * gap) / colCount;
    /*for(int index = 0; index < colCount; index++) {
      List<Widget> curColumn = [];
      for(int i = index; i < children.length; i = i + colCount) {
        curColumn.add(Container(
          width: itemWidth,
          child: children[i],
        ));
        if ( i + colCount < children.length) {
          curColumn.add(SizedBox(width: gap));
        }
      }
      colList.add(Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: colList,
      ));
    }*/
    //渲染列,先向第一列塞数据，以此类推
    for (int index = 0; index < colCount; index++) {
      List<Widget> curRow = [];
      for(int i = index * colCount; i < (index + 1) * colCount && i < children.length; i++) {
        curRow.add(Container(
          width: itemWidth,
          child: children[i],
        ));
        if ( i < (index + 1) * colCount - 1) {
          curRow.add(SizedBox(width: gap));
        }
      }
      colList.add(Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: curRow,
      ));
      if (index + colCount < children.length) {
        colList.add(SizedBox(
          height: gap,
        ));
      }
    }
    /*for(int i = index * colCount; i < children.length && i < (index + 1) * colCount; i++) {
      curRow.add(Container(
        width: itemWidth,
        child: children[i],
      ));
      if ( i < (index + 1) * colCount - 1) {
        curRow.add(SizedBox(width: gap));
      }
    }*/
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: colList,
    );
  }
}