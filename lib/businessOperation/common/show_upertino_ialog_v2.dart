import 'package:flutter/cupertino.dart';

Future<T?> showCommonAlertV2<T>({
  required BuildContext context,
  String? title,
  String? content,
  TextAlign? contentTextAlign,
  required List<CommonAlertActionV2> actions,
  bool barrierDismissible = false
}) {
  // title 或者 content 不为空
  assert(title != null || content != null);
  // 最少有一个按钮
  assert(actions.length > 0);

  return showCupertinoDialog(
    context: context,
    builder: (context) {
      return _CommonAlertContentWidget(
        title: title,
        content: content,
        actions: actions,
        contentTextAlign: contentTextAlign,
      );
    },
    barrierDismissible: barrierDismissible,
  );
}

enum CommonAlertActionStyle { normal, cancle }

class CommonAlertActionV2 extends StatelessWidget {
  final CommonAlertActionStyle style;
  final String title;
  final VoidCallback? onPressed;

  final Map<CommonAlertActionStyle, TextStyle> themes = {
    CommonAlertActionStyle.normal: TextStyle(
      color: Color(0xFF00B377),
      fontSize: 17,
      fontWeight: FontWeight.w500,
    ),
    CommonAlertActionStyle.cancle: TextStyle(
      color: Color(0xFF292933),
      fontSize: 17,
      fontWeight: FontWeight.w400,
    ),
  };

  CommonAlertActionV2({
    required this.title,
    this.onPressed,
    this.style = CommonAlertActionStyle.normal,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        if (this.onPressed != null) {
          this.onPressed!();
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        constraints: BoxConstraints(minHeight: 50),
        child: Center(
          child: Text(
            this.title,
            style: themes[this.style],
          ),
        ),
      ),
    );
  }
}

class _CommonAlertContentWidget extends StatelessWidget {
  final String? title;
  final String? content;
  final TextAlign? contentTextAlign;
  final List<CommonAlertActionV2> actions;

  _CommonAlertContentWidget({
    this.title,
    this.content,
    this.contentTextAlign,
    this.actions = const [],
  });

  Widget _buildTitle() {
    if (this.title != null) {
      return Container(
        padding: EdgeInsets.fromLTRB(30, 20, 30, this.content != null ? 0 : 10),
        child: Text(
          this.title!,
          style: TextStyle(
              color: Color(0xFF292933),
              fontSize: 17,
              fontWeight: FontWeight.w500),
        ),
      );
    } else {
      return Container();
    }
  }

  Widget _buildContent(screeHeight) {
    if (this.content != null) {
      return Container(
        constraints: BoxConstraints(
          maxHeight: screeHeight,  // 最大高度设置为屏幕高度的 70%
        ),
        padding: EdgeInsets.fromLTRB(30, this.title != null ? 10 : 20, 30, 20),
        child:
        SingleChildScrollView(
        child: Text(
          this.content!,
          textAlign: contentTextAlign ?? TextAlign.center,
          style: TextStyle(
              color: Color(0xFF676773),
              fontSize: 14,
              fontWeight: FontWeight.w400),
          ),
         ),
        );
    } else {
      return Container();
    }
  }

  Widget _buildActions() {
    if (this.actions.length == 1) {
      return this.actions[0];
    }
    List<Widget> items = [];
    this.actions.asMap().forEach((index, value) {
      if (index != 0) {
        items.add(Container(
          margin: EdgeInsets.only(top: 10),
          color: Color(0xFFE1E1E5),
          width: 0.5,
        ));
      }
      items.add(Expanded(child: value));
    });
    return Container(
      child: IntrinsicHeight(child: Row(children: items)),
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screeHeight= MediaQuery.of(context).size.height * 0.7;
    return Center(
      child: Container(
        constraints: BoxConstraints(
          minWidth: screenWidth - 100,
          maxWidth: screenWidth - 100,
        ),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTitle(),
            _buildContent(screeHeight),
            Container(
              color: Color(0xFFE1E1E5),
              height: 0.5,
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }
}
