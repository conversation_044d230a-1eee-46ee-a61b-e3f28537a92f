import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
class HorizontalTabsOptions  {
  String label;
  dynamic value;
  HorizontalTabsOptions(this.label, this.value);
}
class HorizontalTabsSelectDataState extends ChangeNotifier {

  int _curIndex = 0;
  int temp = -1;

  updateCurIndex(int index) {
    this._curIndex = index;
    notify();
  }

  int get curIndex => _curIndex;

  notify() {
    if (this.hasListeners) {
      notifyListeners();
    }
  }
}
class HorizontalTabsSelect extends StatelessWidget {
  List<HorizontalTabsOptions> options;
  void Function(dynamic value)? onTap;
  double size;
  Color selectedBgColor;
  Color unSelectedBgColor;
  Color selectedTextColor;
  Color unSelectedTextColor;
  Color selectedBorderColor;
  Color unSelectedBorderColor;
  HorizontalTabsSelect({
    required this.options,
    this.onTap,
    this.size = 50,
    this.selectedBgColor = const Color.fromRGBO(233, 255, 243, 1.0),
    this.unSelectedBgColor = const Color.fromRGBO(249, 249, 249, 1.0),
    this.selectedTextColor = const Color.fromRGBO(0, 185, 85, 1),
    this.unSelectedTextColor = const Color(0xFF9494A6),
    this.selectedBorderColor = const Color.fromRGBO(0, 185, 85, 1),
    this.unSelectedBorderColor = const Color.fromRGBO(249, 249, 249, 1.0)
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) {
        HorizontalTabsSelectDataState _horizontalTabsSelectDataState = new HorizontalTabsSelectDataState();
        return _horizontalTabsSelectDataState;
      },
      child: Consumer<HorizontalTabsSelectDataState>(
        builder: (context, model, child) {
          return Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(top: 3, bottom: 3),
            child: SingleChildScrollView(
              physics: BouncingScrollPhysics(),
              scrollDirection: Axis.horizontal,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ...options.asMap().keys.map((index) {
                    if (options.length != model.temp && index == 0) {
                      //数组长度有改变
                      model.temp = options.length;
                      model.updateCurIndex(index);
                    }
                    return GestureDetector(
                      onTap: () {
                        if (onTap != null) {
                          onTap!(options[index].value);
                        }
                        model.updateCurIndex(index);
                      },
                      child: AnimatedContainer(
                          height: 26,
                          constraints: BoxConstraints(minWidth: size),
                          margin: EdgeInsets.all(5),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(Radius.circular(13)),
                              color: index == model.curIndex ? selectedBgColor : unSelectedBgColor,
                              border: Border.all(
                                  width: 1,
                                  color: index == model.curIndex ? selectedBorderColor : unSelectedBorderColor,
                                  style: BorderStyle.solid
                              )
                          ),
                          padding: EdgeInsets.only(left: 10, right: 10),
                          duration: Duration(milliseconds: 300),
                          curve: Curves.fastOutSlowIn,
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              options[index].label,
                              style: TextStyle(
                                  color: index == model.curIndex ? selectedTextColor : unSelectedTextColor,
                                  fontSize: 12
                              ),
                            ),
                          )
                      ),
                    );
                  })
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}