import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BaseContainer extends StatelessWidget {
  Widget child;
  Color? color;
  BaseContainer({ required this.child, this.color });
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Stack(
      children: [
        Container(
          color: color ?? Colors.white,
          height: MediaQuery.of(context).size.height,
        ),
        SafeArea(child: child)
      ],
    );
  }

}