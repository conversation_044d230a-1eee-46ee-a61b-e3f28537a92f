import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class GlobalData {
  static Color selectedColor = Color.fromRGBO(0, 185, 85, 1);
  static Color unSelectedColor = Color(0xFF9494A6);
  static Color unSelectedTabBgColor = Color.fromRGBO(249, 249, 249, 1.0);
  static Color selectedTabBgColor = Color.fromRGBO(233, 255, 243, 1.0);
  static Color normalViewBgColor = Color(0xFFF9F9F9);
  static Color dataUpColor = Color.fromRGBO(229, 32, 32, 1.0);
  static Color dataDownColor = Color.fromRGBO(32, 229, 123, 1.0);
  static Color textNormalColor = Color.fromRGBO(53, 53, 53, 1.0);
  //卡片风格的padding
  static EdgeInsets normalCardPadding = EdgeInsets.all(7);
}

///tabBar指示器圆角
///参考UnderlineTabIndicator,仅仅修改画笔StrokeCap.square 为 StrokeCap.round
class CustomUnderlineTabIndicator extends Decoration {
  final double indicatorWidth;

  /// Create an underline style selected tab indicator.
  ///
  /// The [borderSide] and [insets] arguments must not be null.
  CustomUnderlineTabIndicator({
    this.borderSide = const BorderSide(width: 2.0, color: Colors.white),
    this.insets = EdgeInsets.zero,
    this.indicatorWidth = 16
  })  : assert(borderSide != null),
        assert(insets != null);

  /// The color and weight of the horizontal line drawn below the selected tab.
  final BorderSide borderSide;

  /// Locates the selected tab's underline relative to the tab's boundary.
  ///
  /// The [TabBar.indicatorSize] property can be used to define the tab
  /// indicator's bounds in terms of its (centered) tab widget with
  /// [TabBarIndicatorSize.label], or the entire tab with
  /// [TabBarIndicatorSize.tab].
  final EdgeInsetsGeometry insets;

  @override
  Decoration? lerpFrom(Decoration? a, double t) {
    if (a is UnderlineTabIndicator) {
      return UnderlineTabIndicator(
        borderSide: BorderSide.lerp(a.borderSide, borderSide, t),
        insets: EdgeInsetsGeometry.lerp(a.insets, insets, t)!,
      );
    }
    return super.lerpFrom(a, t);
  }

  @override
  Decoration? lerpTo(Decoration? b, double t) {
    if (b is UnderlineTabIndicator) {
      return UnderlineTabIndicator(
        borderSide: BorderSide.lerp(borderSide, b.borderSide, t),
        insets: EdgeInsetsGeometry.lerp(insets, b.insets, t)!,
      );
    }
    return super.lerpTo(b, t);
  }


  @override
  _UnderlinePainter createBoxPainter([VoidCallback? onChanged]) {
    return _UnderlinePainter(this, onChanged);
  }

  Rect _indicatorRectFor(Rect rect, TextDirection textDirection) {
    assert(rect != null);
    assert(textDirection != null);
    final Rect indicator = insets.resolve(textDirection).deflateRect(rect);
    return Rect.fromLTWH(
      (indicator.left + indicator.right - this.indicatorWidth) / 2 ,
      indicator.bottom - borderSide.width,
      indicatorWidth,
      borderSide.width,
    );
  }

  @override
  Path getClipPath(Rect rect, TextDirection textDirection) {
    return Path()..addRect(_indicatorRectFor(rect, textDirection));
  }
}
class _UnderlinePainter extends BoxPainter {
  _UnderlinePainter(this.decoration, VoidCallback? onChanged)
      : assert(decoration != null),
        super(onChanged);

  final CustomUnderlineTabIndicator? decoration;

  BorderSide? get borderSide => decoration?.borderSide;

  EdgeInsetsGeometry? get insets => decoration?.insets;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration? configuration) {
    assert(configuration != null);
    assert(configuration!.size != null);
    final Rect rect = offset & configuration!.size!;
    final TextDirection textDirection = configuration.textDirection!;
    final Rect indicator = decoration!._indicatorRectFor(rect, textDirection)
        .deflate(decoration!.borderSide.width / 2.0);
    final Paint paint = decoration!.borderSide.toPaint();
    paint.strokeWidth = 5;
    paint.strokeCap = StrokeCap.round; //主要是修改此处  圆角
    canvas.drawLine(indicator.bottomLeft, indicator.bottomRight, paint);
  }
}

///吸顶组件
class SliverDelegate extends SliverPersistentHeaderDelegate {
  double maxHeight; //导航栏最大高度
  double minHeight; //导航栏最小高度
  Widget tabBar;
  Color? tabBarColor;
  SliverDelegate({required this.maxHeight, required this.minHeight, required this.tabBar, Color? tabBarColor});
  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    // TODO: implement build
    return Container(
      alignment: Alignment.centerLeft,
      height: this.maxHeight,
      margin: EdgeInsets.only(bottom: 10),
      color: tabBarColor != null ? tabBarColor : Colors.transparent,
      child: this.tabBar,
    );
  }

  @override
  // TODO: implement maxExtent
  double get maxExtent => this.maxHeight;

  @override
  // TODO: implement minExtent
  double get minExtent => this.minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    // TODO: implement shouldRebuild
    return false;
  }

}
class SliverTabBarOnTop extends StatelessWidget {
  Widget? tabBar;
  late List<Widget> body;
  Color? tabBarColor;
  Color? color;
  ScrollController? controller;
  SliverTabBarOnTop({Widget? tabBar, required List<Widget> body, Color? tabBarColor, Color? color, Widget? header, ScrollController? controller}) {
    this.tabBar = tabBar;
    this.tabBarColor = tabBarColor == null ? Colors.transparent : tabBarColor;
    this.body = body;
    this.color = color;
    this.controller = controller ?? ScrollController();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: BouncingScrollPhysics(),
      controller: controller,
      slivers: [
        if (this.tabBar != null)
          SliverAppBar(
            pinned: true,
            backgroundColor: tabBarColor,
            foregroundColor: tabBarColor,
            forceElevated: false,
            automaticallyImplyLeading: false,
            flexibleSpace: tabBar,
            title: null,
          ),
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (BuildContext context, int index) {
              return Container(
                alignment: Alignment.centerLeft,
                color: color == null ? Colors.transparent : color,
                padding: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
                child: this.body[index]
              );
            },
            childCount: this.body.length
          ),
        )
      ],
    );
  }
}