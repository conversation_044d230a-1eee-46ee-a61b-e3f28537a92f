import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/widget/login-out-widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/data/mine_menu_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/widget/user-info-widget.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/widget/used-util-widget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class BusinessMinePage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    // TODO: implement initState
    return BusinessMinePageState();
  }
}

class BusinessMinePageState extends BaseState<BusinessMinePage> {
  CardVisibleBean cardVisible = new CardVisibleBean();
  List<MineMenuModel>? menuConfig;
  String imageHost = "";
  String? name;
  String? department;

  @override
  void onCreate() {
    super.onCreate();
    XYYContainer.bridgeCall("app_host").then((value) {
      imageHost = value["interface"];
      setState(() {});
    });
    this.getAuth();
    requestMenuConfig();
    UserInfoUtil.getUserInfo().then((value) {
      setState(() {
        name = value?.realName;
        department = "${value?.department ?? ""}";
      });
    });
  }
  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        Container(
          color: Color(0xFFF1F6F9),
          height: MediaQuery.of(context).size.height,
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(15.0),
                bottomRight: Radius.circular(15.0),
            ),
            image: DecorationImage(
                image: AssetImage('assets/images/business/business-home-totalPerformance-bg.png'),
                fit: BoxFit.cover,
                alignment: Alignment.topCenter
            )
          ),
          height: 220,
        ),
        SafeArea(
          child: Container(
            padding: EdgeInsets.fromLTRB(12, 15, 12, 0),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        SizedBox(height: 10),
                        UserInfoWidget(name: name, department: department),
                        SizedBox(height: 20),
                        //常用工具
                        UsedUtilWidget(cardVisible: cardVisible)
                      ],
                    ),
                  ),
                ),
              ],
            )
          )
        ),
      ],
    );
  }

  getAuth() async {
    var result = await NetworkV2<CardVisibleBean>(CardVisibleBean()).requestDataV2('/mop/index/visibleList', method: RequestMethod.GET);
    cardVisible = result.getData() ?? new CardVisibleBean();
    setState(() {});
  }
  void requestMenuConfig() async{
    EasyLoading.show();
    try {
      var roleJSON = await UserAuthManager.getRoleJSONString();
      var result = await NetworkV2<MineMenuParentModel>(MineMenuParentModel())
          .requestDataV2("getFindMenu",
          parameters: {"role": roleJSON}, method: RequestMethod.GET);
      EasyLoading.dismiss();
      if (mounted && result.isSuccess == true) {
        setState(() {
          menuConfig = result.getData()?.rows;
        });
      }
    } catch (e) {
      EasyLoading.dismiss();
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    // TODO: implement getTitleName
    return null;
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '我的';
  }

}