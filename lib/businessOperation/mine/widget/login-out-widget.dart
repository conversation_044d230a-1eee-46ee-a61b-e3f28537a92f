import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
class LoginOutWidget extends StatelessWidget {

  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(10),
      child: InkWell(
        borderRadius: BorderRadius.circular(10),
        onTap: loginOut,
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.all(15),
          child: Text(
            '退出登录',
            style: TextStyle(
              color: Color.fromRGBO(130, 130, 130, 1),
              fontSize: 14
            ),
          ),
        ),
      ),
    );
  }
  void loginOut() {
    //退出登录方法

  }
}