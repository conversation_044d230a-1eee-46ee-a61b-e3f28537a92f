import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:flutter/material.dart';

typedef ScheduleImageChange = void Function(
    List<String>? imageList, String content);

class XScheduleImageItem extends StatefulWidget {
  /// 是否是完善拜访
  final bool isPerfectVisit;

  /// 是否获取了通话时长
  final bool hasTalkTime;

  final List<String>? currentImages;

  /// 图片count
  final int limitCount;

  /// 图片变化回调
  final ScheduleImageChange imageChange;

  final String itemKey;

  XScheduleImageItem({
    this.isPerfectVisit = false,
    this.hasTalkTime = true,
    this.limitCount = 10,
    this.currentImages,
    required this.imageChange,
    required this.itemKey,
  });

  @override
  State<StatefulWidget> createState() {
    return XScheduleImageItemState();
  }
}

class XScheduleImageItemState extends State<XScheduleImageItem> {
  late XScheduleImageWidget addItem;
  List<XScheduleImageWidget> imageList = [];

  @override
  void initState() {
    this.addItem = XScheduleImageWidget(
      isAdd: true,
      tapAction: this.imageTapAction,
    );
    if (widget.currentImages != null) {
      List<XScheduleImageWidget> current = widget.currentImages!
          .map((e) => XScheduleImageWidget(
        url: e,
        tapAction: this.imageTapAction,
        deleteCallback: this.deleteImageAction,
      ))
          .toList();
      this.imageList.addAll(current);
    }
    if (this.imageList.length < widget.limitCount) {
      this.imageList.add(addItem);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.all(10),
      child: Wrap(
        runAlignment: WrapAlignment.start,
        crossAxisAlignment: WrapCrossAlignment.start,
        children: this.imageList,
        spacing: 10,
        runSpacing: 10,
      ),
    );
  }

  void imageTapAction(XScheduleImageWidget item) {
    if (item.isAdd) {
      this.addImageAction();
    } else {
      this.showBigImage(item);
    }
  }

  void requestImageHandler() {
    String isCamera =
    (widget.isPerfectVisit && !widget.hasTalkTime) ? "true" : "false";
    XYYContainer.bridgeCall('schedule_select_image', parameters: {
      "isCamera": "true",
      "address": "",
    }).then((value) {
      Map<dynamic, dynamic> result = value;
      if (result["success"] == 'true') {
        setState(() {
          String imageUrl = result["imageUrl"] ?? "";
          print("guan imageUrl:$imageUrl");
          XScheduleImageWidget item = XScheduleImageWidget(
            url: imageUrl,
            tapAction: this.imageTapAction,
            deleteCallback: this.deleteImageAction,
          );
          this.imageList.insert(this.imageList.length - 1, item);

          if (this.imageList.length >= widget.limitCount + 1 &&
              this.imageList.contains(this.addItem)) {
            this.imageList.remove(this.addItem);
          }
          this.imageChangeAction();
        });
      } else {
        XYYContainer.toastChannel.toast("图片上传失败");
      }
    }).catchError((_) {
      XYYContainer.toastChannel.toast("图片上传失败!");
    });
  }

  void addImageAction() {
    requestImageHandler();
  }

  void showBigImage(XScheduleImageWidget item) {
    Navigator.of(context).pushNamed('/photo_view_page',
        arguments: {"urlPath": item.url, "delete": false, "callback": () {}});
  }

  void deleteImageAction(XScheduleImageWidget item) {
    if (this.imageList.contains(item)) {
      this.imageList.remove(item);
      if (this.imageList.length < widget.limitCount &&
          !this.imageList.contains(this.addItem)) {
        this.imageList.add(this.addItem);
      }
      this.imageChangeAction();
    }
    setState(() {});
  }

  void imageChangeAction() {
    List<String> imageUrls = this.imageList.map((e) => e.url ?? "").toList();
    imageUrls.removeWhere((element) => element.length == 0);
    widget.imageChange(imageUrls, widget.itemKey);
  }
}

class XScheduleImageWidget extends StatelessWidget {
  final bool isAdd;
  final String? url;
  final ValueChanged<XScheduleImageWidget>? deleteCallback;
  final ValueChanged<XScheduleImageWidget> tapAction;

  XScheduleImageWidget({
    this.isAdd = false,
    this.url,
    this.deleteCallback,
    required this.tapAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.tapAction(this);
      },
      child: Container(
        child: Stack(
          alignment: Alignment.center,
          children: [
            this.imageWidget(context),
            Visibility(
              visible: !this.isAdd,
              child: Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () {
                    if (this.deleteCallback != null) {
                      this.deleteCallback!(this);
                    }
                  },
                  child: Container(
                    child: Image.asset(
                      "assets/images/schedule/schedule_image_delete.png",
                      width: 27,
                      height: 27,
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imageWidget(BuildContext context) {
    double imageWidth = this.imageWidth(context);
    if (this.isAdd) {
      return Image.asset(
        "assets/images/schedule/schedule_image_add.png",
        width: imageWidth,
        height: imageWidth,
        fit: BoxFit.cover,
      );
    } else if (this.url != null && this.url!.isNotEmpty) {
      return ImageWidget(
        url: this.url!,
        w: imageWidth,
        h: imageWidth,
        defImagePath: "assets/images/base/icon_default_image.png",
        fit: BoxFit.cover,
      );
    } else {
      return Container();
    }
  }

  double imageWidth(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double totalWidth = screenWidth;
    double totalGap = 6 * 10;
    double width = (totalWidth - totalGap) / 5;
    return width;
  }
}
