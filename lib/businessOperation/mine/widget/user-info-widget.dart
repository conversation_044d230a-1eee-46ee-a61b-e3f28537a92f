import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class UserInfoWidget extends StatelessWidget {
  final String? name;
  final String? department;

  UserInfoWidget({ this.name, this.department });

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/mine/mine_head_icon.png',
            width: 50,
            height: 50,
            fit: BoxFit.cover,
          ),
          SizedBox(width: 5),
          Transform.translate(
            offset: Offset(0, -2),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    Text(
                      name ?? "--",
                      style: TextStyle(
                        color: Color(0xFFFFFFFF),
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Sized<PERSON>ox(width: 10),
                    GestureDetector(
                      onTap: () async {
                        XYYContainer.storageChannel.put('mianType_shop', 'douya',space:'douya_shop');
                        Navigator.pushNamedAndRemoveUntil(context, '/main', (route) => false);
                      },
                      child: checkBtn(),
                    ),
                    /*Icon(
                      Icons.navigate_next,
                      size: 18,
                      color: Colors.white,
                    )*/
                  ],
                ),
                Text(
                  department ?? "--",
                  style: TextStyle(
                    color: Color(0xFFFFFFFF),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Spacer(),
        ],
      ),
    );
  }
  //切换销售版按钮
  Widget checkBtn() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.translate(
          offset: Offset(0, 1.5),
          child: Icon(
            Icons.swap_horiz,
            color: Colors.white,
            size: 16,
          ),
        ),
        Text(
          '切换到销售版',
          style: TextStyle(
              color: Color(0xFFFFFFFF),
              fontSize: 12
          ),
        ),
      ],
    );
  }
}