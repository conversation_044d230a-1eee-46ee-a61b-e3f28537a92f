import 'dart:async';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-card-bean.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/page/business_wechat_upload_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
class MenuList {
  final String title;
  final IconData icon;
  MenuList(this.title, this.icon);
}

class UsedUtilWidget extends StatelessWidget {
  CardVisibleBean cardVisible = new CardVisibleBean();
  UsedUtilWidget({required this.cardVisible});
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
        ),
        padding: EdgeInsets.only(bottom: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(15),
              child: Text(
                '常用工具',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,

                ),
              ),
            ),
            if (cardVisible.operationRankingVisible == true)
              getMenuListWidget(MenuList('运营排行', Icons.monitor), context),
            if (cardVisible.recruitmentRankingVisible == true)
              getMenuListWidget(MenuList('招商排行',Icons.bar_chart_outlined), context),
            if (cardVisible.operationVisitsVisible == true)
              getMenuListWidget(MenuList('运营拜访', Icons.sensor_door_outlined), context),
            if (cardVisible.recruitmentVisitsVisible == true)
              getMenuListWidget(MenuList('招商拜访', Icons.amp_stories_outlined), context),
            getMenuListWidget(MenuList('我的企微', Icons.account_circle_outlined), context),
            getMenuListWidget(MenuList('更多设置', Icons.settings_rounded), context),

          ],
        )
    );
  }
  Widget getMenuListWidget(MenuList list ,BuildContext context) {
    return Column(
      children: [
        Material(
          child: Ink(
            color: Colors.white,
            child: InkWell(
              onTap: () {
                switch (list.title) {
                  case '运营排行': {
                    Navigator.of(context).pushNamed('/business_operation_rank_page');
                    break;
                  }
                  case '招商排行': {
                    Navigator.of(context).pushNamed('/business_investment_rank_page', arguments: {
                      "timePeriod": 'today'
                    });
                    break;
                  }
                  case '运营拜访': {
                    Navigator.of(context).pushNamed('/business_operation_visit_page');
                    break;
                  }
                  case '招商拜访': {
                    Navigator.of(context).pushNamed('/business_investment_visit_page');
                    break;
                  }
                  case '我的企微': {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => BusinessWechatUploadPage(),
                        fullscreenDialog: true
                    ));
                    break;
                  }
                  case '更多设置': {
                    XYYContainer.bridgeCall('event_track',
                        parameters: {"action_type": "mc-mine-install"});
                    String router = "xyy://crm-app.ybm100.com/mine/setting";
                    XYYContainer.open(router);
                    break;
                  }
                  default: {
                    break;
                  }
                }
              },
              child: Padding(
                padding: EdgeInsets.all(15),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Transform.translate(
                              offset: Offset(0, 1.5),
                              child: Icon(
                                list.icon,
                                size: 20,
                                color: Color.fromRGBO(95, 95, 95, 1.0),
                              ),
                            ),
                            SizedBox(width: 3),
                            Text(
                              list.title,
                              style: TextStyle(
                                  fontSize: 12,
                                  color: Color.fromRGBO(72, 72, 72, 1.0)
                              ),
                            )
                          ],
                        ),
                        Icon(
                          Icons.navigate_next_rounded,
                          size: 20,
                          color: Color.fromRGBO(132, 132, 132, 1.0),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        //Divider(height: 1, color: Color.fromRGBO(215, 215, 215, 1.0), indent: 15, endIndent: 15,)
      ],
    );
  }
}