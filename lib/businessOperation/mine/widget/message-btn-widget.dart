import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/global_cache_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class MessageIconWidget extends StatefulWidget {
  final ValueNotifier<int>? controller;

  MessageIconWidget({this.controller});

  @override
  State<StatefulWidget> createState() {
    return MessageIconWidgetState();
  }
}

class MessageIconWidgetState extends State<MessageIconWidget>
    with EventBusObserver {
  late ValueNotifier<int> controller;

  @override
  void initState() {
    if (widget.controller != null) {
      this.controller = widget.controller!;
    } else {
      this.controller = ValueNotifier(0);
    }
    super.initState();
    eventBus.addListener(
        observer: this,
        eventName: MessageEventBusName.REFRESH_MESSAGE_COUNT,
        callback: eventCallBack);
    eventCallBack(null);
  }

  eventCallBack(arg) {
    GlobalCacheManager.instance.messageCountMap.then((msgCountMap) {
      var count =
          msgCountMap?.values.reduce((value, element) => value + element) ?? 0;
      controller.value = count;
    });
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
    eventBus.removeListener(observer: this);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.of(context).pushNamed("/message_root_page");
      },
      child: Container(
        child: Stack(
          children: [
            Image.asset(
              'assets/images/business/message.png',
              width: 37,
              height: 37,
            ),
            Positioned(
              top: 0,
              right: 0,
              child: ValueListenableBuilder(
                valueListenable: this.controller,
                builder: (BuildContext context, int value, Widget? child) {
                  return Visibility(
                    visible: value > 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Color(0xFFFF2121),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}