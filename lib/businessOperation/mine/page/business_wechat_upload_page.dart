import 'dart:io';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/common/baseContainer.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/global/global-data.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/home/<USER>/business-home-userInfo-bean.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/businessOperation/mine/widget/img-upload-widget.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class BusinessWechatUploadPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    // TODO: implement initState
    return BusinessWechatUploadPageState();
  }
}

class BusinessWechatUploadPageState extends BaseState<BusinessWechatUploadPage> {
  String imgUrl = "";
  String host = '';
  @override
  void initState() {
    XYYContainer.bridgeCall("app_host").then((value) {
      if (value is Map) {
        this.host = value['license_image_host'] ?? "";
      }
    });
    getQrcode();
  }

  //获取二维码
  getQrcode() async {
    var result = await NetworkV2<BusinessHomeQrcodeBean>(BusinessHomeQrcodeBean()).requestDataV2('/mop/qrcode/query', method: RequestMethod.GET);
    var data = result.getData() ?? BusinessHomeQrcodeBean();
    if (data.qrcodeImage != null && data.qrcodeImage != '') {
      this.imgUrl = data.qrcodeImage!;
      setState(() {});
    }
  }
  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    "请上传企业微信二维码",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                SizedBox(height: 10),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    "卖家中心对接联系人会展示该二维码",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ),
                SizedBox(height: 10),
                  if (imgUrl == "")
                    GestureDetector(
                      onTap: () async {
                        var url = Platform.isAndroid ? '/app/crm/mop/common/uploadImg' : '/mop/common/uploadImg';
                        var result = await XYYContainer.photoForAlbumWithUpload(url,
                            imageCount: 1,
                            limitWidth: 800,
                            limitHeight: 800);
                        String imageUrl = result != null ? result[0] : '';
                        if (imageUrl != "") {
                          this.imgUrl = host + imageUrl;
                          setState(() {});
                        }
                      },
                      child: Container(
                          alignment: Alignment.topCenter,
                          width: 200,
                          height: 200,
                          padding: EdgeInsets.all(5),
                          child: Image.asset(
                            'assets/images/business/business-qrcode.png',
                            width: 200,
                            height: 200,
                            fit: BoxFit.cover,
                          )
                      ),
                    )
                  else
                    Container(
                      child: Stack(
                        children: [
                          GestureDetector(
                            onTap: () {
                              //预览
                              Navigator.of(context).pushNamed('/photo_view_page',
                                  arguments: {"urlPath": imgUrl, "delete": false, "callback": () {}});
                            },
                            child: Container(
                                alignment: Alignment.topCenter,
                                width: 200,
                                height: 200,
                                padding: EdgeInsets.all(5),
                                child: ImageWidget(
                                  url: imgUrl,
                                  w: 200,
                                  h: 200,
                                  fit: BoxFit.cover,
                                )
                            ),
                          ),
                          //删除按钮
                          Positioned(
                            top: 5,
                            right: 5,
                            child: GestureDetector(
                              onTap: () {
                                this.imgUrl = "";
                                setState(() {});
                              },
                              child: Container(
                                child: Image.asset(
                                  "assets/images/schedule/schedule_image_delete.png",
                                  width: 32,
                                  height: 32,
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                ,
                Container(
                  padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
                  decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
                  child: Row(
                    children: [
                      Expanded(
                          flex: 1,
                          child: TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: Container(
                                height: 42,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(21),
                                  border: Border.all(
                                      color: Color(0xFFCACACA), width: 1),
                                ),
                                padding: EdgeInsets.only(left: 10, right: 10),
                                child: Center(
                                  child: Text(
                                    '取消',
                                    style: TextStyle(
                                        color: Color(0xFF222222), fontSize: 14),
                                  ),
                                ),
                              ),
                              style: ButtonStyle(
                                overlayColor: MaterialStateProperty.all<Color>(
                                    Colors.transparent),
                                padding: MaterialStateProperty.all<EdgeInsets>(
                                    EdgeInsets.only(right: 10)),
                                minimumSize:
                                MaterialStateProperty.all<Size>(Size.zero),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              )
                          )
                      ),
                      SizedBox(width: 10),
                      Expanded(
                          flex: 1,
                          child: TextButton(
                              onPressed: () async {
                                //submitVisit();
                                var result = await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2('/mop/qrcode/add', method: RequestMethod.GET, parameters: {
                                  "qrcodeImage": this.imgUrl
                                });
                                if (result.status == "success") {
                                  XYYContainer.toastChannel.toast("提交成功");
                                  Navigator.pop(context);
                                }
                              },
                              child: Container(
                                height: 42,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(21),
                                  color: Color(0xFF00B955),
                                ),
                                padding: EdgeInsets.only(left: 10, right: 10),
                                child: Center(
                                  child: Text(
                                    '提交',
                                    style: TextStyle(
                                        color: Color(0xFFFFFFFF), fontSize: 14),
                                  ),
                                ),
                              ),
                              style: ButtonStyle(
                                overlayColor: MaterialStateProperty.all<Color>(
                                    Colors.transparent),
                                padding: MaterialStateProperty.all<EdgeInsets>(
                                    EdgeInsets.only(right: 10)),
                                minimumSize:
                                MaterialStateProperty.all<Size>(Size.zero),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ))),
                    ],
                  ),
                )
              ]
            ),

          ],
        )
    );
  }

  @override
  String getTitleName() {
    // TODO: implement getTitleName
    return '企微二维码';
  }

}