import 'dart:io';
import 'dart:math';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/drive_route_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_visit_recommendation_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_join_the_program_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/common_show_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
import 'package:flutter_baidu_mapapi_utils/flutter_baidu_mapapi_utils.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';

class AddVisitRecommendationPage extends BasePage {
  String? lat;
  String? lng;

  AddVisitRecommendationPage({this.lat, this.lng});

  @override
  BaseState<StatefulWidget> initState() {
    return _visitContent();
  }
}

class _visitContent extends BaseState<AddVisitRecommendationPage> {
  String? mylat = "";
  String? mylng = "";

  final zoomLevel = [
    50,
    100,
    200,
    500,
    1000,
    2000,
    5000,
    10000,
    20000,
    25000,
    50000,
    100000,
    200000,
    500000,
    1000000,
    2000000
  ];
  late BMFMapOptions mapOptions;

  BMFMapController? myMapController;

  DriveRouteModel? driveRoute;

  BMFPolyline? polyline;

  List<ScheduleVisitRecommendationData>? listData;

  ValueNotifier<String> distanceValueNotifier = ValueNotifier("--");

  @override
  void onCreate() async {
    super.onCreate();
    mapOptions = BMFMapOptions(zoomLevel: 12, showZoomControl: false);
    var locationData = await XYYContainer.locationChannel.locate();
    if (locationData.isSuccess == true) {
      mylat = locationData.latitude;
      mylng = locationData.longitude;
    } else {
      XYYContainer.toastChannel.toast('获取位置信息失败${locationData.isSuccess}');
    }
    requestVisitDetail();
  }

  void _onGetBMFDrivingRouteResult(
      BMFDrivingRouteResult result, BMFSearchErrorCode errorCode) {
    dismissLoadingDialog();
    if (errorCode != BMFSearchErrorCode.NO_ERROR) {
      showToast(errorCode.toString());
    } else {
      showToast("获取轨迹成功");
    }
    print("dddd search result $errorCode,${result.toMap()}");
    if (result.routes?.first != null) {
      driveRoute = DriveRouteModel.withModel(result.routes!.first);
      addRoutePolyline();
      distanceValueNotifier.value = driveRoute?.distance ?? "--";
    }
  }

  void buildDrivingPlanRoute() async {
    var visitResult = listData;
    if (visitResult == null || visitResult.isEmpty == true) {
      // 没有点
      return;
    }
    List<BMFPlanNode>? wayPoints;
    if (visitResult.length == 1) {
      // 只有一个点
      var coordinate = BMFCoordinate(convertDouble(visitResult.first.poiLat),
          convertDouble(visitResult.first.poiLng));
      var singleMarker = buildMarker(0, coordinate);
      if (singleMarker != null) {
        myMapController?.cleanAllMarkers();
        myMapController?.addMarker(singleMarker);
        myMapController?.setCenterCoordinate(coordinate, true);
        if (Platform.isAndroid) {
          myMapController?.setZoomTo(16);
        }
      }
      return;
    } else if (visitResult.length > 1) {
      myMapController?.cleanAllMarkers();
      double minLat = 0.0;
      double minLng = 0.0;
      for (var i = 0; i < visitResult.length; i++) {
        var coordinate = BMFCoordinate(convertDouble(visitResult[i].poiLat),
            convertDouble(visitResult[i].poiLng));
        var singleMarker = buildMarker(i, coordinate);
        if (singleMarker != null) {
          minLat += convertDouble(visitResult[i].poiLat);
          minLng += convertDouble(visitResult[i].poiLng);
          myMapController?.addMarker(singleMarker);
        }
      }
      if (Platform.isAndroid) {
        myMapController?.setZoomTo(16);
      }
      var minLatStr = (minLat / visitResult.length).toStringAsFixed(6);
      var minLngStr = (minLng / visitResult.length).toStringAsFixed(6);

      var centerCoordinate =
          BMFCoordinate(convertDouble(minLatStr), convertDouble(minLngStr));
      var centerSingleMarker = buildMarker(100, centerCoordinate);
      myMapController?.addMarker(centerSingleMarker!);
      myMapController?.setCenterCoordinate(centerCoordinate, true);

      // 超过两个点
      // try {
      //   wayPoints = visitResult
      //       .sublist(1, visitResult.length - 1)
      //       .map((e) => BMFPlanNode(
      //           pt: BMFCoordinate(
      //               convertDouble(e.poiLat), convertDouble(e.poiLng))))
      //       .toList();
      // } catch (e) {
      //   print("wayPoints search error $e");
      // }
    }
    // var drivingRoutePlanOption = BMFDrivingRoutePlanOption(
    //     from: BMFPlanNode(
    //         pt: BMFCoordinate(convertDouble(visitResult.first.poiLat),
    //             convertDouble(visitResult.first.poiLng))),
    //     to: BMFPlanNode(
    //         pt: BMFCoordinate(convertDouble(visitResult.last.poiLat),
    //             convertDouble(visitResult.last.poiLng))),
    //     wayPointsArray: wayPoints,
    //     drivingPolicy: BMFDrivingPolicy.DIS_FIRST); // 路程最短

    // BMFDrivingRouteSearch drivingRouteSearch = BMFDrivingRouteSearch();

    // /// 检索回调
    // drivingRouteSearch.onGetDrivingRouteSearchResult(
    //     callback: _onGetBMFDrivingRouteResult);
    // showLoadingDialog();
    // if (Platform.isAndroid) {
    //   var searchLineResult =
    //       await XYYContainer.bridgeCall("search_driving_line", parameters: {
    //     'drivingRoutePlanOption': drivingRoutePlanOption.toMap(),
    //   }) as Map;
    //   print("dddd search bridgeCall ${searchLineResult.toString()}");
    //   try {
    //     BMFDrivingRouteResult result =
    //         BMFDrivingRouteResult.fromMap(searchLineResult['result']);
    //     BMFSearchErrorCode errorCode =
    //         BMFSearchErrorCode.values[searchLineResult['errorCode'] as int];
    //     _onGetBMFDrivingRouteResult(result, errorCode);
    //   } catch (e) {
    //     print("dddd search error $e");
    //   }
    // } else {
    //   bool result =
    //       await drivingRouteSearch.dringRouteSearch(drivingRoutePlanOption);
    //   if (result) {
    //     print("发起检索成功");
    //   } else {
    //     print("发起检索失败");
    //   }
    // }
  }

  Future<void> requestVisitDetail() async {
    var params = {
      'lat': widget.lat,
      'lng': widget.lng,
    };
    showLoadingDialog();
    var result = await NetworkV2<ScheduleVisitRecommendationData>(
            ScheduleVisitRecommendationData())
        // visit/propose/list
        .requestDataV2("visit/propose/list",
            method: RequestMethod.GET, parameters: params);
    dismissLoadingDialog();
    // print("bbbb , ${result}");
    if (mounted && result.isSuccess == true) {
      listData = result.getListData();
      countNum = 0;
      listData?.forEach((item) {
        if (item.inPlanFlag == true) {
          countNum++;
        }
      });
      buildDrivingPlanRoute();
      setState(() {});
    }
  }

  void addRoutePolyline() {
    if (driveRoute == null) {
      return;
    }
    List<BMFMarker> markers = [];

    /// 起点marker
    var startMarker = buildMarker(0, driveRoute!.startNode?.location);
    if (startMarker != null) {
      markers.add(startMarker);
    }

    /// 终点marker
    var endMarker =
        buildMarker((listData?.length ?? 0) - 1, driveRoute!.endNode?.location);
    if (endMarker != null) {
      markers.add(endMarker);
    }

    /// 驾车途径点marker
    for (int index = 0; index < (driveRoute!.wayPoints?.length ?? 0); index++) {
      var wayMarker = buildMarker(index + 1, driveRoute!.wayPoints?[index].pt);
      if (wayMarker != null) {
        markers.add(wayMarker);
      }
    }

    /// 添加marker
    myMapController?.cleanAllMarkers();

    /// 添加路线polyline
    if (polyline != null) {
      myMapController?.removeOverlay(polyline!.Id);
    }

    polyline = BMFPolyline(
      width: Platform.isAndroid ? 15 : 5,
      coordinates: driveRoute!.routeCoordinates!,
      indexs: driveRoute!.routeCoordinates!.map((e) => 1).toList(),
      textures: [
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_unknown.png",
      ],
      dottedLine: false,
    );
    myMapController?.addPolyline(polyline!);
    myMapController?.addMarkers(markers);

    /// 根据polyline设置地图显示范围
    BMFCoordinateBounds coordinateBounds =
        getVisibleMapRect(polyline!.coordinates);

    if (Platform.isAndroid) {
      setZoomLevel(coordinateBounds);
    }

    myMapController?.setVisibleMapRectWithPadding(
      visibleMapBounds: coordinateBounds,
      insets: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
      animated: true,
    );
  }

  void setZoomLevel(BMFCoordinateBounds bounds) async {
    var locationDistance = await BMFCalculateUtils.getLocationDistance(
            bounds.northeast, bounds.southwest) ??
        0;
    double level = -1;
    for (var i = 0; i < zoomLevel.length; i++) {
      if (zoomLevel[i] < locationDistance) {
        level = 18 - i + 3;
      }
    }
    if (level != -1) {
      myMapController?.setZoomTo(level);
    }
  }

  double convertDouble(String value) {
    try {
      return double.tryParse(value) ?? 0;
    } catch (e) {}
    return 0;
  }

  /// 获取地图显示区域
  BMFCoordinateBounds getVisibleMapRect(List<BMFCoordinate> coordinates) {
    BMFCoordinate first = coordinates[0];
    double leftBottomX = first.latitude;
    double leftBottomY = first.longitude;
    double rightTopX = first.latitude;
    double rightTopY = first.longitude;

    for (BMFCoordinate coordinate in coordinates) {
      if (coordinate.latitude < leftBottomX) {
        leftBottomX = coordinate.latitude;
      }

      if (coordinate.longitude < leftBottomY) {
        leftBottomY = coordinate.longitude;
      }

      if (coordinate.latitude > rightTopX) {
        rightTopX = coordinate.latitude;
      }

      if (coordinate.longitude > rightTopY) {
        rightTopY = coordinate.longitude;
      }
    }

    BMFCoordinate leftBottom = BMFCoordinate(leftBottomX, leftBottomY);
    BMFCoordinate rightTop = BMFCoordinate(rightTopX, rightTopY);

    BMFCoordinateBounds coordinateBounds =
        BMFCoordinateBounds(northeast: rightTop, southwest: leftBottom);

    return coordinateBounds;
  }

  BMFMarker? buildMarker(int index, BMFCoordinate? coord) {
    if (index == 100 && coord != null) {
      return BMFMarker.icon(
        scaleX: 1,
        scaleY: 1,
        position: coord,
        title: "12312asdasda",
        icon: "assets/images/schedule/schedule_location_icon_big.png",
      );
    }

    if (index < 0) {
      return null;
    }

    var iconPath = "assets/images/schedule/marker_";
    var visitData = listData?[index];
    if (coord == null || visitData == null) {
      return null;
    }

    iconPath += index > 5 ? "normal" : "warning";

    // 本地最多25个图片
    if (index >= 25) {
      iconPath += "/25.png";
    } else {
      iconPath += "/${index + 1}.png";
    }

    print("dddd buildMarker $iconPath");
    return BMFMarker.icon(
      scaleX: 1,
      scaleY: 1,
      position: coord,
      title: index.toString(),
      icon: iconPath,
    );
  }

  int countNum = 0;

  // 跳转导航
  void navigateIconClick(lat, lng, address) {
    if (lat == null || lng == null) {
      XYYContainer.toastChannel.toast("客户信息获取异常");
      return;
    }
    XYYContainer.bridgeCall('map_navigation', parameters: {
      'latitude': convertDouble(lat),
      'longitude': convertDouble(lng),
      'address': address,
    });
  }

  void checkdSelect(ScheduleVisitRecommendationData? item) {
    // 未注册且不在私海
    if (!(item?.inPrivateFlag ?? true)) {
      CommonAsyncUtils.dialog(
        context: context,
        title: '提示',
        content: Text('客户尚未认领，请认领客户商品集后再加入拜访计划'),
        onConfirm: () async {
          var result =
              await NetworkV2<CustomerDetailDataV2>(CustomerDetailDataV2())
                  .requestDataV2('customerV2/openDetail',
                      parameters: {'customerId': item?.customerId},
                      method: RequestMethod.GET);

          if (result.isSuccess == true) {
            var data = result.getData();
            CustomerSkuCollectDialog.showSkuCollectDialog(
                    context, data?.bindSkuCollect)
                .then((value) {
              if (value == null || value.isEmpty) {
                return;
              }
              showCupertinoDialog(
                context: this.context,
                builder: (BuildContext context) {
                  return CupertinoAlertDialog(
                    title: Text("确认是否认领"),
                    actions: [
                      CupertinoDialogAction(
                        child: Text("取消"),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                      CupertinoDialogAction(
                        child: Text("确定"),
                        onPressed: () {
                          Navigator.of(context).pop();
                          this.requestReviceCustomer(
                              value.map((e) => e.skuCollectCode).join(','),
                              item);
                        },
                      ),
                    ],
                  );
                },
              );
            });
          } else {
            showToast('${result.errorMsg ?? "获取客户详情异常"}');
          }
        },
      );
    } else {
      joinTheProgram(item?.customerId, item?.proposeReason);
    }
  }

  // 认领客户商品集请求
  void requestReviceCustomer(
      String skuCollectCodes, ScheduleVisitRecommendationData? item) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {"id": item?.customerId, "skuCollectCodes": skuCollectCodes},
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      showToast("认领成功");
      joinTheProgram(item?.customerId, item?.proposeReason);
    } else {
      if (result.code != null && result.code == 405) {
        showCupertinoDialog(
          context: this.context,
          builder: (BuildContext context) {
            return CupertinoAlertDialog(
              title: Text("已达到私海数量最大限度，无法认领"),
              actions: [
                CupertinoDialogAction(
                  child: Text("确定"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
      showToast('${result.errorMsg ?? "认领客户失败"}');
    }
  }

  // 加入计划
  void joinTheProgram(customerId, proposeReason) async {
    var params = {
      'customerId': customerId,
      'proposeReason': proposeReason,
    };
    showLoadingDialog();
    var result = await NetworkV2<ScheduleJoinTheProgramData>(
            ScheduleJoinTheProgramData())
        // visit/propose/list
        .requestDataV2("visit/plan/add",
            method: RequestMethod.GET, parameters: params);
    dismissLoadingDialog();
    // print("bbbb , ${result}");
    if (mounted && result.isSuccess == true) {
      XYYContainer.toastChannel.toast('加入成功');
      for (ScheduleVisitRecommendationData? item in listData ?? []) {
        if (item?.customerId == customerId) {
          item?.inPlanFlag = true;
          item?.inPrivateFlag = true;
        }
      }
      countNum++;
      buildDrivingPlanRoute();
      setState(() {});
    }
  }

  void backToCustomerPage() {
    EventBus().sendMessage(MainTabBarEventBusName.CHANGE_TAB_INDEX, arg: 1);
    XYYContainer.close(context, resultData: {"success": "true"});
    track('mc-visit-recommendation-backToCustomerPage');
    // var router = '/main';
    // router = Uri.encodeFull(router);
    // Navigator.of(context).popAndPushNamed(router);
  }

  List<Widget> getRenderItems(ScheduleVisitRecommendationData? item, index) {
    // print('ssss${item}');
    bool _isButtonDisabled = item?.inPlanFlag ?? false;
    List<Widget> list = [];
    list.add(Container(
      padding: EdgeInsets.only(top: 14, left: 14, right: 12, bottom: 12.5),
      decoration: BoxDecoration(
        color: Color(0xffffffff),
        borderRadius: BorderRadius.all(Radius.circular(8.0)),
      ),
      // height: 108,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            flex: 0,
            child: Stack(
              alignment: Alignment.center,
              overflow: Overflow.visible,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  //超出部分，可裁剪
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Image.network(
                    item?.poiImage,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: -10,
                  left: -10,
                  child: Container(
                    width: 20,
                    height: 20,
                    padding: EdgeInsets.only(top: 2),
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(
                            'assets/images/base/ranking_bg_icon.png'),
                        fit: BoxFit.fill, // 完全填充
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "$index",
                      style: TextStyle(
                        fontSize: 13.0,
                        height: 1,
                        color: Color(0xffffffff),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
                // height: 80,
                padding: const EdgeInsets.only(left: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${item?.customerName}",
                      textAlign: TextAlign.start,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 15.0,
                          height: 1.4,
                          fontWeight: FontWeight.bold),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 7.5),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 0,
                            child: Container(
                              padding: EdgeInsets.all(2), //容器内补白
                              margin: EdgeInsets.only(right: 7),
                              color: Color(0xffFFF6E3),
                              child: Text(
                                "推荐理由",
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xffAC794B),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              "${item?.proposeReason}",
                              textAlign: TextAlign.left,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 12.0,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 9),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 109,
                            height: 23,
                            child: OutlinedButton.icon(
                                icon: Image.asset(
                                  "assets/images/base/navigation-icon.png",
                                  width: 12,
                                  height: 12,
                                ),
                                label: Text("导航"),
                                style: ButtonStyle(
                                  textStyle:
                                      MaterialStateProperty.all<TextStyle>(
                                    TextStyle(fontSize: 12), // 设置字号为 20
                                  ),
                                  foregroundColor:
                                      MaterialStateProperty.resolveWith(
                                    (states) {
                                      //默认状态使用灰色
                                      return Color(0xff333333);
                                    },
                                  ),
                                ),
                                onPressed: () {
                                  navigateIconClick(item?.poiLat, item?.poiLng,
                                      item?.customerName);
                                }),
                          ),
                          SizedBox(width: 8),
                          SizedBox(
                            width: 109,
                            height: 23,
                            child: OutlinedButton.icon(
                              icon: Image.asset(
                                _isButtonDisabled
                                    ? "assets/images/base/JoinProgram-dis-icon.png"
                                    : "assets/images/base/JoinProgram-icon.png",
                                width: 12,
                                height: 12,
                              ),
                              label: Text(_isButtonDisabled ? "已加入" : "加入计划"),
                              style: ButtonStyle(
                                textStyle: MaterialStateProperty.all<TextStyle>(
                                  TextStyle(fontSize: 12), // 设置字号为 20
                                ),
                                backgroundColor:
                                    MaterialStateProperty.all<Color>(
                                        _isButtonDisabled
                                            ? Color(0xffF4F4F4)
                                            : Color(0xffffffff)),
                                foregroundColor:
                                    MaterialStateProperty.resolveWith(
                                  (states) {
                                    if (states
                                        .contains(MaterialState.disabled)) {
                                      return Colors.grey; // 设置禁用状态下的前景色为灰色
                                    }
                                    //默认状态使用灰色
                                    return Color(0xff333333);
                                  },
                                ),
                              ),
                              onPressed: _isButtonDisabled
                                  ? null
                                  : () {
                                      checkdSelect(item);
                                    },
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                )),
          ),
        ],
      ),
    ));
    list.add(SizedBox(height: 8));
    return list;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffEFEFF4),
      body: Stack(
        children: [
          Container(
            height: 270,
            child: BMFMapWidget(
              onBMFMapCreated: (controller) {
                myMapController = controller;
              },
              mapOptions: mapOptions,
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 280, bottom: 49),
            child: ListView.builder(
              itemCount: listData?.length ?? 0,
              itemBuilder: (context, index) {
                return Column(
                  children: this.getRenderItems(listData?[index], ++index),
                );
              },
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Color(0xffffffff),
                border: Border(
                  top: BorderSide(
                    color: Color(0xffe5e5e5),
                    width: 0.5,
                    style: BorderStyle.solid,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          child: Text("返回客户列表"),
                          onPressed: () => {backToCustomerPage()},
                          style: ButtonStyle(
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            textStyle: MaterialStateProperty.all<TextStyle>(
                              TextStyle(fontSize: 14),
                            ),
                            foregroundColor: MaterialStateProperty.resolveWith(
                              (states) {
                                //默认状态使用灰色
                                return Color(0xff292933);
                              },
                            ),
                          ),
                        ),
                        Image.asset(
                          "assets/images/base/icon_arrow_right.png",
                          width: 15,
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 0,
                    child: SizedBox(
                      width: 0.5,
                      height: 19,
                      child: DecoratedBox(
                        decoration: BoxDecoration(color: Color(0xffDFDFDF)),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TextButton(
                          child: Text("我的拜访计划"),
                          onPressed: () => {addVisitPlan()},
                          style: ButtonStyle(
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            textStyle: MaterialStateProperty.all<TextStyle>(
                              TextStyle(fontSize: 14),
                            ),
                            foregroundColor: MaterialStateProperty.resolveWith(
                              (states) {
                                //默认状态使用灰色
                                return Color(0xff292933);
                              },
                            ),
                          ),
                        ),
                        Text(
                          "($countNum)",
                          style: TextStyle(color: Color(0xff00B377)),
                        ),
                        Image.asset(
                          "assets/images/base/icon_arrow_right.png",
                          width: 15,
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  //拜访计划
  void addVisitPlan() async {
    //TODO 跳转拜访计划
    track('mc-visit-recommendation-addVisitPlan');
    var router = '/visit_plan_page';
    router = Uri.encodeFull(router);
    Navigator.of(context).pushNamed(router);
  }

  @override
  String getTitleName() {
    return "拜访推荐";
  }
}
