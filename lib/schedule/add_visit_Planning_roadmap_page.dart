import 'dart:typed_data';

import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/drive_route_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_visit_planningRoadmap_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/common_show_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
import 'package:flutter_baidu_mapapi_utils/flutter_baidu_mapapi_utils.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import '../home/<USER>/share/share_helper.dart';
import '../home/<USER>/share/share_select_page.dart';
import 'package:screenshot/screenshot.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/share/share_helper.dart';
import 'package:flutter/rendering.dart';

class AddVisitPlanningRoadmap extends BasePage {
  final String? planIdStr;

  AddVisitPlanningRoadmap({required this.planIdStr});

  @override
  BaseState<StatefulWidget> initState() {
    return _visitContent();
  }
}

class _visitContent extends BaseState<AddVisitPlanningRoadmap> {
  GlobalKey repaintWidgetKey = GlobalKey(); // 绘图key值
  GlobalKey totalWidgetKey = GlobalKey(); // 绘图key值
  ValueNotifier<bool> shareNotifier = ValueNotifier<bool>(false);
  dynamic userInfo = {};
  String? lat = "";
  String? lng = "";
  String? bdUrl;
  String? clientsUrl;
  String? userInfoUrl;
  String? listHeaderUrl;

  final zoomLevel = [
    50,
    100,
    200,
    500,
    1000,
    2000,
    5000,
    10000,
    20000,
    25000,
    50000,
    100000,
    200000,
    500000,
    1000000,
    2000000
  ];
  late BMFMapOptions mapOptions;

  BMFMapController? myMapController;

  DriveRouteModel? driveRoute;

  BMFPolyline? polyline;

  ScheduleVisitPlanningRoadmapData? listData;

  ValueNotifier<String> distanceValueNotifier = ValueNotifier("--");
  ScreenshotController screenshotController = ScreenshotController();

  @override
  void onCreate() async {
    super.onCreate();
    mapOptions = BMFMapOptions(zoomLevel: 12, showZoomControl: false);
    userInfo = await UserInfoUtil.getUserInfo();
    var locationData = await XYYContainer.locationChannel.locate();
    if (locationData.isSuccess == true) {
      lat = locationData.latitude;
      lng = locationData.longitude;
    } else {
      XYYContainer.toastChannel.toast('获取位置信息失败${locationData.isSuccess}');
    }
    requestVisitDetail();
  }

  /// 获取固定控件 url --优化手段
  Future<void> initImgUrl() async {
    // 获取列表 表头
    if (listHeaderUrl?.isEmpty ?? true) {
      var listHeaderFile =
          await screenshotController.captureFromWidget(SizedBox(
            width: 200,
            height: 45,
            child: Image.asset(
              "assets/images/visit/plan_header.png",
              width: 200,
              height: 45,
            ),
          ));
      listHeaderUrl =
          (await ShareHelper().formUnit8ToFie(listHeaderFile))?.path;
    }
    // 获取用户信息
    if (userInfoUrl?.isEmpty ?? true) {
      // 获取用户信息
      var infoFile = await screenshotController.captureFromWidget(Container(
        height: 67,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(8),
            bottomRight: Radius.circular(8),
          ),
          color: Color(0xffffffff),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Expanded(
              flex: 4,
              child: Align(
                alignment: Alignment.center,
                child: buildBottomLeftColumn(),
              ),
            ),
            Expanded(
              flex: 3,
              child: Align(
                alignment: Alignment.centerRight,
                child: buildBottomRightColumn(),
              ),
            ),
          ],
        ),
      ));
      userInfoUrl = (await ShareHelper().formUnit8ToFie(infoFile))?.path;
    }
  }

  /// 防重复提交
  Future<void> intervalClick(int needTime) async {
    // 获取百度地图
    Uint8List? bdUni = await myMapController?.takeSnapshot();
    var bdFile = await ShareHelper().formUnit8ToFie(bdUni);
    await initImgUrl();
    // 获取客户列表
    await ShareHelper().onSharePlusShare(repaintWidgetKey).then((img) async {
      bdUrl = bdFile?.path;
      clientsUrl = img;
      shareNotifier.value = !shareNotifier.value;
      await Future.delayed(Duration(milliseconds: 500));
      // 获取两图合并后总图
      await ShareHelper().onSharePlusShare(totalWidgetKey).then((value) async {
        return showModalBottomSheet(
          context: this.context,
          isScrollControlled: true, //弹窗是否可以滑动
          builder: (BuildContext context) {
            return ShareSelectPage(path: value ?? '');
          },
        ).then((value) {
          bdUrl = null;
          clientsUrl = null;
          shareNotifier.value = !shareNotifier.value;
        });
      });
    });
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(this.getTitleName(), rightButtons: [
      LoaingButton(
          onPressed: () async {
            await intervalClick(2);
          },
          size: Size(22, 22),
          backgroundColor: Colors.transparent,
          textColor: Colors.black,
          content: Container(
              margin: EdgeInsets.only(right: 12),
              width: 22,
              height: 22,
              alignment: Alignment.center,
              child: Image(
                image: AssetImage('assets/images/share/topBar-share-icon.png'),
                width: 22,
                height: 22,
              )))
    ]);
  }

  void _onGetBMFDrivingRouteResult(
      BMFDrivingRouteResult result, BMFSearchErrorCode errorCode) {
    dismissLoadingDialog();
    if (errorCode != BMFSearchErrorCode.NO_ERROR) {
      showToast(errorCode.toString());
    } else {
      showToast("获取轨迹成功");
    }
    print("dddd search result $errorCode,${result.toMap()}");
    if (result.routes?.first != null) {
      driveRoute = DriveRouteModel.withModel(result.routes!.first);
      addRoutePolyline();
      distanceValueNotifier.value = driveRoute?.distance ?? "--";
    }
  }

  void buildDrivingPlanRoute() async {
    var visitResult = listData?.planList;
    if (visitResult == null || visitResult.isEmpty == true) {
      // 没有点
      return;
    }
    List<BMFPlanNode>? wayPoints;
    if (visitResult.length == 1) {
      // 只有一个点
      var coordinate = BMFCoordinate(convertDouble(visitResult.first.poiLat),
          convertDouble(visitResult.first.poiLng));
      var singleMarker = buildMarker(0, coordinate);
      if (singleMarker != null) {
        myMapController?.cleanAllMarkers();
        myMapController?.addMarker(singleMarker);
        myMapController?.setCenterCoordinate(coordinate, true);
        if (Platform.isAndroid) {
          myMapController?.setZoomTo(16);
        }
      }
      return;
    } else if (visitResult.length > 2) {
      // 超过两个点
      try {
        wayPoints = visitResult
            .sublist(1, visitResult.length - 1)
            .map((e) => BMFPlanNode(
                pt: BMFCoordinate(
                    convertDouble(e.poiLat), convertDouble(e.poiLng))))
            .toList();
      } catch (e) {
        print("wayPoints search error $e");
      }
    }
    var drivingRoutePlanOption = BMFDrivingRoutePlanOption(
        from: BMFPlanNode(
            pt: BMFCoordinate(convertDouble(visitResult.first.poiLat),
                convertDouble(visitResult.first.poiLng))),
        to: BMFPlanNode(
            pt: BMFCoordinate(convertDouble(visitResult.last.poiLat),
                convertDouble(visitResult.last.poiLng))),
        wayPointsArray: wayPoints,
        drivingPolicy: BMFDrivingPolicy.DIS_FIRST); // 路程最短

    BMFDrivingRouteSearch drivingRouteSearch = BMFDrivingRouteSearch();

    /// 检索回调
    drivingRouteSearch.onGetDrivingRouteSearchResult(
        callback: _onGetBMFDrivingRouteResult);
    showLoadingDialog();
    if (Platform.isAndroid) {
      var searchLineResult =
          await XYYContainer.bridgeCall("search_driving_line", parameters: {
        'drivingRoutePlanOption': drivingRoutePlanOption.toMap(),
      }) as Map;
      print("dddd search bridgeCall ${searchLineResult.toString()}");
      try {
        BMFDrivingRouteResult result =
            BMFDrivingRouteResult.fromMap(searchLineResult['result']);
        BMFSearchErrorCode errorCode =
            BMFSearchErrorCode.values[searchLineResult['errorCode'] as int];
        _onGetBMFDrivingRouteResult(result, errorCode);
      } catch (e) {
        print("dddd search error $e");
      }
    } else {
      bool result =
          await drivingRouteSearch.dringRouteSearch(drivingRoutePlanOption);
      if (result) {
        print("发起检索成功");
      } else {
        print("发起检索失败");
      }
    }
  }

  Future<void> requestVisitDetail() async {
    var params = {'lat': lat, 'lng': lng, "planIdStr": widget.planIdStr};
    showLoadingDialog();
    var result = await NetworkV2<ScheduleVisitPlanningRoadmapData>(
            ScheduleVisitPlanningRoadmapData())
        // visit/propose/list
        .requestDataV2("visit/plan/share/list",
            method: RequestMethod.GET, parameters: params);
    initImgUrl();
    dismissLoadingDialog();
    // print("bbbb , ${result}");
    if (mounted && result.isSuccess == true) {
      listData = result.getData();
     
      setState(() {});
      buildDrivingPlanRoute();
    }
  }

  void addRoutePolyline() {
    if (driveRoute == null) {
      return;
    }
    List<BMFMarker> markers = [];

    /// 起点marker
    var startMarker = buildMarker(0, driveRoute!.startNode?.location);
    if (startMarker != null) {
      markers.add(startMarker);
    }

    /// 终点marker
    var endMarker = buildMarker(
        (listData?.planList?.length ?? 0) - 1, driveRoute!.endNode?.location);
    if (endMarker != null) {
      markers.add(endMarker);
    }

    /// 驾车途径点marker
    for (int index = 0; index < (driveRoute!.wayPoints?.length ?? 0); index++) {
      var wayMarker = buildMarker(index + 1, driveRoute!.wayPoints?[index].pt);
      if (wayMarker != null) {
        markers.add(wayMarker);
      }
    }

    /// 添加marker
    myMapController?.cleanAllMarkers();

    /// 添加路线polyline
    if (polyline != null) {
      myMapController?.removeOverlay(polyline!.Id);
    }

    polyline = BMFPolyline(
      width: Platform.isAndroid ? 15 : 5,
      coordinates: driveRoute!.routeCoordinates!,
      indexs: driveRoute!.routeCoordinates!.map((e) => 1).toList(),
      textures: [
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_unknown.png",
      ],
      dottedLine: false,
    );
    myMapController?.addPolyline(polyline!);
    myMapController?.addMarkers(markers);

    /// 根据polyline设置地图显示范围
    BMFCoordinateBounds coordinateBounds =
        getVisibleMapRect(polyline!.coordinates);

    if (Platform.isAndroid) {
      setZoomLevel(coordinateBounds);
    }

    myMapController?.setVisibleMapRectWithPadding(
      visibleMapBounds: coordinateBounds,
      insets: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
      animated: true,
    );
  }

  void setZoomLevel(BMFCoordinateBounds bounds) async {
    var locationDistance = await BMFCalculateUtils.getLocationDistance(
            bounds.northeast, bounds.southwest) ??
        0;
    double level = -1;
    for (var i = 0; i < zoomLevel.length; i++) {
      if (zoomLevel[i] < locationDistance) {
        level = 18 - i + 3;
      }
    }
    if (level != -1) {
      myMapController?.setZoomTo(level);
    }
  }

  double convertDouble(String value) {
    try {
      return double.tryParse(value) ?? 0;
    } catch (e) {}
    return 0;
  }

  /// 获取地图显示区域
  BMFCoordinateBounds getVisibleMapRect(List<BMFCoordinate> coordinates) {
    BMFCoordinate first = coordinates[0];
    double leftBottomX = first.latitude;
    double leftBottomY = first.longitude;
    double rightTopX = first.latitude;
    double rightTopY = first.longitude;

    for (BMFCoordinate coordinate in coordinates) {
      if (coordinate.latitude < leftBottomX) {
        leftBottomX = coordinate.latitude;
      }

      if (coordinate.longitude < leftBottomY) {
        leftBottomY = coordinate.longitude;
      }

      if (coordinate.latitude > rightTopX) {
        rightTopX = coordinate.latitude;
      }

      if (coordinate.longitude > rightTopY) {
        rightTopY = coordinate.longitude;
      }
    }

    BMFCoordinate leftBottom = BMFCoordinate(leftBottomX, leftBottomY);
    BMFCoordinate rightTop = BMFCoordinate(rightTopX, rightTopY);

    BMFCoordinateBounds coordinateBounds =
        BMFCoordinateBounds(northeast: rightTop, southwest: leftBottom);

    return coordinateBounds;
  }

  BMFMarker? buildMarker(int index, BMFCoordinate? coord) {
    if (index < 0) {
      return null;
    }
    var iconPath = "assets/images/schedule/marker_";
    var visitData = listData?.planList?[index];
    if (coord == null || visitData == null) {
      return null;
    }

    iconPath += "normal";

    // 本地最多25个图片
    if (index >= 25) {
      iconPath += "/25.png";
    } else {
      iconPath += "/${index + 1}.png";
    }
    print("dddd buildMarker $iconPath");
    return BMFMarker.icon(
      scaleX: 1,
      scaleY: 1,
      position: coord,
      title: index.toString(),
      icon: iconPath,
    );
  }

  Widget getContentView() {
    // print('ssss${item}');
    List<Widget> list = [];
    List<ScheduleVisitPlanningRoadmapPlanListData>? visitResult =
        listData?.planList;
    var index = 0;
    visitResult?.forEach((item) {
      var poiImage = item.poiImage;
      ++index;
      list.add(Container(
        padding: EdgeInsets.only(top: 14, left: 14, right: 12, bottom: 12.5),
        decoration: BoxDecoration(
          color: Color(0xffffffff),
          borderRadius: BorderRadius.all(Radius.circular(8.0)),
        ),
        // height: 108,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 0,
              child: Stack(
                alignment: Alignment.center,
                overflow: Overflow.visible,
                children: [
                  Container(
                    width: 63,
                    height: 63,
                    //超出部分，可裁剪
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: item?.poiImage != null
                        ? Image.network(
                            item?.poiImage,
                            fit: BoxFit.cover,
                          )
                        : null,
                  ),
                  Positioned(
                    top: -10,
                    left: -10,
                    child: Container(
                      width: 55,
                      height: 22,
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(
                              'assets/images/base/planningRoadmap-icon.png'),
                          fit: BoxFit.fill, // 完全填充
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: EdgeInsets.only(top: 5.2, left: 2.5),
                            width: 19,
                            height: 22,
                            child: Text(
                              "$index",
                              style: TextStyle(
                                fontSize: 12.0,
                                height: 1,
                                color: Color(0xffffffff),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(top: 3),
                            child: Text(
                              item?.planTagText,
                              style: TextStyle(
                                fontSize: 12.0,
                                height: 1,
                                color: Color(0xffffffff),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Container(
                  // height: 80,
                  padding: const EdgeInsets.only(left: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${item?.customerName}",
                        textAlign: TextAlign.start,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 15.0,
                            height: 1.4,
                            fontWeight: FontWeight.bold),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 7),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item?.latestOrderTime != null
                                  ? "上次下单：${item?.latestOrderTime}"
                                  : '',
                              textAlign: TextAlign.start,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 12.0,
                                  height: 1.4,
                                  color: Color(0xff676773)),
                            ),
                            Text(
                              item?.latestVisitTime != ''
                                  ? "上次拜访：${item?.latestVisitTime}"
                                  : '',
                              textAlign: TextAlign.start,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 12.0,
                                  height: 1.4,
                                  color: Color(0xff676773)),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )),
            ),
          ],
        ),
      ));
      list.add(SizedBox(height: 10));
    });

    return Container(
      padding: EdgeInsets.only(top: 10),
      color: Color(0xff00B377),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 10, right: 10),
            child: Column(
              children: list,
            ),
          ),
        ],
      ),
    );
  }

  //尾部左边：姓名、部门
  Widget buildBottomLeftColumn() {
    return Container(
      // color: Colors.green,
      child: Row(
        children: <Widget>[
          Container(
            child: Image.asset(
              'assets/images/share/share_logo.png',
              width: 43,
              height: 43,
            ),
          ),
          const SizedBox(width: 7.5),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  userInfo.realName ?? '--',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF676773),
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  '${userInfo.department ?? '--'}',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF676773),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //尾部右边：日期、星期、天干地支
  Widget buildBottomRightColumn() {
    return Container(
      // color: Colors.blue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '${listData?.chineseDate} ${listData?.weekDay}', //12月26日 星期六
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Color(0xFF00B377)),
          ),
          SizedBox(height: 5),
          Text(
            '${listData?.ganZhiDate}', //天干地支
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF85858F),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildWidget(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: shareNotifier,
        builder: (BuildContext context, bool refel, Widget? child) {
          return Scaffold(
            backgroundColor: Color(0xff00B377),
            body: Stack(
              children: [
                Container(
                    height: 270,
                    child: BMFMapWidget(
                      onBMFMapCreated: (controller) {
                        myMapController = controller;
                      },
                      mapOptions: mapOptions,
                    )),
                Container(
                  margin: EdgeInsets.only(top: 270),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: RepaintBoundary(
                      key: repaintWidgetKey,
                      child: getContentView(),
                    ),
                  ),
                ),
                refel
                    ? Container(
                        margin: EdgeInsets.only(top: 6666),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          child: RepaintBoundary(
                            key: totalWidgetKey,
                            child: Column(
                              children: [
                                Stack(
                                  children: [
                                    Image.file(File(bdUrl ?? '')),
                                    Positioned(
                                      top: 0,
                                      left: 0,
                                      child: Image.file(
                                        File(listHeaderUrl ?? '')),width: 200,height: 45,)
                                    
                                  ],
                                ),
                                Image.file(File(clientsUrl ?? '')),
                                Image.file(File(userInfoUrl ?? '')),
                              ],
                            ),
                          ),
                        ),
                      )
                    : SizedBox()
              ],
            ),
          );
        });
  }

  @override
  String getTitleName() {
    return "计划路线图";
  }
}
