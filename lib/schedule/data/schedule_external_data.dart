import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_external_data.g.dart';

@JsonSerializable()
class ScheduleExternalModel extends BaseModelV2 {
  dynamic registerFlag = 1;

  /// 客户信息
  ScheduleExternalCustomerModel? customer;

  /// 联系人信息
  List<ScheduleExternalContactModel>? contactList;

  /// 对象经营状况
  ScheduleExternalBasicInfoModel? merchantBasicInfo;

    /// 对象经营状况（新）
  ScheduleExternalmerchantMapModel? merchantMap;

  ScheduleExternalModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleExternalModelToJson(this);
  }

  factory ScheduleExternalModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExternalModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleExternalModel.fromJson(json);
  }
}

@JsonSerializable()
class ScheduleExternalCustomerModel {
  String? id;
  String? name;

  /// 距离    3.0.0新增
  String? distance;

  /// 客户标签 3.0.0 新增
  List<String>? labs;

  /// 荷叶客户需传递PoiId 进行联系人的选择
  dynamic? poiId;

  ///必填资质；-1 正常；1-临期；2-过期
  @JsonKey(defaultValue: -1)
  dynamic licenseValidateMust;

  ///非必填资质 -1 正常；1-临期；2-过期
  @JsonKey(defaultValue: -1)
  dynamic licenseValidateIssue;

  /// 3:冻结
  dynamic merchantStatus;

  ScheduleExternalCustomerModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleExternalCustomerModelToJson(this);
  }

  factory ScheduleExternalCustomerModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExternalCustomerModelFromJson(json);
}

@JsonSerializable()
class ScheduleExternalContactModel {
  int? isKP;
  dynamic? id;
  String? contactMobile;
  String? contactName;
  String? contactJob;

  /// 是否KP （1：是；2：否；)
  int? isEffective;

  ScheduleExternalContactModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleExternalContactModelToJson(this);
  }

  factory ScheduleExternalContactModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExternalContactModelFromJson(json);
}

@JsonSerializable()
class ScheduleExternalBasicInfoModel {
  /// 经营面积
  dynamic? areaSize;

  /// 周边情况
  dynamic? aroundEnv;

  /// 客户需求SKU数
  dynamic? buySkus;

  /// 客户流量
  dynamic? buyersAmount;

  /// 主要消费人群 多选（请用key值拼接，英文半角逗号分割）
  String? buyersType;

  /// 客户人数
  String? clerkNum;

  /// 主要消费结构 多选（请用key值拼接，英文半角逗号分割）
  String? mainlyConsumeMedTypes;

  /// 是否医保
  dynamic? medicalInsurance;

  /// 客户需求
  String? merchantDemand;

  /// 客户类型 -- 废弃
  String? merchantType;

  /// 月销售额
  dynamic? monthlySales;

  /// 是否需要员工培训
  dynamic? needClerkTrains;

  /// 是否需要门店诊断
  dynamic? needMerchantDiagnose;

  /// 是否需要动销
  dynamic? needPullSales;

  /// 核心供应商
  String? purchaseWay;

  /// 缺失品类
  String? shortOfTypes;

  ScheduleExternalBasicInfoModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleExternalBasicInfoModelToJson(this);
  }

  factory ScheduleExternalBasicInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExternalBasicInfoModelFromJson(json);
}

@JsonSerializable()
///我也不知道这些参数是啥，去问后台
class ScheduleExternalmerchantMapModel {
  dynamic? id;
  String? customerName;
  dynamic? biLifecycle;
  String? poiLongitude;
  String? poiLatitude;
  String? address;
  dynamic? licenseStatus;
  dynamic? customerType;
  dynamic? biLevel;
  String? registerMobile;
  dynamic? registerFlag;
  dynamic? merchantId;
  dynamic? licenseValidateFlag;
  dynamic? poiId;
  dynamic? firstOrderFlag;
  String? ecCustomerName;
  String? ecAddress;
  dynamic? provinceCode;
  dynamic? cityCode;
  dynamic? areaCode;
  String? statusName;
  dynamic? poiRegisterFlag;
  dynamic? licenseValidateMust;
  dynamic? licenseValidateIssue;
  dynamic? merchantStatus;

  ScheduleExternalmerchantMapModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleExternalmerchantMapModelToJson(this);
  }

  factory ScheduleExternalmerchantMapModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleExternalmerchantMapModelFromJson(json);
}



