import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_post_data_params.g.dart';

@JsonSerializable()
class SchedulePostDataParamsModel {
  /// 经营信息
  ScheduleMerchantBasicInfoModel crmMerchantBasicInfoVo =
      ScheduleMerchantBasicInfoModel();

  /// 拜访信息
  ScheduleMerchantVisitParamsModel merchantVisitParam = ScheduleMerchantVisitParamsModel();

  SchedulePostDataParamsModel();

  Map<String, dynamic> toJson() {
    return _$SchedulePostDataParamsModelToJson(this);
  }

  factory SchedulePostDataParamsModel.fromJson(Map<String, dynamic> json) =>
      _$SchedulePostDataParamsModelFromJson(json);
}


@JsonSerializable()
class ScheduleMerchantVisitParamsModel {
  String? id;
  /// 陪访人oaId
  String? accompanyOaId;

  /// 药店地址
  String? address;

  /// 联系人
  String? contactor;

  /// 图片路径数组
  List<String> image = [];

  /// 是否维价
  String? isDimprice;

  /// 是否KP （1：是；2：否；)
  String isEffective = "1";

  /// 纬度
  String? lat;

  /// 经度
  String? lng;

  /// 药店ID
  String? merchantId;

  /// 药店名称
  String? merchantName;

  /// 手机号
  String? mobile;

  /// 通话时长
  String? talkTime;

  /// 拜访目的
  String? visitReason;

  /// 拜访类型 1：上门拜访，2：电话拜访
  String? visitType = "1";

  String? customerId;

  String? sysUserId;

  String? visitDemo;

  // 新增资质状态相关字段
  String? poiStatus;
  String? operateStatus;
  String? siteStatus;
  String? businessLicenseStatus;
  String? institutionLicenseStatus;
  String? drugLicenseStatus;
  String? equipmentLicenseStatus;
  String? foodLicenseStatus;

  ScheduleMerchantVisitParamsModel();

  factory ScheduleMerchantVisitParamsModel.fromScheduleMerchantVisitModel(
    ScheduleMerchantVisitModel scheduleMerchantVisitModel,
    String? customerId,
    String? sysUserId,
    String? visitDemo,
    String? id, {
    Map<String, String>? qualificationStatusMap,
  }) {
    var scheduleMerchantVisitParamsModel = ScheduleMerchantVisitParamsModel();
    scheduleMerchantVisitParamsModel.id = id;
    scheduleMerchantVisitParamsModel.accompanyOaId = scheduleMerchantVisitModel.accompanyOaId;
    scheduleMerchantVisitParamsModel.address = scheduleMerchantVisitModel.address;
    scheduleMerchantVisitParamsModel.contactor = scheduleMerchantVisitModel.contactor;
    scheduleMerchantVisitParamsModel.image = scheduleMerchantVisitModel.image;
    scheduleMerchantVisitParamsModel.isDimprice = scheduleMerchantVisitModel.isDimprice;
    scheduleMerchantVisitParamsModel.isEffective = scheduleMerchantVisitModel.isEffective;
    scheduleMerchantVisitParamsModel.lat = scheduleMerchantVisitModel.lat;
    scheduleMerchantVisitParamsModel.lng = scheduleMerchantVisitModel.lng;
    scheduleMerchantVisitParamsModel.merchantId = scheduleMerchantVisitModel.merchantId;
    scheduleMerchantVisitParamsModel.merchantName = scheduleMerchantVisitModel.merchantName;
    scheduleMerchantVisitParamsModel.mobile = scheduleMerchantVisitModel.mobile;
    scheduleMerchantVisitParamsModel.talkTime = scheduleMerchantVisitModel.talkTime;
    scheduleMerchantVisitParamsModel.visitReason = scheduleMerchantVisitModel.visitReason;
    scheduleMerchantVisitParamsModel.visitType = scheduleMerchantVisitModel.visitType;
    scheduleMerchantVisitParamsModel.customerId = customerId;
    scheduleMerchantVisitParamsModel.sysUserId = sysUserId;
    scheduleMerchantVisitParamsModel.visitDemo = visitDemo;
    // 资质状态字段赋值
    if (qualificationStatusMap != null) {
      scheduleMerchantVisitParamsModel.poiStatus = qualificationStatusMap['poiStatus'] ?? '';
      scheduleMerchantVisitParamsModel.operateStatus = qualificationStatusMap['operateStatus'] ?? '';
      scheduleMerchantVisitParamsModel.siteStatus = qualificationStatusMap['siteStatus'] ?? '';
      scheduleMerchantVisitParamsModel.businessLicenseStatus = qualificationStatusMap['businessLicenseStatus'] ?? '';
      scheduleMerchantVisitParamsModel.institutionLicenseStatus = qualificationStatusMap['institutionLicenseStatus'] ?? '';
      scheduleMerchantVisitParamsModel.drugLicenseStatus = qualificationStatusMap['drugLicenseStatus'] ?? '';
      scheduleMerchantVisitParamsModel.equipmentLicenseStatus = qualificationStatusMap['equipmentLicenseStatus'] ?? '';
      scheduleMerchantVisitParamsModel.foodLicenseStatus = qualificationStatusMap['foodLicenseStatus'] ?? '';
    }
    return scheduleMerchantVisitParamsModel;
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleMerchantVisitParamsModelToJson(this);
  }

  factory ScheduleMerchantVisitParamsModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleMerchantVisitParamsModelFromJson(json);
}
