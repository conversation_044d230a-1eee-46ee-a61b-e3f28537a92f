// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_external_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleExternalModel _$ScheduleExternalModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleExternalModel()
    ..registerFlag = json['registerFlag']
    ..customer = json['customer'] == null
        ? null
        : ScheduleExternalCustomerModel.fromJson(
            json['customer'] as Map<String, dynamic>)
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map((e) =>
            ScheduleExternalContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..merchantBasicInfo = json['merchantBasicInfo'] == null
        ? null
        : ScheduleExternalBasicInfoModel.fromJson(
            json['merchantBasicInfo'] as Map<String, dynamic>)
    ..merchantMap = json['merchantMap'] == null
        ? null
        : ScheduleExternalmerchantMapModel.fromJson(
            json['merchantMap'] as Map<String, dynamic>);
}

Map<String, dynamic> _$ScheduleExternalModelToJson(
        ScheduleExternalModel instance) =>
    <String, dynamic>{
      'registerFlag': instance.registerFlag,
      'customer': instance.customer,
      'contactList': instance.contactList,
      'merchantBasicInfo': instance.merchantBasicInfo,
      'merchantMap': instance.merchantMap,
    };

ScheduleExternalCustomerModel _$ScheduleExternalCustomerModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleExternalCustomerModel()
    ..id = json['id'] as String?
    ..name = json['name'] as String?
    ..distance = json['distance'] as String?
    ..labs = (json['labs'] as List<dynamic>?)?.map((e) => e as String).toList()
    ..poiId = json['poiId']
    ..licenseValidateMust = json['licenseValidateMust'] ?? -1
    ..licenseValidateIssue = json['licenseValidateIssue'] ?? -1
    ..merchantStatus = json['merchantStatus'];
}

Map<String, dynamic> _$ScheduleExternalCustomerModelToJson(
        ScheduleExternalCustomerModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'distance': instance.distance,
      'labs': instance.labs,
      'poiId': instance.poiId,
      'licenseValidateMust': instance.licenseValidateMust,
      'licenseValidateIssue': instance.licenseValidateIssue,
      'merchantStatus': instance.merchantStatus,
    };

ScheduleExternalContactModel _$ScheduleExternalContactModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleExternalContactModel()
    ..isKP = json['isKP'] as int?
    ..id = json['id']
    ..contactMobile = json['contactMobile'] as String?
    ..contactName = json['contactName'] as String?
    ..contactJob = json['contactJob'] as String?
    ..isEffective = json['isEffective'] as int?;
}

Map<String, dynamic> _$ScheduleExternalContactModelToJson(
        ScheduleExternalContactModel instance) =>
    <String, dynamic>{
      'isKP': instance.isKP,
      'id': instance.id,
      'contactMobile': instance.contactMobile,
      'contactName': instance.contactName,
      'contactJob': instance.contactJob,
      'isEffective': instance.isEffective,
    };

ScheduleExternalBasicInfoModel _$ScheduleExternalBasicInfoModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleExternalBasicInfoModel()
    ..areaSize = json['areaSize']
    ..aroundEnv = json['aroundEnv']
    ..buySkus = json['buySkus']
    ..buyersAmount = json['buyersAmount']
    ..buyersType = json['buyersType'] as String?
    ..clerkNum = json['clerkNum'] as String?
    ..mainlyConsumeMedTypes = json['mainlyConsumeMedTypes'] as String?
    ..medicalInsurance = json['medicalInsurance']
    ..merchantDemand = json['merchantDemand'] as String?
    ..merchantType = json['merchantType'] as String?
    ..monthlySales = json['monthlySales']
    ..needClerkTrains = json['needClerkTrains']
    ..needMerchantDiagnose = json['needMerchantDiagnose']
    ..needPullSales = json['needPullSales']
    ..purchaseWay = json['purchaseWay'] as String?
    ..shortOfTypes = json['shortOfTypes'] as String?;
}

ScheduleExternalmerchantMapModel _$ScheduleExternalmerchantMapModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleExternalmerchantMapModel()
    ..id = json["id"]
    ..customerName = json["customerName"]
    ..biLifecycle = json["biLifecycle"]
    ..poiLongitude = json["poiLongitude"]
    ..poiLatitude = json["poiLatitude"]
    ..address = json["address"]
    ..licenseStatus = json["licenseStatus"]
    ..customerType = json["customerType"]
    ..biLevel = json["biLevel"]
    ..registerMobile = json["registerMobile"]
    ..registerFlag = json["registerFlag"]
    ..merchantId = json["merchantId"]
    ..licenseValidateFlag = json["licenseValidateFlag"]
    ..poiId = json["poiId"]
    ..firstOrderFlag = json["firstOrderFlag"]
    ..ecCustomerName = json["ecCustomerName"]
    ..ecAddress = json["ecAddress"]
    ..provinceCode = json["provinceCode"]
    ..cityCode = json["cityCode"]
    ..areaCode = json["areaCode"]
    ..statusName = json["statusName"]
    ..poiRegisterFlag = json["poiRegisterFlag"]
    ..licenseValidateMust = json["licenseValidateMust"]
    ..licenseValidateIssue = json["licenseValidateIssue"]
    ..merchantStatus = json["merchantStatus"];
}

Map<String, dynamic> _$ScheduleExternalBasicInfoModelToJson(
        ScheduleExternalBasicInfoModel instance) =>
    <String, dynamic>{
      'areaSize': instance.areaSize,
      'aroundEnv': instance.aroundEnv,
      'buySkus': instance.buySkus,
      'buyersAmount': instance.buyersAmount,
      'buyersType': instance.buyersType,
      'clerkNum': instance.clerkNum,
      'mainlyConsumeMedTypes': instance.mainlyConsumeMedTypes,
      'medicalInsurance': instance.medicalInsurance,
      'merchantDemand': instance.merchantDemand,
      'merchantType': instance.merchantType,
      'monthlySales': instance.monthlySales,
      'needClerkTrains': instance.needClerkTrains,
      'needMerchantDiagnose': instance.needMerchantDiagnose,
      'needPullSales': instance.needPullSales,
      'purchaseWay': instance.purchaseWay,
      'shortOfTypes': instance.shortOfTypes,
    };

Map<String, dynamic> _$ScheduleExternalmerchantMapModelToJson(
        ScheduleExternalmerchantMapModel instance) =>
    <String, dynamic>{
      "id": instance.id,
      "customerName": instance.customerName,
      "biLifecycle": instance.biLifecycle,
      "poiLongitude": instance.poiLongitude,
      "poiLatitude": instance.poiLatitude,
      "address": instance.address,
      "licenseStatus": instance.licenseStatus,
      "customerType": instance.customerType,
      "biLevel": instance.biLevel,
      "registerMobile": instance.registerMobile,
      "registerFlag": instance.registerFlag,
      "merchantId": instance.merchantId,
      "licenseValidateFlag": instance.licenseValidateFlag,
      "poiId": instance.poiId,
      "firstOrderFlag": 1,
      "ecCustomerName": instance.ecCustomerName,
      "ecAddress": instance.ecAddress,
      "provinceCode": instance.provinceCode,
      "cityCode": instance.cityCode,
      "areaCode": instance.areaCode,
      "statusName": instance.statusName,
      "poiRegisterFlag": instance.poiRegisterFlag,
      "licenseValidateMust": instance.licenseValidateMust,
      "licenseValidateIssue": instance.licenseValidateIssue,
      "merchantStatus": instance.merchantStatus
    };
