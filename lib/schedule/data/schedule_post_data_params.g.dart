// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_post_data_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SchedulePostDataParamsModel _$SchedulePostDataParamsModelFromJson(
    Map<String, dynamic> json) {
  return SchedulePostDataParamsModel()
    ..crmMerchantBasicInfoVo = ScheduleMerchantBasicInfoModel.fromJson(
        json['crmMerchantBasicInfoVo'] as Map<String, dynamic>)
    ..merchantVisitParam = ScheduleMerchantVisitParamsModel.fromJson(
        json['merchantVisitParam'] as Map<String, dynamic>);
}

Map<String, dynamic> _$SchedulePostDataParamsModelToJson(
        SchedulePostDataParamsModel instance) =>
    <String, dynamic>{
      'crmMerchantBasicInfoVo': instance.crmMerchantBasicInfoVo,
      'merchantVisitParam': instance.merchantVisitParam,
    };

ScheduleMerchantVisitParamsModel _$ScheduleMerchantVisitParamsModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleMerchantVisitParamsModel()
    ..id = json['id'] as String?
    ..accompanyOaId = json['accompanyOaId'] as String?
    ..address = json['address'] as String?
    ..contactor = json['contactor'] as String?
    ..image = (json['image'] as List<dynamic>).map((e) => e as String).toList()
    ..isDimprice = json['isDimprice'] as String?
    ..isEffective = json['isEffective'] as String
    ..lat = json['lat'] as String?
    ..lng = json['lng'] as String?
    ..merchantId = json['merchantId'] as String?
    ..merchantName = json['merchantName'] as String?
    ..mobile = json['mobile'] as String?
    ..talkTime = json['talkTime'] as String?
    ..visitReason = json['visitReason'] as String?
    ..visitType = json['visitType'] as String?
    ..customerId = json['customerId'] as String?
    ..sysUserId = json['sysUserId'] as String?
    ..visitDemo = json['visitDemo'] as String?
    // 新增资质状态相关字段
    ..poiStatus = json['poiStatus'] as String?
    ..operateStatus = json['operateStatus'] as String?
    ..siteStatus = json['siteStatus'] as String?
    ..businessLicenseStatus = json['businessLicenseStatus'] as String?
    ..institutionLicenseStatus = json['institutionLicenseStatus'] as String?
    ..drugLicenseStatus = json['drugLicenseStatus'] as String?
    ..equipmentLicenseStatus = json['equipmentLicenseStatus'] as String?
    ..foodLicenseStatus = json['foodLicenseStatus'] as String?;
}

Map<String, dynamic> _$ScheduleMerchantVisitParamsModelToJson(
        ScheduleMerchantVisitParamsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accompanyOaId': instance.accompanyOaId,
      'address': instance.address,
      'contactor': instance.contactor,
      'image': instance.image,
      'isDimprice': instance.isDimprice,
      'isEffective': instance.isEffective,
      'lat': instance.lat,
      'lng': instance.lng,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'mobile': instance.mobile,
      'talkTime': instance.talkTime,
      'visitReason': instance.visitReason,
      'visitType': instance.visitType,
      'customerId': instance.customerId,
      'sysUserId': instance.sysUserId,
      'visitDemo': instance.visitDemo,
      // 新增字段
      'poiStatus': instance.poiStatus,
      'operateStatus': instance.operateStatus,
      'siteStatus': instance.siteStatus,
      'businessLicenseStatus': instance.businessLicenseStatus,
      'institutionLicenseStatus': instance.institutionLicenseStatus,
      'drugLicenseStatus': instance.drugLicenseStatus,
      'equipmentLicenseStatus': instance.equipmentLicenseStatus,
      'foodLicenseStatus': instance.foodLicenseStatus,
    };
