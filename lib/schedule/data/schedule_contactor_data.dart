import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_contactor_data.g.dart';

@JsonSerializable()
class ScheduleContactorData extends BaseModelV2 {
  //联系人生日
  dynamic contactBirth;
  //联系人职责
  dynamic? contactDuty;
  //联系人岗位
  dynamic? contactJob;
  //联系人岗位名称
  dynamic? contactJobName;
  //联系人电话
  dynamic? contactMobile;
  //联系人姓名
  dynamic? contactName;
  //姓别
  dynamic? contactSex;
  //标签(多个逗号分隔)
  dynamic? contactTag;
  //主键
  dynamic? id;
  //药店ID
  dynamic? merchantId;
  //药店名称
  dynamic? merchantName;
  //是否KP
  dynamic? isEffective;

  dynamic? poiId;

  ScheduleContactorData();

  Map<String, dynamic> toJson() {
    return _$ScheduleContactorDataToJson(this);
  }

  factory ScheduleContactorData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleContactorDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleContactorData.fromJson(json);
  }
}
