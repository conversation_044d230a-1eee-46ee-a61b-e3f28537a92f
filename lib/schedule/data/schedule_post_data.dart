import 'package:json_annotation/json_annotation.dart';

part 'schedule_post_data.g.dart';

@JsonSerializable()
class SchedulePostDataModel {
  /// 经营信息
  ScheduleMerchantBasicInfoModel crmMerchantBasicInfo =
      ScheduleMerchantBasicInfoModel();

  /// 拜访信息
  ScheduleMerchantVisitModel merchantVisit = ScheduleMerchantVisitModel();

  /// 任务信息
  CRMPersonalScheduleModel personalSchedule = CRMPersonalScheduleModel();

  /// 任务系统2.0 增加的参数 - 之前的任务参数暂时不做删除处理
  List<dynamic> taskMamagemDetailList = [];

  /// 选择未完善的联系人信息后，完善联系人传的参数
  CRMMerchantContactNewModel? merchantContactNew;

  /// 是否分享    0 不分享 ，1 分享
  String? shareStatus;

  /// 要传一个空对象，好像没什么用了。。放着吧
  Map<dynamic, dynamic> teamDynamic = {};

  SchedulePostDataModel();

  Map<String, dynamic> toJson() {
    return _$SchedulePostDataModelToJson(this);
  }

  factory SchedulePostDataModel.fromJson(Map<String, dynamic> json) =>
      _$SchedulePostDataModelFromJson(json);
}

@JsonSerializable()
class ScheduleMerchantBasicInfoModel {
  /// 经营面积
  dynamic? areaSize;

  /// 周边情况
  dynamic? aroundEnv;

  /// 客户需求SKU数
  dynamic? buySkus;

  /// 客户流量
  dynamic? buyersAmount;

  /// 主要消费人群 多选（请用key值拼接，英文半角逗号分割）
  String? buyersType;

  /// 客户人数
  String? clerkNum;

  /// 主要消费结构 多选（请用key值拼接，英文半角逗号分割）
  String? mainlyConsumeMedTypes;

  /// 是否医保
  dynamic? medicalInsurance;

  /// 客户需求
  String? merchantDemand;

  /// 客户类型 -- 废弃
  String? merchantType;

  /// 月销售额
  dynamic? monthlySales;

  /// 是否需要员工培训
  dynamic? needClerkTrains;

  /// 是否需要门店诊断
  dynamic? needMerchantDiagnose;

  /// 是否需要动销
  dynamic? needPullSales;

  /// 核心供应商
  String? purchaseWay;

  /// 缺失品类
  String? shortOfTypes;

  ScheduleMerchantBasicInfoModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleMerchantBasicInfoModelToJson(this);
  }

  factory ScheduleMerchantBasicInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleMerchantBasicInfoModelFromJson(json);

  /// 校验必填字段是否填写
  bool checkNeedData() {
    Map<String, dynamic> param = this.toJson();
    List<String> needData = [
      "medicalInsurance",
      "areaSize",
      "clerkNum",
      "aroundEnv",
      "buyersType",
      "buyersAmount",
      "buySkus",
      "mainlyConsumeMedTypes",
      "needPullSales",
      "needMerchantDiagnose",
      "needClerkTrains",
      "monthlySales",
    ];
    List<dynamic> result = needData.map((e) => param[e]).toList();
    result.removeWhere(
        (element) => element == null || element.toString().length <= 0);
    return result.length == needData.length;
  }
}

@JsonSerializable()
class ScheduleMerchantVisitModel {
  /// 陪访人oaId
  String? accompanyOaId;

  /// 药店地址
  String? address;

  /// 联系人
  String? contactor;

  /// 图片路径数组
  List<String> image = [];

  /// 是否维价
  String? isDimprice;

  /// 是否KP （1：是；2：否；)
  String isEffective = "1";

  /// 纬度
  String? lat;

  /// 经度
  String? lng;

  /// 药店ID  客户编码
  String? merchantId;

  /// 药店名称
  String? merchantName;

  /// 手机号
  String? mobile;

  /// 通话时长
  String? talkTime;

  /// 拜访目的
  String? visitReason;

  /// 客户 id
  String? customerId;

  /// 拜访类型 1：上门拜访，2：电话拜访
  String? visitType = "1";

    ScheduleMerchantVisitModel();

  Map<String, dynamic> toJson() {
    return _$ScheduleMerchantVisitModelToJson(this);
  }

  factory ScheduleMerchantVisitModel.fromJson(Map<String, dynamic> json) =>
      _$ScheduleMerchantVisitModelFromJson(json);
}

@JsonSerializable()
class CRMPersonalScheduleModel {
  /// 提醒时间
  String? alertTime;

  /// 日程结束时间
  String? endTime;

  /// 日程id
  String? id;

  /// 拜访总结
  String? remark;

  /// 日程主题
  String? scheduleTheme;

  /// 日程开始时间
  String? startTime;

  /// 关联任务号ID
  String? taskId;

  /// 日程类型 1电话拜访，2上门拜访
  String? type = "2";

  CRMPersonalScheduleModel();

  Map<String, dynamic> toJson() {
    return _$CRMPersonalScheduleModelToJson(this);
  }

  factory CRMPersonalScheduleModel.fromJson(Map<String, dynamic> json) =>
      _$CRMPersonalScheduleModelFromJson(json);
}

@JsonSerializable()
class CRMMerchantContactNewModel {
  /// 岗位
  String? contactJob;

  /// 联系电话
  String? contactMobile;

  /// 联系人
  String? contactName;

  CRMMerchantContactNewModel();

  Map<String, dynamic> toJson() {
    return _$CRMMerchantContactNewModelToJson(this);
  }

  factory CRMMerchantContactNewModel.fromJson(Map<String, dynamic> json) =>
      _$CRMMerchantContactNewModelFromJson(json);
}
