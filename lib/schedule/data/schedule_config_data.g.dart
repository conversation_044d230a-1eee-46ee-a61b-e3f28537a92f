// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_config_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleConfigSectionData _$ScheduleConfigSectionDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleConfigSectionData()
    ..title = json['title'] as String
    ..sectionKey = json['sectionKey'] as String
    ..items = (json['items'] as List<dynamic>)
        .map((e) => ScheduleConfigItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleConfigSectionDataToJson(
        ScheduleConfigSectionData instance) =>
    <String, dynamic>{
      'title': instance.title,
      'sectionKey': instance.sectionKey,
      'items': instance.items,
    };

ScheduleConfigItemData _$ScheduleConfigItemDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleConfigItemData()
    ..itemKey = json['itemKey'] as String
    ..itemType = json['itemType'] as int?
    ..selectType = json['selectType'] as int?
    ..itemTitle = json['itemTitle'] as String
    ..itemPlaceHolder = json['itemPlaceHolder'] as String
    ..itemPlaceHolderColor = json['itemPlaceHolderColor'] as int?
    ..isCanEdit = json['isCanEdit'] as bool?
    ..imageCount = json['imageCount'] as int?
    ..textCount = json['textCount'] as int?
    ..defaultText = json['defaultText'] as String?
    ..isOn = json['isOn'] as bool?;
}

Map<String, dynamic> _$ScheduleConfigItemDataToJson(
        ScheduleConfigItemData instance) =>
    <String, dynamic>{
      'itemKey': instance.itemKey,
      'itemType': instance.itemType,
      'selectType': instance.selectType,
      'itemTitle': instance.itemTitle,
      'itemPlaceHolder': instance.itemPlaceHolder,
      'itemPlaceHolderColor': instance.itemPlaceHolderColor,
      'isCanEdit': instance.isCanEdit,
      'imageCount': instance.imageCount,
      'textCount': instance.textCount,
      'defaultText': instance.defaultText,
      'isOn': instance.isOn,
    };
