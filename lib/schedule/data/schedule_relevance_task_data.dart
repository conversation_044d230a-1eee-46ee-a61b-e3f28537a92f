import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

part 'schedule_relevance_task_data.g.dart';

@JsonSerializable()
class ScheduleRelevanceTaskData extends BaseModelV2<ScheduleRelevanceTaskData> {
  bool? lastPage;
  List<ScheduleRelevanceTaskItem>? rows;

  ScheduleRelevanceTaskData();

  @override
  ScheduleRelevanceTaskData fromJsonMap(Map<String, dynamic>? json) {
    return ScheduleRelevanceTaskData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ScheduleRelevanceTaskDataToJson(this);
  }

  factory ScheduleRelevanceTaskData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleRelevanceTaskDataFromJson(json);
}

@JsonSerializable()
class ScheduleRelevanceTaskItem extends BaseModelV2<ScheduleRelevanceTaskItem> {
  int? id;
  String? theme;
  int? implementStatus;
  String? implementStatusName;
  int? customerId;
  String? customerName;
  String? creatorName;
  String? startTime;
  String? endTime;
  String? taskTime;
  int? type;
  String? typeName;

  @JsonKey(ignore: true)
  bool isSelected = false;

  @JsonKey(ignore: true)
  dynamic filledInfo;

  ScheduleRelevanceTaskItem();

  @override
  ScheduleRelevanceTaskItem fromJsonMap(Map<String, dynamic>? json) {
    return ScheduleRelevanceTaskItem.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ScheduleRelevanceTaskItemToJson(this);
  }

  factory ScheduleRelevanceTaskItem.fromJson(Map<String, dynamic> json) =>
      _$ScheduleRelevanceTaskItemFromJson(json);
}
