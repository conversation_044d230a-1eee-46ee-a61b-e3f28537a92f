import 'package:json_annotation/json_annotation.dart';

part 'schedule_config_data.g.dart';

@JsonSerializable()
class ScheduleConfigSectionData {
  String title = "";
  String sectionKey = "";

  List<ScheduleConfigItemData> items = [];

  ScheduleConfigSectionData();

  Map<String, dynamic> toJson() {
    return _$ScheduleConfigSectionDataToJson(this);
  }

  factory ScheduleConfigSectionData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleConfigSectionDataFromJson(json);
}

@JsonSerializable()
class ScheduleConfigItemData {
  String itemKey = "";

  /// 1:选择类型 2:文本类型 3:多行文本输入 4:开关 5:图片选择
  int? itemType = 1;

  /// 1:关联任务 2:拜访方式 3:拜访对象 4:联系人 5:拜访目的 6:对象经营状况 7:陪防人 8:通话时长 9:岗位
  int? selectType = 1;

  /// 标题
  String itemTitle = "";

  /// 提示文案
  String itemPlaceHolder = "";

  int? itemPlaceHolderColor = 0xFF8E8E93;

  /// 是否可编辑
  bool? isCanEdit = true;

  /// 选择图片的数量
  int? imageCount = 10;

  /// 输入文本长度限制
  int? textCount = 500;

  String? defaultText = "";

  /// 开关状态
  bool? isOn = false;

  ScheduleConfigItemData();

  Map<String, dynamic> toJson() {
    return _$ScheduleConfigItemDataToJson(this);
  }

  factory ScheduleConfigItemData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleConfigItemDataFromJson(json);
}
