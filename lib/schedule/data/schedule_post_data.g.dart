// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_post_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SchedulePostDataModel _$SchedulePostDataModelFromJson(
    Map<String, dynamic> json) {
  return SchedulePostDataModel()
    ..crmMerchantBasicInfo = ScheduleMerchantBasicInfoModel.fromJson(
        json['crmMerchantBasicInfo'] as Map<String, dynamic>)
    ..merchantVisit = ScheduleMerchantVisitModel.fromJson(
        json['merchantVisit'] as Map<String, dynamic>)
    ..personalSchedule = CRMPersonalScheduleModel.fromJson(
        json['personalSchedule'] as Map<String, dynamic>)
    ..taskMamagemDetailList = json['taskMamagemDetailList'] as List<dynamic>
    ..merchantContactNew = json['merchantContactNew'] == null
        ? null
        : CRMMerchantContactNewModel.fromJson(
            json['merchantContactNew'] as Map<String, dynamic>)
    ..shareStatus = json['shareStatus'] as String?
    ..teamDynamic = json['teamDynamic'] as Map<String, dynamic>;
}

Map<String, dynamic> _$SchedulePostDataModelToJson(
        SchedulePostDataModel instance) =>
    <String, dynamic>{
      'crmMerchantBasicInfo': instance.crmMerchantBasicInfo,
      'merchantVisit': instance.merchantVisit,
      'personalSchedule': instance.personalSchedule,
      'taskMamagemDetailList': instance.taskMamagemDetailList,
      'merchantContactNew': instance.merchantContactNew,
      'shareStatus': instance.shareStatus,
      'teamDynamic': instance.teamDynamic,
    };

ScheduleMerchantBasicInfoModel _$ScheduleMerchantBasicInfoModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleMerchantBasicInfoModel()
    ..areaSize = json['areaSize']
    ..aroundEnv = json['aroundEnv']
    ..buySkus = json['buySkus']
    ..buyersAmount = json['buyersAmount']
    ..buyersType = json['buyersType'] as String?
    ..clerkNum = json['clerkNum'] as String?
    ..mainlyConsumeMedTypes = json['mainlyConsumeMedTypes'] as String?
    ..medicalInsurance = json['medicalInsurance']
    ..merchantDemand = json['merchantDemand'] as String?
    ..merchantType = json['merchantType'] as String?
    ..monthlySales = json['monthlySales']
    ..needClerkTrains = json['needClerkTrains']
    ..needMerchantDiagnose = json['needMerchantDiagnose']
    ..needPullSales = json['needPullSales']
    ..purchaseWay = json['purchaseWay'] as String?
    ..shortOfTypes = json['shortOfTypes'] as String?;
}

Map<String, dynamic> _$ScheduleMerchantBasicInfoModelToJson(
        ScheduleMerchantBasicInfoModel instance) =>
    <String, dynamic>{
      'areaSize': instance.areaSize,
      'aroundEnv': instance.aroundEnv,
      'buySkus': instance.buySkus,
      'buyersAmount': instance.buyersAmount,
      'buyersType': instance.buyersType,
      'clerkNum': instance.clerkNum,
      'mainlyConsumeMedTypes': instance.mainlyConsumeMedTypes,
      'medicalInsurance': instance.medicalInsurance,
      'merchantDemand': instance.merchantDemand,
      'merchantType': instance.merchantType,
      'monthlySales': instance.monthlySales,
      'needClerkTrains': instance.needClerkTrains,
      'needMerchantDiagnose': instance.needMerchantDiagnose,
      'needPullSales': instance.needPullSales,
      'purchaseWay': instance.purchaseWay,
      'shortOfTypes': instance.shortOfTypes,
    };

ScheduleMerchantVisitModel _$ScheduleMerchantVisitModelFromJson(
    Map<String, dynamic> json) {
  return ScheduleMerchantVisitModel()
    ..accompanyOaId = json['accompanyOaId'] as String?
    ..address = json['address'] as String?
    ..contactor = json['contactor'] as String?
    ..image = (json['image'] as List<dynamic>).map((e) => e as String).toList()
    ..isDimprice = json['isDimprice'] as String?
    ..isEffective = json['isEffective'] as String
    ..lat = json['lat'] as String?
    ..lng = json['lng'] as String?
    ..merchantId = json['merchantId'] as String?
    ..customerId = json['customerId'] as String?
    ..merchantName = json['merchantName'] as String?
    ..mobile = json['mobile'] as String?
    ..talkTime = json['talkTime'] as String?
    ..visitReason = json['visitReason'] as String?
    ..visitType = json['visitType'] as String?;
}

Map<String, dynamic> _$ScheduleMerchantVisitModelToJson(
        ScheduleMerchantVisitModel instance) =>
    <String, dynamic>{
      'accompanyOaId': instance.accompanyOaId,
      'address': instance.address,
      'contactor': instance.contactor,
      'image': instance.image,
      'isDimprice': instance.isDimprice,
      'isEffective': instance.isEffective,
      'lat': instance.lat,
      'lng': instance.lng,
      'merchantId': instance.merchantId,
      'customerId': instance.customerId,
      'merchantName': instance.merchantName,
      'mobile': instance.mobile,
      'talkTime': instance.talkTime,
      'visitReason': instance.visitReason,
      'visitType': instance.visitType,
    };

CRMPersonalScheduleModel _$CRMPersonalScheduleModelFromJson(
    Map<String, dynamic> json) {
  return CRMPersonalScheduleModel()
    ..alertTime = json['alertTime'] as String?
    ..endTime = json['endTime'] as String?
    ..id = json['id'] as String?
    ..remark = json['remark'] as String?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..startTime = json['startTime'] as String?
    ..taskId = json['taskId'] as String?
    ..type = json['type'] as String?;
}

Map<String, dynamic> _$CRMPersonalScheduleModelToJson(
        CRMPersonalScheduleModel instance) =>
    <String, dynamic>{
      'alertTime': instance.alertTime,
      'endTime': instance.endTime,
      'id': instance.id,
      'remark': instance.remark,
      'scheduleTheme': instance.scheduleTheme,
      'startTime': instance.startTime,
      'taskId': instance.taskId,
      'type': instance.type,
    };

CRMMerchantContactNewModel _$CRMMerchantContactNewModelFromJson(
    Map<String, dynamic> json) {
  return CRMMerchantContactNewModel()
    ..contactJob = json['contactJob'] as String?
    ..contactMobile = json['contactMobile'] as String?
    ..contactName = json['contactName'] as String?;
}

Map<String, dynamic> _$CRMMerchantContactNewModelToJson(
        CRMMerchantContactNewModel instance) =>
    <String, dynamic>{
      'contactJob': instance.contactJob,
      'contactMobile': instance.contactMobile,
      'contactName': instance.contactName,
    };
