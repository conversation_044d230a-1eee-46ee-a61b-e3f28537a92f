class ScheduleShowData {
  /// 选择的任务名称
  String? scheduleTheme = "";

  /// 选择的拜访类型名称 - 默认上门拜访
  String? visitTypeName = "上门拜访";

  /// 是否KP - 展示名称 默认 是
  String? isKPName = "是";

  /// 选择得拜访目的名称
  String? purposeName;

  /// 岗位名称
  String? jobName;

  /// 联系人名称，如果是注册电话 填写了姓名后与merchantVisit.contactor不同，其他情况理论上两个名称一致
  String? contactor;

  /// 是否存在联系人信息
  bool isExistContactor = false;

  /// 当前选中的联系人id
  String? selectContactorId;

  /// 选择的陪访人名称
  String? selectAccompanyName;

  /// 是否是注册客户
  bool isRegister = true;

  /// 是否是荷叶健康得拜访
  bool isHeyeVisit = false;

  /// 荷叶拜访选择对象后 存储poiId供选择联系人时使用
  dynamic? poiId;
}
