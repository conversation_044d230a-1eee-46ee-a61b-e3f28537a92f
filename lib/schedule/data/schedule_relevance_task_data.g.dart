// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_relevance_task_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleRelevanceTaskData _$ScheduleRelevanceTaskDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleRelevanceTaskData()
    ..lastPage = json['lastPage'] as bool?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) =>
            ScheduleRelevanceTaskItem.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleRelevanceTaskDataToJson(
        ScheduleRelevanceTaskData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };

ScheduleRelevanceTaskItem _$ScheduleRelevanceTaskItemFromJson(
    Map<String, dynamic> json) {
  return ScheduleRelevanceTaskItem()
    ..id = json['id'] as int?
    ..theme = json['theme'] as String?
    ..implementStatus = json['implementStatus'] as int?
    ..implementStatusName = json['implementStatusName'] as String?
    ..customerId = json['customerId'] as int?
    ..customerName = json['customerName'] as String?
    ..creatorName = json['creatorName'] as String?
    ..startTime = json['startTime'] as String?
    ..endTime = json['endTime'] as String?
    ..taskTime = json['taskTime'] as String?
    ..type = json['type'] as int?
    ..typeName = json['typeName'] as String?;
}

Map<String, dynamic> _$ScheduleRelevanceTaskItemToJson(
        ScheduleRelevanceTaskItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'theme': instance.theme,
      'implementStatus': instance.implementStatus,
      'implementStatusName': instance.implementStatusName,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'creatorName': instance.creatorName,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'taskTime': instance.taskTime,
      'type': instance.type,
      'typeName': instance.typeName,
    };
