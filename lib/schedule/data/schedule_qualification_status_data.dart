import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_qualification_status_data.g.dart';

@JsonSerializable()
class ScheduleQualificationStatusData extends BaseModelV2<ScheduleQualificationStatusData> {
  final List<StatusOption>? businessLicenseStatus;
  final List<StatusOption>? operateStatus;
  final List<StatusOption>? drugLicenseStatus;
  final List<StatusOption>? siteStatus;
  final List<StatusOption>? poiStatus;
  final List<StatusOption>? equipmentLicenseStatus;
  final List<StatusOption>? institutionLicenseStatus;
  final List<StatusOption>? foodLicenseStatus;

  ScheduleQualificationStatusData({
    this.businessLicenseStatus,
    this.operateStatus,
    this.drugLicenseStatus,
    this.siteStatus,
    this.poiStatus,
    this.equipmentLicenseStatus,
    this.institutionLicenseStatus,
    this.foodLicenseStatus,
  });

  factory ScheduleQualificationStatusData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleQualificationStatusDataFromJson(json);

  @override
  ScheduleQualificationStatusData fromJsonMap(Map<String, dynamic>? json) {
    if (json == null) return ScheduleQualificationStatusData();
    return ScheduleQualificationStatusData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() => _$ScheduleQualificationStatusDataToJson(this);
}

@JsonSerializable()
class StatusOption {
  final int code;
  final String desc;

  StatusOption({
    required this.code,
    required this.desc,
  });

  factory StatusOption.fromJson(Map<String, dynamic> json) => _$StatusOptionFromJson(json);
  Map<String, dynamic> toJson() => _$StatusOptionToJson(this);
} 