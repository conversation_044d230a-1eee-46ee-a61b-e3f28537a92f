// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_qualification_status_data.dart';

ScheduleQualificationStatusData _$ScheduleQualificationStatusDataFromJson(Map<String, dynamic> json) => ScheduleQualificationStatusData(
      businessLicenseStatus: (json['businessLicenseStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      operateStatus: (json['operateStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      drugLicenseStatus: (json['drugLicenseStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      siteStatus: (json['siteStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      poiStatus: (json['poiStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      equipmentLicenseStatus: (json['equipmentLicenseStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      institutionLicenseStatus: (json['institutionLicenseStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
      foodLicenseStatus: (json['foodLicenseStatus'] as List<dynamic>?)
          ?.map((e) => StatusOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ScheduleQualificationStatusDataToJson(
        ScheduleQualificationStatusData instance) => <String, dynamic>{
      'businessLicenseStatus':
          instance.businessLicenseStatus?.map((e) => e.toJson()).toList(),
      'operateStatus': instance.operateStatus?.map((e) => e.toJson()).toList(),
      'drugLicenseStatus':
          instance.drugLicenseStatus?.map((e) => e.toJson()).toList(),
      'siteStatus': instance.siteStatus?.map((e) => e.toJson()).toList(),
      'poiStatus': instance.poiStatus?.map((e) => e.toJson()).toList(),
      'equipmentLicenseStatus':
          instance.equipmentLicenseStatus?.map((e) => e.toJson()).toList(),
      'institutionLicenseStatus':
          instance.institutionLicenseStatus?.map((e) => e.toJson()).toList(),
      'foodLicenseStatus': instance.foodLicenseStatus?.map((e) => e.toJson()).toList(),
    };

StatusOption _$StatusOptionFromJson(Map<String, dynamic> json) => StatusOption(
      code: json['code'] as int,
      desc: json['desc'] as String,
    );

Map<String, dynamic> _$StatusOptionToJson(StatusOption instance) => <String, dynamic>{
      'code': instance.code,
      'desc': instance.desc,
    }; 