import 'dart:convert';

import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_contactor_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/widget/select_contact_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class VisitSelectContactPage extends BasePage {
  final String customerId;
  final String customerName;
  final dynamic? selectId;

  VisitSelectContactPage( 
      {required this.customerId, required this.customerName, this.selectId});

  @override
  BaseState<StatefulWidget> initState() {
    return VisitSelectContactPageState();
  }
}

class VisitSelectContactPageState extends BaseState<VisitSelectContactPage> {
  List<ScheduleContactorData> dataSource = [];

  dynamic? selectId;
  int? selectIndex;

  @override
  void initState() {
    this.selectId = widget.selectId;
    this.requestContactListData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Stack(
        children: [
          Column(
            children: [
              Divider(
                color: Color(0xFFF6F6F6),
                height: 0.5,
              ),
              Expanded(
                child: EasyRefresh(
                  child: ListView.builder(
                    itemCount: this.dataSource.length,
                    itemBuilder: (context, index) {
                      ScheduleContactorData model = this.dataSource[index];
                      return GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          this.selectAction(index);
                        },
                        child: SelectContactItem(
                          model: model,
                          isSelected:
                              model.id.toString() == this.selectId.toString(),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          Visibility(
            visible: dataSource.isEmpty,
            child: PageStateWidget.pageEmpty(
              PageState.Error,
              stateText: "当前客户无联系人信息，请先去创建",
              buttonText: "新建联系人",
              imagePath: 'assets/images/base/state_empty.png',
              errorClick: this.addContactAction,
            ),
          ),
          Positioned(
            bottom: 20,
            right: 15,
            child: IconButton(
              onPressed: this.addContactAction,
              iconSize: 60,
              icon: Image.asset(
                'assets/images/schedule/select_contact_add.png',
                width: 60,
                height: 60,
              ),
            ),
          )
        ],
      ),
    );
  }

  /// Events
  void selectedFinished() {
    if (this.selectIndex != null) {
      ScheduleContactorData model = this.dataSource[this.selectIndex!];
      Navigator.of(context).pop(model.toJson());
    } else {
      Navigator.of(context).pop();
    }
  }

  void selectAction(int index) {
    ScheduleContactorData model = this.dataSource[index];
    this.selectId = model.id;
    this.selectIndex = index;
    setState(() {});
  }

  /// 添加联系人
  void addContactAction() {
    Map<String, dynamic> param = {
      "merchantId": widget.customerId,
      "merchantName": widget.customerName
    };
    String jsonStr = json.encode(param);
    var routerPath = "/add_contact_page?contactJSON=$jsonStr";
    routerPath = Uri.encodeFull(routerPath);
    Navigator.of(context).pushNamed(routerPath).then((resultData) {
      print('resultData - $resultData');
        this.requestContactListData();
    });;
  }

  /// Request
  void requestContactListData() async {
    showLoadingDialog();
    var reslut = await NetworkV2<ScheduleContactorData>(ScheduleContactorData())
        .requestDataV2('/contact/v290/queryContactsByMerchantId',
            parameters: {"merchantId": widget.customerId, "limit": "1000"});
    dismissLoadingDialog();
    if (reslut.isSuccess == true) {
      if (mounted) {
        setState(() {
          this.dataSource = reslut.getListData() ?? [];
          // this.dataSource.add(this.getInvalidContact());
        });
      }
    }
  }

  ScheduleContactorData getInvalidContact() {
    return ScheduleContactorData()
      ..contactName = "无效联系人拜访"
      ..id = -1;
  }

  /// NavigationBar
  @override
  String getTitleName() {
    return "选择联系人";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        TextButton(
          child: Text(
            '确定',
            style: TextStyle(
              color: Color(0xFF35C561),
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
          onPressed: () {
            FocusScope.of(context).requestFocus(FocusNode());
            this.selectedFinished();
          },
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
      ],
    );
  }
}
