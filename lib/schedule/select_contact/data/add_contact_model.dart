import 'package:date_format/date_format.dart';
import 'package:json_annotation/json_annotation.dart';

part 'add_contact_model.g.dart';

@JsonSerializable()
class AddContactModel {
  /// 创建人id
  dynamic creator;

  /// 生日
  dynamic contactBirth;

  /// 用于页面回显
  @JsonKey(ignore: true)
  String get birthdayStr {
    int timeStamp = int.tryParse("${this.contactBirth}") ?? 0;
    if (timeStamp <= 0) {
      return "";
    }
    DateTime time = DateTime.fromMillisecondsSinceEpoch(timeStamp);
    var dateTime = formatDate(time, [yyyy, '-', mm, '-', dd]);
    return dateTime;
  }

  /// 岗位
  dynamic contactJob;

  /// 岗位名称仅用于页面回显
  dynamic contactJobName;

  /// 是否kp
  @JsonKey(ignore: true)
  dynamic isEffective;

  /// 手机号
  dynamic contactMobile;

  /// 姓名
  dynamic contactName;

  /// 性别
  dynamic contactSex = 1;

  /// 性别名称仅用于回显
  @Json<PERSON>ey(ignore: true)
  String get contactSexName {
    if (contactSex.toString().length > 0) {
      return contactSex.toString() == "0" ? "女" : "男";
    }
    return "";
  }

  /// 标签
  dynamic contactTag;

  /// 客户id
  dynamic merchantId;

  /// 客户名称
  @JsonKey(ignore: true)
  dynamic merchantName;

  // dynamic sysUserId;

  /// 编辑的情况下 需要传递当前联系人id
  dynamic id;

  AddContactModel();

  Map<String, dynamic> toJson() {
    return _$AddContactModelToJson(this);
  }

  factory AddContactModel.fromJson(Map<String, dynamic> json) =>
      _$AddContactModelFromJson(json);
}
