import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/picker/string_value_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/base/time_picker/datetime_picker_theme.dart';
import 'package:XyyBeanSproutsFlutter/common/base/time_picker/flutter_datetime_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/data/add_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/widget/add_contact_input_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/widget/add_contact_select_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_contact/widget/add_contact_tag_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AddContactPage extends BasePage {
  final String? contactJSON;

  AddContactPage({this.contactJSON});

  @override
  BaseState<StatefulWidget> initState() {
    return AddContactPageState();
  }
}

class AddContactPageState extends BaseState<AddContactPage> {
  AddContactModel addModel = AddContactModel();

  @override
  void initState() {
    if (widget.contactJSON != null && widget.contactJSON!.length > 0) {
      Map<String, dynamic> jsonMap = json.decode(widget.contactJSON!);
      this.addModel = AddContactModel.fromJson(jsonMap);
      if (jsonMap.keys.contains("merchantName")) {
        this.addModel.merchantName = jsonMap["merchantName"];
      }
    }
    if (this.addModel.id == null) {
      // 新建联系人，添加默认性别
      this.addModel.contactSex = 1;
    }
    UserInfoUtil.sysUserId().then((value) {
      // this.addModel.sysUserId = value;
      this.addModel.creator = value;
    });
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        color: Color(0xFFF5F5F5),
        child: ListView(
          children: [
            AddContactInputItem(
              itemKey: "contactName",
              isCanEdit: true,
              placeHolder: '姓名',
              content: this.addModel.contactName,
              textChange: (result) {
                this.addModel.contactName = result;
              },
            ),
            AddContactInputItem(
              itemKey: "contactMobile",
              isCanEdit: true,
              placeHolder: '手机号',
              content: this.addModel.contactMobile,
              textChange: (result) {
                this.addModel.contactMobile = result;
              },
            ),
            AddContactInputItem(
              itemKey: "customerName",
              placeHolder: "",
              textChange: (result) {},
              isCanEdit: false,
              content: this.addModel.merchantName ?? "",
            ),
            Container(height: 10),
            AddContactSelectItem(
              itemKey: 'contactSex',
              itemTitle: '性别',
              content: this.addModel.contactSexName,
              selectAction: this.selectSex,
            ),
            AddContactSelectItem(
              itemKey: 'contactBirth',
              itemTitle: '生日',
              content: this.addModel.birthdayStr,
              selectAction: this.selectBirthday,
            ),
            AddContactSelectItem(
              itemKey: 'contactJob',
              itemTitle: '岗位',
              content: this.addModel.contactJobName,
              selectAction: this.selectJob,
            ),
            Container(height: 10),
            AddContactTagItem(
              tags: "${this.addModel.contactTag ?? ""}",
              valueChanged: (String value) {
                this.addModel.contactTag = value;
              },
            ),
          ],
        ),
      ),
    );
  }

  void selectSex(String itemKey, ValueNotifier<String?> controller) {
    FocusScope.of(context).requestFocus(FocusNode());
    List<Map<String, String>> sexList = [
      {'男': '1'},
      {'女': '0'},
    ];
    showStringValuePickerView<Map<String, String>>(
      context: context,
      source: sexList,
      defaultValue: this.addModel.contactSexName,
      middleTitle: "请选择性别",
      sourceBuilder: (value) {
        return value.keys.first;
      },
      selectedAction: (value) {
        this.addModel.contactSex = value.values.first;
        controller.value = this.addModel.contactSexName;
      },
    );
  }

  void selectBirthday(String itemKey, ValueNotifier<String?> controller) {
    FocusScope.of(context).requestFocus(FocusNode());
    var currentTime = (this.addModel.contactBirth != null &&
            this.addModel.contactBirth.toString().isNotEmpty)
        ? DateTime.parse(this.addModel.contactBirth)
        : DateTime.now();
    DatePicker.showYDMDatePicker(
      context,
      title: "请选择生日",
      currentTime: currentTime,
      maxTime: DateTime.now(),
      theme: datePickerTheme(),
      onConfirm: (value) {
        if (value == null) {
          XYYContainer.toastChannel.toast("时间错误");
          return;
        }
        var dateTime = formatDate(value, [yyyy, '-', mm, '-', dd]);
        this.addModel.contactBirth = dateTime;
        controller.value = dateTime;
      },
    );
  }

  DatePickerTheme datePickerTheme() {
    return DatePickerTheme(
      titleStyle: TextStyle(
        fontSize: 14,
        color: Color(0xFF8E8E93),
        fontWeight: FontWeight.w500,
      ),
      doneStyle: TextStyle(
        fontSize: 15,
        color: Color(0xFF35C561),
        fontWeight: FontWeight.w500,
      ),
      cancelStyle: TextStyle(
        fontSize: 15,
        color: Color(0xFF333333),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  void selectJob(String itemKey, ValueNotifier<String?> controller) async {
    FocusScope.of(context).requestFocus(FocusNode());
    // 读取文件内容
    String jsonString =
        await rootBundle.loadString("assets/sources/schedule/CRMJobMap.json");
    // 转List
    List<dynamic> jsonList = json.decode(jsonString);
    List<Map<String, dynamic>> resultMap =
        jsonList.map((e) => e as Map<String, dynamic>).toList();
    showStringValuePickerView<Map<String, dynamic>>(
      context: context,
      source: resultMap,
      defaultValue: this.addModel.contactJob,
      middleTitle: "请选择岗位",
      sourceBuilder: (value) {
        return value['contactJobName']!;
      },
      selectedAction: (value) {
        this.addModel.contactJob = value['contactJob'];
        this.addModel.contactJobName = value['contactJobName'];
        this.addModel.isEffective = value['isEffective'];
        controller.value = value['contactJobName'];
      },
    );
  }

  @override
  String getTitleName() {
    return this.addModel.id != null ? "编辑联系人" : "新建联系人";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      rightButtons: [
        TextButton(
          child: Text(
            '完成',
            style: TextStyle(
              color: Color(0xFF35C561),
              fontSize: 15,
              fontWeight: FontWeight.normal,
            ),
          ),
          onPressed: () {
            FocusScope.of(context).requestFocus(FocusNode());
            this.requestAddContactAction();
          },
          style: ButtonStyle(
            overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
            padding: MaterialStateProperty.all<EdgeInsets>(
                EdgeInsets.only(right: 10)),
            minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        )
      ],
    );
  }

  void requestAddContactAction() async {
    if (this.addModel.contactName?.toString().isEmpty ?? true) {
      showToast("请填写姓名");
      return;
    }
    if (this.addModel.contactMobile?.toString().isEmpty ?? true) {
      showToast("请填写手机号");
      return;
    }
    if (this.addModel.contactSexName.toString().isEmpty) {
      showToast("请选择性别");
      return;
    }
    if (this.addModel.contactJobName?.toString().isEmpty ?? true) {
      showToast("请选择岗位");
      return;
    }

    bool isEdit =
        (this.addModel.id != null) && (this.addModel.id.toString().isNotEmpty);
    String requestPath;
    if (isEdit) {
      requestPath = "/contact/v290/update";
    } else {
      requestPath = "/contact/v290/add";
    }
    var param = this.addModel.toJson();
    param.removeWhere((key, value) => value.toString().isEmpty);
    var result = await Network<NetworkBaseModel>(NetworkBaseModel())
        .requestData(requestPath, parameters: param, method: RequestMethod.GET);
    if (result.isSuccess == true) {
      showToast(isEdit ? "编辑联系人成功" : "新建联系人成功");
      Navigator.of(context).pop(result.data);
    } else {
      showToast(result.msg ?? "联系人创建失败");
    }
  }
}
