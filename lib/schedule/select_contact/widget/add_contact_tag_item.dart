import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// ignore: must_be_immutable
class AddContactTagItem extends StatefulWidget {
  final ValueChanged<String> valueChanged;

  final String? tags;

  AddContactTagItem({
    required this.valueChanged,
    this.tags,
  });

  @override
  State<StatefulWidget> createState() {
    return AddContactTagItemState();
  }
}

class AddContactTagItemState extends State<AddContactTagItem> {
  FocusNode _focusNode = FocusNode();

  TextEditingController _textEditingController = TextEditingController();

  List<String> tagList = [];

  @override
  void initState() {
    if (widget.tags != null && widget.tags?.isNotEmpty == true) {
      tagList = widget.tags!.split(",");
    }
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Text(
              '标签',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.normal,
                color: Color(0xFF666666),
              ),
            ),
          ),
          Container(
            color: Color(0xFFFFFFFF),
            padding: EdgeInsets.only(left: 15, right: 15),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 50,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: Color(0xFFFFFFFF),
                    ),
                    child: Container(
                      child: EnsureVisibleWhenFocused(
                        focusNode: _focusNode,
                        child: TextField(
                          focusNode: _focusNode,
                          inputFormatters: [
                            CustomizedTextInputFormatter(
                              filterPattern: RegExp(
                                  r'[\u4e00-\u9fa5，,。.：:（）()?？!！a-zA-Z0-9⼀⼁⼃⼂乛]*'),
                            ),
                          ],
                          controller: this._textEditingController,
                          onEditingComplete: this.addTextTag,
                          style: TextStyle(
                            fontSize: 15,
                            color: Color(0xFF333333),
                          ),
                          textAlign: TextAlign.left,
                          scrollPadding: EdgeInsets.zero,
                          decoration: InputDecoration(
                            counterText: "",
                            counterStyle: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF9494A6),
                            ),
                            hintText: '请输入标签,最多8个字',
                            hintStyle: TextStyle(
                              fontSize: 15,
                              color: Color(0xFF9494A6),
                            ),
                            hintMaxLines: 1,
                            border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                            ),
                            contentPadding: EdgeInsets.zero,
                            isDense: true,
                          ),
                          maxLength: 8, // 最大长度
                          maxLengthEnforcement: MaxLengthEnforcement
                              .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                          keyboardAppearance: Brightness.light,
                        ),
                      ),
                    ),
                  ),
                ),
                TextButton(
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFF007EFF),
                      fontSize: 15,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  onPressed: () {
                    FocusScope.of(context).requestFocus(FocusNode());
                    this.addTextTag();
                  },
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          Visibility(
            visible: this.tagList.length > 0,
            child: Container(
              child: Wrap(
                children: this.getTagItems(),
                spacing: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> getTagItems() {
    return this
        .tagList
        .map(
          (e) => AddContactTagWidget(
            text: e,
            isEdit: true,
            deleteAction: (text) {
              if (this.tagList.contains(text)) {
                this.tagList.remove(text);
                widget.valueChanged(this.tagList.join("、"));
                setState(() {});
              }
            },
          ),
        )
        .toList();
  }

  void addTextTag() {
    var tagText = this._textEditingController.text;
    this._textEditingController.text = "";
    if (tagText.isEmpty) {
      return;
    }
    if (this.tagList.contains(tagText)) {
      XYYContainer.toastChannel.toast('不允许添加重复的标签');
      return;
    }
    if (this.tagList.length >= 10) {
      XYYContainer.toastChannel.toast('标签数量不能超过10个');
      return;
    }
    this.tagList.add(tagText);
    widget.valueChanged(this.tagList.join("、"));
    setState(() {});
  }
}

class AddContactTagWidget extends StatelessWidget {
  final String text;
  final bool isEdit;
  final ValueChanged<String> deleteAction;

  AddContactTagWidget({
    required this.text,
    this.isEdit = true,
    required this.deleteAction,
  });

  @override
  Widget build(BuildContext context) {
    return Chip(
      backgroundColor: Color(0xFFE3E3E9),
      label: Container(
        child: Text(
          this.text,
          style: TextStyle(
            fontSize: 13,
            color: Color(0xFF666666),
            fontWeight: FontWeight.normal,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
        ),
      ),
      onDeleted: () {
        this.deleteAction(this.text);
      },
    );
  }
}
