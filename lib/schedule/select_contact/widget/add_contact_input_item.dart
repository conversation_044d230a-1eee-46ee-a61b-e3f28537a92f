import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// ignore: must_be_immutable
class AddContactInputItem extends StatefulWidget {
  final String itemKey;
  final String placeHolder;
  final bool isCanEdit;
  final ValueChanged<String> textChange;
  String? content;

  AddContactInputItem({
    required this.itemKey,
    required this.placeHolder,
    required this.textChange,
    this.isCanEdit = true,
    this.content,
  });

  @override
  State<StatefulWidget> createState() {
    return AddContactInputItemState();
  }
}

class AddContactInputItemState extends State<AddContactInputItem> {
  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.only(left: 15),
      child: Column(
        children: [
          Container(
            height: 50,
            alignment: Alignment.center,
            margin: EdgeInsets.only(right: 15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Color(0xFFFFFFFF),
            ),
            child: Container(
              child: EnsureVisibleWhenFocused(
                focusNode: _focusNode,
                child: TextField(
                  focusNode: _focusNode,
                  enabled: widget.isCanEdit,
                  keyboardType: this.inputTypeForItemKey(),
                  inputFormatters: [
                    this.regepxForIteKey(),
                  ],
                  controller: TextEditingController.fromValue(
                    TextEditingValue(
                      text: widget.content ?? "",
                      selection: TextSelection.fromPosition(
                        ///用来设置文本的位置
                        TextPosition(
                          affinity: TextAffinity.downstream,
                          // 光标向后移动的长度
                          offset: widget.content?.length ?? 0,
                        ),
                      ),
                    ),
                  ),
                  onChanged: (text) {
                    widget.content = text;
                    widget.textChange(text);
                  },
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF333333),
                  ),
                  textAlign: TextAlign.left,
                  scrollPadding: EdgeInsets.zero,
                  decoration: InputDecoration(
                    counterText: "",
                    counterStyle: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9494A6),
                    ),
                    hintText: widget.placeHolder,
                    hintStyle: TextStyle(
                      fontSize: 15,
                      color: Color(0xFF9494A6),
                    ),
                    hintMaxLines: 1,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLength: this.limitCountForItemKey(), // 最大长度
                  maxLengthEnforcement: MaxLengthEnforcement
                      .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                  keyboardAppearance: Brightness.light,
                ),
              ),
            ),
          ),
          Divider(
            color: Color(0xFFCCCCCC),
            height: 0.5,
          )
        ],
      ),
    );
  }

  TextInputType inputTypeForItemKey() {
    switch (widget.itemKey) {
      case "contactName":
        return TextInputType.text;
      case "contactMobile":
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }

  TextInputFormatter regepxForIteKey() {
    switch (widget.itemKey) {
      case "mobile":
        return FilteringTextInputFormatter.allow(RegExp(r'[0-9]'));
      default:
        return CustomizedTextInputFormatter(
            filterPattern:
                RegExp(r'[\u4e00-\u9fa5，,。.：:（）()?？!！a-zA-Z0-9⼀⼁⼃⼂乛]*'));
    }
  }

  int limitCountForItemKey() {
    switch (widget.itemKey) {
      case "contactName":
        return 20;
      case "contactMobile":
        return 11;
      default:
        return 20;
    }
  }
}
