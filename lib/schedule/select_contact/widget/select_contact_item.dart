import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_contactor_data.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class SelectContactItem extends StatefulWidget {
  bool isSelected;

  final ScheduleContactorData model;

  SelectContactItem({required this.model, this.isSelected = false});

  @override
  State<StatefulWidget> createState() {
    return SekectContactItemState();
  }
}

class SekectContactItemState extends State<SelectContactItem> {
  bool isInvalid = false;

  @override
  void initState() {
    super.initState();
    this.isInvalid = widget.model.id.toString() == "-1";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 10, right: 10, bottom: 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        child: Text(
                          this.getContactName(),
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ),
                      Visibility(
                        visible: !this.isInvalid,
                        child: Container(
                          padding: EdgeInsets.only(top: 5),
                          child: Text(
                            widget.model.contactMobile ?? "",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8E8E93),
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Image.asset(
                  widget.isSelected
                      ? 'assets/images/schedule/select_object_selected.png'
                      : 'assets/images/schedule/select_object_normal.png',
                  width: 20,
                  height: 20,
                )
              ],
            ),
          ),
          Divider(
            color: Color(0xFFE1E1E5),
            height: 0.5,
          )
        ],
      ),
    );
  }

  String getContactName() {
    if (widget.model.id.toString() != "-1") {
      return "${widget.model.contactName ?? ""}(${widget.model.contactJobName ?? ""})";
    }
    return widget.model.contactName ?? "";
  }
}
