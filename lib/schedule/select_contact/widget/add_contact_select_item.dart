import 'package:flutter/material.dart';

/// 选择回调
typedef AddCOntactSelectAction = void Function(
  String itemKey,
  ValueNotifier<String?> controller,
);

// ignore: must_be_immutable
class AddContactSelectItem extends StatefulWidget {
  final String itemTitle;
  final String itemKey;
  String? content;
  final AddCOntactSelectAction selectAction;

  AddContactSelectItem({
    required this.itemTitle,
    required this.itemKey,
    this.content,
    required this.selectAction,
  });

  @override
  State<StatefulWidget> createState() {
    return AddContactSelectItemState();
  }
}

class AddContactSelectItemState extends State<AddContactSelectItem> {
  ValueNotifier<String?>? contentNotifier;

  @override
  void initState() {
    contentNotifier = ValueNotifier(widget.content ?? "");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      padding: EdgeInsets.only(left: 15, right: 15),
      color: Color(0xFFFFFFFF),
      child: Row(
        children: [
          Text(
            widget.itemTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              color: Color(0xFF333333),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                widget.selectAction(widget.itemKey, this.contentNotifier!);
              },
              behavior: HitTestBehavior.opaque,
              child: Row(
                children: [
                  Expanded(
                    child: ValueListenableBuilder(
                      valueListenable: contentNotifier!,
                      builder: (context, value, child) {
                        if (contentNotifier?.value?.isNotEmpty ?? false) {
                          widget.content = contentNotifier?.value;
                        }
                        return Text(
                          _getShowContent(),
                          style: TextStyle(
                            fontSize: 15,
                            color: _getContentTextColor(),
                          ),
                          textAlign: TextAlign.end,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        );
                      },
                    ),
                  ),
                  Image.asset(
                    'assets/images/schedule/schedule_item_arrow.png',
                    width: 16,
                    height: 16,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isEmptyContent() {
    return widget.content?.isEmpty ?? true;
  }

  Color _getContentTextColor() {
    return _isEmptyContent() ? Color(0xFF8E8E93) : Color(0xFF333333);
  }

  String _getShowContent() {
    return _isEmptyContent() ? "请选择" : (widget.content ?? "请选择");
  }
}
