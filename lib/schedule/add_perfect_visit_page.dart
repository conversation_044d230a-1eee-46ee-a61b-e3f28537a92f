import 'dart:convert';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/picker/time_ms_picker.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_visit_base.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_config_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CRMPerfectVisitModel {
  /// 日程id
  String visitId = "";

  /// 药店名称
  String merchantName = "";

  /// 药店id
  String merchantId = "";

  /// 药店编码
  String customerId = "";

  /// 联系人名称
  String? contactor = "";

  /// 联系人电话
  String mobile = "";

  /// 是否kp
  String isEffective = "";

  /// 通话时长
  String? talkTime = "-";

  /// 是否是已注册客户 1是已注册。2是未注册
  String isRegisterFlag = "1";

  fromJsonMap(Map<String, dynamic> json) {
    return this
      ..visitId = json['visitId'] as String
      ..merchantName = json['merchantName'] as String? ?? ""
      ..merchantId = json['merchantId'] as String? ?? ""
      ..customerId = json['customerId'] as String? ?? ""
      ..contactor = json['contactor'] as String?
      ..mobile = json['mobile'] as String
      ..isEffective = json['isEffective'] as String
      ..talkTime = json['talkTime'] as String?
      ..isRegisterFlag = json['isRegisterFlag'] as String;
  }
}

class AddPerfectVisitPage extends AddVisitBasePage {
  final String? perfectJSON;

  AddPerfectVisitPage({required this.perfectJSON}) : super(pageType: 2);

  @override
  BaseState<StatefulWidget> initState() {
    return AddPerfectVisitPageState();
  }
}

class AddPerfectVisitPageState extends AddVisitBasePageState {
  @override
  AddPerfectVisitPage get widget => super.widget as AddPerfectVisitPage;

  late CRMPerfectVisitModel perfectModel;

  @override
  void initState() {
    super.initState();

    /// 存在外部传入的数据
    if (widget.perfectJSON != null && widget.perfectJSON!.length > 0) {
      Map<String, dynamic> perfectMap = json.decode(widget.perfectJSON!);
      this.perfectModel = CRMPerfectVisitModel().fromJsonMap(perfectMap);
      this.configPerfectDefaultData();
    }
  }

  @override
  void selectDurationAction(ValueNotifier<String?> controller) {
    super.selectDurationAction(controller);
    showTimeMSValuePickerView(
        context: context,
        title: "选择通话时长",
        defaultValue: this.postModel.merchantVisit.talkTime,
        selectedAction: (value) {
          this.postModel.merchantVisit.talkTime = value;
          print("feng: - $value");
          controller.value = this.getDurationText();
        });
  }

  @override
  void loadConfigData() async {
    super.loadConfigData();
  }

  void configPerfectDefaultData() {
    this.postModel.merchantVisit.visitType = "2";
    this.showModel.visitTypeName = "电话拜访";
    this.postModel.personalSchedule.type = "1";

    this.postModel.merchantVisit.merchantName = this.perfectModel.merchantName;
    this.postModel.merchantVisit.merchantId = this.perfectModel.merchantId;
    this.postModel.merchantVisit.customerId = this.perfectModel.customerId;
    this.postModel.merchantVisit.contactor = this.perfectModel.contactor;
    this.postModel.merchantVisit.isEffective = this.perfectModel.isEffective;
    this.postModel.merchantVisit.talkTime = this.perfectModel.talkTime;
    this.postModel.merchantVisit.mobile = this.perfectModel.mobile;
    this.postModel.personalSchedule.id = this.perfectModel.visitId;

    /// 请求客户的任务状态
    this.requestCustomHaveTask(this.perfectModel.merchantId);

    this.showModel.isKPName = this.perfectModel.isEffective == "1" ? "是" : "否";

    /// 未注册
    if (this.perfectModel.isRegisterFlag == "2") {
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "info",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "info") {
        ScheduleConfigItemData itemModel = sectionModel.items.firstWhere(
            (element) => element.itemKey == "kp",
            orElse: () => ScheduleConfigItemData());
        if (itemModel.itemKey == "kp") {
          itemModel.itemType = 4;
          itemModel.isOn = this.perfectModel.isEffective == "1";
        }
      }
    }

    /// 无通话时长 则需要选择通话时长
    int time = int.tryParse(this.perfectModel.talkTime ?? "0") ?? 0;
    if (time <= 0) {
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "info",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "info") {
        ScheduleConfigItemData itemModel = sectionModel.items.firstWhere(
            (element) => element.itemKey == "duration",
            orElse: () => ScheduleConfigItemData());
        if (itemModel.itemKey == "duration") {
          itemModel.isCanEdit = true;
          itemModel.selectType = 8;
          itemModel.itemPlaceHolder = "请选择通话时长(必填)";
          itemModel.itemType = 1;
        }
      }
    }

    /// 无联系人姓名 则认为是未完善的联系人信息
    if (this.isNotPerfectContact()) {
      /// 处理数据
      this.postModel.merchantContactNew = CRMMerchantContactNewModel();
      this.postModel.merchantContactNew?.contactMobile =
          this.perfectModel.mobile;

      /// 处理选项
      ScheduleConfigSectionData sectionModel = this.configList.firstWhere(
          (element) => element.sectionKey == "info",
          orElse: () => ScheduleConfigSectionData());
      if (sectionModel.sectionKey == "info") {
        int itemIndex = sectionModel.items
            .indexWhere((element) => element.itemKey == "contacts");
        if (itemIndex != -1) {
          ScheduleConfigItemData itemModel = sectionModel.items[itemIndex];
          itemModel.itemType = 2;
          itemModel.isCanEdit = true;
          itemModel.itemPlaceHolder = "请填写联系人姓名(必填)";

          ScheduleConfigItemData jobItemModel = sectionModel.items.firstWhere(
              (element) => element.itemKey == "job",
              orElse: () => ScheduleConfigItemData());
          if (jobItemModel.itemKey == "job") {
            jobItemModel.itemTitle = "岗位";
            jobItemModel.itemKey = "job";
            jobItemModel.itemType = 1;
            jobItemModel.selectType = 9;
            jobItemModel.isCanEdit = true;
            jobItemModel.itemPlaceHolder = "请选择(必填)";
          } else {
            ScheduleConfigItemData jobItemModel = ScheduleConfigItemData();
            jobItemModel.itemTitle = "岗位";
            jobItemModel.itemKey = "job";
            jobItemModel.itemType = 1;
            jobItemModel.selectType = 9;
            jobItemModel.isCanEdit = true;
            jobItemModel.itemPlaceHolder = "请选择(必填)";
            sectionModel.items.insert(itemIndex + 1, jobItemModel);
          }
        }
      }
    }
  }

  bool isNotPerfectContact() {
    bool isNotPerfect = (this.perfectModel.contactor == null ||
            this.perfectModel.contactor!.length == 0) ||
        (this.perfectModel.contactor == "注册电话");
    return isNotPerfect;
  }

  @override
  bool checkNeedData() {
    int time = int.tryParse(this.perfectModel.talkTime ?? "0") ?? 0;
    if (time <= 0) {
      int selectTime =
          int.tryParse(this.postModel.merchantVisit.talkTime ?? "0") ?? 0;
      if (selectTime <= 0) {
        showToast("请填写通话时长");
        return false;
      }
      if (this.postModel.merchantVisit.image.length < 0) {
        showToast("请截图手机通话记录，并上传图片");
        return false;
      }
    }
    return super.checkNeedData();
  }

  @override
  void inputText(String? text, String itemKey) {
    if (itemKey == "contacts") {
      this.postModel.merchantContactNew?.contactName = text;
      this.postModel.merchantVisit.contactor = text;
      this.showModel.contactor = text;
    } else {
      super.inputText(text, itemKey);
    }
  }

  String? getDurationText() {
    int time = int.tryParse(this.perfectModel.talkTime ?? "0") ?? 0;
    if (time > 0) {
      int minute = time ~/ 60;
      int second = time % 60;
      if (minute <= 0) {
        return "$second秒";
      }
      return "$minute分$second秒";
    }
    time = int.tryParse(this.postModel.merchantVisit.talkTime ?? "0") ?? 0;
    if (time > 0) {
      int minute = time ~/ 60;
      int second = time % 60;
      if (minute <= 0) {
        return "$second秒";
      }
      return "$minute分$second秒";
    } else {
      return null;
    }
  }

  @override
  String? showText(String itemKey) {
    if (itemKey == "duration") {
      return this.getDurationText();
    }
    if (itemKey == "mobile") {
      return this.perfectModel.mobile;
    }
    if (itemKey == "contacts") {
      return this.postModel.merchantVisit.contactor;
    }
    return super.showText(itemKey);
  }

  @override
  void showSuccessToast() {
    showToast('完善拜访成功');
  }
}
