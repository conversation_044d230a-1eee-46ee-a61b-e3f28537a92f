import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_merchant_check.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_no_visit_customer_bd_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/widgets/schedule_no_visit_bd_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleNoVisitCustomerBDPage extends BasePage {
  final String? searchUserId;

  ScheduleNoVisitCustomerBDPage(this.searchUserId);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleNoVisitCustomerBDPageState();
  }
}

class ScheduleNoVisitCustomerBDPageState
    extends BaseState<ScheduleNoVisitCustomerBDPage> {
  EasyRefreshController _refreshController = EasyRefreshController();
  List<ScheduleNoVisitCustomerBDData>? dataSource;

  // List<ScheduleNoVisitCustomerBDData>? dataSource = [
  //   ScheduleNoVisitCustomerBDData()
  //     ..customerId = '23123'
  //     ..merchantId = '123123'
  //     ..levelDesc = "未评级"
  //     ..merchantName = "湖北智仁药房连锁有限公司",
  //   ScheduleNoVisitCustomerBDData()
  //     ..customerId = '23123'
  //     ..merchantId = '123123'
  //     ..levelDesc = "S++"
  //     ..merchantName = "湖北智仁药房连锁有限公司",
  //   ScheduleNoVisitCustomerBDData()
  //     ..customerId = '23123'
  //     ..merchantId = '123123'
  //     ..levelDesc = "A--"
  //     ..merchantName = "湖北智仁药房连锁有限公司湖北智仁药房连锁有限公司湖北智仁药房连锁有限公司湖北智仁药房连锁有限公司湖北智仁药房连锁有限公司"
  // ];
  PageState pageState = PageState.Empty;

  Map<String, dynamic> params = {};

  int page = 1;

  bool? isLastPage = true;

  @override
  void onCreate() {
    if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
    } else {
      params.clear();
    }
    showLoadingDialog(msg: "加载中...");
    requestListData(true);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      child: buildListWidget(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  @override
  String getTitleName() {
    return "未拜访客户列表";
  }

  Widget buildListWidget() {
    return Container(
      padding: EdgeInsets.all(10),
      child: EasyRefresh(
        controller: _refreshController,
        onRefresh: () async {
          return await requestListData(true);
        },
        onLoad: isLastPage != false
            ? null
            : () async {
                return await requestListData(false);
              },
        child: ListView.builder(
          itemCount: this.dataSource?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            ScheduleNoVisitCustomerBDData? model = this.dataSource?[index];
            if (model == null) {
              return Container();
            }
            return GestureDetector(
              onTap: () {
                handleItemClick(model);
              },
              behavior: HitTestBehavior.opaque,
              child: ScheduleNoVisitBDItemWidget(
                model,
                index == 0,
                index == (this.dataSource?.length ?? 0) - 1,
              ),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog(msg: "加载中...");
          requestListData(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  Future<void> requestListData(bool isRefresh) async {
    params["limit"] = 20;
    if (isRefresh) {
      params["offset"] = 1;
    } else {
      params["offset"] = page + 1;
    }
    var value = await NetworkV2<ScheduleNoVisitCustomerBDListData>(
            ScheduleNoVisitCustomerBDListData())
        .requestDataV2("visit/noVisitList",
            parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _refreshController.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          if (!isRefresh) {
            page++;
          }else{
            page=1;
          }
          var result = value.getData();
          if (result != null) {
            isLastPage = result.isLastPage;
            if (isRefresh) {
              if (result.list?.isNotEmpty == true) {
                dataSource = result.list!;
                pageState = PageState.Normal;
              } else {
                dataSource = [];
                pageState = PageState.Empty;
              }
            } else {
              if (result.list?.isNotEmpty == true) {
                if (dataSource == null) {
                  dataSource = [];
                }
                dataSource?.addAll(result.list!);
                pageState = PageState.Normal;
              }
            }
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
        _refreshController.finishRefresh();
        _refreshController.finishLoad();
      });
    }
  }

  void handleItemClick(ScheduleNoVisitCustomerBDData model) {
    if (model.merchantId != null) {
      jumpCustomerPageByMerchantId(model.merchantId?.toString() ?? "",canJumpPublic: false);
    }
  }
}
