// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_analysis_bd_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleAnalysisBDData _$ScheduleAnalysisBDDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleAnalysisBDData()
    ..validVisits = json['validVisits']
    ..noVisits = json['noVisits']
    ..visitCoverRate = json['visitCoverRate']
    ..conversionRate = json['conversionRate']
    ..noConversions = json['noConversions']
    ..visitList = (json['visitList'] as List<dynamic>?)
        ?.map((e) =>
            ScheduleAnalysisBDVisitData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..visitConversionList = (json['visitConversionList'] as List<dynamic>?)
        ?.map((e) =>
            ScheduleAnalysisBDVisitData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleAnalysisBDDataToJson(
        ScheduleAnalysisBDData instance) =>
    <String, dynamic>{
      'validVisits': instance.validVisits,
      'noVisits': instance.noVisits,
      'visitCoverRate': instance.visitCoverRate,
      'conversionRate': instance.conversionRate,
      'noConversions': instance.noConversions,
      'visitList': instance.visitList,
      'visitConversionList': instance.visitConversionList,
    };

ScheduleAnalysisBDVisitData _$ScheduleAnalysisBDVisitDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleAnalysisBDVisitData()
    ..level = json['level']
    ..levelDesc = json['levelDesc']
    ..visitCoverRate = json['visitCoverRate']
    ..conversionRate = json['conversionRate'];
}

Map<String, dynamic> _$ScheduleAnalysisBDVisitDataToJson(
        ScheduleAnalysisBDVisitData instance) =>
    <String, dynamic>{
      'level': instance.level,
      'levelDesc': instance.levelDesc,
      'visitCoverRate': instance.visitCoverRate,
      'conversionRate': instance.conversionRate,
    };
