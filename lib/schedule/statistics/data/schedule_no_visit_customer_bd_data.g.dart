// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_no_visit_customer_bd_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleNoVisitCustomerBDListData _$ScheduleNoVisitCustomerBDListDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleNoVisitCustomerBDListData()
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) =>
            ScheduleNoVisitCustomerBDData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleNoVisitCustomerBDListDataToJson(
        ScheduleNoVisitCustomerBDListData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };

ScheduleNoVisitCustomerBDData _$ScheduleNoVisitCustomerBDDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleNoVisitCustomerBDData()
    ..merchantId = json['merchantId']
    ..customerId = json['customerId']
    ..levelDesc = json['levelDesc']
    ..merchantName = json['merchantName'];
}

Map<String, dynamic> _$ScheduleNoVisitCustomerBDDataToJson(
        ScheduleNoVisitCustomerBDData instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'customerId': instance.customerId,
      'levelDesc': instance.levelDesc,
      'merchantName': instance.merchantName,
    };
