import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_no_visit_customer_bdm_data.g.dart';

@JsonSerializable()
class ScheduleNoVisitCustomerBDMData extends BaseModelV2<ScheduleNoVisitCustomerBDMData>{


  dynamic oaId;
  dynamic crmUserName;
  dynamic groupId;
  dynamic crmUserDepartment;
  dynamic haveChild;
  dynamic noVisits;
  dynamic isBd;

  bool isBDM() {
    return isBd == 2;
  }


  ScheduleNoVisitCustomerBDMData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleNoVisitCustomerBDMData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleNoVisitCustomerBDMDataToJson(this);
  }

  factory ScheduleNoVisitCustomerBDMData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleNoVisitCustomerBDMDataFromJson(json);
}
