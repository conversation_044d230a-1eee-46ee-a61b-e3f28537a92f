// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_visit_planningRoadmap_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleVisitPlanningRoadmapData _$ScheduleVisitPlanningRoadmapDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleVisitPlanningRoadmapData()
    ..chineseDate = json['chineseDate']
    ..weekDay = json['weekDay']
    ..ganZhiDate = json['ganZhiDate']
    ..planList = (json['planList'] as List<dynamic>?)
        ?.map((e) => ScheduleVisitPlanningRoadmapPlanListData.fromJson(
            e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleVisitPlanningRoadmapDataToJson(
        ScheduleVisitPlanningRoadmapData instance) =>
    <String, dynamic>{
      'chineseDate': instance.chineseDate,
      'weekDay': instance.weekDay,
      'ganZhiDate': instance.ganZhiDate,
      'planList': instance.planList,
    };

ScheduleVisitPlanningRoadmapPlanListData
    _$ScheduleVisitPlanningRoadmapPlanListDataFromJson(
        Map<String, dynamic> json) {
  return ScheduleVisitPlanningRoadmapPlanListData()
    ..id = json['id']
    ..customerId = json['customerId']
    ..customerName = json['customerName']
    ..customerType = json['customerType']
    ..address = json['address']
    ..distance = json['distance']
    ..latestOrderTime = json['latestOrderTime']
    ..latestVisitTime = json['latestVisitTime']
    ..planTagText = json['planTagText']
    ..planTag = json['planTag']
    ..poiLat = json['poiLat']
    ..poiLng = json['poiLng']
    ..poiImage = json['poiImage'];
}

Map<String, dynamic> _$ScheduleVisitPlanningRoadmapPlanListDataToJson(
        ScheduleVisitPlanningRoadmapPlanListData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'customerType': instance.customerType,
      'address': instance.address,
      'distance': instance.distance,
      'latestOrderTime': instance.latestOrderTime,
      'latestVisitTime': instance.latestVisitTime,
      'planTagText': instance.planTagText,
      'planTag': instance.planTag,
      'poiLat': instance.poiLat,
      'poiLng': instance.poiLng,
      'poiImage': instance.poiImage,
    };
