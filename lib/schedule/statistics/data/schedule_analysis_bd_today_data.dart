import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_analysis_bd_today_data.g.dart';

@JsonSerializable()
class ScheduleAnalysisBDTodayData extends BaseModelV2<ScheduleAnalysisBDTodayData> {

  dynamic doorVisits;
  dynamic validDoorVisits;
  dynamic phoneVisits;
  dynamic conversions;
  dynamic chineseDate;
  dynamic weekDay;
  dynamic ganZhiDate;
  List<ScheduleAnalysisBDTodayVisitData>? visitResult;


  ScheduleAnalysisBDTodayData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleAnalysisBDTodayData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleAnalysisBDTodayDataToJson(this);
  }

  factory ScheduleAnalysisBDTodayData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleAnalysisBDTodayDataFromJson(json);
}

@JsonSerializable()
class ScheduleAnalysisBDTodayVisitData {


  dynamic customerId;
  dynamic merchantId;
  dynamic merchantName;
  dynamic isPlaceOrder;
  dynamic visitTime;
  dynamic longitude;
  dynamic latitude;
  dynamic inPlan;




  ScheduleAnalysisBDTodayVisitData({
    this.customerId,
    this.merchantId,
    this.merchantName,
    this.isPlaceOrder,
    this.visitTime,
    this.longitude,
    this.latitude,
    this.inPlan,
  });

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleAnalysisBDTodayVisitData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleAnalysisBDTodayVisitDataToJson(this);
  }

  factory ScheduleAnalysisBDTodayVisitData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleAnalysisBDTodayVisitDataFromJson(json);
}
