import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_analysis_bd_data.g.dart';

@JsonSerializable()
class ScheduleAnalysisBDData extends BaseModelV2<ScheduleAnalysisBDData> {
  dynamic validVisits; //有效拜访
  dynamic noVisits; //未拜访
  dynamic visitCoverRate; //拜访覆盖率
  dynamic conversionRate; //拜访转化率
  dynamic noConversions; //未转化客户数
  List<ScheduleAnalysisBDVisitData>? visitList; //已拜访客户分等级统计
  List<ScheduleAnalysisBDVisitData>? visitConversionList; //拜访转化率分等级统计

  ScheduleAnalysisBDData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleAnalysisBDData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleAnalysisBDDataToJson(this);
  }

  factory ScheduleAnalysisBDData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleAnalysisBDDataFromJson(json);
}

@JsonSerializable()
class ScheduleAnalysisBDVisitData {
  dynamic level;
  dynamic levelDesc;
  dynamic visitCoverRate; //拜访覆盖率
  dynamic conversionRate; //拜访转化率

  ScheduleAnalysisBDVisitData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleAnalysisBDVisitData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleAnalysisBDVisitDataToJson(this);
  }

  factory ScheduleAnalysisBDVisitData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleAnalysisBDVisitDataFromJson(json);
}
