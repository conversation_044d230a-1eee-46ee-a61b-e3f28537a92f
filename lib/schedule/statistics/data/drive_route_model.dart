import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';

class BaseRouteModel {
  String? duration;
  String? distance;
  BMFRouteNode? startNode;
  BMFRouteNode? endNode;
  List<BMFCoordinate>? routeCoordinates = [];

  BaseRouteModel({this.duration, this.distance});

  BaseRouteModel.withModel(routeLine) {
    if (routeLine.duration.minutes > 0) {
      duration = "${routeLine.duration.minutes}分钟";
    }
    if (routeLine.duration.hours > 0) {
      duration = "${routeLine.duration.hours}小时";
    }
    if (routeLine.duration.hours > 0 && routeLine.duration.minutes > 0) {
      duration = "${routeLine.duration.hours}小时${routeLine.duration.minutes}分";
    }

    if (routeLine.distance < 100) {
      distance = "${routeLine.distance}米";
    } else {
      distance = (routeLine.distance / 1000.0).toStringAsFixed(1) + "公里";
    }

    startNode = routeLine.starting;
    endNode = routeLine.terminal;
  }
}

class DriveRouteModel extends BaseRouteModel {
  String? lightNum;
  String? congestionMetres;

  List<BMFPlanNode>? wayPoints;
  List<RouteStepModel>? steps = [];
  List<int>? trafficIndexList = [];

  DriveRouteModel.withModel(BMFDrivingRouteLine routeLine)
      : super.withModel(routeLine) {
    lightNum = "${routeLine.lightNum}";
    congestionMetres = "${routeLine.congestionMetres}";

    routeLine.steps?.forEach((element) {
      routeCoordinates?.addAll(element.points!);
      trafficIndexList
          ?.addAll(element.traffics != null ? element.traffics! : [1]);

      RouteStepModel stepModel = RouteStepModel(
          image: "resoures/driving.png", instruction: element.instruction!);
      steps?.add(stepModel);
    });
    wayPoints = routeLine.wayPoints;

    RouteStepModel startStepModel = RouteStepModel(
        image: "resoures/animation_green.png", instruction: "起点（我的位置）");
    RouteStepModel endStepModel = RouteStepModel(
        image: "resoures/animation_red.png",
        instruction: endNode?.title != null ? "终点（${endNode!.title}）" : "终点");
    steps?.insert(0, startStepModel);
    steps?.add(endStepModel);
  }
}

class RouteStepModel {
  String? image;
  String? instruction;

  RouteStepModel({this.image, this.instruction});
}
