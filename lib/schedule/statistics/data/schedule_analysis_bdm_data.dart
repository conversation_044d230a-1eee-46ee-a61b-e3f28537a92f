import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_analysis_bdm_data.g.dart';

@JsonSerializable()
class ScheduleAnalysisBDMData extends BaseModelV2<ScheduleAnalysisBDMData>{


  dynamic oaId;
  dynamic crmUserName;
  dynamic doorVisits;
  dynamic validDoorVisits;
  dynamic phoneVisits;
  dynamic conversions;
  dynamic crmUserDepartment;
  dynamic haveChild;
  dynamic groupId;


  dynamic validVisits;
  dynamic visitCoverRate;
  dynamic conversionRate;

  dynamic isBd;


  ScheduleAnalysisBDMData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleAnalysisBDMData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleAnalysisBDMDataToJson(this);
  }

  factory ScheduleAnalysisBDMData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleAnalysisBDMDataFromJson(json);
}
