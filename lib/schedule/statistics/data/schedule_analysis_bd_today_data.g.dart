// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_analysis_bd_today_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleAnalysisBDTodayData _$ScheduleAnalysisBDTodayDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleAnalysisBDTodayData()
    ..doorVisits = json['doorVisits']
    ..validDoorVisits = json['validDoorVisits']
    ..phoneVisits = json['phoneVisits']
    ..conversions = json['conversions']
    ..chineseDate = json['chineseDate']
    ..weekDay = json['weekDay']
    ..ganZhiDate = json['ganZhiDate']
    ..visitResult = (json['visitResult'] as List<dynamic>?)
        ?.map((e) => ScheduleAnalysisBDTodayVisitData.fromJson(
            e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ScheduleAnalysisBDTodayDataToJson(
        ScheduleAnalysisBDTodayData instance) =>
    <String, dynamic>{
      'doorVisits': instance.doorVisits,
      'validDoorVisits': instance.validDoorVisits,
      'phoneVisits': instance.phoneVisits,
      'conversions': instance.conversions,
      'visitResult': instance.visitResult,
      'chineseDate': instance.chineseDate,
      'weekDay': instance.weekDay,
      'ganZhiDate': instance.ganZhiDate,
    };

ScheduleAnalysisBDTodayVisitData _$ScheduleAnalysisBDTodayVisitDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleAnalysisBDTodayVisitData()
    ..customerId = json['customerId']
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..isPlaceOrder = json['isPlaceOrder']
    ..visitTime = json['visitTime']
    ..longitude = json['longitude']
    ..latitude = json['latitude']
    ..inPlan = json['inPlan'];
}

Map<String, dynamic> _$ScheduleAnalysisBDTodayVisitDataToJson(
        ScheduleAnalysisBDTodayVisitData instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'isPlaceOrder': instance.isPlaceOrder,
      'visitTime': instance.visitTime,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'inPlan': instance.inPlan,
    };
