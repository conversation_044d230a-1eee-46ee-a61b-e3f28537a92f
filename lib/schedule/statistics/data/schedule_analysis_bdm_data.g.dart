// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_analysis_bdm_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleAnalysisBDMData _$ScheduleAnalysisBDMDataFromJson(
    Map<String, dynamic> json) {
  return ScheduleAnalysisBDMData()
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..doorVisits = json['doorVisits']
    ..validDoorVisits = json['validDoorVisits']
    ..phoneVisits = json['phoneVisits']
    ..conversions = json['conversions']
    ..crmUserDepartment = json['crmUserDepartment']
    ..haveChild = json['haveChild']
    ..groupId = json['groupId']
    ..validVisits = json['validVisits']
    ..visitCoverRate = json['visitCoverRate']
    ..conversionRate = json['conversionRate']
    ..isBd = json['isBd'];
}

Map<String, dynamic> _$ScheduleAnalysisBDMDataToJson(
        ScheduleAnalysisBDMData instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'doorVisits': instance.doorVisits,
      'validDoorVisits': instance.validDoorVisits,
      'phoneVisits': instance.phoneVisits,
      'conversions': instance.conversions,
      'crmUserDepartment': instance.crmUserDepartment,
      'haveChild': instance.haveChild,
      'groupId': instance.groupId,
      'validVisits': instance.validVisits,
      'visitCoverRate': instance.visitCoverRate,
      'conversionRate': instance.conversionRate,
      'isBd': instance.isBd,
    };
