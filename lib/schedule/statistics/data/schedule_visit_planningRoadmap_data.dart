import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_visit_planningRoadmap_data.g.dart';

@JsonSerializable()
class ScheduleVisitPlanningRoadmapData extends BaseModelV2<ScheduleVisitPlanningRoadmapData> {

  dynamic chineseDate;
  dynamic weekDay;
  dynamic ganZhiDate;
  List<ScheduleVisitPlanningRoadmapPlanListData>? planList;


  ScheduleVisitPlanningRoadmapData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleVisitPlanningRoadmapData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleVisitPlanningRoadmapDataToJson(this);
  }

  factory ScheduleVisitPlanningRoadmapData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleVisitPlanningRoadmapDataFromJson(json);
}

@JsonSerializable()
class ScheduleVisitPlanningRoadmapPlanListData {

  dynamic id;
  dynamic customerId;
  dynamic customerName;
  dynamic customerType;
  dynamic address;
  dynamic distance;
  dynamic latestOrderTime;
  dynamic latestVisitTime;
  dynamic planTagText;
  dynamic planTag;
  dynamic poiLat;
  dynamic poiLng;
  dynamic poiImage;

  ScheduleVisitPlanningRoadmapPlanListData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleVisitPlanningRoadmapPlanListData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleVisitPlanningRoadmapPlanListDataToJson(this);
  }

  factory ScheduleVisitPlanningRoadmapPlanListData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleVisitPlanningRoadmapPlanListDataFromJson(json);
}