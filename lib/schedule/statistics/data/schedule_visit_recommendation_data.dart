import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_visit_recommendation_data.g.dart';

@JsonSerializable()
class ScheduleVisitRecommendationData extends BaseModelV2<ScheduleVisitRecommendationData> {

  int? customerId;
  int? poiId;
  int? merchantId;
  dynamic customerName;
  dynamic poiLat;
  dynamic poiLng;
  bool? inPlanFlag;
  bool? inPrivateFlag;
  bool? visitFlag;
  dynamic proposeReason;
  dynamic poiImage;


  ScheduleVisitRecommendationData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleVisitRecommendationData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleVisitRecommendationDataToJson(this);
  }

  factory ScheduleVisitRecommendationData.fromJson(Map<String, dynamic>? json) =>
      _$ScheduleVisitRecommendationDataFromJson(json!);
}

