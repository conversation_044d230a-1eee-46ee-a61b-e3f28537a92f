import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_no_visit_customer_bd_data.g.dart';

@JsonSerializable()
class ScheduleNoVisitCustomerBDListData extends BaseModelV2<ScheduleNoVisitCustomerBDListData>{


  dynamic isLastPage;
  List<ScheduleNoVisitCustomerBDData>? list;


  ScheduleNoVisitCustomerBDListData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleNoVisitCustomerBDListData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleNoVisitCustomerBDListDataToJson(this);
  }

  factory ScheduleNoVisitCustomerBDListData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleNoVisitCustomerBDListDataFromJson(json);
}

@JsonSerializable()
class ScheduleNoVisitCustomerBDData extends BaseModelV2<ScheduleNoVisitCustomerBDData>{


  dynamic merchantId;
  dynamic customerId;
  dynamic levelDesc;
  dynamic merchantName;


  ScheduleNoVisitCustomerBDData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleNoVisitCustomerBDData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleNoVisitCustomerBDDataToJson(this);
  }

  factory ScheduleNoVisitCustomerBDData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleNoVisitCustomerBDDataFromJson(json);
}
