import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_join_the_program_data.g.dart';

@JsonSerializable()
class ScheduleJoinTheProgramData extends BaseModelV2<ScheduleJoinTheProgramData> {

  dynamic code;
  ScheduleJoinTheProgramData();

  fromJsonMap(Map<String, dynamic> json) {
    return ScheduleJoinTheProgramData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ScheduleJoinTheProgramDataToJson(this);
  }

  factory ScheduleJoinTheProgramData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleJoinTheProgramDataFromJson(json);
}

