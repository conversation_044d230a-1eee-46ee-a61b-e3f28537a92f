import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/sort_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_analysis_bdm_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/widgets/schedule_analysis_bdm_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleAnalysisBDMPage extends BasePage {
  final String? groupId;
  final String? searchUserId;
  final String? name;
  final String? isToday; //true、false

  ScheduleAnalysisBDMPage(
      this.groupId, this.searchUserId, this.name, this.isToday);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleAnalysisTodayBDMPageState();
  }
}

class ScheduleAnalysisTodayBDMPageState
    extends BaseState<ScheduleAnalysisBDMPage> {
  EasyRefreshController _refreshController = EasyRefreshController();

  PageState pageState = PageState.Empty;

  // 列表数据源
  List<ScheduleAnalysisBDMData>? dataSource;

  dynamic haveChild;

  // 是否为今日
  bool isToday = true;

  // 数据范围BD名
  String? dataRangeName = "全部";

  Map<String, dynamic> params = {};

  @override
  void onCreate() {
    isToday = widget.isToday == "true";

    if (widget.groupId != null) {
      params['groupId'] = widget.groupId;
      dataRangeName = widget.name ?? "全部";
    } else if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
      dataRangeName = widget.name ?? "全部";
    } else {
      params.clear();
      dataRangeName = "全部";
    }
    requestListData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      child: Column(
        children: [buildSortWidget(), buildListWidget()],
      ),
    );
  }

  @override
  String getTitleName() {
    return isToday ? "今日拜访统计" : "${DateTime.now().month}月拜访统计";
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        Visibility(
          visible: false, // 如果选择了一个人，无法区分出是BD还是BDM，暂时隐藏
          child: TextButton(
            onPressed: () {
              XYYContainer.open(
                  'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false',
                  callback: (params) {
                try {
                  if (!(params?.containsKey("name") ?? false)) {
                    return;
                  }
                  setState(() {
                    this.dataRangeName = params?["name"];
                  });
                  if (this.dataRangeName == '全部') {
                    this.params.remove('groupId');
                    this.params.remove('searchUserId');
                  } else {
                    if (params?["isgroup"] == 'true') {
                      this.params.remove('searchUserId');
                      this.params['groupId'] = params?['id'];
                    } else {
                      this.params.remove('groupId');
                      this.params['searchUserId'] = params?['id'];
                    }
                  }
                  this._refreshController.callRefresh();
                } catch (e) {}
              });
            },
            child: Container(
              constraints: BoxConstraints(maxWidth: 90),
              margin: EdgeInsets.only(right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    child: Text(
                      this.dataRangeName ?? "--",
                      textAlign: TextAlign.right,
                      style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 16,
                          fontWeight: FontWeight.w500),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 5),
                  Image.asset(
                    'assets/images/pop_data/pop_statistics_right.png',
                    width: 12,
                    height: 12,
                  ),
                ],
              ),
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            ),
          ),
        )
      ],
    );
  }

  void resetSortParams() {
    params.remove("conversionsSort");
    params.remove("visitCountSort");
  }

  ValueNotifier<SortControllerButtonState> visitCountValueNotifier =
      ValueNotifier(SortControllerButtonState.normal);
  ValueNotifier<SortControllerButtonState> conversionsValueNotifier =
      ValueNotifier(SortControllerButtonState.normal);

  Widget buildSortWidget() {
    return Visibility(
      visible: isToday && haveChild == 0,
      child: Container(
        height: 44,
        color: Colors.white,
        child: Row(
          children: [
            SizedBox(
              width: 15,
            ),
            SortControllerButton(
              controller: visitCountValueNotifier,
              name: "拜访客户数",
              sortKey: 1,
              onPressed: (key, controller) {
                resetSortParams();
                conversionsValueNotifier.value =
                    SortControllerButtonState.normal;
                switch (controller.value) {
                  case SortControllerButtonState.normal:
                    params.remove("visitCountSort");
                    break;
                  case SortControllerButtonState.asc:
                    params["visitCountSort"] = 1;
                    break;
                  case SortControllerButtonState.desc:
                    params["visitCountSort"] = 2;
                    break;
                }
                requestListData();
              },
            ),
            SizedBox(
              width: 35,
            ),
            Visibility(
              visible: false,
              child: SortControllerButton(
                controller: conversionsValueNotifier,
                name: "拜访转化率",
                sortKey: 2,
                onPressed: (key, controller) {
                  resetSortParams();
                  visitCountValueNotifier.value =
                      SortControllerButtonState.normal;
                  switch (controller.value) {
                    case SortControllerButtonState.normal:
                      params.remove("conversionsSort");
                      break;
                    case SortControllerButtonState.asc:
                      params["conversionsSort"] = 1;
                      break;
                    case SortControllerButtonState.desc:
                      params["conversionsSort"] = 2;
                      break;
                  }
                  requestListData();
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildListWidget() {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(10),
        child: EasyRefresh(
          controller: _refreshController,
          onRefresh: () async {
            return await requestListData();
          },
          onLoad: null,
          child: ListView.builder(
            itemCount: this.dataSource?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              ScheduleAnalysisBDMData? model = this.dataSource?[index];
              if (model == null) {
                return Container();
              }
              return GestureDetector(
                onTap: () {
                  handleItemClick(model);
                },
                behavior: HitTestBehavior.opaque,
                child: ScheduleAnalysisBDMItemWidget(
                  isToday,
                  model,
                  index == 0,
                  index == (this.dataSource?.length ?? 0) - 1,
                ),
              );
            },
          ),
          emptyWidget: this.getEmptyWidget(),
        ),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestListData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  Future<void> requestListData() async {
    showLoadingDialog(msg: "加载中...");
    var value =
        await NetworkV2<ScheduleAnalysisBDMData>(ScheduleAnalysisBDMData())
            .requestDataV2(getRequestUrl(),
                parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _refreshController.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          var result = value.getListData();
          if (result?.isNotEmpty == true) {
            dataSource = result!;
            pageState = PageState.Normal;
            haveChild = dataSource?.first.haveChild;
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
      });
    }
  }

  void handleItemClick(ScheduleAnalysisBDMData model) {
    if (model.haveChild == 1) {
      // 跳转子页面
      var router =
          "/schedule_analysis_bdm_page?groupId=${model.groupId}&name=${model.crmUserDepartment}&isToday=$isToday";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    } else {
      // 这个人是bd跳转到bd统计
      if (isToday) {
        var router =
            "/schedule_analysis_bd_today_page?searchUserId=${model.oaId}&searchUserName=${model.crmUserName}";
        router = Uri.encodeFull(router);
        XYYContainer.open(router);
      } else {
        XYYContainer.open(
            "/schedule_analysis_bd_page?searchUserId=${model.oaId}");
      }
    }
  }

  String getRequestUrl() {
    if (isToday) {
      return "visit/dayM";
    }
    return "visit/monthM";
  }
}
