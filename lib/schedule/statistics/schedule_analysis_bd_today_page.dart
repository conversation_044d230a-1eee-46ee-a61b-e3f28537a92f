import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/share/share_select_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/drive_route_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_analysis_bd_today_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/widgets/schedule_analysis_bd_detail_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/common_show_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
import 'package:flutter_baidu_mapapi_utils/flutter_baidu_mapapi_utils.dart';
import 'package:screenshot/screenshot.dart';
import 'widgets/vertical_dotted_painter.dart';
import 'dart:typed_data';
import 'package:XyyBeanSproutsFlutter/home/<USER>/share/share_helper.dart';
import 'package:flutter/rendering.dart';
import 'package:collection/collection.dart';

class ScheduleAnalysisBDTodayPage extends BasePage {
  final String? searchUserId;
  final String? searchUserName;

  ScheduleAnalysisBDTodayPage(this.searchUserId, this.searchUserName);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleAnalysisBDTodayPageState();
  }
}

class ScheduleAnalysisBDTodayPageState
    extends BaseState<ScheduleAnalysisBDTodayPage> {
  final zoomLevel = [
    50,
    100,
    200,
    500,
    1000,
    2000,
    5000,
    10000,
    20000,
    25000,
    50000,
    100000,
    200000,
    500000,
    1000000,
    2000000
  ];
  late BMFMapOptions mapOptions;
  BMFMapController? myMapController;

  DriveRouteModel? driveRoute;

  BMFPolyline? polyline;

  ScheduleAnalysisBDTodayData? analysisData;
  UserInfoData? userInfo;
  ValueNotifier<String> distanceValueNotifier = ValueNotifier("--");
  ScreenshotController screenshotController = ScreenshotController();
  GlobalKey topWidgetKey = GlobalKey(); // 绘图key值
  GlobalKey bottomWidgetKey = GlobalKey(); // 绘图key值
  GlobalKey totalWidgetKey = GlobalKey(); // 绘图key值
  ValueNotifier<bool> shareNotifier = ValueNotifier<bool>(false);
  // 截图 图地址
  String? topUrl;
  String? bdUrl;
  String? clientsUrl;
  String? userInfoUrl;
  String? listHeaderUrl;
  @override
  void onCreate() async {
    super.onCreate();
    mapOptions = BMFMapOptions(zoomLevel: 12, showZoomControl: false);
    requestVisitDetail();
  }

  void _onGetBMFDrivingRouteResult(
      BMFDrivingRouteResult result, BMFSearchErrorCode errorCode) {
    dismissLoadingDialog();
    if (errorCode != BMFSearchErrorCode.NO_ERROR) {
      showToast(errorCode.toString());
    } else {
      showToast("获取轨迹成功");
    }
    print("guan search result $errorCode,${result.toMap()}");
    if (result.routes?.first != null) {
      driveRoute = DriveRouteModel.withModel(result.routes!.first);
      addRoutePolyline();
      distanceValueNotifier.value = driveRoute?.distance ?? "--";
    }
  }

  void buildDrivingPlanRoute() async {
    var visitResult = analysisData?.visitResult;
    if (visitResult == null || visitResult.isEmpty == true) {
      // 没有点
      return;
    }
    List<BMFPlanNode>? wayPoints;
    if (visitResult.length == 1) {
      // 只有一个点
      var coordinate = BMFCoordinate(convertDouble(visitResult.first.latitude),
          convertDouble(visitResult.first.longitude));
      var singleMarker = buildMarker(0, coordinate);
      if (singleMarker != null) {
        myMapController?.cleanAllMarkers();
        myMapController?.addMarker(singleMarker);
        myMapController?.setCenterCoordinate(coordinate, true);
        if (Platform.isAndroid) {
          myMapController?.setZoomTo(16);
        }
      }
      return;
    } else if (visitResult.length > 2) {
      // 超过两个点
      wayPoints = visitResult
          .sublist(1, visitResult.length - 1)
          .map((e) => BMFPlanNode(
              pt: BMFCoordinate(
                  convertDouble(e.latitude), convertDouble(e.longitude))))
          .toList();
    }
    var drivingRoutePlanOption = BMFDrivingRoutePlanOption(
        from: BMFPlanNode(
            pt: BMFCoordinate(convertDouble(visitResult.first.latitude),
                convertDouble(visitResult.first.longitude))),
        to: BMFPlanNode(
            pt: BMFCoordinate(convertDouble(visitResult.last.latitude),
                convertDouble(visitResult.last.longitude))),
        wayPointsArray: wayPoints,
        drivingPolicy: BMFDrivingPolicy.DIS_FIRST); // 路程最短

    print("guan search start");

    BMFDrivingRouteSearch drivingRouteSearch = BMFDrivingRouteSearch();

    /// 检索回调
    drivingRouteSearch.onGetDrivingRouteSearchResult(
        callback: _onGetBMFDrivingRouteResult);
    showLoadingDialog();
    if (Platform.isAndroid) {
      var searchLineResult =
          await XYYContainer.bridgeCall("search_driving_line", parameters: {
        'drivingRoutePlanOption': drivingRoutePlanOption.toMap(),
      }) as Map;
      print("guan search bridgeCall ${searchLineResult.toString()}");
      try {
        BMFDrivingRouteResult result =
            BMFDrivingRouteResult.fromMap(searchLineResult['result']);
        BMFSearchErrorCode errorCode =
            BMFSearchErrorCode.values[searchLineResult['errorCode'] as int];
        _onGetBMFDrivingRouteResult(result, errorCode);
      } catch (e) {
        print("guan search error $e");
      }
    } else {
      bool result =
          await drivingRouteSearch.dringRouteSearch(drivingRoutePlanOption);
      if (result) {
        print("发起检索成功");
      } else {
        print("发起检索失败");
      }
    }
  }
  /// 获取固定控件 url --优化手段
  Future<void> initImgUrl() async {
        // 获取用户信息
    if (userInfoUrl?.isEmpty ?? true) {
      var infoFile = await screenshotController.captureFromWidget(Container(
        height: 67,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(8),
            bottomRight: Radius.circular(8),
          ),
          color: Color(0xffffffff),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Expanded(
              flex: 4,
              child: Align(
                alignment: Alignment.center,
                child: buildBottomLeftColumn(),
              ),
            ),
            Expanded(
              flex: 3,
              child: Align(
                alignment: Alignment.centerRight,
                child: buildBottomRightColumn(),
              ),
            ),
          ],
        ),
      ));
      userInfoUrl = (await ShareHelper().formUnit8ToFie(infoFile))?.path;
    }
    // 获取列表 表头
    if (listHeaderUrl?.isEmpty ?? true) {
      var listHeaderFile =
          await screenshotController.captureFromWidget(buildListHeader());
      listHeaderUrl =
          (await ShareHelper().formUnit8ToFie(listHeaderFile))?.path;
    }
  }
  /// 防重复提交
  Future<void> intervalClick() async {
    // 获取百度地图
    var bdFile;
    myMapController?.takeSnapshot().then((value) async {
      bdFile = await ShareHelper().formUnit8ToFie(value);
    });

   await initImgUrl();

    // 获取客户列表
    await ShareHelper().onSharePlusShare(topWidgetKey).then((topImg) async {
      await ShareHelper()
          .onSharePlusShare(bottomWidgetKey)
          .then((bottomImg) async {
        topUrl = topImg;
        bdUrl = bdFile?.path;
        clientsUrl = bottomImg;
        shareNotifier.value = !shareNotifier.value;
        await Future.delayed(Duration(milliseconds: 500));
        // 获取两图合并后总图
        await ShareHelper()
            .onSharePlusShare(totalWidgetKey)
            .then((value) async {
          return showModalBottomSheet(
            context: this.context,
            isScrollControlled: true, //弹窗是否可以滑动
            builder: (BuildContext context) {
              return ShareSelectPage(path: value ?? '');
            },
          ).then((value) {
            topUrl = null;
            bdUrl = null;
            clientsUrl = null;
            shareNotifier.value = !shareNotifier.value;
          });
        });
      });
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: shareNotifier,
        builder: (BuildContext context, bool refel, Widget? child) {
          return Stack(
            children: [
              Column(
                children: [
                  RepaintBoundary(
                    key: topWidgetKey,
                    child: buildScheduleAnalysisWidget(),
                  ),
                  Expanded(
                    child: BMFMapWidget(
                      onBMFMapCreated: (controller) {
                        myMapController = controller;
                      },
                      mapOptions: mapOptions,
                    ),
                  ),
                  buildScheduleListWidget(),
                ],
              ),
              refel
                  ? Container(
                      margin: EdgeInsets.only(top: 6666),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: RepaintBoundary(
                          key: totalWidgetKey,
                          child: Column(
                            children: [
                              Image.file(File(topUrl ?? '')),
                              Image.file(File(bdUrl ?? '')),
                              Image.file(File(listHeaderUrl ?? '')),
                              Image.file(File(clientsUrl ?? '')),
                              Image.file(File(userInfoUrl ?? '')),
                            ],
                          ),
                        ),
                      ),
                    )
                  : SizedBox()
            ],
          );
        });
  }

  @override
  String getTitleName() {
    return "今日统计详情";
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(this.getTitleName(), rightButtons: [
      LoaingButton(
          onPressed: () async {
            await intervalClick();
          },
          size: Size(22, 22),
          backgroundColor: Colors.transparent,
          textColor: Colors.black,
          content: Container(
              margin: EdgeInsets.only(right: 12),
              width: 22,
              height: 22,
              alignment: Alignment.center,
              child: Image(
                image: AssetImage('assets/images/share/topBar-share-icon.png'),
                width: 22,
                height: 22,
              )))
    ]);
  }

  Widget buildScheduleAnalysisWidget() {
    return GestureDetector(
      onTap: () {
        if (widget.searchUserId == null) {
          UserInfoUtil.getUserInfo().then((value) {
            var router =
                "/visit_manage_page?userId=${value?.sysUserId}&userName=${value?.realName}";
            router = Uri.encodeFull(router);
            Navigator.of(context).pushNamed(router);
          });
        } else {
          var router =
              "/visit_manage_page?userId=${widget.searchUserId}&userName=${widget.searchUserName}";
          router = Uri.encodeFull(router);
          Navigator.of(context).pushNamed(router);
        }
      },
      child: ScheduleAnalysisBDDetailWidget(
        doorVisits: analysisData?.doorVisits,
        conversions: analysisData?.conversions,
        phoneVisits: analysisData?.phoneVisits,
        validDoorVisits: analysisData?.validDoorVisits,
        visitMileage: distanceValueNotifier,
      ),
    );
  }

  Widget buildListHeader() {
    return Container(
      color: Colors.white,
      height: 40,
      padding: EdgeInsets.only(left: 10),
      child: Row(
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: const Color(0xff00b377)),
          ),
          SizedBox(
            width: 5,
          ),
          Text(
            "已下单",
            style: TextStyle(
                color: const Color(0xff292933),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
          SizedBox(
            width: 30,
          ),
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
                shape: BoxShape.circle, color: const Color(0xfffd9319)),
          ),
          SizedBox(
            width: 5,
          ),
          Text(
            "未下单",
            style: TextStyle(
                color: const Color(0xff292933),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
          SizedBox(
            width: 30,
          ),
          Container(
            width: 11,
            height: 11,
            child: Image.asset(
              'assets/images/visit/icon_visit_blue.png',
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Text(
            "计划内",
            style: TextStyle(
                color: const Color(0xff292933),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
        ],
      ),
    );
  }

  Widget buildScheduleListWidget() {
    return Container(
      height: 290,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Color(0x0D292933),
              offset: Offset(0, 1),
              blurRadius: 2,
            )
          ]),
      child: Column(
        children: [
          buildListHeader(),
          Divider(
            height: 0.5,
          ),
          Expanded(
              child: SingleChildScrollView(
            child: RepaintBoundary(
              key: bottomWidgetKey,
              child: analysisData?.visitResult?.isEmpty ?? true
                  ? Container(
                      color: Colors.white,
                      alignment: Alignment.center,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            "assets/images/base/state_empty.png",
                            width: 108,
                            height: 80,
                          ),
                          SizedBox(
                            height: 12,
                          ),
                          Text(
                            "暂无拜访客户",
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                                color: const Color(0xff676773)),
                          )
                        ],
                      ),
                    )
                  : Container(
                      color: Colors.white,
                      child: Column(
                        children: analysisData?.visitResult
                                ?.mapIndexed((index, value) =>
                                    buildVisitItemWidget(index))
                                .toList() ??
                            [],
                      ),
                    ),
            ),
          ))
        ],
      ),
    );
  }

  Widget buildVisitItemWidget(index) {
    var isFirst = index == 0;
    var isLast = index == (analysisData?.visitResult?.length ?? 0) - 1;
    var itemData = analysisData?.visitResult?[index];
    if (itemData == null) {
      return Container();
    }
    return CustomPaint(
      painter: VerticalDottedPainter(isTop: isFirst, isBottom: isLast),
      child: Container(
        padding: EdgeInsets.only(
            left: 10, right: 10, top: isFirst ? 15 : 0, bottom: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 35,
              child: Text(
                getVisitTime(itemData.visitTime as int),
                style: TextStyle(
                    color: const Color(0xff9494a6),
                    fontSize: 12,
                    fontWeight: FontWeight.normal),
              ),
            ),
            SizedBox(
              width: 10,
            ),
            Container(
              width: 17,
              height: 17,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: itemData.isPlaceOrder == true
                      ? const Color(0xff00b377)
                      : const Color(0xfffd9319)),
              child: Text(
                (index + 1).toString(),
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
            ),
            SizedBox(
              width: 10,
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 230),
              child: Text(
                itemData.merchantName?.toString() ?? "--",
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: TextStyle(
                    color: const Color(0xff292933),
                    fontSize: 15,
                    fontWeight: FontWeight.normal),
              ),
            ),
            (itemData.inPlan ?? false)
                ? Container(
                    width: 11,
                    height: 11,
                    margin: EdgeInsets.only(left: 10),
                    child: Image.asset(
                      'assets/images/visit/icon_visit_blue.png',
                    ),
                  )
                : SizedBox(),
          ],
        ),
      ),
    );
  }

  String getVisitTime(int visitTime) {
    return formatDate(
      DateTime.fromMillisecondsSinceEpoch(visitTime),
      [HH, ':', nn],
    );
  }

  /// 获取地图显示区域
  BMFCoordinateBounds getVisibleMapRect(List<BMFCoordinate> coordinates) {
    BMFCoordinate first = coordinates[0];
    double leftBottomX = first.latitude;
    double leftBottomY = first.longitude;
    double rightTopX = first.latitude;
    double rightTopY = first.longitude;

    for (BMFCoordinate coordinate in coordinates) {
      if (coordinate.latitude < leftBottomX) {
        leftBottomX = coordinate.latitude;
      }

      if (coordinate.longitude < leftBottomY) {
        leftBottomY = coordinate.longitude;
      }

      if (coordinate.latitude > rightTopX) {
        rightTopX = coordinate.latitude;
      }

      if (coordinate.longitude > rightTopY) {
        rightTopY = coordinate.longitude;
      }
    }

    BMFCoordinate leftBottom = BMFCoordinate(leftBottomX, leftBottomY);
    BMFCoordinate rightTop = BMFCoordinate(rightTopX, rightTopY);

    BMFCoordinateBounds coordinateBounds =
        BMFCoordinateBounds(northeast: rightTop, southwest: leftBottom);

    return coordinateBounds;
  }

  BMFMarker? buildMarker(int index, BMFCoordinate? coord) {
    if (index < 0) {
      return null;
    }
    var iconPath = "assets/images/schedule/marker_";
    var visitData = analysisData?.visitResult?[index];
    if (coord == null || visitData == null) {
      return null;
    }

    iconPath += visitData.isPlaceOrder == true ? "normal" : "warning";

    // 本地最多25个图片
    if (index >= 25) {
      iconPath += "/25.png";
    } else {
      iconPath += "/${index + 1}.png";
    }
    print("guan buildMarker $iconPath");
    return BMFMarker.icon(
      scaleX: 1,
      scaleY: 1,
      position: coord,
      title: index.toString(),
      icon: iconPath,
    );
  }

  void setZoomLevel(BMFCoordinateBounds bounds) async {
    var locationDistance = await BMFCalculateUtils.getLocationDistance(
            bounds.northeast, bounds.southwest) ??
        0;
    double level = -1;
    for (var i = 0; i < zoomLevel.length; i++) {
      if (zoomLevel[i] < locationDistance) {
        level = 18 - i + 3;
      }
    }
    if (level != -1) {
      myMapController?.setZoomTo(level);
    }
  }

  /// 路线 Polyline
  void addRoutePolyline() {
    if (driveRoute == null) {
      return;
    }
    List<BMFMarker> markers = [];

    /// 起点marker
    var startMarker = buildMarker(0, driveRoute!.startNode?.location);
    if (startMarker != null) {
      markers.add(startMarker);
    }

    /// 终点marker
    var endMarker = buildMarker((analysisData?.visitResult?.length ?? 0) - 1,
        driveRoute!.endNode?.location);
    if (endMarker != null) {
      markers.add(endMarker);
    }

    /// 驾车途径点marker
    for (int index = 0; index < (driveRoute!.wayPoints?.length ?? 0); index++) {
      var wayMarker = buildMarker(index + 1, driveRoute!.wayPoints?[index].pt);
      if (wayMarker != null) {
        markers.add(wayMarker);
      }
    }

    /// 添加marker
    myMapController?.cleanAllMarkers();

    /// 添加路线polyline
    if (polyline != null) {
      myMapController?.removeOverlay(polyline!.Id);
    }

    polyline = BMFPolyline(
      width: Platform.isAndroid ? 15 : 5,
      coordinates: driveRoute!.routeCoordinates!,
      indexs: driveRoute!.routeCoordinates!.map((e) => 1).toList(),
      textures: [
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_unknown.png",
      ],
      dottedLine: false,
    );
    myMapController?.addPolyline(polyline!);
    myMapController?.addMarkers(markers);

    /// 根据polyline设置地图显示范围
    BMFCoordinateBounds coordinateBounds =
        getVisibleMapRect(polyline!.coordinates);

    if (Platform.isAndroid) {
      setZoomLevel(coordinateBounds);
    }

    myMapController?.setVisibleMapRectWithPadding(
      visibleMapBounds: coordinateBounds,
      insets: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
      animated: true,
    );
  }

  Future<void> requestVisitDetail() async {
    var params = {};
    if (widget.searchUserId != null) {
      params = {"searchUserId": widget.searchUserId};
    }
    showLoadingDialog();

    userInfo = await UserInfoUtil.getUserInfo();
    var result = await NetworkV2<ScheduleAnalysisBDTodayData>(
            ScheduleAnalysisBDTodayData())
        .requestDataV2("visit/dayBd",
            method: RequestMethod.GET, parameters: params);
    initImgUrl();
    dismissLoadingDialog();
    if (mounted && result.isSuccess == true) {
      analysisData = result.getData();
      
      setState(() {});
      buildDrivingPlanRoute();
    }
  }

  double convertDouble(String value) {
    try {
      return double.tryParse(value) ?? 0;
    } catch (e) {}
    return 0;
  }

  //尾部左边：姓名、部门
  Widget buildBottomLeftColumn() {
    return Container(
      child: Row(
        children: <Widget>[
          Container(
            child: Image.asset(
              'assets/images/share/share_logo.png',
              width: 43,
              height: 43,
            ),
          ),
          const SizedBox(width: 7.5),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  userInfo?.realName ?? '--',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF676773),
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  userInfo?.department ?? '--',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF676773),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //尾部右边：日期、星期、天干地支
  Widget buildBottomRightColumn() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '${analysisData?.chineseDate ?? ''} ${analysisData?.weekDay ?? ''}', //12月26日 星期六
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Color(0xFF00B377)),
          ),
          SizedBox(height: 5),
          Text(
            '${analysisData?.ganZhiDate ?? ''}', //天干地支
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF85858F),
            ),
          ),
        ],
      ),
    );
  }
}
