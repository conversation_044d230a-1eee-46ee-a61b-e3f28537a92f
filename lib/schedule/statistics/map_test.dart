import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/drive_route_model.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';

class MapTest extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return MapTestState();
  }
}

class MapTestState extends BaseState<MapTest> {
  late BMFMapOptions mapOptions;

  late DriveRouteModel _driveRoute;

  late BMFMapController myMapController;

  BMFPolyline? _polyline;

  var nodeList = [
    BMFPlanNode(cityName: "北京", pt: BMFCoordinate( 39.998379,116.477085,)),
    BMFPlanNode(cityName: "北京", pt: BMFCoordinate( 40.001696,116.484416,)),
    BMFPlanNode(cityName: "北京", pt: BMFCoordinate( 39.996721,116.491746,)),
    BMFPlanNode(cityName: "北京", pt: BMFCoordinate( 39.989535,116.479313,)),
    BMFPlanNode(cityName: "北京", pt: BMFCoordinate( 39.983122,116.477588,)),
  ];

  @override
  void initState() {
    super.initState();
    mapOptions = BMFMapOptions(
      zoomLevel: 12,
    );
    Future.delayed(Duration(seconds: 1), () {
      buildPlanList();
    });
  }

  /// 路线 Polyline
  void _addRoutePolyline() {
    List<BMFMarker> markers = [];

    /// 起点marker

    BMFCoordinate? startCoord = _driveRoute.startNode?.location;
    if (startCoord != null) {
      BMFMarker startMarker = BMFMarker.icon(
        position: startCoord,
        title: "first",
        icon: "assets/images/message/message_notification_close.png",
      );
      markers.add(startMarker);
    }

    /// 终点marker
    BMFCoordinate? endCoord = _driveRoute.endNode?.location;
    if (endCoord != null) {
      BMFMarker endMarker = BMFMarker.icon(
        position: endCoord,
        title: "last",
        icon: "assets/images/message/message_notification_close.png",
      );
      markers.add(endMarker);
    }

    /// 驾车途径点marker
    _driveRoute.wayPoints?.forEach((element) {
      BMFMarker marker = BMFMarker.icon(
        position: element.pt!,
        title: element.name,
        icon: "assets/images/message/message_notification_close.png",
      );
      markers.add(marker);
    });

    /// 添加marker
    myMapController.cleanAllMarkers();
    myMapController.addMarkers(markers);

    /// 添加路线polyline
    if (_polyline != null) {
      myMapController.removeOverlay(_polyline!.Id);
    }

    _polyline = BMFPolyline(
      width: 8,
      coordinates: _driveRoute.routeCoordinates!,
      // indexs: _driveRoute.trafficIndexList!,
      indexs: _driveRoute.routeCoordinates!.map((e) => 1).toList(),
      textures: [
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_smooth.png",
        "assets/images/schedule/traffic_texture_smooth.png",
      ],
      dottedLine: false,
    );
    myMapController.addPolyline(_polyline!);

    /// 根据polyline设置地图显示范围
    BMFCoordinateBounds coordinateBounds =
        getVisibleMapRect(_polyline!.coordinates);

    myMapController.setVisibleMapRectWithPadding(
      visibleMapBounds: coordinateBounds,
      insets: EdgeInsets.only(top: 65.0, bottom: 70.0, left: 10, right: 10),
      animated: true,
    );
    setState(() {});
  }

  /// 获取地图显示区域
  BMFCoordinateBounds getVisibleMapRect(List<BMFCoordinate> coordinates) {
    BMFCoordinate fisrt = coordinates[0];
    double leftBottomX = fisrt.latitude;
    double leftBottomY = fisrt.longitude;
    double rightTopX = fisrt.latitude;
    double rightTopY = fisrt.longitude;

    for (BMFCoordinate coordinate in coordinates) {
      if (coordinate.latitude < leftBottomX) {
        leftBottomX = coordinate.latitude;
      }

      if (coordinate.longitude < leftBottomY) {
        leftBottomY = coordinate.longitude;
      }

      if (coordinate.latitude > rightTopX) {
        rightTopX = coordinate.latitude;
      }

      if (coordinate.longitude > rightTopY) {
        rightTopY = coordinate.longitude;
      }
    }

    BMFCoordinate leftBottom = BMFCoordinate(leftBottomX, leftBottomY);
    BMFCoordinate rightTop = BMFCoordinate(rightTopX, rightTopY);

    BMFCoordinateBounds coordinateBounds =
        BMFCoordinateBounds(northeast: rightTop, southwest: leftBottom);

    return coordinateBounds;
  }

  void _onGetBMFDrivingRouteResult(
      BMFDrivingRouteResult result, BMFSearchErrorCode errorCode) {
    showToast(errorCode.toString());
    print("guan search _onGetBMFDrivingRouteResult");
    print("guan search ${errorCode},${result.toMap()}");
    if (result.routes?.first != null) {
      _driveRoute = DriveRouteModel.withModel(result.routes!.first);
      _addRoutePolyline();
    }
  }



  Future<void> buildPlanList() async {
    var drivingRoutePlanOption = BMFDrivingRoutePlanOption(
        from: nodeList.first,
        to: nodeList.last,
        wayPointsArray: nodeList.sublist(1, nodeList.length - 1));

    print("guan search start");

    /// 驾车检索参数设置
    // BMFDrivingRoutePlanOption drivingRoutePlanOption =
    //     BMFDrivingRoutePlanOption(from: from, to: to);
    drivingRoutePlanOption.drivingRequestTrafficType = false
        ? BMFDrivingRequestTrafficType.PATH_AND_TRAFFICE
        : BMFDrivingRequestTrafficType.NONE;
    drivingRoutePlanOption.drivingPolicy = BMFDrivingPolicy.values[1];

    BMFDrivingRouteSearch drivingRouteSearch = BMFDrivingRouteSearch();

    /// 检索回调
    drivingRouteSearch.onGetDrivingRouteSearchResult(
        callback: _onGetBMFDrivingRouteResult);
    //
    // var searchLineResult =
    //     await XYYContainer.bridgeCall("search_driving_line", parameters: {
    //   'drivingRoutePlanOption': drivingRoutePlanOption.toMap(),
    // }) as Map;
    // print("guan search end");
    // print("guan search ${searchLineResult.toString()}");
    // try{
    //
    // BMFDrivingRouteResult result =
    //     BMFDrivingRouteResult.fromMap(searchLineResult['result']);
    // BMFSearchErrorCode errorCode =
    //     BMFSearchErrorCode.values[searchLineResult['errorCode'] as int];
    // _onGetBMFDrivingRouteResult(result, errorCode);
    // }catch (e){
    //   print("guan search error ${e}");
    // }

    bool result =
    await drivingRouteSearch.dringRouteSearch(drivingRoutePlanOption);
    if (result) {
      print("guan search result success");
      print("发起检索成功");
    } else {
      print("guan search result fail");
      print("发起检索失败");
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return BMFMapWidget(
      onBMFMapCreated: (controller) {
        myMapController = controller;
      },
      mapOptions: mapOptions,
    );
  }

  @override
  String getTitleName() {
    return "测试";
  }
}

