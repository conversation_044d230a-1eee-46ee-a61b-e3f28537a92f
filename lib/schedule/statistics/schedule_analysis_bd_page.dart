import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_analysis_bd_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/widgets/shcedule_analysis_bd_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleAnalysisBDPage extends BasePage {
  final String? searchUserId;

  ScheduleAnalysisBDPage(this.searchUserId);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleAnalysisBDPageState();
  }
}

class ScheduleAnalysisBDPageState extends BaseState<ScheduleAnalysisBDPage> {
  EasyRefreshController _refreshController = EasyRefreshController();

  PageState pageState = PageState.Empty;
  ScheduleAnalysisBDData? scheduleAnalysisBDData;

  // ScheduleAnalysisBDData? scheduleAnalysisBDData = ScheduleAnalysisBDData()
  // ..validVisits = 16
  // ..noVisits = 10
  // ..visitCoverRate = 62
  // ..conversionRate = 54.2
  // ..noConversions = 54
  // ..visitList = [
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 1
  //     ..levelDesc = "S"
  //     ..visitCoverRate = "40"
  //     ..conversionRate = "40",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 5
  //     ..levelDesc = "A"
  //     ..visitCoverRate = "50"
  //     ..conversionRate = "50",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 9
  //     ..levelDesc = "B"
  //     ..visitCoverRate = "70"
  //     ..conversionRate = "70",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 13
  //     ..levelDesc = "C"
  //     ..visitCoverRate = "10"
  //     ..conversionRate = "10",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 14
  //     ..levelDesc = "D"
  //     ..visitCoverRate = "99"
  //     ..conversionRate = "99",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = -1
  //     ..levelDesc = "未注册"
  //     ..visitCoverRate = "69.2"
  //     ..conversionRate = "69.2"
  // ]
  // ..visitConversionList = [
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 1
  //     ..levelDesc = "S"
  //     ..visitCoverRate = "40"
  //     ..conversionRate = "40",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 5
  //     ..levelDesc = "A"
  //     ..visitCoverRate = "50"
  //     ..conversionRate = "50",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 9
  //     ..levelDesc = "B"
  //     ..visitCoverRate = "70"
  //     ..conversionRate = "70",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 13
  //     ..levelDesc = "C"
  //     ..visitCoverRate = "10"
  //     ..conversionRate = "10",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = 14
  //     ..levelDesc = "D"
  //     ..visitCoverRate = "99"
  //     ..conversionRate = "99",
  //   ScheduleAnalysisBDVisitData()
  //     ..level = -1
  //     ..levelDesc = "未注册"
  //     ..visitCoverRate = "69.2"
  //     ..conversionRate = "69.2"
  // ];

  @override
  void onCreate() {
    super.onCreate();
    requestAnalysisData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      padding: EdgeInsets.all(10),
      child: EasyRefresh(
        controller: _refreshController,
        onRefresh: () async {
          return await requestAnalysisData();
        },
        onLoad: null,
        child: buildContentWidget(),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  @override
  String getTitleName() {
    return "${DateTime.now().month}月统计详情";
  }

  getEmptyWidget() {
    if (scheduleAnalysisBDData != null) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestAnalysisData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  Widget buildContentWidget() {
    return SingleChildScrollView(
      child: Column(
        children: [
          Row(
            children: [
              buildValidVisitWidget(),
              SizedBox(
                width: 10,
              ),
              buildNoVisitWidget()
            ],
          ),
          SizedBox(
            height: 10,
          ),
          buildVisitCoverWidget(),
          SizedBox(
            height: 10,
          ),
          buildVisitConversionWidget()
        ],
      ),
    );
  }

  Widget buildValidVisitWidget() {
    return Expanded(
      child: Container(
        height: 70,
        padding: EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(4)),
        child: Row(
          children: [
            Image.asset(
              "assets/images/schedule/schedule_statistics_item_valid_visit.png",
              width: 40,
              height: 40,
            ),
            SizedBox(
              width: 10,
            ),
            Expanded(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "有效拜访：",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontWeight: FontWeight.w600,
                      fontSize: 14),
                ),
                SizedBox(
                  height: 1,
                ),
                RichText(
                  text: TextSpan(
                      text: scheduleAnalysisBDData?.validVisits?.toString() ??
                          "--",
                      style: TextStyle(
                          color: const Color(0xff0D0E10),
                          fontWeight: FontWeight.w600,
                          fontSize: 20),
                      children: [
                        TextSpan(
                            text: "  家",
                            style: TextStyle(
                                fontSize: 11,
                                color: const Color(0xff9494a6),
                                fontWeight: FontWeight.normal))
                      ]),
                  textAlign: TextAlign.end,
                )
              ],
            ))
          ],
        ),
      ),
    );
  }

  Widget buildNoVisitWidget() {
    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          jumpNoVisitCustomerPage();
        },
        child: Container(
          height: 70,
          padding: EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Row(
            children: [
              Image.asset(
                "assets/images/schedule/schedule_statistics_item_no_visit.png",
                width: 40,
                height: 40,
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "未拜访：",
                    style: TextStyle(
                        color: const Color(0xff676773),
                        fontWeight: FontWeight.w600,
                        fontSize: 14),
                  ),
                  SizedBox(
                    height: 1,
                  ),
                  RichText(
                    text: TextSpan(
                        text: scheduleAnalysisBDData?.noVisits?.toString() ??
                            "--",
                        style: TextStyle(
                            color: const Color(0xff0D0E10),
                            fontWeight: FontWeight.w600,
                            fontSize: 20),
                        children: [
                          TextSpan(
                              text: "  家",
                              style: TextStyle(
                                  fontSize: 11,
                                  color: const Color(0xff9494a6),
                                  fontWeight: FontWeight.normal))
                        ]),
                    textAlign: TextAlign.end,
                  )
                ],
              )),
              Image.asset(
                "assets/images/schedule/schedule_item_arrow.png",
                width: 12,
                height: 12,
              )
            ],
          ),
        ),
      ),
    );
  }

  void jumpNoVisitCustomerPage() {
    if (widget.searchUserId == null) {
      UserInfoUtil.getUserInfo().then((value) {
        XYYContainer.open(
            "/schedule_no_visit_customer_bd_page?searchUserId=${value?.sysUserId}");
      });
    } else {
      XYYContainer.open(
          "/schedule_no_visit_customer_bd_page?searchUserId=${widget.searchUserId}");
    }
  }

  Widget buildVisitCoverWidget() {
    var visitCoverRate = scheduleAnalysisBDData?.visitList;
    List<Widget> widgetList = [
      Container(
        alignment: Alignment.centerLeft,
        child: RichText(
            text: TextSpan(
                text: "拜访覆盖率  ",
                style: TextStyle(
                    color: const Color(0xff676773),
                    fontWeight: FontWeight.normal,
                    fontSize: 14),
                children: [
              TextSpan(
                  text: "${scheduleAnalysisBDData?.visitCoverRate ?? "--"}%",
                  style: TextStyle(
                      color: const Color(0xff2b2a2f),
                      fontSize: 15,
                      fontWeight: FontWeight.w600))
            ])),
      ),
      SizedBox(
        height: 10,
      ),
      Divider(
        height: 0.5,
        color: const Color(0xfff5f5f5),
      ),
    ];
    if (visitCoverRate?.isNotEmpty == true) {
      sortVisitItemList(visitCoverRate!);
      widgetList.addAll(visitCoverRate.map((e) {
        return ScheduleAnalysisBDItemWidget(e, true);
      }).toList());
    }

    return Container(
      padding: EdgeInsets.all(10),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: widgetList,
      ),
    );
  }

  Widget buildVisitConversionWidget() {
    var visitConversionRate = scheduleAnalysisBDData?.visitConversionList;
    List<Widget> widgetList = [
      Row(
        children: [
          RichText(
              text: TextSpan(
                  text: "拜访转化率  ",
                  style: TextStyle(
                      color: const Color(0xff676773),
                      fontWeight: FontWeight.normal,
                      fontSize: 14),
                  children: [
                TextSpan(
                    text: "${scheduleAnalysisBDData?.conversionRate ?? "--"}%",
                    style: TextStyle(
                        color: const Color(0xff2b2a2f),
                        fontSize: 15,
                        fontWeight: FontWeight.w600))
              ])),
          Expanded(
            child: Center(
              child: RichText(
                  text: TextSpan(
                      text: "未转化客户数  ",
                      style: TextStyle(
                          color: const Color(0xff676773),
                          fontWeight: FontWeight.normal,
                          fontSize: 14),
                      children: [
                    TextSpan(
                        text:
                            "${scheduleAnalysisBDData?.noConversions ?? "--"}",
                        style: TextStyle(
                            color: const Color(0xff2b2a2f),
                            fontSize: 15,
                            fontWeight: FontWeight.w600))
                  ])),
            ),
          )
        ],
      ),
      SizedBox(
        height: 10,
      ),
      Divider(
        height: 0.5,
        color: const Color(0xfff5f5f5),
      ),
    ];
    if (visitConversionRate?.isNotEmpty == true) {
      sortVisitItemList(visitConversionRate!);
      widgetList.addAll(visitConversionRate.map((e) {
        return ScheduleAnalysisBDItemWidget(e, false);
      }).toList());
    }

    return Container(
      padding: EdgeInsets.all(10),
      color: Colors.white,
      child: Column(
        children: widgetList,
      ),
    );
  }

  void sortVisitItemList(List<ScheduleAnalysisBDVisitData> list) {
    // 排序，负数在末尾，其余正数按升序排
    list.sort((e1, e2) {
      if (e1.level is int && e2.level is int) {
        if (e1.level < 0) {
          if (e2.level < 0) {
            return e1.level - e2.level;
          } else {
            return 1;
          }
        } else {
          if (e2.level < 0) {
            return -1;
          } else {
            return e1.level - e2.level;
          }
        }
      }
      return 0;
    });
  }

  Future<void> requestAnalysisData() async {
    showLoadingDialog();
    var value =
        await NetworkV2<ScheduleAnalysisBDData>(ScheduleAnalysisBDData())
            .requestDataV2("visit/monthBd",
                method: RequestMethod.GET,
                parameters: {"searchUserId": widget.searchUserId});
    dismissLoadingDialog();
    if (mounted) {
      pageState = PageState.Error;
      if (value.isSuccess == true) {
        var result = value.getData();
        if (result != null) {
          pageState = PageState.Normal;
          scheduleAnalysisBDData = result;
        }
      }
      setState(() {});
    }
  }
}
