import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_no_visit_customer_bdm_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/widgets/schedule_no_visit_bdm_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleNoVisitCustomerBDMPage extends BasePage {
  final String? groupId;
  final String? searchUserId;
  final String? name;

  ScheduleNoVisitCustomerBDMPage(this.groupId, this.searchUserId, this.name);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleNoVisitCustomerBDMPageState();
  }
}

class ScheduleNoVisitCustomerBDMPageState
    extends BaseState<ScheduleNoVisitCustomerBDMPage> {
  EasyRefreshController _refreshController = EasyRefreshController();
  List<ScheduleNoVisitCustomerBDMData>? dataSource;

  // List<ScheduleNoVisitCustomerBDMData>? dataSource = [
  //   ScheduleNoVisitCustomerBDMData()
  //   ..oaId="123"
  //   ..noVisits=123
  //   ..crmUserName="龙葵-张继科"
  //   ..crmUserDepartment="湖北销售部1"
  //   ..haveChild=true
  //   ..groupId="29384u",
  //   ScheduleNoVisitCustomerBDMData()
  //     ..oaId=null
  //     ..noVisits=123
  //     ..crmUserName="龙葵-张继科"
  //     ..crmUserDepartment="湖北省区副总经理"
  //     ..haveChild=true
  //     ..groupId="29384u",
  //   ScheduleNoVisitCustomerBDMData()
  //     ..oaId="123"
  //     ..noVisits=123
  //     ..crmUserName="龙葵-张继科"
  //     ..crmUserDepartment="湖北销售部242323535345341"
  //     ..haveChild=true
  //     ..groupId="29384u",
  // ];
  PageState pageState = PageState.Normal;

  // 数据范围BD名
  String? dataRangeName = "全部";

  Map<String, dynamic> params = {};

  @override
  void onCreate() {
    if (widget.groupId != null) {
      params['groupId'] = widget.groupId;
      dataRangeName = widget.name ?? "全部";
    } else if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
      dataRangeName = widget.name ?? "全部";
    } else {
      params.clear();
      dataRangeName = "全部";
    }
    requestListData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      child: buildListWidget(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  @override
  String getTitleName() {
    return "未拜访客户统计";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        Visibility(
          // 如果选择了一个人，无法区分出是BD还是BDM，暂时隐藏
          visible: false,
          child: TextButton(
            onPressed: () {
              XYYContainer.open(
                  'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false',
                  callback: (params) {
                try {
                  if (!(params?.containsKey("name") ?? false)) {
                    return;
                  }
                  setState(() {
                    this.dataRangeName = params?["name"];
                  });
                  if (this.dataRangeName == '全部') {
                    this.params.remove('groupId');
                    this.params.remove('searchUserId');
                  } else {
                    if (params?["isgroup"] == 'true') {
                      this.params.remove('searchUserId');
                      this.params['groupId'] = params?['id'];
                    } else {
                      this.params.remove('groupId');
                      this.params['searchUserId'] = params?['id'];
                    }
                  }
                  this._refreshController.callRefresh();
                } catch (e) {}
              });
            },
            child: Container(
              constraints: BoxConstraints(maxWidth: 90),
              margin: EdgeInsets.only(right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    child: Text(
                      this.dataRangeName ?? "--",
                      textAlign: TextAlign.right,
                      style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 16,
                          fontWeight: FontWeight.w500),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 5),
                  Image.asset(
                    'assets/images/pop_data/pop_statistics_right.png',
                    width: 12,
                    height: 12,
                  ),
                ],
              ),
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            ),
          ),
        )
      ],
    );
  }

  Widget buildListWidget() {
    return Container(
      padding: EdgeInsets.all(10),
      child: EasyRefresh(
        controller: _refreshController,
        onRefresh: () async {
          return await requestListData();
        },
        onLoad: null,
        child: ListView.builder(
          itemCount: this.dataSource?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            ScheduleNoVisitCustomerBDMData? model = this.dataSource?[index];
            if (model == null) {
              return Container();
            }
            return GestureDetector(
              onTap: () {
                print("点击事件");
                handleItemClick(model);
              },
              behavior: HitTestBehavior.opaque,
              child: ScheduleNoVisitBDMItemWidget(
                model,
                index == 0,
                index == (this.dataSource?.length ?? 0) - 1,
              ),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestListData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  Future<void> requestListData() async {
    showLoadingDialog(msg: "加载中...");
    var value = await NetworkV2<ScheduleNoVisitCustomerBDMData>(
            ScheduleNoVisitCustomerBDMData())
        .requestDataV2("visit/noVisitM",
            parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _refreshController.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          var result = value.getListData();
          if (result?.isNotEmpty == true) {
            dataSource = result!;
            pageState = PageState.Normal;
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
      });
    }
  }

  void handleItemClick(ScheduleNoVisitCustomerBDMData model) {
    if (model.haveChild == 1) {
      // 跳转子页面
      var router =
          "/schedule_no_visit_customer_bdm_page?groupId=${model.groupId}&name=${model.crmUserDepartment}";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    } else {
      if (model.isBDM()) {
        // bdm不跳转
        return;
      }
      XYYContainer.open(
          "/schedule_no_visit_customer_bd_page?searchUserId=${model.oaId}");
    }
  }
}
