import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_no_visit_customer_bd_data.dart';
import 'package:flutter/material.dart';

class ScheduleNoVisitBDItemWidget extends StatelessWidget {
  final ScheduleNoVisitCustomerBDData itemData;
  final bool isFirst;
  final bool isLast;

  ScheduleNoVisitBDItemWidget(this.itemData, this.isFirst, this.isLast);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      decoration:
          BoxDecoration(color: Colors.white, borderRadius: getBorderRadius()),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            "assets/images/schedule/schedule_statistics_item_customer.png",
            width: 22,
            height: 22,
          ),
          SizedBox(
            width: 11,
          ),
          Expanded(
            child: Text(
              itemData.merchantName?.toString() ?? "",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: const Color(0xff292933),
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
            ),
          ),
          SizedBox(
            width: 10,
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 2,horizontal: 6),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: const Color(0xfff2fffa),
                  borderRadius: BorderRadius.circular(2),
                  border:
                      Border.all(color: const Color(0xff00b377), width: 0.5)),
              child: Text(
                itemData.levelDesc ?? "--",
                style: TextStyle(
                    color: const Color(0xff00B377),
                    fontSize: 12,
                    fontWeight: FontWeight.normal),
              )),
          SizedBox(
            width: 15,
          ),
          Image.asset(
            "assets/images/schedule/schedule_statistics_item_arrow.png",
            width: 12.5,
            height: 12.5,
          ),
        ],
      ),
    );
  }

  BorderRadius getBorderRadius() {
    if (isFirst == true) {
      return BorderRadius.only(
          topLeft: Radius.circular(4), topRight: Radius.circular(4));
    }
    if (isLast == true) {
      return BorderRadius.only(
          bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4));
    }
    return BorderRadius.zero;
  }
}
