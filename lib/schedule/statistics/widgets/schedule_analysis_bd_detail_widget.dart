import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ScheduleAnalysisBDDetailWidget extends StatelessWidget {
  final dynamic doorVisits;
  final dynamic validDoorVisits;
  final dynamic phoneVisits;
  final dynamic conversions;
  final ValueNotifier<String> visitMileage;

  ScheduleAnalysisBDDetailWidget(
      {this.doorVisits,
      this.validDoorVisits,
      this.phoneVisits,
      this.conversions,
      required this.visitMileage});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Color(0x0D292933),
              offset: Offset(0, 1),
              blurRadius: 2,
            )
          ]),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            height: 87,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    height: 87,
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Spacer(),
                        Row(
                          children: [
                            Image.asset(
                              "assets/images/schedule/schedule_analysis_shangmen_icon.png",
                              width: 16,
                              height: 16,
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              "上门拜访",
                              style: TextStyle(
                                  color: const Color(0xff676773),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Text(
                              doorVisits?.toString() ?? "--",
                              style: TextStyle(
                                  color: const Color(0xff0d0e10),
                                  fontSize: 23,
                                  fontWeight: FontWeight.w600),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              "家",
                              style: TextStyle(
                                  color: const Color(0xff9494a6),
                                  fontSize: 11,
                                  fontWeight: FontWeight.normal),
                            ),
                            SizedBox(
                              width: 7,
                            ),
                            Container(
                              height: 20,
                              padding: EdgeInsets.only(left: 15, right: 10),
                              decoration: BoxDecoration(
                                  image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/schedule/schedule_analysis_tips_bg.png'),
                                fit: BoxFit.fill,
                              )),
                              child: Text(
                                "有效${validDoorVisits ?? "--"}家",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.normal,
                                    fontSize: 12),
                              ),
                            )
                          ],
                        ),
                        Spacer()
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 87,
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Image.asset(
                              "assets/images/schedule/schedule_analysis_phone_icon.png",
                              width: 16,
                              height: 16,
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              "电话拜访",
                              style: TextStyle(
                                  color: const Color(0xff676773),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14),
                            )
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            Text(
                              phoneVisits?.toString() ?? "--",
                              style: TextStyle(
                                  color: const Color(0xff0d0e10),
                                  fontSize: 23,
                                  fontWeight: FontWeight.w600),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              "家",
                              style: TextStyle(
                                  color: const Color(0xff9494a6),
                                  fontSize: 11,
                                  fontWeight: FontWeight.normal),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
                Image.asset(
                  "assets/images/schedule/schedule_analysis_arrow.png",
                  width: 12,
                  height: 12,
                ),
                SizedBox(
                  width: 10,
                )
              ],
            ),
          ),
          Divider(
            height: 0.5,
            color: const Color(0xfff5f5f5),
          ),
          Container(
            height: 41,
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 10),
                    child: RichText(
                        text: TextSpan(
                            text: "拜访后下单",
                            style: TextStyle(
                                color: const Color(0xff676773),
                                fontWeight: FontWeight.normal,
                                fontSize: 14),
                            children: [
                          TextSpan(
                              text: "  ${conversions ?? "--"}家",
                              style: TextStyle(
                                  color: const Color(0xff2b2a2f),
                                  fontWeight: FontWeight.w500))
                        ])),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left: 10),
                    child: ValueListenableBuilder(
                      valueListenable: visitMileage,
                      builder: (context, value, child) {
                        return RichText(
                            text: TextSpan(
                                text: "拜访里程",
                                style: TextStyle(
                                    color: const Color(0xff676773),
                                    fontWeight: FontWeight.normal,
                                    fontSize: 14),
                                children: [
                              TextSpan(
                                  text: "  ${visitMileage.value}",
                                  style: TextStyle(
                                      color: const Color(0xff2b2a2f),
                                      fontWeight: FontWeight.w500))
                            ]));
                      },
                    ),
                  ),
                ),
                SizedBox(
                  width: 22,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
