import 'package:flutter/material.dart';
import 'package:flutter/painting.dart';

class VerticalDottedPainter extends CustomPainter {
  final bool isTop;
  final bool isBottom;

  VerticalDottedPainter({this.isTop = false, this.isBottom = false});

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint() // 画笔
      ..strokeWidth = 0.5 // 画笔宽度
      ..isAntiAlias = true // 是否开启抗锯齿
      ..color = Color(0xFFc6c6c6); // 画笔颜色

    var dashHeight = 3;
    var dashSpace = 2;
    double startY = getStartOffset();
    final space = (dashSpace + dashHeight);

    while (startY < size.height + getEndOffset()) {
      canvas.drawLine(Offset(getXOffset(), startY),
          Offset(getXOffset(), startY + dashHeight), paint);
      startY += space;
    }
  }

  double getXOffset() {
    return 63.5;
  }

  double getStartOffset() {
    if (isTop) {
      return 30;
    } else {
      return 0;
    }
  }

  double getEndOffset() {
    if (isBottom) {
      return -35;
    } else {
      return 0;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
