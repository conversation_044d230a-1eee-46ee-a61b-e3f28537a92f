import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_analysis_bdm_data.dart';
import 'package:flutter/material.dart';

class ScheduleAnalysisBDMItemWidget extends StatelessWidget {
  final ScheduleAnalysisBDMData itemData;
  final bool isFirst;
  final bool isLast;
  final bool isToday;

  ScheduleAnalysisBDMItemWidget(
      this.isToday, this.itemData, this.isFirst, this.isLast);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      decoration:
          BoxDecoration(color: Colors.white, borderRadius: getBorderRadius()),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                isBD()
                    ? "assets/images/schedule/schedule_statistics_item_user.png"
                    : "assets/images/schedule/schedule_statistics_item_group.png",
                width: 22,
                height: 22,
              ),
              SizedBox(
                width: 11,
              ),
              Expanded(
                  child: Text(
                isBD() ? itemData.crmUserName : itemData.crmUserDepartment,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: const Color(0xff292933),
                    fontSize: 16,
                    fontWeight: FontWeight.w600),
              )),
              SizedBox(
                width: 10,
              ),
              Image.asset(
                "assets/images/schedule/schedule_statistics_item_arrow.png",
                width: 12.5,
                height: 12.5,
              ),
            ],
          ),
          SizedBox(
            height: isBD() ? 7 : 0,
          ),
          Visibility(
              visible: isBD(),
              child: isToday
                  ? buildTodayStatisticsWidget()
                  : buildMonthStatisticsWidget())
        ],
      ),
    );
  }

  Widget buildTodayStatisticsWidget() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Container(
            height: 61,
            padding: EdgeInsets.only(left: 12, right: 12, top: 6, bottom: 6),
            decoration: BoxDecoration(
                color: Color(0xfffafbfc),
                borderRadius: BorderRadius.circular(4)),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      "上门拜访",
                      style: TextStyle(
                          color: const Color(0xff666666),
                          fontWeight: FontWeight.normal,
                          fontSize: 13),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      itemData.doorVisits?.toString() ?? "",
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                          color: const Color(0xff292933)),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Container(
                      height: 19.5,
                      decoration: BoxDecoration(
                          image: DecorationImage(
                              image: AssetImage(
                                "assets/images/schedule/schedule_statistics_item_tag.png",
                              ),
                              fit: BoxFit.fill)),
                      padding: EdgeInsets.only(left: 15, right: 8),
                      child: Text(
                        "有效${itemData.validDoorVisits}家",
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.normal,
                            color: Colors.white),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Text(
                      "电话拜访",
                      style: TextStyle(
                          color: const Color(0xff666666),
                          fontWeight: FontWeight.normal,
                          fontSize: 13),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Text(
                      itemData.phoneVisits?.toString() ?? "",
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                          color: const Color(0xff292933)),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Container(
          height: 61,
          decoration: BoxDecoration(
              color: const Color(0xfffdfbf3),
              borderRadius: BorderRadius.circular(4)),
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "拜访转化",
                style: TextStyle(
                    color: const Color(0xff666666),
                    fontWeight: FontWeight.normal,
                    fontSize: 13),
              ),
              Expanded(
                child: SizedBox(
                  width: 2,
                ),
              ),
              RichText(
                text: TextSpan(
                    text: itemData.conversions?.toString(),
                    style: TextStyle(
                        color: const Color(0xff00b377),
                        fontWeight: FontWeight.w600,
                        fontSize: 19),
                    children: [
                      TextSpan(
                          text: "家",
                          style: TextStyle(
                              fontSize: 11,
                              color: const Color(0xff9494a6),
                              fontWeight: FontWeight.normal))
                    ]),
                textAlign: TextAlign.end,
              )
            ],
          ),
        )
      ],
    );
  }

  Widget buildMonthStatisticsWidget() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Container(
            height: 61,
            decoration: BoxDecoration(
                color: const Color(0xfffafbfc),
                borderRadius: BorderRadius.circular(4)),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "有效拜访",
                  style: TextStyle(
                      color: const Color(0xff666666),
                      fontWeight: FontWeight.normal,
                      fontSize: 13),
                ),
                Expanded(
                  child: SizedBox(
                    width: 2,
                  ),
                ),
                RichText(
                  text: TextSpan(
                      text: itemData.validVisits?.toString() ?? "--",
                      style: TextStyle(
                          color: const Color(0xff00b377),
                          fontWeight: FontWeight.normal,
                          fontSize: 19),
                      children: [
                        TextSpan(
                            text: "家",
                            style: TextStyle(
                                fontSize: 11,
                                color: const Color(0xff9494a6),
                                fontWeight: FontWeight.normal))
                      ]),
                  textAlign: TextAlign.end,
                )
              ],
            ),
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Expanded(
          child: Container(
            height: 61,
            decoration: BoxDecoration(
                color: const Color(0xfffafbfc),
                borderRadius: BorderRadius.circular(4)),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "拜访覆盖率",
                  style: TextStyle(
                      color: const Color(0xff666666),
                      fontWeight: FontWeight.normal,
                      fontSize: 13),
                ),
                Expanded(
                  child: SizedBox(
                    width: 2,
                  ),
                ),
                RichText(
                  text: TextSpan(
                      text: itemData.visitCoverRate?.toString() ?? "--",
                      style: TextStyle(
                          color: const Color(0xff00b377),
                          fontWeight: FontWeight.normal,
                          fontSize: 19),
                      children: [
                        TextSpan(
                            text: "%",
                            style: TextStyle(
                                fontSize: 11,
                                color: const Color(0xff9494a6),
                                fontWeight: FontWeight.normal))
                      ]),
                  textAlign: TextAlign.end,
                )
              ],
            ),
          ),
        ),
        SizedBox(
          width: 8,
        ),
        Expanded(
          child: Container(
            height: 61,
            decoration: BoxDecoration(
                color: const Color(0xfffafbfc),
                borderRadius: BorderRadius.circular(4)),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "拜访转化率",
                  style: TextStyle(
                      color: const Color(0xff666666),
                      fontWeight: FontWeight.normal,
                      fontSize: 13),
                ),
                Expanded(
                  child: SizedBox(
                    width: 2,
                  ),
                ),
                RichText(
                  text: TextSpan(
                      text: itemData.conversionRate?.toString() ?? "--",
                      style: TextStyle(
                          color: const Color(0xff00b377),
                          fontWeight: FontWeight.normal,
                          fontSize: 19),
                      children: [
                        TextSpan(
                            text: "%",
                            style: TextStyle(
                                fontSize: 11,
                                color: const Color(0xff9494a6),
                                fontWeight: FontWeight.normal))
                      ]),
                  textAlign: TextAlign.end,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool isBD() {
    return itemData.haveChild != 1;
  }

  BorderRadius getBorderRadius() {
    if (isFirst == true) {
      return BorderRadius.only(
          topLeft: Radius.circular(4), topRight: Radius.circular(4));
    }
    if (isLast == true) {
      return BorderRadius.only(
          bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4));
    }
    return BorderRadius.zero;
  }
}
