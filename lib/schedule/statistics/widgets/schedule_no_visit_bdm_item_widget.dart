import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_no_visit_customer_bdm_data.dart';
import 'package:flutter/material.dart';

class ScheduleNoVisitBDMItemWidget extends StatelessWidget {
  final ScheduleNoVisitCustomerBDMData itemData;
  final bool isFirst;
  final bool isLast;

  ScheduleNoVisitBDMItemWidget(this.itemData, this.isFirst, this.isLast);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      decoration:
          BoxDecoration(color: Colors.white, borderRadius: getBorderRadius()),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            isPerson()
                ? "assets/images/schedule/schedule_statistics_item_user.png"
                : "assets/images/schedule/schedule_statistics_item_group.png",
            width: 22,
            height: 22,
          ),
          SizedBox(
            width: 11,
          ),
          Text(
            isPerson() ? itemData.crmUserName : itemData.crmUserDepartment,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: const Color(0xff292933),
                fontSize: 16,
                fontWeight: FontWeight.w600),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 5),
              alignment: Alignment.centerRight,
              child: Visibility(
                visible: isPerson(),
                child: Text(
                    "${itemData.isBDM() ? "--" : (itemData.noVisits ?? "--")}家",
                    style: TextStyle(
                        fontSize: 13,
                        color: const Color(0xff292933),
                        fontWeight: FontWeight.normal)),
              ),
            ),
          ),
          Image.asset(
            "assets/images/schedule/schedule_statistics_item_arrow.png",
            width: 12.5,
            height: 12.5,
          ),
        ],
      ),
    );
  }

  bool isPerson() {
    return itemData.haveChild != 1;
  }

  BorderRadius getBorderRadius() {
    if (isFirst == true) {
      return BorderRadius.only(
          topLeft: Radius.circular(4), topRight: Radius.circular(4));
    }
    if (isLast == true) {
      return BorderRadius.only(
          bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4));
    }
    return BorderRadius.zero;
  }
}
