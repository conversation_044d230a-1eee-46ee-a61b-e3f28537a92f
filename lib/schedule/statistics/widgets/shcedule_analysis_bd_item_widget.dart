import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_analysis_bd_data.dart';
import 'package:flutter/material.dart';

class ScheduleAnalysisBDItemWidget extends StatelessWidget {
  final levelWidgetConfig = {
    1: LevelWidgetConfig(code: 1, text: "S", color: const Color(0xff00b377)),
    5: LevelWidgetConfig(code: 5, text: "A", color: const Color(0xffFFAA60)),
    9: LevelWidgetConfig(code: 9, text: "B", color: const Color(0xff0579FF)),
    13: LevelWidgetConfig(code: 13, text: "C", color: const Color(0xffE869FF)),
    14: LevelWidgetConfig(code: 14, text: "D", color: const Color(0xffF94F4F)),
    -1: LevelWidgetConfig(
        code: -1,
        text: "未注册",
        suffix: "",
        color: const Color(0xff292933),
        fontWeight: FontWeight.w600,
        fontSize: 11),
  };

  final ScheduleAnalysisBDVisitData itemData;
  final bool? isCoverRate;

  ScheduleAnalysisBDItemWidget(this.itemData, this.isCoverRate);

  @override
  Widget build(BuildContext context) {
    return buildContentItemWidget();
  }

  Widget buildContentItemWidget() {
    var config = levelWidgetConfig[itemData.level] ?? LevelWidgetConfig();
    return Container(
      padding: EdgeInsets.only(top: 8),
      child: Row(
        children: [
          Container(
            width: 60,
            alignment: Alignment.centerLeft,
            child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: itemData.levelDesc ?? "--",
                    style: TextStyle(
                        color: config.color,
                        fontWeight: config.fontWeight,
                        fontSize: config.fontSize),
                    children: [
                      TextSpan(
                          text: config.suffix, style: config.suffixTextStyle)
                    ])),
          ),
          Expanded(
            child: Container(
                height: 10,
                child: buildCornerProgressIndicator(
                    double.tryParse(isCoverRate == true
                            ? itemData.visitCoverRate
                            : itemData.conversionRate) ??
                        0,
                    100)),
          ),
          Container(
            width: 58,
            alignment: Alignment.centerRight,
            child: Text(
              "${(isCoverRate == true
                  ? itemData.visitCoverRate
                  : itemData.conversionRate) ?? "--"}%",
              style: TextStyle(
                  color: const Color(0xff292933),
                  fontWeight: FontWeight.normal,
                  fontSize: 14),
            ),
          )
        ],
      ),
    );
  }

  Widget buildCornerProgressIndicator(double value, double total) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: const Color(0xffE7E7E7),
          ),
        ),
        FractionallySizedBox(
          heightFactor: 1.1,
          widthFactor: value / total,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: const Color(0xff00b377),
                boxShadow: [
                  BoxShadow(color: const Color(0x6b00b377), blurRadius: 2)
                ],
                border: Border.all(width: 0.6, color: const Color(0xff009362))),
          ),
        )
      ],
    );
  }
}

class LevelWidgetConfig {
  final int code;
  final String text;
  final String suffix;
  final Color color;
  final FontWeight fontWeight;
  final double fontSize;
  final TextStyle suffixTextStyle;

  LevelWidgetConfig(
      {this.code = 9999999,
      this.text = "其他",
      this.suffix = " 级",
      this.color = const Color(0xff00B377),
      this.fontWeight = FontWeight.w600,
      this.fontSize = 18,
      this.suffixTextStyle = const TextStyle(
          color: const Color(0xff292933),
          fontWeight: FontWeight.normal,
          fontSize: 12)});
}
