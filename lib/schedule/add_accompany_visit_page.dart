import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/add_visit_base.dart';
import 'package:flutter/material.dart';

class AddAccompanyVisitPage extends AddVisitBasePage {
  AddAccompanyVisitPage({
    String? externalJson,
    String? rolesJSON,
    dynamic isHeyeVisit,
  }) : super(
          pageType: 3,
          externalJson: externalJson,
          rolesJSON: rolesJSON,
          isHeyeVisit: isHeyeVisit,
        );

  @override
  BaseState<StatefulWidget> initState() {
    return AddAccompanyVisitPageState();
  }
}

class AddAccompanyVisitPageState extends AddVisitBasePageState {
  /// 选择陪访人员
  @override
  void selectAccompanyAction(ValueNotifier<String?> controller) {
    //6.6.6 版本之前
    // String routerPath = "xyy://crm-app.ybm100.com/alloc_people?type=1&isAccompanySelect=1";
    // XYYContainer.open(routerPath, callback: (result) {
    //   if (result != null) {
    //     this.postModel.merchantVisit.accompanyOaId = result["selectdId"];
    //     this.showModel.selectAccompanyName = result["selectedName"];
    //     this.reloadPage();
    //   }
    // });

    String routerPath =
        'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=true&checkIsM=true&canPickGroup=false&canShowAll=false';
    XYYContainer.open(routerPath, callback: (params) {
      if (params != null && mounted) {
        setState(() {
          this.postModel.merchantVisit.accompanyOaId = params["id"].toString();
          this.showModel.selectAccompanyName = params["name"];
        });
      }
    });
  }

  @override
  String? showText(String itemKey) {
    if (itemKey == "accompany") {
      return this.showModel.selectAccompanyName;
    }
    return super.showText(itemKey);
  }

  @override
  bool checkNeedData() {
    if (this.postModel.merchantVisit.accompanyOaId == null) {
      showToast("请选择陪访人");
      return false;
    }

    if (this.postModel.merchantVisit.visitType != null) {
      if (this.postModel.merchantVisit.visitType == "1") {
        if (this.postModel.merchantVisit.image.length == 0) {
          showToast("请拍照上传");
          return false;
        }
      }
    }
    return super.checkNeedData();
  }

  @override
  void showSuccessToast() {
    showToast("新建陪访成功");
  }
}
