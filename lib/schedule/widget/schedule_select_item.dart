import 'package:flutter/material.dart';

/// 选择回调
typedef ScheduleSelectAction = void Function(
  String itemKey,
  ValueNotifier<String?> controller,
);

// ignore: must_be_immutable
class ScheduleSelectItem extends StatefulWidget {
  String title;
  String placeHolder;
  String itemKey;
  String? content;
  int? placeHolderColor;
  ScheduleSelectAction? selectAction;
  final bool? showBorder;

  ScheduleSelectItem({
    required this.title,
    required this.placeHolder,
    this.placeHolderColor,
    required this.itemKey,
    this.content,
    this.selectAction,
    this.showBorder,
  });

  @override
  State<StatefulWidget> createState() {
    return ScheduleSelectItemState();
  }
}

class ScheduleSelectItemState extends State<ScheduleSelectItem> {
  ValueNotifier<String?>? contentNotifier;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    contentNotifier?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    contentNotifier = ValueNotifier(widget.content ?? "");
    return GestureDetector(
      onTap: () {
        widget.selectAction!(widget.itemKey, this.contentNotifier!);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        color: Color(0xFFFFFFFF),
        padding: EdgeInsets.only(left: 10),
        child: Column(
          children: [
            Container(
              height: 50,
              padding: EdgeInsets.only(right: 10),
              child: Row(
                children: [
                  Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF333333),
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: ValueListenableBuilder(
                            valueListenable: contentNotifier!,
                            builder: (context, value, child) {
                              if (contentNotifier?.value?.isNotEmpty ?? false) {
                                widget.content = contentNotifier?.value;
                              }
                              return Text(
                                _getShowContent(),
                                style: TextStyle(
                                  fontSize: 15,
                                  color: _getContentTextColor(),
                                ),
                                textAlign: TextAlign.end,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            },
                          ),
                        ),
                        Image.asset(
                          'assets/images/schedule/schedule_item_arrow.png',
                          width: 16,
                          height: 16,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            (widget.showBorder ?? true) ? Container(
            color: Color(0xFFE1E1E5),
            height: 0.5,
          ) : SizedBox(),
          ],
        ),
      ),
    );
  }

  bool _isEmptyContent() {
    return widget.content?.isEmpty ?? true;
  }

  Color _getContentTextColor() {
    return _isEmptyContent() ? _getDefaultTextColor() : Color(0xFF333333);
  }

  Color _getDefaultTextColor() {
    return widget.placeHolderColor != null
        ? Color(widget.placeHolderColor!)
        : Color(0xFF8E8E93);
  }

  String _getShowContent() {
    return _isEmptyContent()
        ? widget.placeHolder
        : (widget.content ?? widget.placeHolder);
  }
}
