import 'package:XyyBeanSproutsFlutter/common/custom_keyboard/keyboard_page/voice_keyboard.dart';
import 'package:XyyBeanSproutsFlutter/common/custom_keyboard/keyboard_page/voice_toolbar.dart';
import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:XyyBeanSproutsFlutter/schedule/widget/schedule_input_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:keyboard_actions/keyboard_actions.dart';

typedef ScheduleTextItemShowKeyboard = void Function(
    bool isShow, bool isCustomKeyboard);

// ignore: must_be_immutable
class ScheduleTextItem extends StatefulWidget {
  final String title;
  final String placehold;
  final String itemKey;
  String? content;
  final int? maxLenght;
  final ScheduleValueChange? valueChange;
  final ScheduleTextItemShowKeyboard? keyboardShow;
  final bool? showTitleColor;
  

  ScheduleTextItem({
    required this.title,
    required this.placehold,
    required this.itemKey,
    this.maxLenght,
    this.content,
    this.valueChange,
    this.keyboardShow,
    this.showTitleColor,
    Key? key
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScheduleTextItemState();
  }
}

class ScheduleTextItemState extends State<ScheduleTextItem> {
  FocusNode _focusNode = FocusNode();

  bool isCustomKeyboard = false;

  ValueNotifier<String> _voiceController = ValueNotifier("");

  late TextEditingController _editController;

  @override
  void initState() {
    _editController = TextEditingController.fromValue(
      TextEditingValue(
        text: widget.content ?? "",
        selection: TextSelection.fromPosition(
          ///用来设置文本的位置
          TextPosition(
            affinity: TextAffinity.downstream,
            // 光标向后移动的长度
            offset: widget.content?.length ?? 0,
          ),
        ),
      ),
    );

    /// 监听语音键盘的输入
    _voiceController.addListener(() {
      addText(_voiceController.value);
    });

    _focusNode.addListener(() {
      /// 回调 键盘类型
      if (widget.keyboardShow != null) {
        widget.keyboardShow!(_focusNode.hasFocus, isCustomKeyboard);
      }
    });
    super.initState();
  }

  addText(String insertText) {
    TextSelection selection = _editController.selection;
    String text = _editController.text;
    String newText =
        selection.textBefore(text) + insertText + selection.textAfter(text);
    _editController.value = TextEditingValue(
        text: newText,
        selection: selection.copyWith(
            baseOffset: selection.baseOffset + insertText.length,
            extentOffset: selection.baseOffset + insertText.length));
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _editController.dispose();
    _voiceController.dispose();
    super.dispose();
  }

  KeyboardActionsConfig _buildKeyboardConfig() {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.ALL,
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          focusNode: _focusNode,
          toolbarAlignment: MainAxisAlignment.start,
          toolbarButtons: [
            (node) {
              return VoiceToolBar(
                selectValue: isCustomKeyboard ? 1 : 0,
                toolBarActions: (value) {
                  isCustomKeyboard = value == 1;
                  setState(() {});
                  _focusNode.unfocus();
                  Future.delayed(Duration(milliseconds: 100), () {
                    _focusNode.requestFocus();

                    /// 回调 键盘类型
                    if (widget.keyboardShow != null) {
                      widget.keyboardShow!(true, isCustomKeyboard);
                    }
                  });
                },
              );
            },
          ],
          footerBuilder: this.isCustomKeyboard
              ? (context) {
                  return VoiceKeyboard(controller: _voiceController);
                }
              : null,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardActions(
      config: _buildKeyboardConfig(),
      disableScroll: true,
      keepFocusOnTappingNode: true,
      child: Container(
        color: Color(0xFFFFFFFF),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.only(left: 10, right: 10),
              height: 30,
              alignment: Alignment.centerLeft,
              color: (widget.showTitleColor ?? true) ? Color(0xFFEFEFF4) : null,
              child: Text(
                "拜访总结",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF333333),
                ),
              ),
            ),
            Container(
              height: 88,
              padding: EdgeInsets.only(left: 10, right: 10, bottom: 10, top: 5),
              child: EnsureVisibleWhenFocused(
                focusNode: _focusNode,
                child: TextFormField(
                  focusNode: _focusNode,
                  showCursor: true,
                  readOnly: this.isCustomKeyboard,
                  inputFormatters: [
                    CustomizedTextInputFormatter(
                        filterPattern: RegExp(
                            r'[\u4e00-\u9fa5，,。.：:（）()?？!！a-zA-Z0-9⼀⼁⼃⼂乛]*')),
                  ],
                  controller: _editController,
                  onChanged: (text) {
                    widget.content = text;
                    widget.valueChange!(widget.itemKey, text);
                  },
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF292933),
                  ),
                  scrollPadding: EdgeInsets.zero,
                  decoration: InputDecoration(
                    counterStyle: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9494A6),
                    ),
                    hintText: widget.placehold,
                    hintStyle: TextStyle(
                      fontSize: 15,
                      color: Color(0xFF9494A6),
                    ),
                    hintMaxLines: 3,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLength: widget.maxLenght, // 最大长度
                  maxLengthEnforcement: MaxLengthEnforcement
                      .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                  maxLines: 8,
                  keyboardAppearance: Brightness.light,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
