import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_qualification_status_data.dart';

class QualificationStatusEditSheet extends StatefulWidget {
  final Map<String, dynamic>? initialData;
  final List<String>? licenseTypes;
  final Map<String, List<StatusOption>>? licenseTypeOptions;

  const QualificationStatusEditSheet({Key? key, this.initialData, this.licenseTypes, this.licenseTypeOptions}) : super(key: key);

  @override
  State<QualificationStatusEditSheet> createState() => _QualificationStatusEditSheetState();
}

class _QualificationStatusEditSheetState extends State<QualificationStatusEditSheet> {
  late List<String> licenseTypes;
  late Map<String, List<StatusOption>> licenseTypeOptions;
  late Map<String, String> licenseStatusMap;

  @override
  void initState() {
    super.initState();
    licenseTypes = widget.licenseTypes ?? [];
    licenseTypeOptions = widget.licenseTypeOptions ?? {};
    licenseStatusMap = {};
    for (var type in licenseTypes) {
      final options = licenseTypeOptions[type] ?? [];
      // initialData 里存 code，否则默认第一个 code
      licenseStatusMap[type] = widget.initialData?[type]?.toString() ?? (options.isNotEmpty ? options[0].code.toString() : '');
    }
    print('[DEBUG] licenseTypes: ' + licenseTypes.toString());
    print('[DEBUG] licenseTypeOptions: ' + licenseTypeOptions.toString());
    print('[DEBUG] licenseStatusMap: ' + licenseStatusMap.toString());
  }

  Widget buildLicenseRadioGroup(String label, String key) {
    final options = licenseTypeOptions[key] ?? [];
    List<Widget> children = [];
    if (label == '营业执照') {
      children.add(Container(
        width: double.infinity,
        color: Color(0xFFF5F5F7),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Text('根据客户现有证件必选：', style: TextStyle(fontSize: 15, color: Color(0xFF292933))),
      ));
    }
    children.addAll([
      Text('$label:', style: TextStyle(fontSize: 15, color: Color(0xFF292933), fontWeight: FontWeight.w500)),
      SizedBox(height: 8),
      Wrap(
        spacing: 24,
        runSpacing: 8,
        children: List.generate(options.length, (index) {
          final codeStr = options[index].code.toString();
          final desc = options[index].desc;
          final isSelected = licenseStatusMap[key] == codeStr;
          return GestureDetector(
            onTap: () => setState(() => licenseStatusMap[key] = codeStr),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                  color: isSelected ? Color(0xFF00B377) : Color(0xFFCCCCCC),
                  size: 20,
                ),
                SizedBox(width: 4),
                Text(desc, style: TextStyle(fontSize: 15, color: Color(0xFF292933))),
              ],
            ),
          );
        }),
      ),
    ]);

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width, // 100%宽度
      height: MediaQuery.of(context).size.height * 0.8, // 高度占屏幕80%
      child: SafeArea(
        child: Container(
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部栏
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Text("取消", style: TextStyle(color: Colors.green, fontSize: 16)),
                    ),
                    Text("客户资质情况", style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold)),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(licenseStatusMap), // 直接返回 code map
                      child: Text("确定", style: TextStyle(color: Colors.green, fontSize: 16)),
                    ),
                  ],
                ),
              ),
              Divider(height: 1),
              // 滚动内容
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...licenseTypes.map((type) => buildLicenseRadioGroup(type, type)).toList(),
                      SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 