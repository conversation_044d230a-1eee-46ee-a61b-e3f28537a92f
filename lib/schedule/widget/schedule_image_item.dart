import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_post_data.dart';
import 'package:flutter/material.dart';

typedef ScheduleImageChange = void Function(
    List<String>? imageList, String content);

class ScheduleImageItem extends StatefulWidget {
  /// 是否是完善拜访
  final bool isPerfectVisit;

  /// 是否获取了通话时长
  final bool hasTalkTime;

  /// 获取的当前定位地址
  final ScheduleMerchantVisitModel? address;

  final List<String>? currentImages;

  /// 图片count
  final int limitCount;

  /// 图片变化回调
  final ScheduleImageChange imageCahnge;

  final String itemKey;

  ScheduleImageItem({
    this.isPerfectVisit = false,
    this.hasTalkTime = true,
    this.limitCount = 10,
    this.address,
    this.currentImages,
    required this.imageCahnge,
    required this.itemKey,
    Key? key
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ScheduleImageItemState();
  }
}

class ScheduleImageItemState extends State<ScheduleImageItem> {
  late ScheduleImageWidget addItem;
  List<ScheduleImageWidget> imageList = [];

  @override
  void initState() {
    this.addItem = ScheduleImageWidget(
      isAdd: true,
      tapAction: this.imageTapAction,
    );
    if (widget.currentImages != null) {
      List<ScheduleImageWidget> current = widget.currentImages!
          .map((e) => ScheduleImageWidget(
                url: e,
                tapAction: this.imageTapAction,
                deleteCallback: this.deleteImageAction,
              ))
          .toList();
      this.imageList.addAll(current);
    }
    if (this.imageList.length < widget.limitCount) {
      this.imageList.add(addItem);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.all(10),
      child: Wrap(
        runAlignment: WrapAlignment.start,
        crossAxisAlignment: WrapCrossAlignment.start,
        children: this.imageList,
        spacing: 10,
        runSpacing: 10,
      ),
    );
  }

  void imageTapAction(ScheduleImageWidget item) {
    if (item.isAdd) {
      this.addImageAction();
    } else {
      this.showBigImage(item);
    }
  }

  void requestImageHandler() {
    String isCamera =
        (widget.isPerfectVisit && !widget.hasTalkTime) ? "true" : "false";
    XYYContainer.bridgeCall('schedule_select_image', parameters: {
      "isCamera": isCamera,
      "address": widget.address?.address ?? "",
    }).then((value) {
      Map<dynamic, dynamic> result = value;
      if (result["success"] == 'true') {
        setState(() {
          String imageUrl = result["imageUrl"] ?? "";
          print("guan imageUrl:$imageUrl");
          ScheduleImageWidget item = ScheduleImageWidget(
            url: imageUrl,
            tapAction: this.imageTapAction,
            deleteCallback: this.deleteImageAction,
          );
          this.imageList.insert(this.imageList.length - 1, item);

          if (this.imageList.length >= widget.limitCount + 1 &&
              this.imageList.contains(this.addItem)) {
            this.imageList.remove(this.addItem);
          }
          this.imageChangeAction();
        });
      } else {
        XYYContainer.toastChannel.toast("图片上传失败");
      }
    }).catchError((_) {
      XYYContainer.toastChannel.toast("图片上传失败!");
    });
  }

  void addImageAction() {
    if (widget.address?.address == null ||
        widget.address?.address?.isNotEmpty != true) {
      if (!Platform.isAndroid) {
        showDialog<Null>(
            context: context, //BuildContext对象
            barrierDismissible: false,
            builder: (BuildContext context) {
              return MessageDialog(
                title: "提示",
                negativeText: "知道了",
                message: "请开通定位权限，以便生成的照片能带上地址水印",
                onPositivePressEvent: () {
                  Navigator.pop(context);
                  requestImageHandler();
                },
                onCloseEvent: () {
                  Navigator.pop(context);
                },
              ); //调用对话框
            });
      } else {
        showDialog<Null>(
            context: context, //BuildContext对象
            barrierDismissible: false,
            builder: (BuildContext context) {
              return MessageDialog(
                title: "提示",
                negativeText: "取消",
                positiveText: "继续拍照",
                message: "当前还未获取到定位地址，您拍的照片可能不会加上地址水印，请重试",
                onPositivePressEvent: () {
                  Navigator.pop(context);
                  requestImageHandler();
                },
                onCloseEvent: () {
                  Navigator.pop(context);
                },
              ); //调用对话框
            });
      }
    } else {
      requestImageHandler();
    }
  }

  void showBigImage(ScheduleImageWidget item) {
    Navigator.of(context).pushNamed('/photo_view_page',
        arguments: {"urlPath": item.url, "delete": false, "callback": () {}});
  }

  void deleteImageAction(ScheduleImageWidget item) {
    if (this.imageList.contains(item)) {
      this.imageList.remove(item);
      if (this.imageList.length < widget.limitCount &&
          !this.imageList.contains(this.addItem)) {
        this.imageList.add(this.addItem);
      }
      this.imageChangeAction();
      setState(() {});
    }
  }

  void imageChangeAction() {
    List<String> imageUrls = this.imageList.map((e) => e.url ?? "").toList();
    imageUrls.removeWhere((element) => element.length == 0);
    widget.imageCahnge(imageUrls, widget.itemKey);
  }
}

class ScheduleImageWidget extends StatelessWidget {
  final bool isAdd;
  final String? url;
  final ValueChanged<ScheduleImageWidget>? deleteCallback;
  final ValueChanged<ScheduleImageWidget> tapAction;

  ScheduleImageWidget({
    this.isAdd = false,
    this.url,
    this.deleteCallback,
    required this.tapAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.tapAction(this);
      },
      child: Container(
        child: Stack(
          alignment: Alignment.center,
          children: [
            this.imageWidget(context),
            Visibility(
              visible: !this.isAdd,
              child: Positioned(
                top: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () {
                    if (this.deleteCallback != null) {
                      this.deleteCallback!(this);
                    }
                  },
                  child: Container(
                    child: Image.asset(
                      "assets/images/schedule/schedule_image_delete.png",
                      width: 27,
                      height: 27,
                      fit: BoxFit.fill,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imageWidget(BuildContext context) {
    double imageWidth = this.imageWidth(context);
    if (this.isAdd) {
      return Image.asset(
        "assets/images/schedule/schedule_image_add.png",
        width: imageWidth,
        height: imageWidth,
        fit: BoxFit.cover,
      );
    } else if (this.url != null && this.url!.isNotEmpty) {
      return ImageWidget(
        url: this.url!,
        w: imageWidth,
        h: imageWidth,
        defImagePath: "assets/images/base/icon_default_image.png",
        fit: BoxFit.cover,
      );
    } else {
      return Container();
    }
  }

  double imageWidth(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double totalWidth = screenWidth;
    double totalGap = 6 * 10;
    double width = (totalWidth - totalGap) / 5;
    return width;
  }
}
