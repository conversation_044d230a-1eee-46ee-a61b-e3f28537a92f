import 'package:flutter/material.dart';

typedef ScheduleSwitchChange = void Function(String itemKey, bool isOn);

class ScheduleSwitchItem extends StatefulWidget {
  final String title;
  final String itemKey;
  final bool isOn;
  final ScheduleSwitchChange valueChanged;

  ScheduleSwitchItem({
    required this.title,
    required this.itemKey,
    required this.isOn,
    required this.valueChanged,
  });

  @override
  State<StatefulWidget> createState() {
    return ScheduleSwitchItemState();
  }
}

class ScheduleSwitchItemState extends State<ScheduleSwitchItem> {
  bool isOn = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              Switch.adaptive(
                value: widget.isOn,
                activeColor: Color(0xFF35C561),
                activeTrackColor: Color(0xFFF5F5F5),
                onChanged: (value) {
                  this.isOn = value;
                  widget.valueChanged(widget.itemKey, value);
                },
              ),
            ],
          ),
          Container(
            color: Color(0xFFE1E1E5),
            height: 0.5,
          ),
        ],
      ),
    );
  }
}
