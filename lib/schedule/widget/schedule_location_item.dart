import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/location/location_data.dart';
import 'package:flutter/material.dart';

class ScheduleLocationItem extends StatefulWidget {
  final ValueChanged<LocationData> locationChange;

  ScheduleLocationItem({required this.locationChange});

  @override
  State<StatefulWidget> createState() {
    return ScheduleLocationItemState();
  }
}

class ScheduleLocationItemState extends State<ScheduleLocationItem> {
  String? address;
  LocationData? locationModel;

  @override
  void initState() {
    this.getLocation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: this.tapLocation,
              child: Container(
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/schedule/schedule_location_icon.png',
                      width: 12.5,
                      height: 14.5,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: 5),
                      child: Text(
                        this.address ?? '定位中...',
                        style:
                            TextStyle(color: Color(0xFF35C561), fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          TextButton(
            onPressed: this.getLocation,
            child: Text(
              '重新定位',
              style: TextStyle(
                color: Color(0xFF35C561),
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  void getLocation() {
    XYYContainer.locationChannel.locate().then((value) {
      if (this.mounted && (value.isSuccess ?? false)) {
        widget.locationChange(value);
        this.locationModel = value;
        this.address = value.address;
        setState(() {});
      }
    });
  }

  void tapLocation() {
    if (this.locationModel != null && this.locationModel?.isSuccess == true) {
      String routerPath =
          "xyy://crm-app.ybm100.com/customer/customer_map?address=${this.address}&poiLatitude=${this.locationModel?.latitude}&poiLongitude=${this.locationModel?.longitude}";
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    }
  }
}
