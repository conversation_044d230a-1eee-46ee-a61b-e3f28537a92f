import 'package:flutter/material.dart';

class ScheduleShowItem extends StatelessWidget {
  final String title;
  final String? placeholder;
  final String? content;

  ScheduleShowItem({
    required this.title,
    this.placeholder,
    this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.only(left: 10),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(right: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  this.title,
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF333333),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 50,
                    padding: EdgeInsets.only(right: 15),
                    alignment: Alignment.centerRight,
                    child: Text(
                      _getShowContent(),
                      style: TextStyle(
                        fontSize: 15,
                        color: _getContentTextColor(),
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            color: Color(0xFFE1E1E5),
            height: 0.5,
          ),
        ],
      ),
    );
  }

  bool _isEmptyContent() {
    return this.content?.isEmpty ?? true;
  }

  Color _getContentTextColor() {
    return _isEmptyContent() ? Color(0xFF8E8E93) : Color(0xFF333333);
  }

  String _getShowContent() {
    return _isEmptyContent()
        ? (this.placeholder ?? "")
        : (this.content ?? (this.placeholder ?? ""));
  }
}
