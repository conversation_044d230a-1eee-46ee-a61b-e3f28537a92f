import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

typedef ScheduleValueChange = void Function(String? title, String content);

// ignore: must_be_immutable
class ScheduleInputItem extends StatefulWidget {
  final String title;
  final String placeHolder;
  final String itemKey;
  final bool isCanEdit;
  final bool? showBorder;

  ScheduleValueChange valueChange;

  String? content;

  ScheduleInputItem({
    required this.title,
    required this.placeHolder,
    required this.itemKey,
    required this.valueChange,
    required this.isCanEdit,
    this.content,
    this.showBorder,
  });

  @override
  State<StatefulWidget> createState() {
    return ScheduleInputItemState();
  }
}

class ScheduleInputItemState extends State<ScheduleInputItem> {
  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    this._focusNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.only(left: 10),
      child: Column(
        children: [
          Container(
            color: Color(0xFFFFFFFF),
            padding: EdgeInsets.only(right: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(right: 5),
                  child: Text(
                    widget.title,
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF333333),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 50,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(right: 15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: Color(0xFFFFFFFF),
                    ),
                    child: Container(
                      child: EnsureVisibleWhenFocused(
                        focusNode: _focusNode,
                        child: TextField(
                          focusNode: _focusNode,
                          enabled: widget.isCanEdit,
                          keyboardType: this.inputTypeForItemKey(),
                          inputFormatters: [
                            this.regepxForIteKey(),
                          ],
                          controller: TextEditingController.fromValue(
                            TextEditingValue(
                              text: widget.content ?? "",
                              selection: TextSelection.fromPosition(
                                ///用来设置文本的位置
                                TextPosition(
                                  affinity: TextAffinity.downstream,
                                  // 光标向后移动的长度
                                  offset: widget.content?.length ?? 0,
                                ),
                              ),
                            ),
                          ),
                          onChanged: (text) {
                            widget.content = text;
                            widget.valueChange(widget.itemKey, text);
                          },
                          style: TextStyle(
                            fontSize: 15,
                            color: Color(0xFF333333),
                          ),
                          textAlign: TextAlign.right,
                          scrollPadding: EdgeInsets.zero,
                          decoration: InputDecoration(
                            counterText: "",
                            counterStyle: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF9494A6),
                            ),
                            hintText: widget.placeHolder,
                            hintStyle: TextStyle(
                              fontSize: 15,
                              color: Color(0xFF9494A6),
                            ),
                            hintMaxLines: 1,
                            border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                            ),
                            contentPadding: EdgeInsets.zero,
                            isDense: true,
                          ),
                          maxLength: this.limitCountForItemKey(), // 最大长度
                          maxLengthEnforcement: MaxLengthEnforcement
                              .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                          keyboardAppearance: Brightness.light,
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          (widget.showBorder ?? true) ? Container(
            color: Color(0xFFE1E1E5),
            height: 0.5,
          ) : SizedBox(),
        ],
      ),
    );
  }

  TextInputType inputTypeForItemKey() {
    switch (widget.itemKey) {
      case "mobile":
        return TextInputType.phone;
      default:
        return TextInputType.text;
    }
  }

  int limitCountForItemKey() {
    switch (widget.itemKey) {
      case "mobile":
        return 11;
      case "contacts":
        return 20;
      case "name":
        return 20;
      default:
        return 50;
    }
  }

  TextInputFormatter regepxForIteKey() {
    switch (widget.itemKey) {
      case "mobile":
        return FilteringTextInputFormatter.allow(RegExp(r'[0-9]'));
      default:
        return CustomizedTextInputFormatter(
            filterPattern:
                RegExp(r'[\u4e00-\u9fa5，,。.：:（）()?？!！a-zA-Z0-9⼀⼁⼃⼂乛]*'));
    }
  }
}

/// 自定义兼容中文拼音输入法正则校验输入框
class CustomizedTextInputFormatter extends TextInputFormatter {
  final Pattern filterPattern;

  CustomizedTextInputFormatter({required this.filterPattern});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.isComposingRangeValid) return newValue;
    return FilteringTextInputFormatter.allow(filterPattern)
        .formatEditUpdate(oldValue, newValue);
  }
}
