import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_content_page.dart';
import 'package:flutter/material.dart';

class ObjectFilterPage extends BasePage {
  final Map<String, String> filterParams;
  final Map<String, dynamic> cacheParams;
  final bool isMapFilter;

  ObjectFilterPage({
    this.filterParams = const {},
    this.cacheParams = const {},
    this.isMapFilter = false,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return ObjectFilterPageState();
  }
}

class ObjectFilterPageState extends BaseState<ObjectFilterPage> {
  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return widget.isMapFilter
        ? null
        : CommonTitleBar(
            this.getTitleName(),
            leftType: LeftButtonType.none,
            leadingWidth: 0,
            rightButtons: [
              LeftButton(
                leftBtnType: LeftButtonType.close,
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )
            ],
          );
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  Widget buildWidget(BuildContext context) {
    return ObjectFilterContentPage(
      filterParams: widget.filterParams,
      cacheParams: widget.cacheParams,
      isMapFilter: widget.isMapFilter,
    );
  }
}
