class SelectObjectFilterConfig {
  static Map<String, dynamic> filterConfig() {
    return {
      "aroundEnvArray": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "近校园", "code": "1"},
        {"selected": "0", "text": "近社区", "code": "2"},
        {"selected": "0", "text": "近商圈", "code": "4"},
        {"selected": "0", "text": "近医院", "code": "3"},
        {"selected": "0", "text": "其他", "code": "0"}
      ],
      "needMerchantDiagnose": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "是", "code": "1"},
        {"selected": "0", "text": "否", "code": "0"}
      ],
      "buySkusArray": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "<=100", "code": "0"},
        {"selected": "0", "text": "101-200", "code": "1"},
        {"selected": "0", "text": "201-300", "code": "2"},
        {"selected": "0", "text": "301-500", "code": "3"},
        {"selected": "0", "text": "501-800", "code": "4"},
        {"selected": "0", "text": "801-1000", "code": "5"},
        {"selected": "0", "text": ">1000", "code": "6"}
      ],
      "licenseStatuses": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "已提交", "code": "2"},
        {"selected": "0", "text": "已通过", "code": "4"},
        {"selected": "0", "text": "首营纸质资质未回收", "code": "3"},
        {"selected": "0", "text": "首营资质审核中", "code": "5"},
        {"selected": "0", "text": "首营一审通过", "code": "6"}
      ],
      "needClerkTrains": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "是", "code": "1"},
        {"selected": "0", "text": "否", "code": "0"}
      ],
      "medicalInsurance": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "是", "code": "1"},
        {"selected": "0", "text": "否", "code": "0"}
      ],
      "needPullSales": [
        {"selected": "1", "text": "全部", "code": "-1"},
        {"selected": "0", "text": "是", "code": "1"},
        {"selected": "0", "text": "否", "code": "0"}
      ]
    };
  }
}
