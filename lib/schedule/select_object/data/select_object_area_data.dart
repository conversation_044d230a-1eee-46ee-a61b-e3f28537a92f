import 'dart:convert';

class SelectObjectAreaCallBackModel {
  List<SelectObjectAreaModel>? areaParam;
  List<SelectObjectAreaModel>? parentParam;

  SelectObjectAreaCallBackModel();

  factory SelectObjectAreaCallBackModel.fromJsonMap(Map<String, dynamic> json) {
    return SelectObjectAreaCallBackModel()
      ..areaParam = ((json['areaParam'] as List<dynamic>?) ?? [])
          .map((e) => SelectObjectAreaModel.fromJsonMap(jsonDecode(e)))
          .toList()
      ..parentParam = ((json['parentParam'] as List<dynamic>?) ?? [])
          .map((e) => SelectObjectAreaModel.fromJsonMap(jsonDecode(e)))
          .toList();
  }
}

class SelectObjectAreaModel {
  dynamic areaName;
  dynamic areaCode;
  dynamic areaLevel;

  SelectObjectAreaModel();

  factory SelectObjectAreaModel.fromJsonMap(Map<String, dynamic> json) {
    return SelectObjectAreaModel()
      ..areaName = json['areaName']
      ..areaCode = json['areaCode']
      ..areaLevel = json['areaLevel'];
  }
}
