// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'select_object_page_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SelectObjectPageData _$SelectObjectPageDataFromJson(Map<String, dynamic> json) {
  return SelectObjectPageData()
    ..lastPage = json['lastPage'] as bool?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => ScheduleExternalModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$SelectObjectPageDataToJson(
        SelectObjectPageData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };
