import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'select_object_page_data.g.dart';

@JsonSerializable()
class SelectObjectPageData extends BaseModelV2 {
  bool? lastPage;

  List<ScheduleExternalModel>? rows;

  SelectObjectPageData();

  factory SelectObjectPageData.fromJson(Map<String, dynamic> json) =>
      _$SelectObjectPageDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return SelectObjectPageData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$SelectObjectPageDataToJson(this);
  }
}
