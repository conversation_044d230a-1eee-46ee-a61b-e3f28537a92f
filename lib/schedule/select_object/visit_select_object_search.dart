import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/check_license_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_page_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class VisitSelectObjectSearchPage extends BasePage {
  final dynamic roleCode;

  VisitSelectObjectSearchPage({this.roleCode});

  @override
  BaseState<StatefulWidget> initState() {
    return VisitSelectObjectSearchPageState();
  }
}

class VisitSelectObjectSearchPageState
    extends BaseState<VisitSelectObjectSearchPage> {
  String? keyWord;
  String? cacheWord;

  String? latitude;
  String? longitude;
  bool isRequestLocation = false;

  bool isManagerLevel = false;

  int page = 0;

  List<ScheduleExternalModel> dataSource = [];

  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    this._requestRoleType();
    this.refreshObjectData();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        child: EasyRefresh(
          controller: _controller,
          onRefresh: () async {
            this.refreshObjectData();
          },
          onLoad: () async {
            this.loadMoreObjectData();
          },
          child: ListView.builder(
            cacheExtent: 99999,
            itemCount: this.dataSource.length,
            itemBuilder: (context, index) {
              ScheduleExternalModel model = this.dataSource[index];
              return GestureDetector(
                onTap: () {
                  ScheduleExternalModel selectModel = this.dataSource[index];
                  CheckLicenseDialog()
                      .showCheckLicenseDialog(
                      context,
                      selectModel.customer?.licenseValidateMust,
                      selectModel.customer?.licenseValidateIssue,
                      selectModel.customer?.merchantStatus == 3,
                      continueVisitCallback: () {
                        this.selectFinished(index);
                      }
                  );
                },
                behavior: HitTestBehavior.opaque,
                child: SelectObjectItem(
                  model: model,
                  isSelected: false,
                  isManagerLevel: this.isManagerLevel,
                  isSearch: true,
                ),
              );
            },
          ),
          emptyWidget: this.getEmptyWidget(),
        ),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void selectFinished(int index) {
    ScheduleExternalModel selectModel = this.dataSource[index];
    if (selectModel.customer == null) {
      showToast("请先选择对象");
      return;
    }
    String? jsonStr = json.encode(selectModel);
    Map<String, dynamic>? jsonMap = json.decode(jsonStr);

    /// 如果当前选择得是荷叶健康的对象，则传递参数isHeyeVisit
    if ("${widget.roleCode}" == "3") {
      jsonMap?["isHeyeVisit"] = true;
    }
    Navigator.of(context)..pop()..pop(jsonMap);
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return VisitSelectObjectSearchBar(
      rightItem: this.getRightItem(),
      onChanged: this.changeAction,
      onEditingComplete: this.searchAction,
    );
  }

  Widget getRightItem() {
    return TextButton(
      child: Text(
        '取消',
        style: TextStyle(
          color: Color(0xFF35C561),
          fontSize: 15,
          fontWeight: FontWeight.w500,
        ),
      ),
      onPressed: () {
        FocusScope.of(context).requestFocus(FocusNode());
        Navigator.of(context).pop();
      },
      style: ButtonStyle(
        overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
        padding:
            MaterialStateProperty.all<EdgeInsets>(EdgeInsets.only(right: 10)),
        minimumSize: MaterialStateProperty.all<Size>(Size.zero),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  /// 搜索事件
  void changeAction(String keyword) {
    this.cacheWord = keyword;
  }

  void searchAction() {
    FocusScope.of(context).requestFocus(FocusNode());
    this.keyWord = this.cacheWord;
    this.refreshObjectData();
  }

  /// Request
  void refreshObjectData() {
    this.showLoadingDialog();
    if (this.isRequestLocation) {
      this.page = 0;
      this.requestObjectData();
    } else {
      this.requestLocation();
    }
  }

  void loadMoreObjectData() {
    this.requestObjectData();
  }

  void requestObjectData() async {
    Map<String, dynamic> params = {
      "keyword": "",
      "limit": "20",
      "offset": "${this.page}",
    };
    if (this.latitude != null && this.longitude != null) {
      params["latitude"] = this.latitude;
      params["longitude"] = this.longitude;
    }
    if (this.keyWord != null && this.keyWord!.length > 0) {
      params["keyword"] = this.keyWord;
    }

    var path = '/task/v290/searchCustomersInfos';
    if ('${widget.roleCode}' == '3') {
      path = '/hyjk/getPrivateCustomerInfo';
    }

    var result = await NetworkV2<SelectObjectPageData>(SelectObjectPageData())
        .requestDataV2(
      path,
      parameters: params,
    );
    this.dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        List<ScheduleExternalModel> source = result.getData()?.rows ?? [];
        if (this.page == 0) {
          this.dataSource = source;
          this._controller.finishRefresh();
        } else {
          this.dataSource.addAll(source);
          bool lastPage = result.getData()?.lastPage == true;
          this._controller.finishLoad(noMore: lastPage);
        }
        this.page += 1;
        setState(() {});
      }
      this._controller.finishRefresh();
    }
  }

  /// 请求定位信息
  void requestLocation() {
    XYYContainer.locationChannel.locate().then((value) {
      if (this.mounted && (value.isSuccess ?? false)) {
        this.latitude = value.latitude;
        this.longitude = value.longitude;
      }
      this.isRequestLocation = true;
      this.refreshObjectData();
    });
  }

  void _requestRoleType() {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      setState(() {
        this.isManagerLevel = value;
      });
    });
  }
}

class VisitSelectObjectSearchBar extends AppBar {
  VisitSelectObjectSearchBar({
    required Widget rightItem,
    required ValueChanged<String> onChanged,
    required VoidCallback onEditingComplete,
  }) : super(
          leading: LeftButton(
            leftBtnType: LeftButtonType.back,
          ),
          actions: [
            rightItem,
          ],
          title: Container(
            height: 35,
            alignment: Alignment.center,
            margin: EdgeInsets.only(right: 15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Color(0xFFFFFFFF),
            ),
            child: Container(
              height: 35,
              padding: EdgeInsets.only(right: 5),
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: Color(0xFFEFEFF4),
                borderRadius: BorderRadius.circular(18),
              ),
              child: TextField(
                onChanged: onChanged,
                onEditingComplete: onEditingComplete,
                textInputAction: TextInputAction.search,
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFF333333),
                ),
                textAlign: TextAlign.left,
                scrollPadding: EdgeInsets.zero,
                decoration: InputDecoration(
                  prefixIcon: Container(
                    margin: EdgeInsets.only(left: 5),
                    child: Image.asset(
                      'assets/images/schedule/select_object_icon.png',
                      width: 25,
                      height: 20,
                    ),
                  ),
                  prefixIconConstraints: BoxConstraints(
                    maxWidth: 25,
                    maxHeight: 20,
                  ),
                  counterText: "",
                  counterStyle: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF9494A6),
                  ),
                  hintText: "搜索对象",
                  hintStyle: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF9494A6),
                  ),
                  hintMaxLines: 1,
                  border: OutlineInputBorder(
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                keyboardAppearance: Brightness.light,
              ),
            ),
          ),
          titleSpacing: 5,
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          toolbarHeight: 44,
          toolbarTextStyle: TextTheme(
            headline6: TextStyle(
              color: Colors.black,
            ),
          ).bodyText2,
          titleTextStyle: TextTheme(
            headline6: TextStyle(
              color: Colors.black,
            ),
          ).headline6,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        );
}
