import 'package:XyyBeanSproutsFlutter/common/filter/common_select_item.dart';
import 'package:flutter/material.dart';

class SelectObjectSelectItem extends CommonSelectItem {
  SelectObjectSelectItem({
    required String title,
    required String itemKey,
    required ValueChanged<String> selectAction,
    String? content = '全部',
  }) : super(
          titleWidget: Text(
            title,
            style: TextStyle(
              fontSize: 15,
              color: Color(0xFF333333),
              fontWeight: FontWeight.w500,
            ),
          ),
          contentWidget: Text(
            content ?? '全部',
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF666666),
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          clickAction: () {
            selectAction(itemKey);
          },
        );
}
