import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:flutter/material.dart';

typedef SelectObjectFilterValueChange = void Function(
    String itemKey, List<String> selectIds);

// ignore: must_be_immutable
class SelectObjectFilterItem extends CommonFilterItem {
  SelectObjectFilterItem.multuple({
    required String title,
    required List<CommonFilterItemEntry> contents,
    List<String> selectedIds = const [],
    String? defaultId,
    bool isAllowMultipleSelection = true,
    bool isMutexByDefault = true,
    bool isAllowClean = false,
    required SelectObjectFilterValueChange valueChange,
    required String itemKey,
  }) : super(
          titleWidget: Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '(多选)',
                style: TextStyle(
                  color: Color(0xFF8E8E93),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              )
            ],
          ),
          contents: contents,
          selectedIds: selectedIds,
          defaultId: defaultId,
          isAllowMultipleSelection: isAllowMultipleSelection,
          isMutexByDefault: isMutexByDefault,
          isAllowClean: isAllowClean,
          changeSelected: (selectedIds) {
            valueChange(itemKey, selectedIds);
          },
        );

  SelectObjectFilterItem.multupleTips({
    required String title,
    required List<CommonFilterItemEntry> contents,
    List<String> selectedIds = const [],
    String? defaultId,
    bool isAllowMultipleSelection = true,
    bool isMutexByDefault = true,
    bool isAllowClean = false,
    required SelectObjectFilterValueChange valueChange,
    required String itemKey,
    required Widget tips,
  }) : super(
          titleWidget: Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '(多选)',
                style: TextStyle(
                  color: Color(0xFF8E8E93),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              tips,
            ],
          ),
          contents: contents,
          selectedIds: selectedIds,
          defaultId: defaultId,
          isAllowMultipleSelection: isAllowMultipleSelection,
          isMutexByDefault: isMutexByDefault,
          isAllowClean: isAllowClean,
          changeSelected: (selectedIds) {
            valueChange(itemKey, selectedIds);
          },
        );

  SelectObjectFilterItem.single({
    required String title,
    required List<CommonFilterItemEntry> contents,
    List<String> selectedIds = const [],
    String? defaultId,
    bool isAllowMultipleSelection = false,
    bool isMutexByDefault = false,
    bool isAllowClean = true,
    required SelectObjectFilterValueChange valueChange,
    required String itemKey,
  }) : super(
          titleWidget: Text(
            title,
            style: TextStyle(
              color: Color(0xFF333333),
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
          contents: contents,
          selectedIds: selectedIds,
          defaultId: defaultId,
          isAllowMultipleSelection: isAllowMultipleSelection,
          isMutexByDefault: isMutexByDefault,
          isAllowClean: isAllowClean,
          changeSelected: (selectedIds) {
            valueChange(itemKey, selectedIds);
          },
        );
}

class SelectObjectTipsPopover extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          centerSlice: Rect.fromLTWH(78, 15, 4, 8),
          image: AssetImage(
            'assets/images/base/select_object_tip_bg.png',
          ),
        ),
      ),
      constraints: BoxConstraints(
        minWidth: 184,
        minHeight: 38,
        maxWidth: MediaQuery.of(context).size.width - 50,
      ),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 10),
      child: Text(
        '潜在：已经注册但是从未下单的客户\n完成首单：本月（自然月）完成首次下单的客户\n活跃：上个自然月有下单的客户\n沉默：前3个月有下单但是上个自然月未下单的客户\n流失：历史上有下单但是最近3个自然月没有下单的客户',
        style: TextStyle(
          inherit: false,
          fontSize: 14,
          color: Colors.white,
        ),
      ),
    );
  }
}
