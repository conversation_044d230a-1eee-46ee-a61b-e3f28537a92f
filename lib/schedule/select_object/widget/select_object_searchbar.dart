import 'package:flutter/material.dart';

// ignore: must_be_immutable
class SelectObjectSearchBar extends StatefulWidget {
  final List<String> itemTitles;
  final VoidCallback filterAction;
  final VoidCallback searchAction;
  final ValueChanged<String> filterItemChange;

  /// 当前选中得标题
  String currentValue;

  SelectObjectSearchBar({
    required this.itemTitles,
    required this.filterAction,
    required this.searchAction,
    required this.filterItemChange,
    this.currentValue = "",
  });

  @override
  State<StatefulWidget> createState() {
    return SelectObjectSearchBarState();
  }
}

class SelectObjectSearchBarState extends State<SelectObjectSearchBar> {
  @override
  Widget build(BuildContext context) {
    /// 客户改版 修改 去掉筛选项 代码暂时先保留吧
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
      child: GestureDetector(
        onTap: widget.searchAction,
        behavior: HitTestBehavior.opaque,
        child: Container(
          height: 35,
          padding: EdgeInsets.only(left: 9),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(35),
            color: Color(0xFFFFFFFF),
          ),
          child: Row(
            children: [
              Image.asset(
                'assets/images/schedule/select_object_icon.png',
                width: 20,
                height: 20,
              ),
              Text(
                '搜索对象',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  color: Color(0xFF666666),
                ),
              )
            ],
          ),
        ),
      ),
    );
    // return Container(
    //   color: Color(0xFFEFEFF4),
    //   padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
    //   child: Column(
    //     children: [
    //       Container(
    //         height: 40,
    //         margin: EdgeInsets.only(left: 5, bottom: 10),
    //         child: Row(
    //           children: [
    //             Expanded(
    //               child: GestureDetector(
    //                 onTap: widget.searchAction,
    //                 behavior: HitTestBehavior.opaque,
    //                 child: Container(
    //                   height: 35,
    //                   padding: EdgeInsets.only(left: 9),
    //                   decoration: BoxDecoration(
    //                     borderRadius: BorderRadius.circular(35),
    //                     color: Color(0xFFFFFFFF),
    //                   ),
    //                   child: Row(
    //                     children: [
    //                       Image.asset(
    //                         'assets/images/schedule/select_object_icon.png',
    //                         width: 20,
    //                         height: 20,
    //                       ),
    //                       Text(
    //                         '搜索对象',
    //                         style: TextStyle(
    //                           fontSize: 14,
    //                           fontWeight: FontWeight.normal,
    //                           color: Color(0xFF666666),
    //                         ),
    //                       )
    //                     ],
    //                   ),
    //                 ),
    //               ),
    //             ),
    //             IconButton(
    //               icon: Image.asset(
    //                 'assets/images/schedule/select_object_filter.png',
    //               ),
    //               iconSize: 40,
    //               onPressed: widget.filterAction,
    //             )
    //           ],
    //         ),
    //       ),
    //       Container(
    //         child: Row(
    //           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
    //           children: this.getTagWidget(),
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }

  List<Widget> getTagWidget() {
    return widget.itemTitles.map((e) {
      return Expanded(
        flex: 1,
        child: Container(
          child: SelectObjectTagView(
            title: e,
            isSelected: widget.currentValue == e,
            changeSelected: (value) {
              if (value == true) {
                widget.currentValue = e;
                widget.filterItemChange(e);
                setState(() {});
              }
            },
          ),
        ),
      );
    }).toList();
  }
}

class SelectObjectTagView extends StatelessWidget {
  final bool isSelected;
  final String title;
  final ValueChanged<bool> changeSelected;

  SelectObjectTagView({
    required this.title,
    required this.isSelected,
    required this.changeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.changeSelected(!this.isSelected);
      },
      child: Container(
        margin: EdgeInsets.only(left: 5, right: 5),
        padding: EdgeInsets.only(top: 4, bottom: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(
            color: this.isSelected ? Color(0xFF35C561) : Colors.transparent,
            width: this.isSelected ? 0.5 : 0,
          ),
          color: Color(0xFFFFFFFF),
        ),
        child: Text(
          this.title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 15,
            color: this.isSelected ? Color(0xFF35C561) : Color(0xFF666666),
          ),
        ),
      ),
    );
  }
}
