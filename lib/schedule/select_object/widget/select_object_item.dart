import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:flutter/material.dart';

class SelectObjectItem extends StatelessWidget {
  final ScheduleExternalModel model;
  final bool isSelected;
  final bool isManagerLevel;
  final bool isSearch;

  SelectObjectItem({
    required this.model,
    required this.isSelected,
    required this.isManagerLevel,
    this.isSearch = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(right: 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(10),
                  // alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        model.customer?.name ?? "",
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        this.getContentText(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF8E8E93),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Visibility(
                visible: !this.isSearch,
                child: Image.asset(
                  this.isSelected
                      ? 'assets/images/schedule/select_object_selected.png'
                      : 'assets/images/schedule/select_object_normal.png',
                  width: 20,
                  height: 20,
                ),
              )
            ],
          ),
          Container(
            color: Color(0xFFEFEFF4),
            height: 0.5,
          )
        ],
      ),
    );
  }

  String getContentText() {
    String labels = model.customer?.labs?.join("") ?? "";
    String distance = model.customer?.distance ?? "";
    return this.isManagerLevel
        ? labels
        : (distance.length == 0
            ? labels
            : (labels.length > 0
                ? labels + " | " + "距您$distance"
                : "距您$distance"));
  }
}
