import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/check_license_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_page_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_searchbar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import 'data/select_roles_data.dart';

class VisitSelectObject extends BasePage {
  final String? rolesJSON;

  VisitSelectObject({this.rolesJSON});

  @override
  BaseState<StatefulWidget> initState() {
    return VisitSelectObjectState();
  }
}

class VisitSelectObjectState extends BaseState<VisitSelectObject> {
  /// 数据源
  List<ScheduleExternalModel> dataSource = [];

  /// 分页参数
  int page = 0;

  /// 当前选择的客户id
  String? selectedId = "";

  /// 客户信息
  ScheduleExternalCustomerModel? customer;

  /// 是否是M级别账号
  bool isManagerLevel = false;

  String? latitude;
  String? longitude;
  bool isRequestLocation = false;

  /// 筛选页面参数
  Map<String, String> filterParams = {};
  Map<String, dynamic> cacheParams = {};

  /// 顶部按钮筛选项
  String? filterTag;

  /// 身份权限信息
  List<SelectRolesData> rolesData = [];
  late SelectRolesData selectRoles;

  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    this.generateRolesData();
    this._requestRoleType();
    this.refreshListData();
    super.initState();
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SelectObjectSearchBar(
            itemTitles: ["90天未下单", "本月无下单", "资质已临期"],
            filterAction: jumpFilter,
            searchAction: jumpSearchPage,
            currentValue: this.getTagValue(),
            filterItemChange: filterTagChange,
          ),
          Expanded(
            child: EasyRefresh(
              onRefresh: () async {
                this.refreshListData();
              },
              onLoad: () async {
                this.loadMoreListData();
              },
              controller: this._controller,
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  ScheduleExternalModel model = this.dataSource[index];
                  return GestureDetector(
                    onTap: () {
                      this.selectedId = model.customer?.id;
                      this.customer = model.customer;
                      setState(() {});
                    },
                    behavior: HitTestBehavior.opaque,
                    child: SelectObjectItem(
                      model: model,
                      isSelected: model.customer?.id == this.selectedId,
                      isManagerLevel: this.isManagerLevel,
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  /// 获取页面顶部tag得Value
  String getTagValue() {
    switch (this.filterTag) {
      case "5":
        return "90天未下单";
      case "6":
        return "本月无下单";
      case "1":
        return "资质已临期";
      default:
        return "";
    }
  }

  /// 页面顶部tag选择调用
  void filterTagChange(String tag) {
    switch (tag) {
      case "90天未下单":
        this.filterTag = "5";
        break;
      case "本月无下单":
        this.filterTag = "6";
        break;
      case "资质已临期":
        this.filterTag = "1";
        break;
      default:
    }
    _controller.callRefresh();
  }

  /// Request
  void refreshListData() {
    this.showLoadingDialog();
    if (this.isRequestLocation) {
      this.page = 0;
      this.requestMerchantList();
    } else {
      this.requestLocation();
    }
  }

  void loadMoreListData() {
    this.requestMerchantList();
  }

  void requestMerchantList() async {
    Map<String, dynamic> params = {
      "keyword": "",
      "limit": "20",
      "offset": "${this.page}",
    };
    if (this.latitude != null && this.longitude != null) {
      params["latitude"] = this.latitude;
      params["longitude"] = this.longitude;
    }

    if (this.filterTag == "5") {
      params["orderConditionCode"] = "5";
    } else if (this.filterTag == "6") {
      params["orderConditionCode"] = "6";
    } else if (this.filterTag == "1") {
      params["licenseStatuses"] = "1";
    }

    // 转换一次 目前存在返回key(licenseStatuses)与上传后台参数key(licenseCode)不一致情况
    if (params.containsKey("licenseStatuses")) {
      params["licenseCode"] = params["licenseStatuses"];
      params.remove("licenseStatuses");
    }

    /// 添加筛选页面参数
    params.addAll(this.filterParams);

    var path = '/task/v290/searchCustomersInfos';
    if ('${this.selectRoles.roleCode}' == '3') {
      path = '/hyjk/getPrivateCustomerInfo';
    }

    var result = await NetworkV2<SelectObjectPageData>(SelectObjectPageData())
        .requestDataV2(
      path,
      parameters: params,
    );
    this.dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        List<ScheduleExternalModel> source = result.getData()?.rows ?? [];
        if (this.page == 0) {
          this.dataSource = source;
        } else {
          this.dataSource.addAll(source);
        }
        this._controller.finishRefresh();
        bool lastPage = result.getData()?.lastPage == true;
        this._controller.finishLoad(noMore: lastPage);
        this.page += 1;
        setState(() {});
      } else {
        this._controller.finishRefresh();
      }
    }
  }

  /// 请求定位信息
  void requestLocation() {
    XYYContainer.locationChannel.locate().then((value) {
      if (this.mounted && (value.isSuccess ?? false)) {
        this.latitude = value.latitude;
        this.longitude = value.longitude;
      }
      this.isRequestLocation = true;
      this.refreshListData();
    });
  }

  void _requestRoleType() {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      setState(() {
        this.isManagerLevel = value;
      });
    });
  }

  void generateRolesData() {
    if (widget.rolesJSON != null) {
      List<dynamic> source = json.decode(widget.rolesJSON!) ?? [];
      if (source.length > 0) {
        this.rolesData = source.map<SelectRolesData>((e) {
          if (e is Map<String, dynamic>) {
            return SelectRolesData().fromJsonMap(e);
          } else {
            return SelectRolesData();
          }
        }).toList();
        this.selectRoles = this.rolesData.first;
      } else {
        this.selectRoles = SelectRolesData()
          ..roleCode = 1
          ..roleName = "药帮忙";
      }
    }
  }

  /// 跳转筛选页面
  void jumpFilter() async {
    dynamic result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ObjectFilterPage(
          filterParams: this.filterParams,
          cacheParams: this.cacheParams,
        ),
        fullscreenDialog: true,
      ),
    );
    if (result != null) {
      if (result is Map<String, dynamic>) {
        if (result.containsKey("filterParams")) {
          this.filterParams = result["filterParams"];

          /// 清空本页选中的tag
          this.filterTag = "";
        }
        if (result.containsKey("cacheParams")) {
          this.cacheParams = result["cacheParams"];
        }
        _controller.callRefresh();
      }
    }
  }

  /// 跳转到搜索页面
  void jumpSearchPage() {
    Navigator.of(context).pushNamed(
        '/select_object_search?roleCode=${this.selectRoles.roleCode}');
  }

  @override
  String getTitleName() {
    if (this.rolesData.length > 1) {
      return "${this.selectRoles.roleName}";
    }
    return "选择对象";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return VisitSelectObjectAppBar(
      titleKey: GlobalKey(),
      centerAction: (titleKey) {
        showAuthDialog(context, titleKey);
      },
      rightAction: () {
        FocusScope.of(context).requestFocus(FocusNode());
        CheckLicenseDialog()
            .showCheckLicenseDialog(
              context,
              customer?.licenseValidateMust?? -1,
              customer?.licenseValidateIssue?? -1,
              customer?.merchantStatus == 3,
              continueVisitCallback: selectedFinished
        );
      },
      isMoreRoles: this.rolesData.length > 0,
      titleStr: this.getTitleName(),
    );
  }

  /// 显示切换权限菜单
  void showAuthDialog(BuildContext context, GlobalKey anchorKey) {
    // XYYContainer.toastChannel.toast('点击了提示');
    dynamic renderBox = anchorKey.currentContext?.findRenderObject()!;
    Offset offset = renderBox.localToGlobal(Offset(0, renderBox.size.height));
    showGeneralDialog(
      context: context,
      barrierLabel: "",
      barrierDismissible: true,
      // 点击遮罩是否关闭对话框
      transitionDuration: const Duration(milliseconds: 200),
      // 对话框打开/关闭的动画时长
      pageBuilder: (
        // 构建对话框内部UI
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        return Stack(
          children: [
            Positioned(
              child: VisitSelectObjectPopoverItems(
                roles: this.rolesData,
                changeRoles: (role) {
                  this.selectRoles = role;
                  if (mounted) {
                    setState(() {});
                  }
                  this.refreshListData();
                },
              ),
              top: offset.dy + 10,
              left: offset.dx + renderBox.size.width / 2 - 40,
            )
          ],
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOut,
          ),
          child: child,
        );
      },
    );
  }

  /// 选择完成
  void selectedFinished() {
    if (this.selectedId == null) {
      showToast("请先选择对象");
      return;
    }
    ScheduleExternalModel selectModel = this.dataSource.firstWhere(
          (element) => element.customer?.id == this.selectedId,
          orElse: () => ScheduleExternalModel(),
        );
    if (selectModel.customer == null) {
      showToast("请先选择对象");
      return;
    }
    String? jsonStr = json.encode(selectModel);
    Map<String, dynamic>? jsonMap = json.decode(jsonStr);

    /// 如果当前选择得是荷叶健康的对象，则传递参数isHeyeVisit
    if ("${this.selectRoles.roleCode}" == "3") {
      jsonMap?["isHeyeVisit"] = true;
    }
    Navigator.of(context).pop(jsonMap);
  }
}

class VisitSelectObjectAppBar extends AppBar {
  final VoidCallback rightAction;
  final ValueChanged<GlobalKey> centerAction;
  final String titleStr;
  final bool isMoreRoles;

  VisitSelectObjectAppBar({
    required this.titleStr,
    required this.isMoreRoles,
    required this.centerAction,
    required this.rightAction,
    required GlobalKey titleKey,
  }) : super(
          leading: LeftButton(
            leftBtnType: LeftButtonType.back,
          ),
          title: GestureDetector(
            onTap: () {
              if (isMoreRoles) {
                centerAction(titleKey);
              }
            },
            child: Row(
              key: titleKey,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  titleStr,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Visibility(
                  visible: isMoreRoles,
                  child: Image.asset(
                    'assets/images/schedule/visit_select_object_down.png',
                    width: 8,
                    height: 8,
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: Text(
                '确定',
                style: TextStyle(
                  color: Color(0xFF35C561),
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: rightAction,
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(
                    EdgeInsets.only(right: 10)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          toolbarHeight: 44, toolbarTextStyle: TextTheme(
            headline6: TextStyle(
              color: Colors.black,
            ),
          ).bodyText2, titleTextStyle: TextTheme(
            headline6: TextStyle(
              color: Colors.black,
            ),
          ).headline6, systemOverlayStyle: SystemUiOverlayStyle.dark,
        );
}

class VisitSelectObjectPopoverItems extends StatelessWidget {
  final List<SelectRolesData> roles;
  final ValueChanged<SelectRolesData> changeRoles;

  VisitSelectObjectPopoverItems({
    required this.roles,
    required this.changeRoles,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: this.generateItems(context),
      ),
    );
  }

  List<Widget> generateItems(BuildContext context) {
    return this
        .roles
        .map(
          (e) => Container(
            padding: EdgeInsets.only(left: 15, right: 15, top: 7, bottom: 7),
            child: TextButton(
              child: Text(
                '${e.roleName}',
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: () {
                this.changeRoles(e);
                Navigator.of(context).pop();
              },
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        )
        .toList();
  }
}
