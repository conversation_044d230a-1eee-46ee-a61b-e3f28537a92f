class AddConfigSource {
  static List addConfig() {
    return [
      {
        "title": "",
        "sectionKey": "related",
        "sectionType": 3,
        "items": [
          {
            "selectType": 2,
            "itemType": 1,
            "itemPlaceHolder": "请选择拜访方式",
            "itemKey": "visitType",
            "itemTitle": "拜访方式"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "info",
        "sectionType": 3,
        "items": [
          {
            "selectType": 3,
            "itemType": 1,
            "itemPlaceHolder": "请添加对象(必填)",
            "itemKey": "object",
            "itemTitle": "对象"
          },
          {
            "selectType": 4,
            "itemType": 1,
            "itemPlaceHolder": "请添加联系人(必填)",
            "itemKey": "contacts",
            "itemTitle": "联系人"
          },
          {
            "itemKey": "kp",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "defaultText": "是",
            "itemTitle": "是否KP"
          },
          {
            "selectType": 5,
            "itemType": 1,
            "itemPlaceHolder": "请选择拜访目的",
            "itemKey": "purpose",
            "itemTitle": "拜访目的"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "newObject",
        "sectionType": 3,
        "items": [
          {
            "selectType": 10,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemPlaceHolderColor": 0xFFFE3D3D,
            "itemKey": "status",
            "itemTitle": "客户资质情况"
          },
        ]
      },
      {
        "title": "拜访总结",
        "sectionKey": "summary",
        "sectionType": 1,
        "items": [
          {
            "itemKey": "summary",
            "itemType": 3,
            "itemPlaceHolder": "请输入拜访总结",
            "textCount": 500,
            "itemTitle": "拜访总结"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "object",
        "sectionType": 3,
        "items": [
          {
            "selectType": 6,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemPlaceHolderColor": 0xFFFE3D3D,
            "itemKey": "status",
            "itemTitle": "对象经营状况"
          },
          {
            "itemKey": "image",
            "itemType": 5,
            "itemPlaceHolder": "",
            "imageCount": 10,
            "itemTitle": ""
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "location",
        "sectionType": 2,
        "items": [
        ]
      }
    ];
  }

  static List perfectConfig() {
    return [
      {
        "title": "",
        "sectionKey": "related",
        "sectionType": 3,
        "items": [
          // {
          //   "selectType": 1,
          //   "itemType": 1,
          //   "itemPlaceHolder": "请选择关联任务",
          //   "itemKey": "task",
          //   "itemTitle": "关联任务"
          // },
          {
            "itemKey": "visitType",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "请选择拜访方式",
            "defaultText": "电话拜访",
            "itemTitle": "拜访方式"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "info",
        "sectionType": 3,
        "items": [
          {
            "itemKey": "object",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "itemTitle": "对象"
          },
          {
            "itemKey": "mobile",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "请添加联系人电话(必填)",
            "itemTitle": "联系方式"
          },
          {
            "itemKey": "contacts",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "itemTitle": "联系人"
          },
          {
            "itemKey": "kp",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "itemTitle": "是否KP"
          },
          {
            "itemKey": "duration",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "itemTitle": "通话时长"
          },
          {
            "selectType": 5,
            "itemType": 1,
            "itemPlaceHolder": "请选择拜访目的",
            "itemKey": "purpose",
            "itemTitle": "拜访目的"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "newObject",
        "sectionType": 3,
        "items": [
          {
            "selectType": 10,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemPlaceHolderColor": 0xFFFE3D3D,
            "itemKey": "status",
            "itemTitle": "客户资质情况"
          },
        ]
      },
      {
        "title": "拜访总结",
        "sectionKey": "summary",
        "sectionType": 1,
        "items": [
          {
            "itemKey": "summary",
            "itemType": 3,
            "itemPlaceHolder": "请输入拜访总结",
            "textCount": 500,
            "itemTitle": "拜访总结"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "object",
        "sectionType": 3,
        "items": [
          {
            "selectType": 6,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemPlaceHolderColor": 0xFFFE3D3D,
            "itemKey": "status",
            "itemTitle": "对象经营状况"
          },
          {
            "itemKey": "image",
            "itemType": 5,
            "itemPlaceHolder": "",
            "imageCount": 10,
            "itemTitle": ""
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "location",
        "sectionType": 2,
        "items": [
          // {
          //   "selectType": 1,
          //   "itemType": 4,
          //   "itemPlaceHolder": "",
          //   "itemKey": "share",
          //   "itemTitle": "分享动态"
          // }
        ]
      }
    ];
  }

  static List accompanyConfig() {
    return [
      {
        "title": "",
        "sectionKey": "related",
        "sectionType": 3,
        "items": [
          {
            "selectType": 7,
            "itemType": 1,
            "itemPlaceHolder": "请选择陪访人",
            "itemKey": "accompany",
            "itemTitle": "陪访人"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "info",
        "sectionType": 3,
        "items": [
          {
            "selectType": 3,
            "itemType": 1,
            "itemPlaceHolder": "请选择拜访对象(必填)",
            "itemKey": "object",
            "itemTitle": "对象"
          },
          {
            "selectType": 4,
            "itemType": 1,
            "itemPlaceHolder": "请添加联系人(必填)",
            "itemKey": "contacts",
            "itemTitle": "联系人"
          },
          {
            "itemKey": "kp",
            "isCanEdit": false,
            "itemType": 2,
            "itemPlaceHolder": "",
            "itemTitle": "是否KP"
          },
          {
            "selectType": 5,
            "itemType": 1,
            "itemPlaceHolder": "请选择拜访目的",
            "itemKey": "purpose",
            "itemTitle": "拜访目的"
          }
        ]
      },
      // {
      //   "title": "",
      //   "sectionKey": "newObject",
      //   "sectionType": 3,
      //   "items": [
      //     {
      //       "selectType": 10,
      //       "itemType": 1,
      //       "itemPlaceHolder": "(必填)",
      //       "itemPlaceHolderColor": 0xFFFE3D3D,
      //       "itemKey": "status",
      //       "itemTitle": "客户资质情况"
      //     },
      //   ]
      // },
      {
        "title": "拜访总结",
        "sectionKey": "summary",
        "sectionType": 1,
        "items": [
          {
            "itemKey": "summary",
            "itemType": 3,
            "itemPlaceHolder": "请输入拜访总结",
            "textCount": 500,
            "itemTitle": "拜访总结"
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "object",
        "sectionType": 3,
        "items": [
          {
            "selectType": 6,
            "itemType": 1,
            "itemPlaceHolder": "(必填)",
            "itemPlaceHolderColor": 0xFFFE3D3D,
            "itemKey": "status",
            "itemTitle": "对象经营状况"
          },
          {
            "itemKey": "image",
            "itemType": 5,
            "itemPlaceHolder": "",
            "imageCount": 10,
            "itemTitle": ""
          }
        ]
      },
      {
        "title": "",
        "sectionKey": "location",
        "sectionType": 2,
        "items": []
      }
    ];
  }
}
