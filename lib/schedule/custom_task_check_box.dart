import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomTask<PERSON>heckBox extends StatefulWidget {
  bool isChecked;

  CheckCallback callback;

  final double width;
  final double height;

  CustomTaskCheckBox(this.isChecked, this.callback, this.width, this.height);

  @override
  State<StatefulWidget> createState() {
    return CustomTaskCheckBoxState();
  }
}

class CustomTaskCheckBoxState extends State<CustomTaskCheckBox> {
  CustomTaskCheckBoxState();

  @override
  Widget build(BuildContext context) {
    widget.callback(widget.isChecked);
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.isChecked = !widget.isChecked;
        });
      },
      child: Container(
        height: widget.height,
        width: widget.width,
        child: Image.asset(widget.isChecked
            ? "assets/images/schedule/schedule_task_check_selected.png"
            : "assets/images/schedule/schedule_task_check_normal.png"),
      ),
    );
  }
}

typedef void CheckCallback(bool isCheck);
