import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/schedule/custom_task_check_box.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_relevance_task_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class ScheduleRelevanceTaskPage extends BasePage {
  final String customerId;

  ScheduleRelevanceTaskPage(this.customerId);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleRelevanceTaskPageState();
  }
}

class ScheduleRelevanceTaskPageState
    extends BaseState<ScheduleRelevanceTaskPage> {
  late FilterModel _filterModel;
  late ListModel _listModel;

  EasyRefreshController _refreshController = EasyRefreshController();
  ScrollController _scrollController = ScrollController();

  @override
  void onCreate() {
    _filterModel = FilterModel();
    _listModel = ListModel(_filterModel, widget.customerId);
    _listModel.requestList(true);
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<FilterModel>(create: (context) => _filterModel),
      ChangeNotifierProvider<ListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        children: [
          buildFilterWidget(),
          Expanded(
            child: Stack(
              children: [
                Container(
                  child: buildListWidget(),
                ),
                Visibility(
                    visible: _filterModel.isTaskTypeDialogShowing,
                    child: Container(
                      child: Column(
                        children: [
                          buildTaskTypeFilterItem("全部", null),
                          buildTaskTypeFilterItem("信息收集", 1),
                          buildTaskTypeFilterItem("系统/软件售卖", 2),
                          Expanded(
                            child: GestureDetector(
                                onTap: () {
                                  _filterModel.isTaskTypeDialogShowing = false;
                                  setState(() {});
                                },
                                child: Container(
                                  color: Color(0x80000000),
                                  child: Container(),
                                )),
                          )
                        ],
                      ),
                    ))
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        GestureDetector(
          onTap: () {
            try {
              if (_listModel.checkSelectedList()) {
                var selectedList = _listModel.getSelectedList();
                dynamic selectedResult;
                selectedResult = selectedList.map((e) {
                  return e.filledInfo;
                });
                var fixResultList = [];
                fixResultList.addAll(selectedResult);
                XYYContainer.close(context, resultData: {
                  "selectedList": fixResultList,
                });
              } else {
                showToast("请填写每一个任务要求的信息后再进行关联");
              }
            } catch (e) {
              showToast("选择失败，请刷新后重新选择并填写");
            }
          },
          behavior: HitTestBehavior.opaque,
          child: Container(
            child: Text(
              "确定",
              style: TextStyle(
                  color: Color(0xFF00B377),
                  fontSize: 16,
                  fontWeight: FontWeight.w500),
            ),
            width: 62,
            height: 44,
            alignment: Alignment.center,
          ),
        )
      ],
    );
  }

  @override
  String getTitleName() {
    return "关联任务";
  }

  Widget buildFilterWidget() {
    return Consumer<FilterModel>(builder: (context, model, child) {
      return Container(
          color: Colors.white,
          height: 44,
          child: Row(children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  _filterModel.isTaskTypeDialogShowing =
                      !_filterModel.isTaskTypeDialogShowing;
                  setState(() {});
                },
                child: Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "任务类型",
                        style: model.isTaskTypeDialogShowing
                            ? TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF292933))
                            : TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.normal,
                                color: Color(0xFF676773)),
                      ),
                      SizedBox(
                        width: 4,
                      ),
                      Image.asset(
                        model.isTaskTypeDialogShowing
                            ? "assets/images/base/icon_filter_arrow_up_selected.png"
                            : "assets/images/base/icon_filter_arrow_down.png",
                        width: 10,
                        height: 10,
                      )
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  _filterModel.changeTimeSort().then((value) {
                    requestList(true);
                  });
                },
                child: Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        "时间排序",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Color(0xFF676773)),
                      ),
                      SizedBox(
                        width: 4,
                      ),
                      Image.asset(
                        model.getSortImagePath(),
                        width: 10,
                        height: 10,
                      )
                    ],
                  ),
                ),
              ),
            )
          ]));
    });
  }

  Widget buildListWidget() {
    return Consumer<ListModel>(builder: (context, model, child) {
      return EasyRefresh.custom(
          controller: _refreshController,
          scrollController: _scrollController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            _listModel.requestList(true).then((value) {
              _scrollController.jumpTo(0.0);
              _refreshController.finishRefresh();
            });
          },
          onLoad: !_listModel.isLastPage!
              ? () async {
                  _listModel.requestList(false).then((value) {
                    _refreshController.finishLoad();
                  });
                }
              : null,
          slivers: [
            SliverPadding(padding: EdgeInsets.only(top: 5)),
            SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                return buildItem(_listModel.list![index]);
              }, childCount: _listModel.list?.length ?? 0),
            ),
            SliverPadding(padding: EdgeInsets.only(top: 15)),
          ],
          emptyWidget: getEmptyWidget());
    });
  }

  Widget? getEmptyWidget() {
    if (_listModel.isSuccess == null) {
      return null;
    }
    if (_listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _listModel.requestList(true);
      });
    }

    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget buildItem(ScheduleRelevanceTaskItem itemData) {
    return GestureDetector(
      onTap: () {
        itemData.isSelected = !itemData.isSelected;
        setState(() {});
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.fromLTRB(15, 10, 15, 0),
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2), color: Colors.white),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTaskCheckBox(itemData.isSelected, (isCheck) {
              itemData.isSelected = isCheck;
            }, 18, 18),
            SizedBox(
              width: 15,
            ),
            Flexible(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            itemData.theme!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 16,
                                color: Color(0xff292933),
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Text(
                          itemData.implementStatusName!,
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: itemData.implementStatus == 1
                                  ? Color(0xff9494a6)
                                  : Color(0xffff7200)),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  buildInfoRowWidget("客户", itemData.customerName!),
                  SizedBox(
                    height: 5,
                  ),
                  buildInfoRowWidget("发布人", itemData.creatorName!),
                  SizedBox(
                    height: 5,
                  ),
                  buildInfoRowWidget("任务时间", itemData.taskTime!),
                  SizedBox(
                    height: 5,
                  ),
                  buildInfoRowWidget("任务类型", itemData.typeName!),
                  SizedBox(
                    height: 10,
                  ),
                  Divider(
                    height: 1,
                    color: Color(0xFFF0F0F2),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pushNamed("/task_dotask",
                          arguments: {
                            "taskId": itemData.id,
                            "isFromVisit": true
                          }).then((value) {
                        if (value is Map) {
                          itemData.filledInfo = value;
                          setState(() {});
                        }
                      });
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      height: 44,
                      alignment: Alignment.center,
                      child: Row(
                        children: [
                          Image.asset(
                            "assets/images/schedule/schedule_do_task_icon.png",
                            width: 15,
                            height: 15,
                          ),
                          SizedBox(
                            width: 11,
                          ),
                          Expanded(
                            child: Text(
                              "填写任务详情",
                              style: TextStyle(
                                  color: Color(itemData.filledInfo == null
                                      ? 0xFF676773
                                      : 0xFF00B377),
                                  fontSize: 14,
                                  fontWeight: FontWeight.normal),
                            ),
                          ),
                          Image.asset(
                            "assets/images/task/dotask_select_arrow.png",
                            width: 15,
                            height: 15,
                          )
                        ],
                      ),
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildInfoRowWidget(String label, String content) {
    return Row(
      children: [
        Container(
          constraints: BoxConstraints(minWidth: 56),
          child: Text(
            label,
            style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 14,
                fontWeight: FontWeight.normal),
          ),
        ),
        SizedBox(
          width: 10,
        ),
        Expanded(
          child: Text(
            content,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: Color(0xff676773),
                fontSize: 14,
                fontWeight: FontWeight.normal),
          ),
        )
      ],
    );
  }

  void requestList(bool isRefresh) {
    _listModel.requestList(isRefresh).then((value) {
      if (mounted && value!) {
        if (isRefresh) {
          _refreshController.finishRefresh();
          _scrollController.jumpTo(0.0);
        } else {
          _refreshController.finishLoad();
        }
      }
    });
  }

  Widget buildTaskTypeFilterItem(String label, int? value) {
    bool isSelected = value == _filterModel.taskType;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _filterModel.taskType = value;
        _filterModel.isTaskTypeDialogShowing = false;
        setState(() {});
        requestList(true);
      },
      child: Container(
        padding: EdgeInsets.fromLTRB(15, 0, 10, 0),
        alignment: Alignment.center,
        color: Color(0xFFF7F7F8),
        height: 40,
        child: Row(
          children: [
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                    color: Color(isSelected ? 0xFF00B377 : 0xFF292933),
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.w500 : FontWeight.normal),
              ),
            ),
            Visibility(
              visible: isSelected,
              child: Image.asset(
                "assets/images/schedule/schedule_task_filter_selected.png",
                width: 18,
                height: 18,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class FilterModel extends ChangeNotifier {
  bool isTaskTypeDialogShowing = false;
  String sortType = "1";
  int? taskType;

  String getSortImagePath() {
    switch (sortType) {
      case "1":
        return "assets/images/base/icon_sort_desc.png";
      case "2":
        return "assets/images/base/icon_sort_asc.png";
      default:
        return "assets/images/base/icon_sort_normal.png";
    }
  }

  Future<bool> changeTimeSort() async {
    switch (sortType) {
      case "1":
        sortType = "2";
        break;
      case "2":
        sortType = "1";
        break;
    }
    notifyListeners();
    return Future.value(true);
  }
}

class ListModel extends ChangeNotifier {
  bool? isSuccess;
  bool? isLastPage = true;
  FilterModel? filterModel;
  List<ScheduleRelevanceTaskItem>? list;
  int offset = 0;
  String customerId;
  bool isDisposed = false;

  ListModel(this.filterModel, this.customerId);

  List<ScheduleRelevanceTaskItem> getSelectedList() {
    return list?.where((element) => element.isSelected).toList() ??
        List.empty();
  }

  Future<bool?> requestList(bool isRefresh) async {
    var params = Map<String, dynamic>();
    params["limit"] = 10;
    params["customerId"] = customerId;
    if (filterModel!.taskType != null) {
      params["type"] = filterModel!.taskType;
    }
    if (filterModel?.sortType != null && filterModel!.sortType.isNotEmpty) {
      params["sortType"] = filterModel!.sortType;
    }
    if (isRefresh) {
      params["offset"] = 0;
    } else {
      params["offset"] = offset + 1;
    }
    if (isRefresh) {
      EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    }
    var result =
        await NetworkV2<ScheduleRelevanceTaskData>(ScheduleRelevanceTaskData())
            .requestDataV2("task/v2/contactTask",
                method: RequestMethod.GET, parameters: params);
    if (!isDisposed) {
      EasyLoading.dismiss();
      isSuccess = result.isSuccess;
      if (result.isSuccess==true && result.getData() != null) {
        if (isRefresh) {
          list = result.getData()!.rows;
          offset = 0;
          list?.forEach((element) {
            element.isSelected = false;
            element.filledInfo = null;
          });
        } else {
          list!.addAll(result.getData()!.rows!);
          offset++;
        }
        isLastPage = result.getData()!.lastPage;
      }
      notifyListeners();
    }
    return result.isSuccess;
  }

  @override
  void dispose() {
    isDisposed = true;
    super.dispose();
  }

  bool checkSelectedList() {
    if (list == null) {
      return true;
    }
    bool isError = false;
    list?.forEach((element) {
      if (element.isSelected && element.filledInfo == null) {
        isError = true;
      }
    });
    return !isError;
  }
}
