
class LicenceCode {
  static int STATUS_UNCOMMITTED = 1; //草稿
  static int STATUS_REJECTED = 4; //已驳回
  static int STATUS_STEP1 = 2; //一审中
  static int STATUS_STEP2 = 3; //二录中
  static int STATUS_AWAITS_RECYCLING = 5; //资质待回收
  static int STATUS_CANCELLATION = 7; //已作废
  static int STATUS_RECYCLED = 6; //资质已回收
  static int STATUS_DISQUALIFICATION = 8; //资质不合格
  static int STATUS_PAST_DUE = 9;

  static int setStatusColor(int? status) {
    switch (status) {
      //审核中、一审通过、二审通过、三审通过
      case 2:
      case 11:
      case 21:
      case 31:
        return 0xFF35C561;
        break;
      //草稿、资质不合格、资质未回收、一审不通过、二审不通过、三审不通过、四审不通过
      case 0:
      case 3:
      case 5:
      case 10:
      case 20:
      case 30:
      case 40:
        return 0xFFFE3D3D;
        break;
      //资质已回收
      default:
        return 0xFF8E8E93;
        break;
    }
  }
}
