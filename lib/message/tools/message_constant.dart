enum MessageType {
  Task, //现在指代的是啥不清楚了
  DynamicEvent, //通知、动态
  License, //资质
  CustomerService, //客服
  NewCustomer, //新门店
  Collect, // 商品集
  Other
}

extension MessageTypeExtension on MessageType {
  static MessageType fromValue(String value) {
    switch (value) {
      case "1":
        return MessageType.Task;
      case "2":
        return MessageType.License;
      case "4":
        return MessageType.DynamicEvent;
      case "5":
        return MessageType.CustomerService;
      case "6":
        return MessageType.NewCustomer;
      case "7":
        return MessageType.Collect;
      default:
        return MessageType.Other;
    }
  }
}
