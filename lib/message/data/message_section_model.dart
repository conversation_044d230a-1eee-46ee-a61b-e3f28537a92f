// ignore: import_of_legacy_library_into_null_safe
import 'package:sticky_and_expandable_list/sticky_and_expandable_list.dart';

import 'message_list_data.dart';

class MessageSectionModel extends ExpandableListSection<MessageListData> {
  String time = "";
  List<MessageListData> source = [];

  MessageSectionModel({required this.time, required this.source});

  @override
  List<MessageListData> getItems() {
    return this.source;
  }

  @override
  bool isSectionExpanded() {
    return true;
  }

  @override
  void setSectionExpanded(bool expanded) {}
}
