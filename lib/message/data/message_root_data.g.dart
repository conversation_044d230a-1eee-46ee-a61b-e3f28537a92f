// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_root_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageRootData _$MessageRootDataFromJson(Map<String, dynamic> json) {
  return MessageRootData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : MessageRootData.fromJson(json['data'] as Map<String, dynamic>)
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => MessageListData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..lastPage = json['lastPage'] as bool?;
}

Map<String, dynamic> _$MessageRootDataToJson(MessageRootData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'rows': instance.rows,
      'lastPage': instance.lastPage,
    };
