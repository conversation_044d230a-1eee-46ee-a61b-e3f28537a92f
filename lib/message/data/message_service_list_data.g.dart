// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_service_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageServiceListData _$MessageServiceListDataFromJson(
    Map<String, dynamic> json) {
  return MessageServiceListData()
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => MessageServiceItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..isLastPage = json['isLastPage'];
}

Map<String, dynamic> _$MessageServiceListDataToJson(
        MessageServiceListData instance) =>
    <String, dynamic>{
      'list': instance.list,
      'isLastPage': instance.isLastPage,
    };

MessageServiceItemData _$MessageServiceItemDataFromJson(
    Map<String, dynamic> json) {
  return MessageServiceItemData()
    ..id = json['id']
    ..keFuName = json['keFuName']
    ..keFuAvatar = json['keFuAvatar']
    ..lastMsg = json['lastMsg'] == null
        ? null
        : MessageServiceMSGItemData.fromJson(
            json['lastMsg'] as Map<String, dynamic>)
    ..unReadMsgNum = json['unReadMsgNum']
    ..type = json['type']
    ..redirectUrl = json['redirectUrl'];
}

Map<String, dynamic> _$MessageServiceItemDataToJson(
        MessageServiceItemData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'keFuName': instance.keFuName,
      'keFuAvatar': instance.keFuAvatar,
      'lastMsg': instance.lastMsg,
      'unReadMsgNum': instance.unReadMsgNum,
      'type': instance.type,
      'redirectUrl': instance.redirectUrl,
    };

MessageServiceMSGItemData _$MessageServiceMSGItemDataFromJson(
    Map<String, dynamic> json) {
  return MessageServiceMSGItemData()
    ..dialogId = json['dialogId']
    ..createTime = json['createTime']
    ..content = json['content'];
}

Map<String, dynamic> _$MessageServiceMSGItemDataToJson(
        MessageServiceMSGItemData instance) =>
    <String, dynamic>{
      'dialogId': instance.dialogId,
      'createTime': instance.createTime,
      'content': instance.content,
    };
