import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:date_format/date_format.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_list_data.g.dart';

@JsonSerializable()
class MessageListData extends BaseModel<MessageListData> {
  int? id;

  /// 消息类型 1 订单卡单 2资质审核 3分配客户 4分享动态 5商品加购 6新开门店 7商品集掉落
  String? customType;

  /// 消息标题
  String? title;

  /// odid
  int? oaId;

  /// 消息内容
  String? content;

  ///  创建时间
  int? createTime;

  /// 是否已读 1未读  2已读
  int? state;

  /// 任务id
  int? taskId;

  /// 审核消息状态 1 未读 2已读
  int? messageStatus;

  /// 审核消息类型 1一审 2二录 3资质临期 4资质过期  1、2跳转资质详情 3、4跳转客户详情得资质页面
  int? messageType;

  /// 资质类型  2资质首营4资质变更
  int? licenseType;

  /// 客户id
  int? merchantId;

  /// 客户id 资质审核消息传递得参数
  int? customerId;

  /// 资质编号
  String? applicationNumber;

  /// 资质消息标题
  String? messageTitle;

  /// 用来展示得时间
  String? time;

  /// 用来分组得时间
  String? dateTime;

  /// 分配客户/分享动态得标题
  String? typeName;

  /// 分配客户/分享动态是否可跳转 参数 true可以 false不可以
  bool? canRedirect;

  /// 加购通知使用得参数
  String? remark;

  /// 分配客户/分享动态 阅读状态 1:已读 0:未读
  int? readStatus;

  String customTitle() {
    switch (this.customType) {
      case "1":
        return this.title ?? "订单卡单提醒";
      case "2":
        return this.messageTitle ?? "资质最新状态通知";
      case "3":
        return this.typeName ?? "分配客户";
      case "4":
        return this.typeName ?? "分享动态";
      case "5":
        return this.title ?? "客户动态";
      default:
        return this.title ?? "最新消息";
    }
  }

  String customContent() {
    return this.content ?? "";
  }

  String customTimeStr() {
    if (this.time != null) {
      return this.time!;
    }
    String timeStr = formatDate(
      DateTime.fromMillisecondsSinceEpoch(this.createTime ?? 0),
      [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ':', ss],
    );
    this.time = timeStr;
    return timeStr;
  }

  String customDateStr() {
    if (this.dateTime != null) {
      return this.dateTime!;
    }
    String dateTimeStr = formatDate(
      DateTime.fromMillisecondsSinceEpoch(this.createTime ?? 0),
      [yyyy, '-', mm, '-', dd],
    );
    this.dateTime = dateTimeStr;
    return dateTimeStr;
  }

  /// 是否可进入详情
  bool customShowDetail() {
    // 是否可跳转
    if (this.customType == "4" || this.customType == "3") {
      return this.canRedirect ?? false;
    }
    return true;
  }

  /// 是否展示已读未读
  bool customShowReadStatus() {
    // 分享动态没有详情， 不展示已读未读
    if (this.customType == "4" || this.customType == "3") {
      return true;
    }
    if (this.customType == "5" || this.customType == "1") {
      return this.state == 2;
    }
    if (this.customType == "2") {
      return this.messageStatus == 2;
    }
    if (this.customType == "6") {
      return this.state == 2;
    }
    if (this.customType == "7") {
      return this.state == 2;
    }
    return true;
  }

  /// 设置消息已读
  void settingReadStatus() {
    // 分享动态没有详情， 不展示已读未读
    if (this.customType == "4" || this.customType == "3") {
      return;
    }
    if (this.customType == "5" || this.customType == "1") {
      this.state = 2;
    }
    if (this.customType == "6" || this.customType == "7") {
      this.state = 2;
    }
    if (this.customType == "2") {
      this.messageStatus = 2;
    }
  }

  MessageListData({
    this.id,
    this.title,
    this.oaId,
    this.content,
    this.createTime,
    this.state,
    this.taskId,
  });

  factory MessageListData.fromJson(Map<String, dynamic> json) =>
      _$MessageListDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageListDataToJson(this);
  }
}
