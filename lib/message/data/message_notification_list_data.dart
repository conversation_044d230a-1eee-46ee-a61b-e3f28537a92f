import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'message_notification_list_data.g.dart';

@JsonSerializable()
class MessageNotificationListData extends BaseModelV2<MessageNotificationListData?> {
  List<MessageNotificationItemData>? rows;
  dynamic isLastPage = false;

  MessageNotificationListData();

  factory MessageNotificationListData.fromJson(Map<String, dynamic> json) =>
      _$MessageNotificationListDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageNotificationListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageNotificationListDataToJson(this);
  }
}

@JsonSerializable()
class MessageNotificationItemData extends BaseModelV2<MessageNotificationItemData?> {
  dynamic messageType; // 消息类型 1 订单卡单 2资质审核 3分配客户 4 分享动态 5 商品加购
  dynamic messageTitle; // 消息类型名称
  dynamic iconUrl; // 图标地址
  dynamic content; // 详情
  dynamic createTime; // 年-月-日
  dynamic unReadCount; // 未读数据


  MessageNotificationItemData();

  factory MessageNotificationItemData.fromJson(Map<String, dynamic> json) =>
      _$MessageNotificationItemDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageNotificationItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageNotificationItemDataToJson(this);
  }
}
