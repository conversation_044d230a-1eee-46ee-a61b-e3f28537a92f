import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'message_service_list_data.g.dart';

@JsonSerializable()
class MessageServiceListData extends BaseModelV2<MessageServiceListData?> {
  List<MessageServiceItemData>? list;
  dynamic isLastPage = false;

  MessageServiceListData();

  factory MessageServiceListData.fromJson(Map<String, dynamic> json) =>
      _$MessageServiceListDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageServiceListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageServiceListDataToJson(this);
  }
}

@JsonSerializable()
class MessageServiceItemData extends BaseModelV2<MessageServiceItemData?> {
  dynamic id;
  dynamic keFuName;
  dynamic keFuAvatar;
  MessageServiceMSGItemData? lastMsg;
  dynamic unReadMsgNum;
  dynamic type;
  dynamic redirectUrl;


  MessageServiceItemData();

  factory MessageServiceItemData.fromJson(Map<String, dynamic> json) =>
      _$MessageServiceItemDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageServiceItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageServiceItemDataToJson(this);
  }
}

@JsonSerializable()
class MessageServiceMSGItemData extends BaseModelV2<MessageServiceMSGItemData?> {
  dynamic dialogId;
  dynamic createTime;
  dynamic content;



  MessageServiceMSGItemData();

  factory MessageServiceMSGItemData.fromJson(Map<String, dynamic> json) =>
      _$MessageServiceMSGItemDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageServiceMSGItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageServiceMSGItemDataToJson(this);
  }
}
