// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_notification_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageNotificationListData _$MessageNotificationListDataFromJson(
    Map<String, dynamic> json) {
  return MessageNotificationListData()
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) =>
            MessageNotificationItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..isLastPage = json['isLastPage'];
}

Map<String, dynamic> _$MessageNotificationListDataToJson(
        MessageNotificationListData instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'isLastPage': instance.isLastPage,
    };

MessageNotificationItemData _$MessageNotificationItemDataFromJson(
    Map<String, dynamic> json) {
  return MessageNotificationItemData()
    ..messageType = json['messageType']
    ..messageTitle = json['messageTitle']
    ..iconUrl = json['iconUrl']
    ..content = json['content']
    ..createTime = json['createTime']
    ..unReadCount = json['unReadCount'];
}

Map<String, dynamic> _$MessageNotificationItemDataToJson(
        MessageNotificationItemData instance) =>
    <String, dynamic>{
      'messageType': instance.messageType,
      'messageTitle': instance.messageTitle,
      'iconUrl': instance.iconUrl,
      'content': instance.content,
      'createTime': instance.createTime,
      'unReadCount': instance.unReadCount,
    };
