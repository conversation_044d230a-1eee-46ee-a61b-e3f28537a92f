import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/message/page/message_tab_page.dart';
import 'package:XyyBeanSproutsFlutter/message/tools/message_constant.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/global_cache_manager.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class MessageRootPage extends BasePage {
  final String? index;

  MessageRootPage({this.index = "0"});

  @override
  BaseState<StatefulWidget> initState() {
    return MessageRootPageState();
  }
}

class MessageRootPageState extends BaseState<MessageRootPage>
    with SingleTickerProviderStateMixin {
  List<String> _tabTitles = [
    "通知消息",
    "客服消息",
  ];
  TabController? _tabController;

  ValueNotifier<int> notificationDotController = ValueNotifier(0);
  ValueNotifier<int> serviceDotController = ValueNotifier(0);

  // 子页面触发刷新的controller
  ValueNotifier<int> dotController = ValueNotifier(0);

  @override
  void onCreate() {
    super.onCreate();
    var index = 0;
    try {
      index = int.tryParse(widget.index ?? "") ?? 0;
    } catch (e) {}
    _tabController = new TabController(
        initialIndex: index, length: _tabTitles.length, vsync: this);
    dotController.addListener(() {
      if (mounted) {
        requestDotData();
      }
    });
    dotController.value = DateTime.now().millisecond;
  }

  @override
  void onDestroy() {
    super.onDestroy();
    dotController.dispose();
    EventBus().sendMessage(MessageEventBusName.REFRESH_MESSAGE_COUNT);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: TabBarView(
        controller: _tabController,
        children: [
          MessageTabPage(MessageTabType.Notification, dotController),
          MessageTabPage(MessageTabType.Service, dotController),
        ],
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      titleWidget: buildTitleWidget(),
      rightButtons: [
        SizedBox(
          width: 80,
        )
      ],
    );
  }

  Widget buildTitleWidget() {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.bottomCenter,
      margin: EdgeInsets.only(left: 25),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Color(0xFF292933),
        indicator: TabCustomIndicator(
            wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        unselectedLabelColor: Color(0xFF676733),
        unselectedLabelStyle:
            TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
        tabs: _tabTitles
            .mapIndexed((index, e) => Stack(children: [
                  Tab(text: e),
                  ValueListenableBuilder<int>(
                      valueListenable: index == 0
                          ? notificationDotController
                          : serviceDotController,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: value > 0,
                          child: Positioned(
                            right: 0,
                            top: 12,
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle, color: Colors.red),
                            ),
                          ),
                        );
                      }),
                ]))
            .toList(),
      ),
    );
  }

  void requestDotData() {
    GlobalCacheManager.instance.messageCountMap.then((value) {
      if (value != null && value.isNotEmpty == true && mounted) {
        int notificationCount = (value[MessageType.DynamicEvent] ?? 0) +
            (value[MessageType.Task] ?? 0) +
            (value[MessageType.License] ?? 0) +
            (value[MessageType.NewCustomer] ?? 0);
        int serviceCount = value[MessageType.CustomerService] ?? 0;
        notificationDotController.value = notificationCount;
        serviceDotController.value = serviceCount;
      }
    });
  }
}
