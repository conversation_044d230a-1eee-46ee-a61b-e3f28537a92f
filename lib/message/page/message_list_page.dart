import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_list_data.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_root_data.dart';
import 'package:XyyBeanSproutsFlutter/message/widget/message_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class MessageListPage extends BasePage {
  final String? messageType;
  final String? messageTitle;

  /// Push跳转使用 二级页面
  final String? subPage;

  MessageListPage({this.messageType, this.messageTitle, this.subPage});

  @override
  BaseState<StatefulWidget> initState() {
    return MessageListPageState();
  }
}

class MessageListPageState extends BaseState<MessageListPage> {
  /// 刷新未读列表
  /// ignore: non_constant_identifier_names
  final int MESSAGE_RELAD_UNREAD = 10071;

  EasyRefreshController _controller = EasyRefreshController();

  /// 分页参数
  int page = 0;

  /// 数据源
  List<MessageListData> dataSource = [];

  @override
  void initState() {
    super.initState();
    EasyLoading.show();
    this.refreshData();
    if (widget.subPage != null) {
      Future.delayed(Duration(milliseconds: 0))
          .then((value) => Navigator.of(context).pushNamed(widget.subPage!));
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: EasyRefresh(
        enableControlFinishLoad: true,
        enableControlFinishRefresh: true,
        controller: _controller,
        onLoad: () async {
          this.loadMoreData();
        },
        onRefresh: () async {
          this.refreshData();
        },
        child: ListView.builder(
          itemCount: this.dataSource.length,
          cacheExtent: 20,
          itemBuilder: (context, index) {
            var model = this.dataSource[index];
            return GestureDetector(
              onTap: () {
                /// 不能跳转详情 则直接返回
                if (!model.customShowDetail()) {
                  return;
                }
                this.jumpPage(model);
              },
              child: MessageListItem(model: model),
            );
          },
        ),
        emptyWidget: getEmptyWidget(),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if ((this.dataSource.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return widget.messageTitle ?? "消息列表";
  }

  /// 跳转
  void jumpPage(MessageListData model) {
    // 1 订单卡单 2资质审核 3分配客户 4分享动态 5商品加购 6新开门店
    switch (model.customType) {
      case "1":
        Navigator.of(context)
            .pushNamed('/TaskDetailPage', arguments: {"taskId": model.taskId});
        break;
      case "2":
        // 资质临期与资质过期 资质消息类型 1一审驳回 2资质不合格 3资质临期 4资质过期
        if (model.messageType == 1 || model.messageType == 2) {
          var routerPath =
              "/license_detail_page?licenseAuditId=${model.applicationNumber ?? ''}&type=${model.licenseType ?? 0}";
          XYYContainer.open(routerPath);
        } else if (model.messageType == 3 || model.messageType == 4) {
          var routerPath =
              "xyy://crm-app.ybm100.com/drugstore/detail/item?position=2&merchantId=${model.customerId ?? 0}";
          XYYContainer.open(routerPath);
        } else {
          var routerPath = "xyy://crm-app.ybm100.com/msg/detail?";
          var params = model.toJson();
          params.forEach((key, value) {
            if (value != null) {
              var encodeValue = Uri.encodeComponent("$value");
              routerPath += "$key=$encodeValue&";
            }
          });
          routerPath = routerPath.substring(0, routerPath.length - 1);
          // routerPath = Uri.encodeFull(routerPath);
          XYYContainer.open(routerPath);
        }
        break;
      case "3":
        var merchantId = model.merchantId ?? 0;
        if (merchantId > 0) {
          // 跳转客户详情
          var routerPath =
              "/customer_private_detail_page?customerId=$merchantId";
          XYYContainer.open(routerPath);
        } else {
          // 跳私海列表
          XYYContainer.bridgeCall('private_list_jump');
        }
        break;
      case "4":
        // 这里不跳转
        return;
      case "5":
        // 跳转客户购物车页面
        /// TODO guanchong 更换购物车跳转链接
        if (model.remark == null) {
          break;
        }
        Map<String, dynamic> remarkJsonMap = json.decode(model.remark!);
        var decodeCustomerId =
            (remarkJsonMap["data"] as Map<String, dynamic>)["customerId"];
        var decodeMerchantId =
        (remarkJsonMap["data"] as Map<String, dynamic>)["merchantId"];
        if (decodeCustomerId is String&&decodeMerchantId is String) {
          jumpCustomerPageByCustomerId(decodeCustomerId).then((value) {
            String router =
                "/goods_recommend_page?merchantId=${decodeMerchantId}&customerId=${decodeCustomerId}&selectedIndex=0";
            router = Uri.encodeFull(router);
            XYYContainer.open(router);
          });
        }
        break;
      case "6":
        // 跳转客户详情
        jumpCustomerPageByCustomerId(model.remark);
        break;
      case "7":
        // 跳转客户详情
        jumpCustomerPageByCustomerId(model.remark);
        break;
      default:
        break;
    }
    // 请求消息已读
    if (!model.customShowReadStatus()) {
      this.requestMessageRead(model);
    }
  }

  /// 设置消息已读
  void requestMessageRead(MessageListData model) async {
    String requestUrl = "/dynamicMessage/list";
    Map<String, dynamic> requestParam = {"id": model.id};
    switch (widget.messageType ?? "1") {
      case "1": //订单卡单
        requestUrl = "/taskMessage/read";
        break;
      case "2": //资质审核
        requestUrl = "/license/message/read";
        break;
      case "3": //分配客户
        requestUrl = "/taskMessage/read";
        requestParam["type"] = "0";
        break;
      case "4": //分享动态
        requestUrl = "/taskMessage/read";
        requestParam["type"] = "1";
        break;
      case "5": //商品加购
        requestUrl = "/taskMessage/read";
        break;
      case "6": //门店开业提醒
        requestUrl = "/taskMessage/read";
        break;
      case "7": // 商品集掉落预警
        requestUrl = "skuCollect/message/read";
        break;
      default:
    }
    var result =
        await Network<NetworkBaseModel>(NetworkBaseModel()).requestData(
      requestUrl,
      method: RequestMethod.GET,
      parameters: requestParam,
    );
    if (result.isSuccess != null) {
      if (result.isSuccess! && mounted) {
        model.settingReadStatus();
        XYYContainer.bridgeCall("event_bus",
            parameters: {'code': MESSAGE_RELAD_UNREAD});
        setState(() {});
      }
    }
  }

  /// Refresh
  refreshData() async {
    this.page = 0;
    this.requestListData();
  }

  /// LoadMore
  loadMoreData() async {
    this.requestListData();
  }

  void requestListData() async {
    String requestUrl = "/dynamicMessage/list";
    Map<String, dynamic> requestParam = {
      "offset": this.page,
      "limit": 10,
    };
    switch (widget.messageType ?? "1") {
      case "1": //订单卡单
        requestUrl = "/license/message/licenseBlockList";
        break;
      case "2": //资质审核
        requestUrl = "/license/message/list";
        break;
      case "3": //分配客户
        requestUrl = "/dynamicMessage/list";
        requestParam["type"] = "0";
        break;
      case "4": //分享动态
        requestUrl = "/dynamicMessage/list";
        requestParam["type"] = "1";
        break;
      case "5": //商品加购
        requestUrl = "/taskMessage/listV2";
        break;
      case "6": //分享动态
        requestUrl = "message/newPoiList";
        requestParam["messageType"] = "6";
        break;
      case "7": //商品集掉落预警
        requestUrl = "skuCollect/message/list";
        break;
      default:
    }

    var result = await Network<MessageRootData>(MessageRootData()).requestData(
      requestUrl,
      method: RequestMethod.GET,
      parameters: requestParam,
    );
    if (mounted) {
      if (result.isSuccess == true) {
        EasyLoading.dismiss();

        if (this.page == 0) {
          this.dataSource = [];
        }
        var source = result.rows;
        var currentSource = this.dataSource;
        if (source != null) {
          // 设置消息类型
          source.forEach((element) {
            element.customType = widget.messageType;
          });
          currentSource.addAll(source);
          this.page += 1;
        }
        setState(() {
          this.dataSource = currentSource;
        });
      } else {
        XYYContainer.toastChannel.toast(result.msg ?? "网络链接异常，请稍后重试！");
      }
      _controller.finishRefresh();
      _controller.finishLoad(noMore: result.lastPage ?? false);
    }
  }
}
