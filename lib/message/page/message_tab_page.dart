import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_notification_list_data.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_service_list_data.dart';
import 'package:XyyBeanSproutsFlutter/message/widget/message_notification_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/message/widget/message_service_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_test/flutter_test.dart';

class MessageTabPage extends BasePage {
  final MessageTabType type;
  final ValueNotifier<int> dotController;

  MessageTabPage(this.type, this.dotController);

  @override
  BaseState<StatefulWidget> initState() {
    return MessageTabPageState();
  }
}

class MessageTabPageState extends BaseState<MessageTabPage> {
  bool isShowTips = false;

  PageState pageState = PageState.Normal;

  int page = 0;

  bool isLastPage = true;

  EasyRefreshController _refreshController = EasyRefreshController();

  List<dynamic>? dataSource;

  String? imageHost;

  @override
  void onCreate() {
    super.onCreate();
    showLoadingDialog();
    requestInterface();
    requestListData(true);
    checkNotificationSetting();
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  void checkNotificationSetting() {
    XYYContainer.bridgeCall("check_notification_setting").then((value) {
      setState(() {
        isShowTips = value == false;
      });
    });
  }

  void requestInterface() {
    XYYContainer.bridgeCall('app_host').then((result) {
      if (result is Map) {
        this.imageHost = result['interface'] ?? "";
        setState(() {});
      }
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [buildTipsWidget(), buildListWidget()],
      ),
    );
  }

  Widget buildListWidget() {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(10),
        child: EasyRefresh(
          controller: _refreshController,
          onRefresh: () async {
            return await requestListData(true);
          },
          onLoad: isLastPage && widget.type == MessageTabType.Notification
              ? null
              : () async {
                  return await requestListData(false);
                },
          child: ListView.builder(
            itemCount: this.dataSource?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              var model = this.dataSource?[index];
              if (model == null) {
                return Container();
              }
              switch (widget.type) {
                case MessageTabType.Notification:
                  if (model is MessageNotificationItemData) {
                    return GestureDetector(
                      onTap: () {
                        if(model.messageType == 8){
                          Navigator.of(context)
                              .pushNamed(
                              "/order_list_cwy_page")
                              .then((value) {
                            widget.dotController.value =
                                DateTime.now().millisecond;
                          });
                        }else{
                          Navigator.of(context)
                              .pushNamed(
                              "/message_list_page?messageType=${model.messageType}" +
                                  "&messageTitle=${Uri.encodeFull(model.messageTitle)}")
                              .then((value) {
                            widget.dotController.value =
                                DateTime.now().millisecond;
                          });
                        }
                      },
                      behavior: HitTestBehavior.opaque,
                      child:
                          MessageNotificationItemWidget(model, imageHost ?? ""),
                    );
                  }
                  break;
                case MessageTabType.Service:
                  if (model is MessageServiceItemData) {
                    //群聊
                    var paramsOrigin = model.redirectUrl.toString().split('?')[1].split('&');
                    Map<String, dynamic> paramMaps = {
                      'chatType': '',
                      'userid': '',
                      'dialogUid': '',
                      'groupDialogName': '',
                    };
                    paramsOrigin.forEach((value) { 
                      var params = value.split('=');
                      if (params[0] == 'chatType')
                        paramMaps['chatType'] = params[1];
                      if (params[0] == 'userid')
                        paramMaps['userid'] = params[1];
                      if (params[0] == 'dialogUid')
                        paramMaps['dialogUid'] = params[1];
                      if (params[0] == 'groupDialogName')
                        paramMaps['groupDialogName'] = params[1];
                    });
                    if (paramMaps['chatType'] == '2' && paramMaps['userid'] != paramMaps['dialogUid']) {
                      //群聊从redirectUrl中取数据
                      //群里名称
                      model.keFuName = paramMaps['groupDialogName'];
                      //群聊头像
                      model.keFuAvatar = 'https://files.ybm100.com/B2BCOS/Kf/group.png';
                    }
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        String routerPath =
                            "xyy://crm-app.ybm100.com/crm/web_view?url=${Uri.encodeComponent("${model.redirectUrl}")}";
                        XYYContainer.open(routerPath, callback: (resultData) {
                          widget.dotController.value =
                              DateTime.now().millisecond;
                        });
                      },
                      child: MessageServiceItemWidget(model, imageHost ?? ""),
                    );
                  }
                  break;
              }
              return Container();
            },
          ),
          emptyWidget: this.getEmptyWidget(),
        ),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog(msg: "加载中...");
          requestListData(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log('didChangeAppLifecycleState');
    //此处可以拓展 是不是从前台回到后台
    if (state == AppLifecycleState.resumed) {
      //on resume
      checkNotificationSetting();
    }
    super.didChangeAppLifecycleState(state);
  }

  Widget buildTipsWidget() {
    return Visibility(
      visible: isShowTips,
      child: Container(
        height: 40,
        color: const Color(0xfffff7ef),
        child: Row(
          children: [
            SizedBox(
              width: 15,
            ),
            Expanded(
                child: Text(
              "打开系统通知，随时接收重要消息",
              style: TextStyle(
                  fontSize: 13,
                  color: const Color(0xff99664d),
                  fontWeight: FontWeight.normal),
            )),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                XYYContainer.bridgeCall("open_notification_setting");
              },
              child: Container(
                height: 23,
                alignment: Alignment.center,
                width: 44,
                margin: EdgeInsets.symmetric(horizontal: 10),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: const Color(0xFF99664D)),
                child: Text(
                  "开启",
                  style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.normal,
                      color: Colors.white),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  isShowTips = false;
                });
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                child: Image.asset(
                  "assets/images/message/message_notification_close.png",
                  width: 15,
                  height: 15,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> requestListData(bool isRefresh) async {
    switch (widget.type) {
      case MessageTabType.Notification:
        return await requestNotificationListData();
      case MessageTabType.Service:
        return await requestServiceListData(isRefresh);
    }
  }

  Future<void> requestNotificationListData() async {
    var result = await NetworkV2<MessageNotificationListData>(
            MessageNotificationListData())
        .requestDataV2("message/queryAllMessages", method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var value = result.getData();
        if (value != null) {
          dataSource = value.rows;
          pageState = PageState.Normal;
        } else {
          pageState = PageState.Empty;
        }
      } else {
        pageState = PageState.Error;
      }
      _refreshController.finishRefresh();
      setState(() {});
    }
  }

  Future<void> requestServiceListData(bool isRefresh) async {
    var params = {};
    params["limit"] = 20;
    if (isRefresh) {
      params["offset"] = 0;
    } else {
      params["offset"] = page + 1;
    }
    var value =
        await NetworkV2<MessageServiceListData>(MessageServiceListData())
            .requestDataV2("im/list",
                parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _refreshController.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          page++;
          var result = value.getData();
          if (result != null) {
            isLastPage = result.isLastPage;
            if (result.list?.isNotEmpty == true) {
              if (isRefresh) {
                dataSource = result.list!;
              } else {
                if (dataSource == null) {
                  dataSource = [];
                }
                dataSource?.addAll(result.list!);
              }
            }
            pageState = PageState.Normal;
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
        _refreshController.finishRefresh();
        _refreshController.finishLoad();
      });
    }
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  bool isSubPage() {
    return true;
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}

enum MessageTabType { Notification, Service }
