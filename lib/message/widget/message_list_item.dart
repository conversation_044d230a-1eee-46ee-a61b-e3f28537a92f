import 'package:XyyBeanSproutsFlutter/message/data/message_list_data.dart';
import 'package:flutter/material.dart';

class MessageListItem extends StatelessWidget {
  final MessageListData? model;

  MessageListItem({this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 10, 15, 0),
      color: Color(0xFFF7F7F8),
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
          color: Color(0xFFFFFFFF),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            getTitleWidget(),
            SizedBox(height: 10),
            Text(
              model?.customContent() ?? "",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF14141A),
              ),
            ),
            getMoreWidget(),
          ],
        ),
      ),
    );
  }

  Widget getTitleWidget() {
    return Row(
      children: [
        Expanded(
          child: Text(
            model?.customTitle() ?? "",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF676773),
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.only(right: 5),
          child: Text(
            model?.customTimeStr() ?? "",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF9494A6),
            ),
          ),
        ),
      ],
    );
  }

  Widget getMoreWidget() {
    return Offstage(
      offstage: !(model?.customShowDetail() ?? false),
      child: Container(
        margin: EdgeInsets.only(top: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '去看看',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF00B377),
              ),
            ),
            Image.asset(
              'assets/images/message/message_more_arrow.png',
              width: 10,
              height: 10,
            ),
            Spacer(),
            unReadRedWidget(),
          ],
        ),
      ),
    );
  }

  Widget unReadRedWidget() {
    return Offstage(
      offstage: (model?.customShowReadStatus() ?? true),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFF2121),
          borderRadius: BorderRadius.circular(5),
        ),
        width: 10,
        height: 10,
      ),
    );
  }
}
