import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_notification_list_data.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';

class MessageNotificationItemWidget extends StatelessWidget {
  final MessageNotificationItemData itemData;
  final String imageHost;

  MessageNotificationItemWidget(this.itemData, this.imageHost);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      height: 78,
      child: Row(
        children: [
          Stack(
            children: [
              ImageWidget(
                url: imageHost + (itemData.iconUrl?.toString() ?? ""),
                w: 48,
                h: 48,
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Visibility(
                  visible: itemData.unReadCount > 0 &&
                      itemData.messageType != 3 &&
                      itemData.messageType != 4,
                  child: Container(
                    height: 10,
                    width: 10,
                    decoration: BoxDecoration(
                        border: Border.all(width: 1, color: Colors.white),
                        color: Colors.red,
                        shape: BoxShape.circle),
                  ),
                ),
              )
            ],
          ),
          SizedBox(
            width: 15,
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        child: Text(
                          itemData.messageTitle?.toString() ?? "--",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: TextStyle(
                              color: const Color(0xff292933),
                              fontWeight: FontWeight.normal,
                              fontSize: 17),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      getFormatDate(),
                      style: TextStyle(
                          color: const Color(0xff9494a6),
                          fontWeight: FontWeight.normal,
                          fontSize: 12),
                    )
                  ],
                ),
                Expanded(child: Container()),
                Text(
                  itemData.content?.toString() ?? "--",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: const Color(0xff9494a6),
                      fontSize: 14,
                      fontWeight: FontWeight.normal),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  String getFormatDate() {
    return formatDate(
      DateTime.fromMillisecondsSinceEpoch(itemData.createTime ?? 0),
      [yyyy, '-', mm, '-', dd],
    );
  }
}
