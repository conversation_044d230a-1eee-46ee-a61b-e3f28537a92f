import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/message/data/message_service_list_data.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';

class MessageServiceItemWidget extends StatelessWidget {
  final MessageServiceItemData itemData;
  final String imageHost;

  MessageServiceItemWidget(this.itemData, this.imageHost);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      height: 78,
      color: Colors.white,
      child: Row(
        children: [
          Stack(
            children: [
              ImageWidget(
                url: itemData.keFuAvatar?.toString() ?? "",
                w: 48,
                h: 48,
              ),
              Visibility(
                visible: itemData.unReadMsgNum > 0,
                child: Container(
                  height: 10,
                  width: 10,
                  decoration:
                      BoxDecoration(border: Border.all(width: 1, color: Colors.white),color: Colors.red, shape: BoxShape.circle),
                ),
              )
            ],
          ),
          SizedBox(
            width: 15,
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        itemData.keFuName?.toString() ?? "--",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                            color: const Color(0xff292933),
                            fontWeight: FontWeight.normal,
                            fontSize: 17),
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      getFormatDate(),
                      style: TextStyle(
                          color: const Color(0xff9494a6),
                          fontWeight: FontWeight.normal,
                          fontSize: 12),
                    )
                  ],
                ),
                Expanded(child: Container()),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    getContentText(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        color: const Color(0xff9494a6),
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  String getContentText() {
    var contentText = itemData.lastMsg?.content?.toString() ?? "--";
    print("guan getContentText ${contentText}");
    String result = "";
    if (contentText.contains("<img ") &&
        !contentText.contains("msg-card-file")) {
      result = "图片消息";
    } else if (contentText.contains("<video ")) {
      result = "视频消息";
    } else if (contentText.contains("msg-card-file")) {
      result = "文件消息";
    } else {
      result = contentText.replaceAll(RegExp("<[^>]+>"), "");
    }
    return result;
  }

  bool isSameDay(DateTime dateTime1, DateTime? dateTime2) {
    return dateTime2 != null &&
        dateTime1.year == dateTime2.year &&
        dateTime1.month == dateTime2.month &&
        dateTime1.day == dateTime2.day;
  }

  String getFormatDate() {
    var dateTime =
        DateTime.fromMillisecondsSinceEpoch(itemData.lastMsg?.createTime ?? 0);
    if (isSameDay(DateTime.now(), dateTime)) {
      return formatDate(
        DateTime.fromMillisecondsSinceEpoch(itemData.lastMsg?.createTime ?? 0),
        [HH, ':', nn, ':', ss],
      );
    } else {
      return formatDate(
        DateTime.fromMillisecondsSinceEpoch(itemData.lastMsg?.createTime ?? 0),
        [yyyy, '-', mm, '-', dd],
      );
    }
  }
}
