import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_public_basic_data.g.dart';

@JsonSerializable()
class CustomerPublicBasicData extends BaseModelV2 {
  dynamic currentLevel;
  dynamic currentLevelName;
  dynamic auditTimeValue;
  dynamic auditKey;

  List<CustomerPublicBasicContactData>? contactList;
  CustomerPublicBasicMerchantData? merchant;
  CustomerPublicBasicInfoData? basicInfo;

  List<CustomerDetailBasicEntry> get customerInfoEntrys {
    List<CustomerDetailBasicEntry> list = [];
    // list.add(CustomerDetailBasicEntry(
    //   title: "门店ID",
    //   content: "${this.merchant?.poiId ?? '-'}",
    // ));

    int createTime = this.merchant?.createTime ?? 0;
    String registerTime = "-";
    if (createTime > 0) {
      registerTime = formatDate(
        DateTime.fromMillisecondsSinceEpoch(createTime),
        [yyyy, '.', mm, '.', dd, ' ', HH, ':', nn, ':', ss],
      );
    }
    list.add(CustomerDetailBasicEntry(
      title: "注册时间",
      content: registerTime,
    ));

    list.add(CustomerDetailBasicEntry(
      title: "注册账号",
      content: "${this.merchant?.nickname ?? '-'}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "注册手机号",
      content: "${this.merchant?.mobile ?? '-'}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "客户注册类型",
      content: "${this.merchant?.customerTypeName ?? '-'}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "客户状态",
      content: "${this.merchant?.statusName ?? '-'}",
    ));

    Color textColor = Color(0xFF333333);
    int lincenseStatus = this.merchant?.licenseStatus ?? -1;
    switch (lincenseStatus) {
      case 1:
      case 3:
      case 5:
      case 6:
        textColor = Color(0xFFFE3D3D);
        break;
      case 2:
      case 4:
        textColor = Color(0xFF35C561);
        break;
      default:
        textColor = Color(0xFF333333);
    }
    list.add(CustomerDetailBasicEntry(
      title: (this.auditKey != null && this.auditKey.length > 0)
          ? this.auditKey
          : "资质状态",
      content: "${this.merchant?.licenseText ?? '-'}",
      contentTextColor: textColor,
    ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "一审通过时长",
    //   content: "${this.auditTimeValue ?? '-'}",
    // ));

    String bds = this
            .merchant
            ?.bdInfos
            ?.map((e) => e.realName ?? "-")
            .toList()
            .join("、") ??
        "-";
    list.add(CustomerDetailBasicEntry(
      title: "跟进BD",
      content: bds,
    ));

    list.add(CustomerDetailBasicEntry(
      title: "当前级别",
      content: "${this.currentLevelName ?? '-'}",
    ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "客户编号",
    //   content: "${this.merchant?.id ?? '-'}",
    // ));

    list.add(CustomerDetailBasicEntry(
      title: "收货地址",
      content: "${this.merchant?.receivingAddress ?? '-'}",
    ));
    if (this.merchant?.merchantType != 1) {
      list.add(CustomerDetailBasicEntry(
        title: "门店工商登记时间",
        content: "${this.merchant?.poiRegisterTime ?? '-'}",
      ));
    }

    list.add(CustomerDetailBasicEntry(
      title: "门店电话",
      content: "${this.merchant?.poiContactMobile ?? '-'}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "门店手机号",
      content: "${this.merchant?.poiPhone ?? '-'}",
    ));

    return list;
  }

  List<CustomerDetailBasicEntry> get customerBasicInfoEntrys {
    List<CustomerDetailBasicEntry> list = [];

    // String isMedicalInsurance = "-";
    // if (this.basicInfo?.medicalInsurance != null) {
    //   isMedicalInsurance =
    //       "${this.basicInfo?.medicalInsurance}" == "1" ? "是" : "否";
    // }
    // list.add(CustomerDetailBasicEntry(
    //   title: "是否可用医保",
    //   content: isMedicalInsurance,
    // ));

    list.add(CustomerDetailBasicEntry(
      title: "客户面积(平方米)",
      content: "${this.basicInfo?.areaSize ?? "-"}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "客户成员数(人)",
      content: "${this.basicInfo?.clerkNum ?? "-"}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "周边环境",
      content: "${this.basicInfo?.aroundEnvName ?? "-"}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "主要消费人群",
      content: "${this.basicInfo?.buyersTypeName ?? "-"}",
    ));

    list.add(CustomerDetailBasicEntry(
      title: "客户流量(人/天)",
      content: "${this.basicInfo?.buyersAmountText ?? "-"}",
    ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "客户需求SKU数",
    //   content: "${this.basicInfo?.buySkusName ?? "-"}",
    // ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "主要消费结构",
    //   content: "${this.basicInfo?.mainlyConsumeMedTypesName ?? "-"}",
    // ));

    // String isNeedPullSales = "-";
    // if (this.basicInfo?.needPullSales != null) {
    //   isNeedPullSales = "${this.basicInfo?.needPullSales}" == "1" ? "是" : "否";
    // }
    // list.add(CustomerDetailBasicEntry(
    //   title: "是否需要动销",
    //   content: isNeedPullSales,
    // ));

    // String needMerchantDiagnose = "-";
    // if (this.basicInfo?.needMerchantDiagnose != null) {
    //   needMerchantDiagnose =
    //       "${this.basicInfo?.needMerchantDiagnose}" == "1" ? "是" : "否";
    // }
    // list.add(CustomerDetailBasicEntry(
    //   title: "是否需要门店诊断",
    //   content: needMerchantDiagnose,
    // ));

    // String isNeedClerkTrains = "-";
    // if (this.basicInfo?.needClerkTrains != null) {
    //   isNeedClerkTrains =
    //       "${this.basicInfo?.needClerkTrains}" == "1" ? "是" : "否";
    // }
    // list.add(CustomerDetailBasicEntry(
    //   title: "是否需要店员培训",
    //   content: isNeedClerkTrains,
    // ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "月销售额",
    //   content: "${this.basicInfo?.monthlySales ?? "-"}",
    // ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "缺失品种",
    //   content: "${this.basicInfo?.shortOfTypes ?? "-"}",
    // ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "核心供应商",
    //   content: "${this.basicInfo?.purchaseWay ?? "-"}",
    // ));

    // list.add(CustomerDetailBasicEntry(
    //   title: "客户需求",
    //   content: "${this.basicInfo?.merchantDemand ?? "-"}",
    // ));

    return list;
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerPublicBasicDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerPublicBasicDataToJson(this);
  }
}

@JsonSerializable()
class CustomerPublicBasicContactData extends BaseModelV2 {
  dynamic id;
  dynamic merchantId;
  dynamic poiId;
  dynamic contactName;
  dynamic contactMobile;
  dynamic createTime;
  dynamic updateTime;
  dynamic merchantName;
  dynamic disabled;
  dynamic createType;
  dynamic contactJobName;
  dynamic isEffective;

  CustomerPublicBasicContactData();

  factory CustomerPublicBasicContactData.fromJson(Map<String, dynamic> json) =>
      _$CustomerPublicBasicContactDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerPublicBasicContactDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerPublicBasicContactDataToJson(this);
  }
}

@JsonSerializable()
class CustomerPublicBasicMerchantData extends BaseModelV2 {
  dynamic id;
  dynamic syncNo;
  dynamic realName;
  dynamic mobile;
  dynamic nickname;
  dynamic status;
  dynamic createTime;
  dynamic authcode;
  dynamic licenseStatus;
  dynamic licenseText;
  dynamic sysUserId;
  dynamic sysUserName;
  dynamic jobNumber;
  dynamic merchantType;
  dynamic registerSource;
  dynamic activeSource;
  dynamic activeTime;
  dynamic businessType;
  dynamic businessTypeName;
  dynamic defaultAddress;
  dynamic statusName;
  dynamic identity;
  dynamic loginName;
  dynamic sysRealName;
  dynamic sysJobNumber;
  dynamic customerType;
  dynamic customerTypeName;
  dynamic kfPhone;
  dynamic bdCount;
  dynamic gjCount;
  dynamic receivingAddress;
  dynamic poiId;
  dynamic poiRegisterTime; // 工商注册时间

  dynamic poiContactMobile; // 门店电话
  dynamic poiPhone; // 门店手机号

  List<CustomerPublicBasicBdInfoData>? bdInfos;

  CustomerPublicBasicMerchantData();

  factory CustomerPublicBasicMerchantData.fromJson(Map<String, dynamic> json) =>
      _$CustomerPublicBasicMerchantDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerPublicBasicMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerPublicBasicMerchantDataToJson(this);
  }
}

@JsonSerializable()
class CustomerPublicBasicBdInfoData extends BaseModelV2 {
  dynamic jobNumber;
  dynamic realName;
  dynamic username;

  CustomerPublicBasicBdInfoData();

  factory CustomerPublicBasicBdInfoData.fromJson(Map<String, dynamic> json) =>
      _$CustomerPublicBasicBdInfoDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerPublicBasicBdInfoDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerPublicBasicBdInfoDataToJson(this);
  }
}

@JsonSerializable()
class CustomerPublicBasicInfoData extends BaseModelV2 {
  dynamic id;
  dynamic fkTbMerchantId;
  dynamic poiId;
  dynamic updateTime;
  dynamic currentLevel;
  dynamic areaSize;
  dynamic sku;
  dynamic purchaseWay;
  dynamic clerkNum;
  dynamic merchantDemand;
  dynamic isBlackRemark;
  dynamic monthlySales;
  dynamic medicalInsurance;
  dynamic monthBuyAmt;
  dynamic registerAmt;
  dynamic aroundEnv;
  dynamic buyFrequency;
  dynamic buySkus;
  dynamic frameVarieties;
  dynamic buyersType;
  dynamic needClerkTrains;
  dynamic needMerchantDiagnose;
  dynamic needPullSales;
  dynamic shortOfTypes;
  dynamic mainlyConsumeMedTypes;
  dynamic buyersAmount;
  dynamic aroundEnvName;
  dynamic buyersTypeName;
  dynamic buySkusName;
  dynamic mainlyConsumeMedTypesName;
  dynamic buyersAmountText;
  dynamic delete;

  CustomerPublicBasicInfoData();

  factory CustomerPublicBasicInfoData.fromJson(Map<String, dynamic> json) =>
      _$CustomerPublicBasicInfoDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerPublicBasicInfoDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerPublicBasicInfoDataToJson(this);
  }
}

class CustomerDetailBasicEntry {
  dynamic title;
  dynamic content;
  Color? contentTextColor;
  bool isCanCopy = false;

  CustomerDetailBasicEntry({
    this.title,
    this.content,
    this.contentTextColor,
    this.isCanCopy = false,
  });
}
