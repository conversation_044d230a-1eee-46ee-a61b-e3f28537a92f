// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_public_basic_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerPublicBasicData _$CustomerPublicBasicDataFromJson(
    Map<String, dynamic> json) {
  return CustomerPublicBasicData()
    ..currentLevel = json['currentLevel']
    ..currentLevelName = json['currentLevelName']
    ..auditTimeValue = json['auditTimeValue']
    ..auditKey = json['auditKey']
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map((e) =>
            CustomerPublicBasicContactData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..merchant = json['merchant'] == null
        ? null
        : CustomerPublicBasicMerchantData.fromJson(
            json['merchant'] as Map<String, dynamic>)
    ..basicInfo = json['basicInfo'] == null
        ? null
        : CustomerPublicBasicInfoData.fromJson(
            json['basicInfo'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerPublicBasicDataToJson(
        CustomerPublicBasicData instance) =>
    <String, dynamic>{
      'currentLevel': instance.currentLevel,
      'currentLevelName': instance.currentLevelName,
      'auditTimeValue': instance.auditTimeValue,
      'auditKey': instance.auditKey,
      'contactList': instance.contactList,
      'merchant': instance.merchant,
      'basicInfo': instance.basicInfo,
    };

CustomerPublicBasicContactData _$CustomerPublicBasicContactDataFromJson(
    Map<String, dynamic> json) {
  return CustomerPublicBasicContactData()
    ..id = json['id']
    ..merchantId = json['merchantId']
    ..poiId = json['poiId']
    ..contactName = json['contactName']
    ..contactMobile = json['contactMobile']
    ..createTime = json['createTime']
    ..updateTime = json['updateTime']
    ..merchantName = json['merchantName']
    ..disabled = json['disabled']
    ..createType = json['createType']
    ..contactJobName = json['contactJobName']
    ..isEffective = json['isEffective'];
}

Map<String, dynamic> _$CustomerPublicBasicContactDataToJson(
        CustomerPublicBasicContactData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'merchantId': instance.merchantId,
      'poiId': instance.poiId,
      'contactName': instance.contactName,
      'contactMobile': instance.contactMobile,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'merchantName': instance.merchantName,
      'disabled': instance.disabled,
      'createType': instance.createType,
      'contactJobName': instance.contactJobName,
      'isEffective': instance.isEffective,
    };

CustomerPublicBasicMerchantData _$CustomerPublicBasicMerchantDataFromJson(
    Map<String, dynamic> json) {
  return CustomerPublicBasicMerchantData()
    ..id = json['id']
    ..syncNo = json['syncNo']
    ..realName = json['realName']
    ..mobile = json['mobile']
    ..nickname = json['nickname']
    ..status = json['status']
    ..createTime = json['createTime']
    ..authcode = json['authcode']
    ..licenseStatus = json['licenseStatus']
    ..licenseText = json['licenseText']
    ..sysUserId = json['sysUserId']
    ..sysUserName = json['sysUserName']
    ..jobNumber = json['jobNumber']
    ..merchantType = json['merchantType']
    ..registerSource = json['registerSource']
    ..activeSource = json['activeSource']
    ..activeTime = json['activeTime']
    ..businessType = json['businessType']
    ..businessTypeName = json['businessTypeName']
    ..defaultAddress = json['defaultAddress']
    ..statusName = json['statusName']
    ..identity = json['identity']
    ..loginName = json['loginName']
    ..sysRealName = json['sysRealName']
    ..sysJobNumber = json['sysJobNumber']
    ..customerType = json['customerType']
    ..customerTypeName = json['customerTypeName']
    ..kfPhone = json['kfPhone']
    ..bdCount = json['bdCount']
    ..gjCount = json['gjCount']
    ..receivingAddress = json['receivingAddress']
    ..poiId = json['poiId']
    ..poiRegisterTime = json['poiRegisterTime']
    ..poiContactMobile = json['poiContactMobile']
    ..poiPhone = json['poiPhone']
    ..bdInfos = (json['bdInfos'] as List<dynamic>?)
        ?.map((e) =>
            CustomerPublicBasicBdInfoData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CustomerPublicBasicMerchantDataToJson(
        CustomerPublicBasicMerchantData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'syncNo': instance.syncNo,
      'realName': instance.realName,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'status': instance.status,
      'createTime': instance.createTime,
      'authcode': instance.authcode,
      'licenseStatus': instance.licenseStatus,
      'licenseText': instance.licenseText,
      'sysUserId': instance.sysUserId,
      'sysUserName': instance.sysUserName,
      'jobNumber': instance.jobNumber,
      'merchantType': instance.merchantType,
      'registerSource': instance.registerSource,
      'activeSource': instance.activeSource,
      'activeTime': instance.activeTime,
      'businessType': instance.businessType,
      'businessTypeName': instance.businessTypeName,
      'defaultAddress': instance.defaultAddress,
      'statusName': instance.statusName,
      'identity': instance.identity,
      'loginName': instance.loginName,
      'sysRealName': instance.sysRealName,
      'sysJobNumber': instance.sysJobNumber,
      'customerType': instance.customerType,
      'customerTypeName': instance.customerTypeName,
      'kfPhone': instance.kfPhone,
      'bdCount': instance.bdCount,
      'gjCount': instance.gjCount,
      'receivingAddress': instance.receivingAddress,
      'poiId': instance.poiId,
      'poiRegisterTime': instance.poiRegisterTime,
      'poiContactMobile': instance.poiContactMobile,
      'poiPhone': instance.poiPhone,
      'bdInfos': instance.bdInfos,
    };

CustomerPublicBasicBdInfoData _$CustomerPublicBasicBdInfoDataFromJson(
    Map<String, dynamic> json) {
  return CustomerPublicBasicBdInfoData()
    ..jobNumber = json['jobNumber']
    ..realName = json['realName']
    ..username = json['username'];
}

Map<String, dynamic> _$CustomerPublicBasicBdInfoDataToJson(
        CustomerPublicBasicBdInfoData instance) =>
    <String, dynamic>{
      'jobNumber': instance.jobNumber,
      'realName': instance.realName,
      'username': instance.username,
    };

CustomerPublicBasicInfoData _$CustomerPublicBasicInfoDataFromJson(
    Map<String, dynamic> json) {
  return CustomerPublicBasicInfoData()
    ..id = json['id']
    ..fkTbMerchantId = json['fkTbMerchantId']
    ..poiId = json['poiId']
    ..updateTime = json['updateTime']
    ..currentLevel = json['currentLevel']
    ..areaSize = json['areaSize']
    ..sku = json['sku']
    ..purchaseWay = json['purchaseWay']
    ..clerkNum = json['clerkNum']
    ..merchantDemand = json['merchantDemand']
    ..isBlackRemark = json['isBlackRemark']
    ..monthlySales = json['monthlySales']
    ..medicalInsurance = json['medicalInsurance']
    ..monthBuyAmt = json['monthBuyAmt']
    ..registerAmt = json['registerAmt']
    ..aroundEnv = json['aroundEnv']
    ..buyFrequency = json['buyFrequency']
    ..buySkus = json['buySkus']
    ..frameVarieties = json['frameVarieties']
    ..buyersType = json['buyersType']
    ..needClerkTrains = json['needClerkTrains']
    ..needMerchantDiagnose = json['needMerchantDiagnose']
    ..needPullSales = json['needPullSales']
    ..shortOfTypes = json['shortOfTypes']
    ..mainlyConsumeMedTypes = json['mainlyConsumeMedTypes']
    ..buyersAmount = json['buyersAmount']
    ..aroundEnvName = json['aroundEnvName']
    ..buyersTypeName = json['buyersTypeName']
    ..buySkusName = json['buySkusName']
    ..mainlyConsumeMedTypesName = json['mainlyConsumeMedTypesName']
    ..buyersAmountText = json['buyersAmountText']
    ..delete = json['delete'];
}

Map<String, dynamic> _$CustomerPublicBasicInfoDataToJson(
        CustomerPublicBasicInfoData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fkTbMerchantId': instance.fkTbMerchantId,
      'poiId': instance.poiId,
      'updateTime': instance.updateTime,
      'currentLevel': instance.currentLevel,
      'areaSize': instance.areaSize,
      'sku': instance.sku,
      'purchaseWay': instance.purchaseWay,
      'clerkNum': instance.clerkNum,
      'merchantDemand': instance.merchantDemand,
      'isBlackRemark': instance.isBlackRemark,
      'monthlySales': instance.monthlySales,
      'medicalInsurance': instance.medicalInsurance,
      'monthBuyAmt': instance.monthBuyAmt,
      'registerAmt': instance.registerAmt,
      'aroundEnv': instance.aroundEnv,
      'buyFrequency': instance.buyFrequency,
      'buySkus': instance.buySkus,
      'frameVarieties': instance.frameVarieties,
      'buyersType': instance.buyersType,
      'needClerkTrains': instance.needClerkTrains,
      'needMerchantDiagnose': instance.needMerchantDiagnose,
      'needPullSales': instance.needPullSales,
      'shortOfTypes': instance.shortOfTypes,
      'mainlyConsumeMedTypes': instance.mainlyConsumeMedTypes,
      'buyersAmount': instance.buyersAmount,
      'aroundEnvName': instance.aroundEnvName,
      'buyersTypeName': instance.buyersTypeName,
      'buySkusName': instance.buySkusName,
      'mainlyConsumeMedTypesName': instance.mainlyConsumeMedTypesName,
      'buyersAmountText': instance.buyersAmountText,
      'delete': instance.delete,
    };
