import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/basic_info/data/customer_public_basic_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPublicBasicInfoPage extends BasePage {
  final dynamic shopId;

  CustomerPublicBasicInfoPage({this.shopId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPublicBasicInfoPageState();
  }
}

class CustomerPublicBasicInfoPageState
    extends BaseState<CustomerPublicBasicInfoPage> {
  CustomerPublicBasicData sourceData = CustomerPublicBasicData();

  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    this.requestBasicInfoData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      child: EasyRefresh(
        controller: _controller,
        onRefresh: () async {
          this.requestBasicInfoData();
        },
        child: ListView(
          children: [
            headerView(title: '客户信息'),
            contentView(entryDatas: sourceData.customerInfoEntrys),
            headerView(title: '经营状态'),
            contentView(entryDatas: sourceData.customerBasicInfoEntrys),
          ],
        ),
      ),
    );
  }

  Widget headerView({required String title}) {
    return Container(
      margin: EdgeInsets.only(top: 20, left: 15, bottom: 5),
      child: Text(
        title,
        style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
      ),
    );
  }

  Widget contentView({required List<CustomerDetailBasicEntry> entryDatas}) {
    return Container(
      padding: EdgeInsets.only(top: 5, bottom: 5),
      margin: EdgeInsets.only(left: 10, right: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Color(0xFFFFFFFF),
      ),
      child: Column(
        children:
            entryDatas.map((e) => CustomerBasicEntryItem(data: e)).toList(),
      ),
    );
  }

  @override
  String getTitleName() {
    return "基本信息";
  }

  void requestBasicInfoData() async {
    showLoadingDialog();
    var result =
        await NetworkV2<CustomerPublicBasicData>(CustomerPublicBasicData())
            .requestDataV2(
      'customer/private/basic',
      parameters: {'merchantId': widget.shopId},
    );
    dismissLoadingDialog();
    if (mounted) {
      _controller.finishRefresh();
      if (result.isSuccess == true) {
        CustomerPublicBasicData? data = result.getData();
        if (data != null) {
          this.sourceData = data;
        }
        setState(() {});
      } else {
        showToast(result.errorMsg ?? "获取基本信息异常");
      }
    }
  }
}

class CustomerBasicEntryItem extends StatelessWidget {
  final CustomerDetailBasicEntry data;

  CustomerBasicEntryItem({required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 145,
            child: Text(
              data.title,
              style: TextStyle(
                color: Color(0xFF8E8E93),
                fontSize: 15,
                height: 1.1,
              ),
            ),
          ),
          Expanded(
            child: Text(
              data.content,
              style: TextStyle(
                color: data.contentTextColor ?? Color(0xFF333333),
                fontSize: 15,
                height: 1.1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
