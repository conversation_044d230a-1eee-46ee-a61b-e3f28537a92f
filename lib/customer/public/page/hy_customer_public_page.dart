import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_item_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_param.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/hy_customer_public_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_area_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class HYCustomerPublicPage extends CustomerBasePage {
  final ValueNotifier<String?>? searchChangeText;
  final ValueNotifier<int>? searchType;

  HYCustomerPublicPage({this.searchChangeText, this.searchType});

  @override
  CustomerBasePageState initState() {
    return HYCustomerPublicPageState();
  }
}

class HYCustomerPublicPageState
    extends CustomerBasePageState<HYCustomerPublicPage> with EventBusObserver {
  /// 筛选参数
  YBMCustomerPublicParam paramModel = YBMCustomerPublicParam();

  /// 数据源
  List<YBMCustomerPublicItemModel> dataSource = [];

  /// 是否是 最后一页
  bool isLastPage = true;

  @override
  List<CommonDropConfigModel> get filterConfig => [
        CommonDropConfigModel(
          defaultTitle: "距离排序",
          selectTitle: '距离排序',
          paramKey: "sortType",
        ),
        CommonDropConfigModel(
          defaultTitle: "认领状态",
          paramKey: "customerStatus",
        ),
        CommonDropConfigModel(
          defaultTitle: "区域选择",
          paramKey: "selectArea",
        ),
      ];

  @override
  void initState() {
    /// 默认距离排序
    this.paramModel.sortType = '1';

    this.refreshList();
    this.addNotification();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 订阅事件
  void addNotification() {
    widget.searchChangeText?.addListener(() {
      this.paramModel.keyword = widget.searchChangeText?.value;
      this.refreshController.callRefresh();
    });
  }

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) async {
    switch (model.paramKey) {
      case "sortType":
        // 排序
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '距离排序', code: "1"),
                CustomerConditionModel(text: '最近新增', code: "2"),
              ],
              selectedCode: this.paramModel.sortType,
              selectAction: (value) {
                this.paramModel.sortType = "${value.code}";
                controller.setSelectText("${value.text}");
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "customerStatus":
        // 注册状态
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '全部', code: "0"),
                CustomerConditionModel(text: '已认领', code: "1"),
                CustomerConditionModel(text: '未认领', code: "2"),
              ],
              selectedCode: this.paramModel.customerStatus,
              selectAction: (value) {
                this.paramModel.customerStatus = "${value.code}";
                if (value.text == "全部") {
                  controller.setSelectText(null);
                } else {
                  controller.setSelectText("${value.text}");
                }
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "selectArea":
        // 新开业
        this.selectAreaAction(controller);

        /// 因为不支持异步调用，这里延迟处理一下收起
        Future.delayed(Duration(milliseconds: 100), () {
          controller.setIsOpen(false);
        });
        break;
      default:
    }
  }

  void selectAreaAction(DropButtonController controller) {
    String router;
    if (Platform.isAndroid) {
      router = 'xyy://crm-app.ybm100.com/select_area_v2?';
    } else {
      router = 'xyy://crm-app.ybm100.com/select_area?';
    }
    if (this.paramModel.cacheParams?.keys.contains("parentParam") == true) {
      router += "parentParam=" +
          json.encode(this.paramModel.cacheParams?["parentParam"]) +
          "&";
    }
    if (this.paramModel.cacheParams?.keys.contains("areaParam") == true) {
      router +=
          "areaParam=" + json.encode(this.paramModel.cacheParams?["areaParam"]);
    }
    router = Uri.encodeFull(router);
    XYYContainer.open(router, callback: (params) {
      if (params != null) {
        // 兼容Android container返回值处理
        if (params.keys.length == 0 ||
            (Platform.isAndroid && params.containsKey("reset"))) {
          this.paramModel.cacheParams?.remove("parentParam");
          this.paramModel.cacheParams?.remove("areaParam");
          this.paramModel.area = null;
          this.paramModel.cityCode = null;
          this.paramModel.provinceCode = null;

          // 重置了 区域
          controller.setSelectText(null);
        } else {
          this.paramModel.cacheParams?.addAll(params);

          SelectObjectAreaCallBackModel backModel =
              SelectObjectAreaCallBackModel.fromJsonMap(params);
          // 省份
          if (backModel.parentParam?.first.areaCode != null) {
            this.paramModel.provinceCode =
                backModel.parentParam?.first.areaCode;
          } else {
            this.paramModel.provinceCode = null;
          }
          // 城市
          if ((backModel.parentParam ?? []).length > 1) {
            if (backModel.parentParam?.last.areaCode !=
                this.paramModel.provinceCode) {
              this.paramModel.cityCode = backModel.parentParam?.last.areaCode;
            } else {
              this.paramModel.cityCode = null;
            }
          } else {
            this.paramModel.cityCode = null;
          }
          // 区域
          if (this.paramModel.cityCode != null &&
              (backModel.areaParam ?? []).length > 0) {
            var areaCode =
                backModel.areaParam?.map((e) => e.areaCode).join(',');
            if (areaCode != this.paramModel.cityCode) {
              this.paramModel.area = areaCode;
            }
          } else {
            this.paramModel.area = null;
          }

          // 选择了 区域
          controller.setSelectText('区域选择');
        }
      }
      this.refreshController.callRefresh();
    });
  }

  @override
  bool get isSearch => widget.searchChangeText != null;

  @override
  bool get isNoMore => this.isLastPage;

  @override
  int get itemCount => this.dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        var model = this.dataSource[index];
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
            this.jumpToDetail(model);
          },
          behavior: HitTestBehavior.opaque,
          child: HYCustomerPublicItem(
            model,
            hasLocation: this.hasLocation,
            serviceInterface: this.interface,
          ),
        );
      };

  /// 跳转客户详情
  void jumpToDetail(YBMCustomerPublicItemModel model) {
    String router =
        "xyy://crm-app.ybm100.com/customer/hy_public_detail?shopId=${model.id}&hyId=${model.hyId ?? ""}&receiveType=${model.receiveType}";
    router = Uri.encodeFull(router);
    XYYContainer.open(
      router,
      callback: (resultData) {
        // 认领成功后会调用
        if (resultData?.containsKey('claimed') == true) {
          // 发送通知
          this.eventBus.sendMessage(CustomerEventBusName.HY_PUBLIC_RECEIVE);
        }
      },
    );
  }

  @override
  Future<void> refreshList() async {
    this.page = 0;
    // 没有数据时 刷新增加loading
    if (this.dataSource.length == 0 && !this.isSearch) {
      showLoadingDialog();
    }
    // 每次刷新时 重新请求定位信息
    await this.requestLocation();
    // 请求列表数据
    this.requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  Future<void> requestLocation() async {
    var result = await XYYContainer.locationChannel.locate();
    this.paramModel.poiLatitude = result.latitude;
    this.paramModel.poiLongitude = result.longitude;
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return this.paramModel.poiLatitude?.isEmpty == false &&
        this.paramModel.poiLongitude?.isEmpty == false;
  }

  /// 请求公海列表数据
  void requestListData() async {
    var params = this.paramModel.toJson();
    params['limit'] = 10;
    params['offset'] = this.page;

    /// 共用药帮忙私海请求类， 处理keyWord
    if (params.containsKey('keyWord')) {
      if (widget.searchType?.value == 1) {
        // 搜索POI信息
        params['hyId'] = params['keyWord'];
        params.remove('keyWord');
      } else {
        // 搜索关键词
      }
    }

    var result = await NetworkV2<HYCusomerPublicModel>(HYCusomerPublicModel())
        .requestDataV2(
      'hyjk/openSeaList',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.pageData?.rows;
        if (rows != null) {
          if (this.page == 0) {
            this.dataSource = rows;
          } else {
            this.dataSource.addAll(rows);
          }
        } else {
          this.dataSource = [];
        }
        this.isLastPage = data?.pageData?.lastPage == true;
        this.page = this.isLastPage ? this.page : this.page + 1;
        setState(() {});
      }
    }
  }
}
