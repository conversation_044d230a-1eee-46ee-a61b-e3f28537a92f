import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/location/location_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_item_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_param.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/ybm_customer_public_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_map_entry.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_area_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/buildLocaRequest.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class YBMCustomerPublicPage extends CustomerBasePage {
  final ValueNotifier<String?>? searchChangeText;

  int? customerExtraType = 0;

  YBMCustomerPublicPage({this.searchChangeText, this.customerExtraType});

  @override
  CustomerBasePageState initState() {
    return YBMCustomerPublicPageState();
  }
}

class YBMCustomerPublicPageState
    extends CustomerBasePageState<YBMCustomerPublicPage> with EventBusObserver {
  /// 筛选参数
  YBMCustomerPublicParam paramModel = YBMCustomerPublicParam();

  /// 客户状态筛选数据源
  List<CustomerConditionModel> customerStatusCondition = [];

  /// 数据源
  List<YBMCustomerPublicItemModel> dataSource = [];

  /// 是否是 最后一页
  bool isLastPage = true;

  var sortTypeController = DropButtonController(
    model: DropButtonModel(
      normalText: "综合排序",
    ),
  );

  var customerStatusController = DropButtonController(
    model: DropButtonModel(
      normalText: "注册状态",
    ),
  );

  var poiRegisterController = DropButtonController(
    model: DropButtonModel(
      normalText: "全部",
    ),
  );

  @override
  List<CommonDropConfigModel> get filterConfig => [
    CommonDropConfigModel(
      defaultTitle: "综合排序",
      selectTitle: '综合排序',
      paramKey: "sortType",
      controller: sortTypeController
    ),
    CommonDropConfigModel(
      defaultTitle: widget.customerExtraType == 1?"已注册": "注册状态",
      paramKey: "customerStatus",
      controller: customerStatusController
    ),
    CommonDropConfigModel(
      defaultTitle: widget.customerExtraType == 2?"新开业门店": "全部",
      paramKey: "poiRegisterFlag",
      controller: poiRegisterController
    ),
  ];

  @override
  void initState() {
    setFilterTabText(widget.customerExtraType?? 0);
    setFilterType(widget.customerExtraType?? 0);
    if (widget.searchChangeText == null) {
      this.refreshList(type: 'init');
    }
    this.addNotification();
    super.initState();
    this.eventBus.addListener(
        observer: this,
        eventName: MainTabBarEventBusName.CHANGE_FILTER_POI_REGISTER,
        callback: (index) {
          setFilterType(index);
          setFilterTabText(index);
          setState(() {});
          this.refreshController.callRefresh();
        });
  }

  void setFilterType(int customerExtraType) {
    if (customerExtraType == 1) {
      //注册状态
      this.paramModel.sortType = "0";
      this.paramModel.customerStatus = "1";
      this.paramModel.poiRegisterFlag = "-1";
    } else if (customerExtraType == 2) {
      //开业
      this.paramModel.sortType = "0";
      this.paramModel.customerStatus = "-1";
      this.paramModel.poiRegisterFlag = "1";
    }
  }

  void setFilterTabText(int index) {
    if (index == 1) {
      sortTypeController.setSelectText("综合排序");
      customerStatusController.setSelectText("已注册");
      poiRegisterController.setSelectText("全部");
    } else if (index == 2) {
      sortTypeController.setSelectText("综合排序");
      customerStatusController.setSelectText("注册状态");
      poiRegisterController.setSelectText("新开业门店");
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        super.buildWidget(context),
        // YBMCustomerMapEntry.showEntry(context: context, isFromPrivate: false),
        YBMCustomerMapEntry.showDraggableEntry(isFromPrivate: false),
      ],
    );
  }

  @override
  void dispose() {
    eventBus.removeListener(observer: this);
    super.dispose();
  }

  // 订阅事件
  void addNotification() {
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_PUBLIC_FILTER,
        callback: (_) {
          this.jumpFilter();
        });
    widget.searchChangeText?.addListener(() {
      this.paramModel.keyword = widget.searchChangeText?.value;
      this.refreshController.callRefresh();
    });
  }

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) async {
    switch (model.paramKey) {
      case "sortType":
        // 排序
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '综合排序', code: "0"),
                CustomerConditionModel(text: '距离排序', code: "2"),
                CustomerConditionModel(text: '最近新增', code: "1"),
              ],
              selectedCode: this.paramModel.sortType,
              selectAction: (value) {
                this.paramModel.sortType = "${value.code}";
                controller.setSelectText("${value.text}");
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "customerStatus":
        // 注册状态
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: this.customerStatusCondition.length == 0
                  ? [
                      CustomerConditionModel(text: '全部', code: "-1"),
                      CustomerConditionModel(text: '已注册', code: "1"),
                      CustomerConditionModel(text: '未注册', code: "2"),
                      CustomerConditionModel(text: '已冻结', code: "3")
                    ]
                  : this.customerStatusCondition,
              selectedCode: this.paramModel.customerStatus,
              selectAction: (value) {
                this.paramModel.customerStatus = "${value.code}";
                if (value.text == "全部") {
                  controller.setSelectText(null);
                } else {
                  controller.setSelectText("${value.text}");
                }
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "poiRegisterFlag":
        // 新开业
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '全部', code: "-1"),
                CustomerConditionModel(text: '新开业门店', code: "1"),
              ],
              selectedCode: this.paramModel.poiRegisterFlag,
              selectAction: (value) {
                this.paramModel.poiRegisterFlag = "${value.code}";
                if (value.text == "全部") {
                  controller.setSelectText(null);
                } else {
                  controller.setSelectText("${value.text}");
                }

                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      default:
    }
  }

  @override
  bool get isSearch => widget.searchChangeText != null;

  @override
  bool get isNoMore => this.isLastPage;

  @override
  int get itemCount => this.dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        var model = this.dataSource[index];
        return GestureDetector(
          onTap: () {
            bool canJump = false;
            model.bindSkuCollect?.forEach((element) {
              if (element.receiveType?.toString() != "1") {
                canJump = true;
              }
            });
            if (!canJump) {
              showToast("客户无可认领商品集无法查看详情");
              return;
            }
            XYYContainer.open('/customer_public_detail?openSeaId=${model.id}',
                callback: (result) {
              if (result != null && result.containsKey('sku_collect_code')) {
                this.updateModelReceive(model, result['sku_collect_code']);
              }
            });
          },
          behavior: HitTestBehavior.opaque,
          child: YBMCustomerPublicItem(
            model,
            hasLocation: this.hasLocation,
            serviceInterface: this.interface,
          ),
        );
      };

  void updateModelReceive(
      YBMCustomerPublicItemModel model, dynamic code) async {
    var userInfo = await UserInfoUtil.getUserInfo();
    model.bindSkuCollect?.forEach((element) {
      if ('$code'.contains(element.skuCollectCode ?? "-1")) {
        element.receiveType = 1;
        element.oaUserId = userInfo?.sysUserId;
        element.oaUserName = userInfo?.realName;
        setState(() {});
      }
    });
  }

  @override
  Future<void> refreshList({String? type}) async {
    this.page = 0;
    // 没有数据时 刷新增加loading
    if (this.dataSource.length == 0 && !this.isSearch) {
      showLoadingDialog();
    }

    // 如果本地没有筛选条件则先请求筛选
    if (this.customerStatusCondition.length == 0) {
      await this.requestCondition();
    }
    // 每次刷新时 重新请求定位信息
    await this.requestLocation(type);
    // 请求列表数据
    this.requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  Future<void> requestLocation(String? type) async {
    LocationData result;
    if (BuildLocaRequest.latitude == null || BuildLocaRequest.longitude == '' || type == null) {
      result = await XYYContainer.locationChannel.locate().timeout(
        Duration(seconds: 3),
        onTimeout: () {
          return LocationData();
        },
      );
    }else {
      result = LocationData(
        latitude: BuildLocaRequest.latitude,
        longitude: BuildLocaRequest.longitude,
      );
    }
    this.paramModel.poiLatitude = result.latitude;
    this.paramModel.poiLongitude = result.longitude;
    XYYContainer.storageChannel.put("customer_map_longitude", result.longitude);
    XYYContainer.storageChannel.put("customer_map_latitude", result.latitude);
    XYYContainer.storageChannel.put("customer_map_location_timestamp",
        DateTime.now().millisecondsSinceEpoch);
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return this.paramModel.poiLatitude?.isEmpty == false &&
        this.paramModel.poiLongitude?.isEmpty == false;
  }

  // 请求客户状态筛选条件
  Future<void> requestCondition() async {
    var result =
        await NetworkV2<CustomerConditionModel>(CustomerConditionModel())
            .requestDataV2('openSea4POI/getCustomerStatus');
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getListData();
        if (data != null) {
          data.forEach((element) {
            element.code = element.id;
            element.text = element.name;
          });
          this.customerStatusCondition = data;
          if (this.widget.customerExtraType == 0) {
            this.paramModel.customerStatus = data
                .firstWhere((element) => "${element.selected}" == "1",
                orElse: () => CustomerConditionModel(text: '全部', code: "-1"))
                .code;
          }
        }
      }
    }
    return;
  }

  /// 请求公海列表数据
  void requestListData() async {
    var params = this.paramModel.toJson();
    params['limit'] = 10;
    params['offset'] = this.page;

    var result = await NetworkV2<YBMCustomerPublicListRowsModel>(
            YBMCustomerPublicListRowsModel())
        .requestDataV2(
      'customerV2/openSeaList',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.GET,
      showErrorToast:false,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.rows;
        if (rows != null) {
          if (this.page == 0) {
            this.dataSource = rows;
          } else {
            this.dataSource.addAll(rows);
          }
        } else {
          this.dataSource = [];
        }
        this.isLastPage = data?.lastPage == true;
        this.page = this.isLastPage ? this.page : this.page + 1;
        setState(() {});
      }
    }
  }

  /// 跳转筛选页面
  void jumpFilter() {
    String router;
    if (Platform.isAndroid) {
      router = 'xyy://crm-app.ybm100.com/select_area_v2?';
    } else {
      router = 'xyy://crm-app.ybm100.com/select_area?';
    }
    if (this.paramModel.cacheParams?.keys.contains("parentParam") == true) {
      router += "parentParam=" +
          json.encode(this.paramModel.cacheParams?["parentParam"]) +
          "&";
    }
    if (this.paramModel.cacheParams?.keys.contains("areaParam") == true) {
      router +=
          "areaParam=" + json.encode(this.paramModel.cacheParams?["areaParam"]);
    }
    router = Uri.encodeFull(router);
    XYYContainer.open(router, callback: (params) {
      if (params != null) {
        // 兼容Android container返回值处理
        if (params.keys.length == 0 ||
            (Platform.isAndroid && params.containsKey("reset"))) {
          this.paramModel.cacheParams?.remove("parentParam");
          this.paramModel.cacheParams?.remove("areaParam");
          this.paramModel.area = null;
          this.paramModel.cityCode = null;
          this.paramModel.provinceCode = null;
        } else {
          this.paramModel.cacheParams?.addAll(params);

          SelectObjectAreaCallBackModel backModel =
              SelectObjectAreaCallBackModel.fromJsonMap(params);
          if (backModel.areaParam?.length == 1) {
            // 只有一个区或没有
            SelectObjectAreaModel? areaModel = backModel.areaParam?.first;
            if (areaModel?.areaLevel != "" &&
                areaModel?.areaLevel?.toString() == "3") {
              // 只选择一个区
              this.paramModel.area = areaModel?.areaCode ?? "";
              this.paramModel.cityCode = null;
              this.paramModel.provinceCode = null;
            } else {
              // 没有选择区，判断市
              SelectObjectAreaModel? parentModel = backModel.parentParam?.last;
              if (parentModel?.areaLevel != "" &&
                  areaModel?.areaLevel?.toString() == "2") {
                this.paramModel.cityCode = parentModel?.areaCode ?? "";
                this.paramModel.area = null;
                this.paramModel.provinceCode = null;
              } else {
                //没有选择区，判断省
                this.paramModel.provinceCode =
                    backModel.parentParam?.first.areaCode ?? "";
                this.paramModel.area = null;
                this.paramModel.cityCode = null;
              }
            }
          } else {
            var code = backModel.areaParam?.map((e) => e.areaCode).join(',');
            this.paramModel.area = code ?? "";
            this.paramModel.cityCode = null;
            this.paramModel.provinceCode = null;
          }
        }
      }
      this.refreshController.callRefresh();
    });
  }
}
