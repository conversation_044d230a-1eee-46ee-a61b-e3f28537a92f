import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_detail_more_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_visit_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_public_detail_header_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_detail_visit_record_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPublicDetailPageV2 extends BasePage {
  final String? openSeaId;

  CustomerPublicDetailPageV2({this.openSeaId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPublicDetailPageStateV2();
  }
}

class CustomerPublicDetailPageStateV2
    extends BaseState<CustomerPublicDetailPageV2> with EventBusObserver {
  /// 数据
  CustomerDetailDataV2? get sourceData => detailDataNotifier.value;

  CustomerDetailVisitData? get visitData => visitDataNotifier.value;

  ValueNotifier<CustomerDetailDataV2?> detailDataNotifier = ValueNotifier(null);
  ValueNotifier<CustomerDetailVisitData?> visitDataNotifier =
      ValueNotifier(null);

  ValueNotifier<bool> isFollowNotifier = ValueNotifier(true);

  /// 用来保存NavigationBar右侧按钮
  GlobalKey _anchorKey = GlobalKey();

  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  String pdcHost = "";

  bool get isFollowPeople => isFollowNotifier.value;

  ScrollController scrollController = ScrollController();
  ValueNotifier<double> headerBgOffsetNotifier = ValueNotifier(0);

  @override
  void initState() {
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          pdcHost = value['h5_host'];
        });
      }
    });
    scrollController.addListener(scrollCallback);
    this.requestAllData();
    this.getRoles();
    super.initState();
  }

  void scrollCallback() {
    headerBgOffsetNotifier.value = scrollController.offset;
  }

  void getRoles() async {
    this.isFollowNotifier.value = await UserInfoUtil.isFollowPeople();
  }

  @override
  void dispose() {
    scrollController.removeListener(scrollCallback);
    this._controller.dispose();
    detailDataNotifier.dispose();
    isFollowNotifier.dispose();
    visitDataNotifier.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    var paddingTop = MediaQuery.of(context).padding.top + 44;
    if (paddingTop <= 44) {
      // 兜底策略，如果状态栏高度不能正常获取，则手动写个高度，防止展示不出来
      paddingTop = 100;
    }
    return Container(
        width: getScreenWidth(),
        height: getScreenHeight(),
        color: Color(0xfff1f6f9),
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.only(top: paddingTop),
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      child: Stack(
                        children: [
                          Positioned(
                            top: 0,
                            child: ValueListenableBuilder<double>(
                                valueListenable: headerBgOffsetNotifier,
                                builder: (context, value, child) {
                                  if (value < 0) {
                                    value = 0;
                                  }
                                  return Transform.translate(
                                    offset: Offset(0, -paddingTop - value),
                                    child: Image.asset(
                                      "assets/images/customer/customer_private_detail_header.png",
                                      fit: BoxFit.fitWidth,
                                      width: getScreenWidth(),
                                    ),
                                  );
                                }),
                          ),
                          EasyRefresh(
                            controller: _controller,
                            onRefresh: () async {
                              return await requestDetailData();
                            },
                            onLoad: null,
                            emptyWidget: null,
                            child: SingleChildScrollView(
                              controller: scrollController,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 10),
                                width: getScreenWidth(),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 5,
                                    ),
                                    ValueListenableBuilder<CustomerDetailDataV2?>(
                                        valueListenable: detailDataNotifier,
                                        builder: (context, value, child) {
                                          return CustomerPublicDetailHeaderWidget(
                                            sourceData,
                                            claimClickCallback:
                                                jumpClaimRecordPage,
                                            titleClickCallback:
                                                showCopyActionSheet,
                                            mapClickCallback: jumpAddressPage,
                                          );
                                        }),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    ValueListenableBuilder<
                                            CustomerDetailVisitData?>(
                                        valueListenable: visitDataNotifier,
                                        builder: (context, value, child) {
                                          return CustomerDetailVisitRecordWidget(
                                              visitData, jumpVisitPage);
                                        }),
                                    SizedBox(
                                      height: 10,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                      valueListenable: isFollowNotifier,
                      builder: (context, value, child) {
                        return Visibility(
                          visible: !value,
                          child: Container(
                            color: Colors.white,
                            width: double.infinity,
                            padding: EdgeInsets.only(
                                left: 10, right: 10, top: 10, bottom: 20),
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: reviceCustomerAction,
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.symmetric(vertical: 11),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    color: const Color(0xff00b377),
                                    borderRadius: BorderRadius.circular(2)),
                                child: Text(
                                  "认领客户",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                ],
              ),
            ),
            Image.asset(
              "assets/images/customer/customer_private_detail_header.png",
              fit: BoxFit.fitWidth,
              height: paddingTop,
              alignment: Alignment.topCenter,
              width: getScreenWidth(),
            ),
          ],
        ));
  }

  void jumpClaimRecordPage() {
    Navigator.of(context).pushNamed(
        "/customer_claim_record_page?customerId=${this.sourceData?.id}&merchantId=${this.sourceData?.merchantId}");
  }

  void jumpVisitPage() {
    Navigator.of(context).pushNamed(
        "/customer_schedule_record_page?customerId=${this.sourceData?.id}&type=1");
  }

  void jumpAddressPage() {
    if (this.sourceData?.id == null) {
      return;
    }
    // String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    // router = router + "id=${this.sourceData?.id}&";
    // router = router + "poiLatitude=${this.sourceData?.poiLatitude}&";
    // router = router + "poiLongitude=${this.sourceData?.poiLongitude}&";
    // router = router + "address=${this.sourceData?.address}&";
    // router = router + "customerName=${this.sourceData?.customerName}&";
    // router = router + "poiId=${this.sourceData?.poiId}&";
    // router = router + "type=false&";
    // router = router + "changePoi=true&";
    // router = router + "changeMap=true";
    // router = Uri.encodeFull(router);
    // XYYContainer.open(router);

    String router = "/ybm_customer_map_page?";
    router = router + "customerId=${this.sourceData?.id}&";
    router = router + "latitude=${this.sourceData?.poiLatitude}&";
    router = router + "longitude=${this.sourceData?.poiLongitude}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  void showCopyActionSheet() {
    showCupertinoModalPopup<void>(
      context: this.context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          message: Text(
              '${this.sourceData?.customerName}（客户编号:${this.sourceData?.merchantId}）'),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Clipboard.setData(
                  ClipboardData(
                      text:
                          '${this.sourceData?.customerName}（客户编号:${this.sourceData?.merchantId}）'),
                );
                Navigator.of(context).pop();
                showToast('复制成功');
              },
              child: Text('复制'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('取消'),
          ),
        );
      },
    );
  }

  void jumpToErrorUpload(dynamic mark) {
    if (mark == "uploadError") {
      /// 错误信息上报
      if (this.sourceData?.poiAuditStatus != null) {
        if (this.sourceData?.poiAuditStatus.toString() == "1") {
          showToast("客户信息审核中，无法提交审核");
          return;
        }
        if (this.sourceData?.poiAuditStatus.toString() == "3") {
          showToast("客户信息审核驳回，无法操作");
          return;
        }
      }
      String pdcUrl =
          this.pdcHost + "/dataError?poiId=${this.sourceData?.poiId}&source=5";
      pdcUrl = Uri.encodeComponent(pdcUrl);
      String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
      XYYContainer.open(router);
    }
  }

  void reviceCustomerAction() {
    CustomerSkuCollectDialog.showSkuCollectDialog(
            context, sourceData?.bindSkuCollect)
        .then((value) {
      print("guan result:$value");
      if (value == null || value.isEmpty) {
        return;
      }
      showCupertinoDialog(
        context: this.context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text("确认是否认领"),
            actions: [
              CupertinoDialogAction(
                child: Text("取消"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              CupertinoDialogAction(
                child: Text("确定"),
                onPressed: () {
                  Navigator.of(context).pop();
                  this.requestReviceCustomer(
                      value.map((e) => e.skuCollectCode).join(','));
                },
              ),
            ],
          );
        },
      );
    });
  }

  @override
  String getTitleName() {
    return "客户详情（B2B）";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      backgroundColor: Colors.transparent,
      leftButtonColor: Colors.white,
      titleTextColor: Colors.white,
      rightButtons: [
        IconButton(
          key: this._anchorKey,
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          color: Colors.white,
          splashColor: Colors.transparent,
          icon: ImageIcon(
            AssetImage(
              'assets/images/customer/customer_detail_more.png',
            ),
            color: Colors.white,
            size: 24,
          ),
          onPressed: () {
            showCustomerMoreMenu(
              context: this.context,
              targetKey: this._anchorKey,
              itemSource: [
                CustomerMoreSourceItem(title: '客户错误信息上报', id: "uploadError"),
              ],
              itemTap: this.jumpToErrorUpload,
            );
          },
        )
      ],
    );
  }

  @override
  bool isExtendBodyBehindAppBar() {
    return true;
  }

  /// 请求所有数据
  Future<void> requestAllData() async {
    showLoadingDialog();
    await Future.wait([requestDetailData(), requestVisitData()]);
    dismissLoadingDialog();
  }

  Future<void> requestDetailData() async {
    if (this.sourceData?.customerName == null) {
      showLoadingDialog();
    }
    var result = await NetworkV2<CustomerDetailDataV2>(CustomerDetailDataV2())
        .requestDataV2('customerV2/openDetail',
            parameters: {'customerId': widget.openSeaId},
            method: RequestMethod.GET);
    dismissLoadingDialog();
    _controller.finishRefresh();
    if (result.isSuccess == true) {
      this.detailDataNotifier.value = result.getData();
    } else {
      showToast('${result.errorMsg ?? "获取客户详情异常"}');
    }
  }

  /// 请求拜访记录数据
  Future<void> requestVisitData() async {
    var result =
        await NetworkV2<CustomerDetailVisitData>(CustomerDetailVisitData())
            .requestDataV2('customerV2/visitList',
                parameters: {'customerId': widget.openSeaId},
                method: RequestMethod.GET);
    if (mounted && result.isSuccess == true) {
      visitDataNotifier.value = result.getData();
    }
  }

  void requestReviceCustomer(String skuCollectCodes) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {
        "id": this.sourceData?.id,
        "skuCollectCodes": skuCollectCodes
      },
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      showToast("认领成功");
      // 发送给 私海刷新列表使用， 公海列表使用返回值进行判断刷新
      eventBus.sendMessage(CustomerEventBusName.YBM_PUBLIC_RECEIVE);
      XYYContainer.close(this.context, resultData: {
        'seaId': '${this.sourceData?.id}',
        'sku_collect_code': '$skuCollectCodes'
      });
    } else {
      if (result.code != null && result.code == 405) {
        showCupertinoDialog(
          context: this.context,
          builder: (BuildContext context) {
            return CupertinoAlertDialog(
              title: Text("已达到私海数量最大限度，无法认领"),
              actions: [
                CupertinoDialogAction(
                  child: Text("确定"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
      showToast('${result.errorMsg ?? "认领客户失败"}');
    }
  }
}
