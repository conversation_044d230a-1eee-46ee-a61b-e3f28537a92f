import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_detail_more_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPublicDetailPage extends BasePage {
  final String? openSeaId;

  CustomerPublicDetailPage({this.openSeaId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPublicDetailState();
  }
}

class CustomerPublicDetailState extends BaseState<CustomerPublicDetailPage>
    with EventBusObserver {
  /// 菜单Source
  List<CustomerDetailItemData> menuSource = [];

  /// 数据
  CustomerDetailData sourceData = CustomerDetailData();

  /// 用来保存NavgationBar右侧按钮
  GlobalKey _anchorKey = GlobalKey();

  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  String pdcHost = "";

  bool isFollowPeople = true;

  @override
  void initState() {
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          pdcHost = value['h5_host'];
        });
      }
    });

    this.menuSource = this.generateMenusSource();
    this.requestDetailData();
    this.getRoles();
    super.initState();
  }

  void getRoles() async {
    this.isFollowPeople = await UserInfoUtil.isFollowPeople();
    setState(() {});
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: EasyRefresh(
            controller: _controller,
            onRefresh: () async {
              this.requestDetailData();
            },
            child: ListView(
              children: [
                // 头部视图
                CustomerDetailHeaderView(
                  model: this.sourceData,
                  merchantNameOnTap: this.showCopyActionSheet,
                  addressOnTap: this.jumpAddressPage,
                  statusOnTap: this.jumpStatusPage,
                ),
                // 列表
                GridView.builder(
                  shrinkWrap: true,
                  // 解决无限高度问题
                  physics: NeverScrollableScrollPhysics(),
                  // 禁用滚动
                  padding:
                      EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 30),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 50.0 / 50.0,
                    mainAxisSpacing: 20,
                    crossAxisSpacing: 0,
                  ),
                  itemCount: this.menuSource.length,
                  itemBuilder: (context, index) {
                    CustomerDetailItemData item = this.menuSource[index];
                    return CustomerDetailItemView(
                      item: item,
                      onTap: () {
                        this.jumpPage(context, index);
                      },
                    );
                  },
                )
              ],
            ),
          ),
        ),
        Visibility(
          visible: !this.isFollowPeople,
          child: Container(
            margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
            decoration: BoxDecoration(
              color: Color(0xFF00B377),
              borderRadius: BorderRadius.circular(2),
            ),
            constraints: BoxConstraints(minWidth: double.infinity),
            child: TextButton(
              onPressed: this.reviceCustomerAction,
              style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all(Colors.transparent)),
              child: Text(
                '认领客户',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  List<CustomerDetailItemData> generateMenusSource() {
    return [
      CustomerDetailItemData(
          title: "基本信息",
          image: "assets/images/customer/customer_menu_basic.png"),
      CustomerDetailItemData(
          title: "订单记录",
          image: "assets/images/customer/customer_menu_order.png"),
      CustomerDetailItemData(
          title: "商品管理",
          image: "assets/images/customer/customer_menu_product.png"),
      CustomerDetailItemData(
          title: "拜访记录",
          image: "assets/images/customer/customer_menu_visit.png"),
      CustomerDetailItemData(
          title: "认领记录",
          image: "assets/images/customer/customer_menu_revice.png"),
    ];
  }

  void jumpPage(BuildContext context, int index) {
    switch (index) {
      case 0: // 基本信息
        Navigator.of(context).pushNamed(
            '/customer_public_basic_info?shopId=${this.sourceData.id}');
        break;
      case 1: // 订单记录
        Navigator.of(context).pushNamed(
            "/customer_public_order_record?shopId=${this.sourceData.id}");
        break;
      case 2:
        String router =
            "xyy://crm-app.ybm100.com/public_customer/good_manager?shopId=${this.sourceData.id}";
        XYYContainer.open(router);
        break;
      case 3:
        Navigator.of(context).pushNamed(
            "/customer_schedule_record_page?customerId=${this.sourceData.id}&type=1");
        break;
      case 4:
        Navigator.of(context).pushNamed(
            "/customer_claim_record_page?customerId=${this.sourceData.id}&merchantId=${this.sourceData.merchantId}");
        break;
      default:
    }
  }

  void jumpAddressPage() {
    if (this.sourceData.id == null) {
      return;
    }
    String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    router = router + "id=${this.sourceData.id}&";
    router = router + "poiLatitude=${this.sourceData.poiLatitude}&";
    router = router + "poiLongitude=${this.sourceData.poiLongitude}&";
    router = router + "address=${this.sourceData.address}&";
    router = router + "customerName=${this.sourceData.customerName}&";
    router = router + "poiId=${this.sourceData.poiId}&";
    router = router + "type=false&";
    router = router + "changePoi=true&";
    router = router + "changeMap=true";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  void jumpStatusPage() {
    if (this.sourceData.poiAuditId == null) {
      showToast("门店审核id为空");
      return;
    }
    String pdcUrl = this.pdcHost +
        "/record/addDetail?poiAuditId=${this.sourceData.poiAuditId}&source=5";
    pdcUrl = Uri.encodeComponent(pdcUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
    XYYContainer.open(router);
  }

  void showCopyActionSheet() {
    showCupertinoModalPopup<void>(
      context: this.context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          message: Text(
              '${this.sourceData.merchantName}（客户编号:${this.sourceData.merchantCode}）'),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Clipboard.setData(
                  ClipboardData(
                      text:
                          '${this.sourceData.merchantName}（客户编号:${this.sourceData.merchantCode}）'),
                );
                Navigator.of(context).pop();
                showToast('复制成功');
              },
              child: Text('复制'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('取消'),
          ),
        );
      },
    );
  }

  void jumpToErrorUpload(dynamic mark) {
    if (mark == "uploadError") {
      /// 错误信息上报
      if (this.sourceData.poiAuditStatus != null) {
        if (this.sourceData.poiAuditStatus.toString() == "1") {
          showToast("客户信息审核中，无法提交审核");
          return;
        }
        if (this.sourceData.poiAuditStatus.toString() == "3") {
          showToast("客户信息审核驳回，无法操作");
          return;
        }
      }
      String pdcUrl =
          this.pdcHost + "/dataError?poiId=${this.sourceData.poiId}&source=5";
      pdcUrl = Uri.encodeComponent(pdcUrl);
      String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
      XYYContainer.open(router);
    }
  }

  void reviceCustomerAction() {
    CustomerSkuCollectDialog.showSkuCollectDialog(
            context, sourceData.bindSkuCollect)
        .then((value) {
      print("guan result:$value");
      if (value == null || value.isEmpty) {
        return;
      }
      showCupertinoDialog(
        context: this.context,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text("确认是否认领"),
            actions: [
              CupertinoDialogAction(
                child: Text("取消"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              CupertinoDialogAction(
                child: Text("确定"),
                onPressed: () {
                  Navigator.of(context).pop();
                  this.requestReviceCustomer(
                      value.map((e) => e.skuCollectCode).join(','));
                },
              ),
            ],
          );
        },
      );
    });
  }

  @override
  String getTitleName() {
    return "客户详情";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      rightButtons: [
        IconButton(
          key: this._anchorKey,
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          icon: Image.asset(
            'assets/images/customer/customer_detail_more.png',
            width: 24,
            height: 24,
          ),
          onPressed: () {
            showCustomerMoreMenu(
              context: this.context,
              targetKey: this._anchorKey,
              itemSource: [
                CustomerMoreSourceItem(title: '客户错误信息上报', id: "uploadError"),
              ],
              itemTap: this.jumpToErrorUpload,
            );
          },
        )
      ],
    );
  }

  void requestDetailData() async {
    if (this.sourceData.customerName == null) {
      showLoadingDialog();
    }
    var result = await NetworkV2<CustomerDetailData>(CustomerDetailData())
        .requestDataV2('openSea4POI/searchOpenSeaDetail',
            parameters: {'id': widget.openSeaId},
            method: RequestMethod.POST,
            contentType: RequestContentType.FORM);
    dismissLoadingDialog();
    _controller.finishRefresh();
    if (result.isSuccess == true) {
      this.sourceData = result.getData() ?? CustomerDetailData();
      if (mounted) {
        setState(() {});
      }
    } else {
      showToast('${result.errorMsg ?? "获取客户详情异常"}');
    }
  }

  void requestReviceCustomer(String skuCollectCodes) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {
        "id": this.sourceData.id,
        "skuCollectCodes": skuCollectCodes
      },
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      showToast("认领成功");
      // 发送给 私海刷新列表使用， 公海列表使用返回值进行判断刷新
      eventBus.sendMessage(CustomerEventBusName.YBM_PUBLIC_RECEIVE);
      XYYContainer.close(this.context, resultData: {
        'seaId': '${this.sourceData.id}',
        'sku_collect_code': '$skuCollectCodes'
      });
    } else {
      if (result.code != null && result.code == 405) {
        showCupertinoDialog(
          context: this.context,
          builder: (BuildContext context) {
            return CupertinoAlertDialog(
              title: Text("已达到私海数量最大限度，无法认领"),
              actions: [
                CupertinoDialogAction(
                  child: Text("确定"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
      showToast('${result.errorMsg ?? "认领客户失败"}');
    }
  }
}
