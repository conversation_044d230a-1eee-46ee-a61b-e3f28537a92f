import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_public_param.g.dart';

@JsonSerializable(createFactory: false)
class YBMCustomerPublicParam {
  /// 区编码 - 入参需要判断
  @JsonKey(ignore: true)
  String? area;

  /// 市编码 - 入参需要判断
  @Json<PERSON>ey(ignore: true)
  String? cityCode;

  /// 省编码 - 入参需要判断
  @JsonKey(ignore: true)
  String? provinceCode;

  /// 关键词 - 搜索页面使用
  String? keyword;

  /// 认领类型
  String? receiveType = "0";

  /// 排序类型
  String? sortType = "0";

  /// 注册类型
  String? customerStatus = "-1";

  /// 开业状态
  String? poiRegisterFlag = "-1";

  /// 筛选页面缓存
  @JsonKey(ignore: true)
  Map<String, dynamic>? cacheParams = {};

  String? poiLatitude;
  String? poiLongitude;

  YBMCustomerPublicParam();

  Map<String, dynamic> toJson() {
    var map = _$YBMCustomerPublicParamToJson(this);
    map.removeWhere((key, value) =>
        value == "-1" ||
        value == -1 ||
        value == null ||
        value == "" ||
        value == "null");
    if (this.area != null) {
      map['area'] = this.area;
    } else if (this.cityCode != null) {
      map['cityCode'] = this.cityCode;
    } else if (this.provinceCode != null) {
      map['provinceCode'] = this.provinceCode;
    }

    return map;
  }
}
