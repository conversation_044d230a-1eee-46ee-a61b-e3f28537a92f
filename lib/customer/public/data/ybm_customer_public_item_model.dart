import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_public_item_model.g.dart';

@JsonSerializable()
class HYCusomerPublicModel extends BaseModelV2<HYCusomerPublicModel> {
  @JsonKey()
  YBMCustomerPublicListRowsModel? pageData;

  @override
  HYCusomerPublicModel fromJsonMap(Map<String, dynamic> json) {
    return _$HYCusomerPublicModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HYCusomerPublicModelToJson(this);
  }
}

@JsonSerializable()
class YBMCustomerPublicListRowsModel
    extends BaseModelV2<YBMCustomerPublicListRowsModel> {
  @JsonKey(defaultValue: [])
  List<YBMCustomerPublicItemModel>? rows;

  dynamic lastPage;

  YBMCustomerPublicListRowsModel();

  factory YBMCustomerPublicListRowsModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerPublicListRowsModelFromJson(json);
  }

  @override
  YBMCustomerPublicListRowsModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerPublicListRowsModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerPublicListRowsModelToJson(this);
  }
}

@JsonSerializable()
class YBMCustomerPublicItemModel
    extends BaseModelV2<YBMCustomerPublicItemModel> {
  /// 地址
  dynamic address;

  /// 客户名称
  dynamic customerName;

  /// 当前距离
  dynamic distance;

  /// 客户id
  dynamic id;

  /// 认领状态 1、已被认领 2未被认领
  dynamic receiveType;

  /// EC 客户地址
  dynamic ecAddress;

  /// EC 客户名称
  dynamic ecCustomerName;

  /// 店铺状态 1,2：正常   3：冻结    4：未注册
  dynamic merchantStatus;

  /// 店铺状态文本
  dynamic merchantStatusName;

  /// 店铺id
  dynamic merchantId;

  /// 是否注册标识 1是，2否 默认注册
  dynamic registerFlag;

  /// 是否是新开业门店 1:是
  dynamic poiRegisterFlag;

  /// 认领销售名称
  dynamic sysUserName;

  /// 认领销售ID
  dynamic sysUserId;

  /// poiid
  dynamic poiId;

  /// 荷叶门店id
  dynamic hyId;

  /// 合作标签
  List<YBMCustomerServiceLinesModel>? serviceLines;

  /// 商品集
  List<CustomerSkuCollectData>? bindSkuCollect;

  YBMCustomerPublicItemModel();

  /// 店铺名称
  String getName() {
    if ("$registerFlag" == "1") {
      if ("$ecCustomerName".length != 0) {
        return "$ecCustomerName";
      } else if ("$customerName".length != 0) {
        return "$customerName";
      } else {
        return "-";
      }
    } else {
      if ("$customerName".length != 0) {
        return "$customerName";
      } else {
        return "-";
      }
    }
  }

  /// 店铺地址
  String getAddress() {
    if ("$registerFlag" == "1") {
      if ("$ecAddress".length != 0) {
        return "$ecAddress";
      } else {
        return "-";
      }
    } else {
      if ("$address".length != 0) {
        return "$address";
      } else {
        return "-";
      }
    }
  }

  /// 店铺注册状态
  String get getMerchantStatusName {
    return '${this.merchantStatusName}';
  }

  Color get getMerchantStatusTextColor {
    if (this.merchantStatusName == "已冻结") {
      return Color(0xFFFFC000);
    }
    if (this.merchantStatusName == "未注册") {
      return Color(0xFFFE3D3D);
    }
    return Color(0xFF00B377);
  }

  @override
  YBMCustomerPublicItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerPublicItemModelFromJson(json);
  }

  factory YBMCustomerPublicItemModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerPublicItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerPublicItemModelToJson(this);
  }
}
