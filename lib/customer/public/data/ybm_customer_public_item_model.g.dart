// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ybm_customer_public_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HYCusomerPublicModel _$HYCusomerPublicModelFromJson(Map<String, dynamic> json) {
  return HYCusomerPublicModel()
    ..pageData = json['pageData'] == null
        ? null
        : YBMCustomerPublicListRowsModel.fromJson(
            json['pageData'] as Map<String, dynamic>);
}

Map<String, dynamic> _$HYCusomerPublicModelToJson(
        HYCusomerPublicModel instance) =>
    <String, dynamic>{
      'pageData': instance.pageData,
    };

YBMCustomerPublicListRowsModel _$YBMCustomerPublicListRowsModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerPublicListRowsModel()
    ..rows = (json['rows'] as List<dynamic>?)
            ?.map((e) =>
                YBMCustomerPublicItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        []
    ..lastPage = json['lastPage'];
}

Map<String, dynamic> _$YBMCustomerPublicListRowsModelToJson(
        YBMCustomerPublicListRowsModel instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'lastPage': instance.lastPage,
    };

YBMCustomerPublicItemModel _$YBMCustomerPublicItemModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerPublicItemModel()
    ..address = json['address']
    ..customerName = json['customerName']
    ..distance = json['distance']
    ..id = json['id']
    ..receiveType = json['receiveType']
    ..ecAddress = json['ecAddress']
    ..ecCustomerName = json['ecCustomerName']
    ..merchantStatus = json['merchantStatus']
    ..merchantStatusName = json['merchantStatusName']
    ..merchantId = json['merchantId']
    ..registerFlag = json['registerFlag']
    ..poiRegisterFlag = json['poiRegisterFlag']
    ..sysUserName = json['sysUserName']
    ..sysUserId = json['sysUserId']
    ..poiId = json['poiId']
    ..hyId = json['hyId']
    ..serviceLines = (json['serviceLines'] as List<dynamic>?)
        ?.map((e) =>
            YBMCustomerServiceLinesModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..bindSkuCollect = (json['bindSkuCollect'] as List<dynamic>?)
        ?.map(
            (e) => CustomerSkuCollectData.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$YBMCustomerPublicItemModelToJson(
        YBMCustomerPublicItemModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'customerName': instance.customerName,
      'distance': instance.distance,
      'id': instance.id,
      'receiveType': instance.receiveType,
      'ecAddress': instance.ecAddress,
      'ecCustomerName': instance.ecCustomerName,
      'merchantStatus': instance.merchantStatus,
      'merchantStatusName': instance.merchantStatusName,
      'merchantId': instance.merchantId,
      'registerFlag': instance.registerFlag,
      'poiRegisterFlag': instance.poiRegisterFlag,
      'sysUserName': instance.sysUserName,
      'sysUserId': instance.sysUserId,
      'poiId': instance.poiId,
      'hyId': instance.hyId,
      'serviceLines': instance.serviceLines,
      'bindSkuCollect': instance.bindSkuCollect,
    };
