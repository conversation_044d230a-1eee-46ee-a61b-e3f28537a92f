import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/header_footer/header_footer_helper.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_claim_item_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_claim_page_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleClaimListPage extends BasePage {
  final String merchantId;
  final String customerId;

  ScheduleClaimListPage(this.merchantId, this.customerId);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleClaimListPageState();
  }
}

const PAGE_SIZE = 10;
bool isShowTips = false;

class ScheduleClaimListPageState extends BaseState<ScheduleClaimListPage> {
  var _controller = EasyRefreshController();

  List<CustomerClaimItemData?>? listData;
  int offset = 1;
  PageState pageState = PageState.Empty;
  bool lastPage = false;
  String? tips;

  @override
  void onCreate() {
    super.onCreate();
    requestClaimList(true);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xffefeff4),
      child: Column(
        children: [
          Visibility(
              visible: isShowTips && tips != null && tips!.isNotEmpty,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 15),
                color: const Color(0xFFFFF7EF),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '系统释放：',
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF99664D)),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          tips ?? "",
                          style: TextStyle(
                              fontWeight: FontWeight.normal,
                              fontSize: 12,
                              color: const Color(0xFF99664D)),
                        )
                      ],
                    )),
                    SizedBox(
                      width: 20,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isShowTips = false;
                        });
                      },
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 7, vertical: 13),
                        child: Image.asset(
                          'assets/images/customer/customer_claim_close.png',
                          width: 9,
                          height: 9,
                        ),
                      ),
                    )
                  ],
                ),
              )),
          Expanded(
            child: EasyRefresh.custom(
                controller: _controller,
                enableControlFinishLoad: true,
                enableControlFinishRefresh: true,
                header: HeaderFooterHelp().getHeader(),
                footer: HeaderFooterHelp().getFooter(),
                onRefresh: () async {
                  requestClaimList(true);
                },
                onLoad: () async {
                  requestClaimList(false);
                },
                emptyWidget: getEmptyWidget(),
                slivers: [
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                      //创建列表项
                      if (listData == null || (listData?.isEmpty ?? true)) {
                        return Container();
                      }
                      var itemData = listData?[index];
                      if (itemData == null) {
                        return Container();
                      }
                      return Container(
                        padding: EdgeInsets.only(
                            left: 10, right: 10, top: 14, bottom: 10),
                        margin: EdgeInsets.only(top: 10, left: 10, right: 10),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              children: [
                                Text(
                                  '操作时间',
                                  style: TextStyle(
                                      color: const Color(0xFF9494A6),
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14),
                                ),
                                Expanded(
                                    child: Container(
                                  constraints: BoxConstraints(minWidth: 20),
                                )),
                                Text(
                                  itemData.createTime == null
                                      ? ""
                                      : TimeUtils()
                                          .formatTime(itemData.createTime!),
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.normal,
                                      color: const Color(0xff292933)),
                                )
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Row(
                              children: [
                                Text(
                                  '商品集',
                                  style: TextStyle(
                                      color: const Color(0xFF9494A6),
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14),
                                ),
                                Expanded(
                                    child: Container(
                                  constraints: BoxConstraints(minWidth: 20),
                                )),
                                Text(
                                  itemData.skuCollectName ?? "",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.normal,
                                      color: const Color(0xff292933)),
                                )
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Row(
                              children: [
                                Text(
                                  '操作类型',
                                  style: TextStyle(
                                      color: const Color(0xFF9494A6),
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14),
                                ),
                                Expanded(
                                    child: Container(
                                  constraints: BoxConstraints(minWidth: 20),
                                )),
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 2,horizontal: 5),
                                  child: Text(
                                    itemData.operationName ?? "",
                                    maxLines: 1,
                                    style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.normal,
                                        color: getOperationTextColor(
                                            itemData.operationType)),
                                  ),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(2),
                                      color: getOperationBackgroundColor(
                                          itemData.operationType),
                                      border: Border.all(
                                          color: getOperationBorderColor(
                                              itemData.operationType))),
                                  alignment: Alignment.center,
                                )
                              ],
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Visibility(
                              visible: itemData.operationType != 3,
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 10),
                                child: Row(
                                  children: [
                                    Text(
                                      '关联BD',
                                      style: TextStyle(
                                          color: const Color(0xFF9494A6),
                                          fontWeight: FontWeight.normal,
                                          fontSize: 14),
                                    ),
                                    Expanded(
                                        child: Container(
                                      constraints: BoxConstraints(minWidth: 20),
                                    )),
                                    Text(
                                      itemData.oaName ?? "",
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.normal,
                                          color: const Color(0xff292933)),
                                    )
                                  ],
                                ),
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  '操作人',
                                  style: TextStyle(
                                      color: const Color(0xFF9494A6),
                                      fontWeight: FontWeight.normal,
                                      fontSize: 14),
                                ),
                                Expanded(
                                    child: Container(
                                  constraints: BoxConstraints(minWidth: 20),
                                )),
                                Text(
                                  itemData.creatorOaName ?? "",
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.normal,
                                      color: const Color(0xff292933)),
                                )
                              ],
                            ),
                          ],
                        ),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0x0D292933),
                                offset: Offset(0, 0.5),
                                blurRadius: 1,
                              )
                            ]),
                      );
                    }, childCount: listData?.length ?? 0),
                  )
                ]),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestClaimList(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(PageState.Empty);
      default:
        return null;
    }
  }

  requestClaimList(bool isRefresh) {
    if (isRefresh && (listData?.isEmpty ?? true)) {
      showLoadingDialog(isMask: true, dismissOnTap: true);
    }
    NetworkV2<CustomerClaimPageData>(CustomerClaimPageData())
        .requestDataV2("merchant/queryCustomerBindRecord",
            parameters: {
              "pageNum": isRefresh ? 1 : offset + 1,
              "pageSize": PAGE_SIZE,
              "merchantId": widget.merchantId,
              "customerId": widget.customerId
            },
            method: RequestMethod.GET)
        .then((value) {
      dismissLoadingDialog();
      if (mounted) {
        setState(() {
          if (value.isSuccess ?? false) {
            var result = value.getData()?.pageInfo;
            if (result != null) {
              lastPage = result.isLastPage ?? false;
              if (isRefresh) {
                listData = result.list;
                offset = 1;
                tips = value.getData()?.tips;
              } else {
                listData?.addAll(result.list ?? List.empty());
                offset++;
              }
              if (this.listData?.isEmpty ?? true) {
                pageState = PageState.Empty;
              } else {
                pageState = PageState.Normal;
              }
            }
          } else {
            pageState = PageState.Error;
          }
        });
        if (isRefresh) {
          _controller.resetLoadState();
        }
        _controller.finishRefresh();
        _controller.finishLoad(
            noMore: value.getData()?.pageInfo?.isLastPage ?? false);
      }
    });
  }

  @override
  String getTitleName() {
    return "认领记录";
  }

  Color getOperationBorderColor(int? type) {
    switch (type) {
      case 1: //认领
        return const Color(0xff1bb279);
      case 2: //分配
        return const Color(0xfffd7158);
      case 3: //释放
      default:
        return const Color(0xff157efb);
    }
  }

  Color getOperationBackgroundColor(int? type) {
    switch (type) {
      case 1: //认领
        return const Color(0xfff3fffa);
      case 2: //分配
        return const Color(0xfffff8f7);
      case 3: //释放
      default:
        return const Color(0xfff1f7ff);
    }
  }

  Color getOperationTextColor(int? type) {
    switch (type) {
      case 1: //认领
        return const Color(0xff1bb279);
      case 2: //分配
        return const Color(0xfffd7158);
      case 3: //释放
      default:
        return const Color(0xff157efb);
    }
  }
}
