import 'package:XyyBeanSproutsFlutter/customer/public/order_record/data/customer_detail_order_model.dart';
import 'package:flutter/material.dart';

class CustomerDetailOrderHeader extends StatelessWidget {
  final CustomerDetailOrderHeaderModel model;

  CustomerDetailOrderHeader({required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10, bottom: 10, left: 15, right: 15),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: [
            columTitle(),
            Divider(
              color: Color(0xFFE1E1E5),
              thickness: 1,
              height: 1,
            ),
            columMonth(),
            Divider(
              color: Color(0xFFE1E1E5),
              thickness: 1,
              height: 1,
            ),
            columLastMonth(),
          ],
        ),
      ),
    );
  }

  Widget columTitle() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          color: Color(0xFFFFFFFF),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          width: 1,
          height: 40,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFBFBFB),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '订单数(单)',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          height: 40,
          width: 1,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFBFBFB),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '订单数(单)',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
      ],
    );
  }

  Widget columMonth() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          color: Color(0xFFFBFBFB),
          alignment: Alignment.center,
          child: Text(
            '当月',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
          ),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          width: 1,
          height: 40,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFFFFFF),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '${this.model.thisMonthCount ?? 0}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          height: 40,
          width: 1,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFFFFFF),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '${this.model.thisMonthMoney ?? 0.00}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
      ],
    );
  }

  Widget columLastMonth() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          color: Color(0xFFFBFBFB),
          alignment: Alignment.center,
          child: Text(
            '上月',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
          ),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          width: 1,
          height: 40,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFFFFFF),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '${this.model.lastMonthCount ?? 0}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
        Container(
          color: Color(0xFFE1E1E5),
          height: 40,
          width: 1,
        ),
        Expanded(
          child: Container(
            color: Color(0xFFFFFFFF),
            height: 40,
            alignment: Alignment.center,
            child: Text(
              '${this.model.lastMonthMoney ?? 0.00}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 15),
            ),
          ),
        ),
      ],
    );
  }
}
