import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_detail_order_model.g.dart';

@JsonSerializable()
class CustomerDetailOrderModel extends BaseModelV2<CustomerDetailOrderModel> {
  dynamic lastMonthMoney;
  dynamic thisMonthCount;
  dynamic lastMonthCount;
  dynamic thisMonthMoney;

  CustomerDetailOrderListModel? orderPageList;

  @override
  CustomerDetailOrderModel fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailOrderModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailOrderModelToJson(this);
  }
}

class CustomerDetailOrderHeaderModel {
  dynamic lastMonthMoney;
  dynamic thisMonthCount;
  dynamic lastMonthCount;
  dynamic thisMonthMoney;

  CustomerDetailOrderHeaderModel({
    this.lastMonthMoney,
    this.thisMonthCount,
    this.lastMonthCount,
    this.thisMonthMoney,
  });
}

@JsonSerializable()
class CustomerDetailOrderListModel
    extends BaseModelV2<CustomerDetailOrderListModel> {
  List<OrderListItemData>? list;

  dynamic isLastPage;

  CustomerDetailOrderListModel();

  factory CustomerDetailOrderListModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailOrderListModelFromJson(json);

  @override
  CustomerDetailOrderListModel fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailOrderListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailOrderListModelToJson(this);
  }
}
