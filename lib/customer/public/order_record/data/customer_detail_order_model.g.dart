// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_detail_order_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerDetailOrderModel _$CustomerDetailOrderModelFromJson(
    Map<String, dynamic> json) {
  return CustomerDetailOrderModel()
    ..lastMonthMoney = json['lastMonthMoney']
    ..thisMonthCount = json['thisMonthCount']
    ..lastMonthCount = json['lastMonthCount']
    ..thisMonthMoney = json['thisMonthMoney']
    ..orderPageList = json['orderPageList'] == null
        ? null
        : CustomerDetailOrderListModel.fromJson(
            json['orderPageList'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerDetailOrderModelToJson(
        CustomerDetailOrderModel instance) =>
    <String, dynamic>{
      'lastMonthMoney': instance.lastMonthMoney,
      'thisMonthCount': instance.thisMonthCount,
      'lastMonthCount': instance.lastMonthCount,
      'thisMonthMoney': instance.thisMonthMoney,
      'orderPageList': instance.orderPageList,
    };

CustomerDetailOrderListModel _$CustomerDetailOrderListModelFromJson(
    Map<String, dynamic> json) {
  return CustomerDetailOrderListModel()
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => OrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..isLastPage = json['isLastPage'];
}

Map<String, dynamic> _$CustomerDetailOrderListModelToJson(
        CustomerDetailOrderListModel instance) =>
    <String, dynamic>{
      'list': instance.list,
      'isLastPage': instance.isLastPage,
    };
