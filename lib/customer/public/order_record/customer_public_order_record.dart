import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/order_record/data/customer_detail_order_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/order_record/widget/customer_detail_order_header.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPublicOrderRecordPage extends BasePage {
  final dynamic shopId;

  CustomerPublicOrderRecordPage({required this.shopId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPublicOrderRecordState();
  }
}

class CustomerPublicOrderRecordState
    extends BaseState<CustomerPublicOrderRecordPage> {
  String smallImageHost = "";
  int page = 0;

  List<OrderListItemData> sourceData = [];

  CustomerDetailOrderHeaderModel headerModel = CustomerDetailOrderHeaderModel();

  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        smallImageHost = value["image_host"] ?? "";
      }
    });
    this.refreshList();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      child: EasyRefresh(
        controller: _controller,
        onRefresh: () async {
          this.refreshList();
        },
        onLoad: () async {
          this.loadMoreList();
        },
        child: ListView.builder(
          itemCount:
              this.sourceData.length > 0 ? this.sourceData.length + 1 : 0,
          itemBuilder: (BuildContext context, int index) {
            if (index == 0) {
              return CustomerDetailOrderHeader(model: this.headerModel);
            }
            OrderListItemData itemData = this.sourceData[index - 1];
            return GestureDetector(
              onTap: () {
                showToast("请先认领后再查看详情");
              },
              child: OrderListItemWidget(this.smallImageHost, itemData),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (sourceData.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshList() {
    this.page = 0;
    if (this.sourceData.length == 0) {
      showLoadingDialog();
    }

    this.requestOrderList();
  }

  void loadMoreList() {
    this.requestOrderList();
  }

  void requestOrderList() async {
    var result =
        await NetworkV2<CustomerDetailOrderModel>(CustomerDetailOrderModel())
            .requestDataV2('order/selectMerchantOrders', parameters: {
      'offset': this.page,
      'limit': 10,
      'merchantId': widget.shopId,
    });
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        _controller.finishRefresh();
        _controller.finishLoad(noMore: false);
        CustomerDetailOrderModel? source = result.getData();
        if (source != null) {
          this.headerModel = CustomerDetailOrderHeaderModel(
            thisMonthCount: source.thisMonthCount,
            thisMonthMoney: source.thisMonthMoney,
            lastMonthCount: source.lastMonthCount,
            lastMonthMoney: source.lastMonthMoney,
          );
          if (this.page == 0) {
            this.sourceData = source.orderPageList?.list ?? [];
          } else {
            this.sourceData.addAll(source.orderPageList?.list ?? []);
          }
          if (source.orderPageList?.isLastPage != true) {
            this.page += 1;
          } else {
            _controller.finishLoad(noMore: true);
          }
          setState(() {});
        }
      } else {
        showToast(result.errorMsg ?? "获取订单列表数据异常");
      }
    }
  }

  @override
  String getTitleName() {
    return "订单记录";
  }
}
