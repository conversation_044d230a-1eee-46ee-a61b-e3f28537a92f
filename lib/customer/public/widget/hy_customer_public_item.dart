import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_item_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class HYCustomerPublicItem extends StatefulWidget {
  final YBMCustomerPublicItemModel model;
  final bool hasLocation;
  final String serviceInterface;

  HYCustomerPublicItem(
    this.model, {
    this.hasLocation = true,
    this.serviceInterface = '',
  });
  @override
  State<StatefulWidget> createState() {
    return HYCustomerPublicItemState();
  }
}

class HYCustomerPublicItemState extends State<HYCustomerPublicItem>
    with EventBusObserver {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        child: Column(
          children: [
            this.merchantNameWidget(),
            this.getMerchatIds(),
            this.merchantAddressWidget(),
            this.getClaimedUser(),
            this.getCooperationWidget(),
            this.getLine(),
            this.getActionWidget(context),
          ],
        ),
      ),
    );
  }

  // 客户名称及状态
  Widget merchantNameWidget() {
    Color statusColor = Color(0xFF35C561);
    if ("${widget.model.receiveType}" == "1") {
      statusColor = Color(0xFF35C561);
    } else {
      statusColor = Color(0xFFFE3D3D);
    }
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10),
      child: Row(
        children: [
          Expanded(
            child: Text(
              this.widget.model.getName(),
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 10),
          Text(
            "${widget.model.receiveType}" == "1" ? '已认领' : '未认领',
            style: TextStyle(color: statusColor, fontSize: 14),
          )
        ],
      ),
    );
  }

  // POI ID  荷叶门店ID
  Widget getMerchatIds() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            'POI ID：${widget.model.poiId}',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
          ),
          Visibility(
            visible:
                widget.model.hyId != null && "${widget.model.hyId}".length > 0,
            child: Text(
              'POI ID：${widget.model.hyId}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr =
        widget.hasLocation ? "距您${widget.model.distance}" : "获取不到当前位置";
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              widget.model.getAddress(),
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 已认领的销售
  Widget getClaimedUser() {
    return Visibility(
      visible: "${widget.model.receiveType}" == "1",
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 9),
        width: double.infinity,
        child: Text(
          '绑定销售：${widget.model.sysUserName ?? '-'}',
          style: TextStyle(color: Color(0xFF666666), fontSize: 12),
        ),
      ),
    );
  }

  // 合作状态
  Widget getCooperationWidget() {
    // ImageCatchWidget
    List<String> imagePaths = this
            .widget
            .model
            .serviceLines
            ?.map((e) => widget.serviceInterface + e.iOSIcon)
            .toList() ??
        [];
    List<Widget> items = [];
    imagePaths.forEach((e) {
      items.add(ImageCatchWidget(
        url: e,
        h: 14,
        fit: BoxFit.fill,
      ));
      items.add(SizedBox(width: 5));
    });
    items.add(Spacer());
    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Row(
        children: items,
      ),
    );
  }

  Widget getLine() {
    return Container(
      height: 0.5,
      margin: EdgeInsets.only(top: 9),
      color: "${widget.model.receiveType}" != "1"
          ? Color(0xFFE1E1E5)
          : Colors.transparent,
    );
  }

  /// 底部按钮
  Widget getActionWidget(BuildContext context) {
    return Visibility(
      visible: "${widget.model.receiveType}" != "1",
      child: Container(
        height: 50,
        child: Row(
          children: [
            Spacer(),
            this.buttonFor('认领', () {
              this.reviceAction(context);
            }),
          ],
        ),
      ),
    );
  }

  Widget buttonFor(String title, VoidCallback onPressed) {
    return TextButton(
        onPressed: onPressed,
        child: Container(
          height: 30,
          width: 80,
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xFF8E8E93), width: 0.5),
            borderRadius: BorderRadius.circular(15),
          ),
          padding: EdgeInsets.only(left: 10, right: 10),
          child: Center(
            child: Text(
              title,
              style: TextStyle(color: Color(0xFF333333), fontSize: 14),
            ),
          ),
        ),
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding:
              MaterialStateProperty.all<EdgeInsets>(EdgeInsets.only(right: 10)),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ));
  }

  /// 认领事件
  void reviceAction(BuildContext context) {
    showCommonAlert(
      context: context,
      title: "确认是否认领",
      actions: [
        CommonAlertAction(
          title: '取消',
          style: CommonAlertActionStyle.cancle,
        ),
        CommonAlertAction(
          title: '确认',
          onPressed: () {
            this.requestReviceCustomer();
          },
        ),
      ],
    );
  }

  /// 认领请求
  void requestReviceCustomer() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'hyjk/receiveCustomer',
      parameters: {"id": widget.model.id},
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      XYYContainer.toastChannel.toast("认领成功");
      eventBus.sendMessage(CustomerEventBusName.HY_PUBLIC_RECEIVE);
      var userInfo = await UserInfoUtil.getUserInfo();
      widget.model.receiveType = '1';
      widget.model.sysUserId = userInfo?.sysUserId;
      widget.model.sysUserName = userInfo?.realName;
    } else {
      if (result.code != null && result.code == 405) {
        showCommonAlert(
          context: context,
          title: '已达到私海数量最大限度，无法认领',
          actions: [
            CommonAlertAction(title: '确认'),
          ],
        );
      }
      XYYContainer.toastChannel.toast('${result.errorMsg ?? "认领客户失败"}');
    }
  }
}
