import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:flutter/material.dart';

class CustomerSkuCollectDialog {
  static Future<List<CustomerSkuCollectData>?> showSkuCollectDialog(
      BuildContext context, List<CustomerSkuCollectData>? skuCollectList,
      {String title = '选择商品集',
      String rightText = '确定',
      bool canSelectIgnoreReceiveType = false}) async {
    return await Navigator.push(
      context,
      new _CustomerSkuCollectDialogRoute(
          skuCollectList,
          MaterialLocalizations.of(context).modalBarrierDismissLabel,
          title,
          rightText,
          canSelectIgnoreReceiveType),
    );
  }
}

class _CustomerSkuCollectDialogRoute<T> extends PopupRoute<T> {
  final List<CustomerSkuCollectData>? skuCollectList;

  _CustomerSkuCollectDialogRoute(this.skuCollectList, this.barrierLabel,
      this.title, this.rightText, this.canSelectIgnoreReceiveType);

  // 是否忽视认领状态强制选择
  final bool canSelectIgnoreReceiveType;

  // 标题
  final String title;

  // 右侧按钮文案
  final String rightText;

  @override
  Color? get barrierColor => Colors.black54;

  @override
  bool get barrierDismissible => true;

  @override
  final String? barrierLabel;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 200);

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    Widget bottomSheet = new MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: _CustomerSkuCollectDialogComponent(
          this, skuCollectList, title, rightText, canSelectIgnoreReceiveType),
    );
    ThemeData inheritTheme = Theme.of(context);
    bottomSheet = new Theme(data: inheritTheme, child: bottomSheet);
    return bottomSheet;
  }
}

class _CustomerSkuCollectDialogComponent extends StatefulWidget {
  final _CustomerSkuCollectDialogRoute route;
  final List<CustomerSkuCollectData>? skuCollectList;

  // 标题
  final String title;

  // 右侧按钮文案
  final String rightText;

  // 是否忽视认领状态强制选择
  final bool canSelectIgnoreReceiveType;

  _CustomerSkuCollectDialogComponent(this.route, this.skuCollectList,
      this.title, this.rightText, this.canSelectIgnoreReceiveType);

  @override
  State<StatefulWidget> createState() {
    return new _CustomerSkuCollectDialogComponentState();
  }
}

class _CustomerSkuCollectDialogComponentState
    extends State<_CustomerSkuCollectDialogComponent> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: AnimatedBuilder(
        animation: widget.route.animation!,
        builder: (BuildContext context, Widget? child) {
          final double bottomPadding = MediaQuery.of(context).padding.bottom;
          return ClipRect(
            child: CustomSingleChildLayout(
              delegate: _BottomContentLayout(widget.route.animation!.value,
                  bottomPadding: bottomPadding),
              child: GestureDetector(
                child: Material(
                  color: Colors.white,
                  child: _buildContentView(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  _buildContentView() {
    var childWidget = <Widget>[
      _buildHeaderView(),
      SizedBox(
        height: 5,
      ),
    ];
    widget.skuCollectList?.forEach((element) {
      childWidget.add(GestureDetector(
        onTap: () {
          element.isCheck = !(element.isCheck ?? false);
          setState(() {});
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 15),
          height: 40,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(element.skuCollectName ?? "--",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        color: 1 == element.receiveType
                            ? const Color(0xff8e8e93)
                            : const Color(0xff333333))),
              ),
              SizedBox(
                width: 10,
              ),
              1 == element.receiveType && !widget.canSelectIgnoreReceiveType
                  ? Text(
                      "关联BD：${element.oaUserName}",
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          color: Color(0xff8e8e93)),
                    )
                  : Image.asset(
                      element.isCheck ?? false
                          ? "assets/images/customer/customer_sku_collect_check_selected.png"
                          : "assets/images/customer/customer_sku_collect_check_unselected.png",
                      width: 18,
                      height: 18,
                    )
            ],
          ),
        ),
      ));
    });
    childWidget.add(SizedBox(
      height: 20,
    ));
    return Column(mainAxisSize: MainAxisSize.min, children: childWidget);
  }

  _buildHeaderView() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 15),
            alignment: Alignment.center,
            child: Text(
              "取消",
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                  color: const Color(0xff292933)),
            ),
          ),
        ),
        Expanded(
            child: Container(
          alignment: Alignment.center,
          child: Text(
            widget.title,
            style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: const Color(0xff292933)),
          ),
        )),
        GestureDetector(
          onTap: () {
            var result = widget.skuCollectList
                    ?.where((value) => value.isCheck ?? false) ??
                [];
            print("guan:$result");
            Navigator.pop(context, result.toList());
          },
          child: Container(
            height: 50,
            padding: EdgeInsets.symmetric(horizontal: 15),
            alignment: Alignment.center,
            child: Text(
              widget.rightText,
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xff00b377)),
            ),
          ),
        ),
      ],
    );
  }
}

class _BottomContentLayout extends SingleChildLayoutDelegate {
  _BottomContentLayout(this.progress, {this.bottomPadding = 0});

  final double progress;
  final double bottomPadding;

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    return new BoxConstraints(
        minWidth: constraints.maxWidth,
        maxWidth: constraints.maxWidth,
        minHeight: 0.0,
        maxHeight: constraints.maxHeight + bottomPadding);
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    double height = size.height - childSize.height * progress;
    return new Offset(0.0, height);
  }

  @override
  bool shouldRelayout(_BottomContentLayout oldDelegate) {
    return progress != oldDelegate.progress;
  }
}
