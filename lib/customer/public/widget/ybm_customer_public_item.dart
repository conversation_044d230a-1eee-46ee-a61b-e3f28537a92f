import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/data/ybm_customer_public_item_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class YBMCustomerPublicItem extends StatefulWidget {
  final YBMCustomerPublicItemModel model;
  final bool hasLocation;
  final String serviceInterface;

  YBMCustomerPublicItem(
    this.model, {
    this.hasLocation = true,
    this.serviceInterface = '',
  });

  @override
  State<StatefulWidget> createState() {
    return YBMCustomerPublicItemState();
  }
}

class YBMCustomerPublicItemState extends State<YBMCustomerPublicItem>
    with EventBusObserver {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            this.merchantNameWidget(),
            this.merchantAddressWidget(),
            this.getCooperationWidget(),
            this.getTagItem(),
            this.getControlGoodsWidget(),
          ],
        ),
      ),
    );
  }

  // 客户名称及状态
  Widget merchantNameWidget() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10),
      child: Row(
        children: [
          Expanded(
            child: Text(
              this.widget.model.getName(),
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 10),
          Text(
            widget.model.getMerchantStatusName,
            style: TextStyle(
              color: widget.model.getMerchantStatusTextColor,
              fontSize: 14,
            ),
          )
        ],
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr =
        widget.hasLocation ? "距您${widget.model.distance}" : "获取不到当前位置";
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              widget.model.getAddress(),
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 合作状态
  Widget getCooperationWidget() {
    // ImageCatchWidget
    List<String> imagePaths = this
            .widget
            .model
            .serviceLines
            ?.map((e) => widget.serviceInterface + e.iOSIcon)
            .toList() ??
        [];
    List<Widget> items = [];
    imagePaths.forEach((e) {
      items.add(ImageCatchWidget(
        url: e,
        h: 14,
        fit: BoxFit.fill,
      ));
      items.add(SizedBox(width: 5));
    });
    items.add(Spacer());
    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Row(
        children: items,
      ),
    );
  }

  // 标签
  Widget getTagItem() {
    return Visibility(
      visible: "${this.widget.model.poiRegisterFlag}" == "1",
      child: Container(
        padding: EdgeInsets.only(left: 10, top: 10),
        child: Container(
          padding: EdgeInsets.fromLTRB(3.5, 0.5, 3.5, 0.5),
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xFFFF2121), width: 0.5),
          ),
          child: Text(
            '新开业',
            style: TextStyle(color: Color(0xFFFF2121), fontSize: 10),
          ),
        ),
      ),
    );
  }

  // 控销
  Widget getControlGoodsWidget() {
    List<Widget> list = [];
    list.addAll(this
            .widget
            .model
            .bindSkuCollect
            ?.map(
              (e) => GestureDetector(
                onTap: () {},
                behavior: HitTestBehavior.opaque,
                child: getControlItem(e),
              ),
            )
            .toList() ??
        []);

    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Column(
        children: list,
      ),
    );
  }

  // 控销item
  Widget getControlItem(CustomerSkuCollectData controlModel) {
    return Container(
      height: 40,
      child: Column(
        children: [
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 0.5),
          Spacer(),
          Row(
            children: [
              Text(
                "${controlModel.skuCollectName}",
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 14,
                ),
              ),
              Spacer(),
              Visibility(
                visible: "${controlModel.receiveType}" != "1",
                child: TextButton(
                  onPressed: () {
                    this.reviceAction(controlModel);
                  },
                  child: Container(
                    height: 28,
                    width: 52,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14),
                        border:
                            Border.all(color: Color(0xFF8E8E93), width: 0.5)),
                    child: Text(
                      '认领',
                      style: TextStyle(color: Color(0xFF333333), fontSize: 14),
                    ),
                  ),
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10, left: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ),
              Visibility(
                visible: "${controlModel.receiveType}" == "1",
                child: Container(
                  padding: EdgeInsets.only(right: 10),
                  child: Text(
                    '关联BD：${controlModel.oaUserName}',
                    style: TextStyle(color: Color(0xFF8E8E93), fontSize: 14),
                  ),
                ),
              ),
            ],
          ),
          Spacer(),
        ],
      ),
    );
  }

  /// 认领商品集事件
  void reviceAction(CustomerSkuCollectData controlModel) {
    showCommonAlert(
      context: context,
      title: "确认是否认领${controlModel.skuCollectName}",
      actions: [
        CommonAlertAction(
          title: '取消',
          style: CommonAlertActionStyle.cancle,
        ),
        CommonAlertAction(
          title: '确认',
          onPressed: () {
            this.requestReviceCustomer("${controlModel.skuCollectCode}");
          },
        ),
      ],
    );
  }

  /// 认领请求
  void requestReviceCustomer(String skuCollectCodes) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {"id": widget.model.id, "skuCollectCodes": skuCollectCodes},
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      XYYContainer.toastChannel.toast("认领成功");
      eventBus.sendMessage(CustomerEventBusName.YBM_PUBLIC_RECEIVE);
      var userInfo = await UserInfoUtil.getUserInfo();
      widget.model.bindSkuCollect?.forEach((element) {
        if (element.skuCollectCode == skuCollectCodes) {
          element.receiveType = "1";
          element.oaUserId = userInfo?.sysUserId;
          element.oaUserName = userInfo?.realName;
          setState(() {});
        }
      });
    } else {
      if (result.code != null && result.code == 405) {
        showCommonAlert(
          context: context,
          title: '已达到私海数量最大限度，无法认领',
          actions: [
            CommonAlertAction(title: '确认'),
          ],
        );
      }
      XYYContainer.toastChannel.toast('${result.errorMsg ?? "认领客户失败"}');
    }
  }
}
