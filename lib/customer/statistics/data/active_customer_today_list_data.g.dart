// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_customer_today_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActiveCustomerTodayListData _$ActiveCustomerTodayListDataFromJson(
    Map<String, dynamic> json) {
  return ActiveCustomerTodayListData()
    ..isLastPage = json['isLastPage']
    ..row = (json['row'] as List<dynamic>?)
        ?.map((e) =>
            ActiveCustomerTodayItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ActiveCustomerTodayListDataToJson(
        ActiveCustomerTodayListData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'row': instance.row,
    };

ActiveCustomerTodayItemData _$ActiveCustomerTodayItemDataFromJson(
    Map<String, dynamic> json) {
  return ActiveCustomerTodayItemData()
    ..customerId = json['customerId']
    ..merchantId = json['merchantId']
    ..level = json['level']
    ..levelDesc = json['levelDesc']
    ..isAddCar = json['isAddCar']
    ..isPlaceOrder = json['isPlaceOrder']
    ..merchantName = json['merchantName'];
}

Map<String, dynamic> _$ActiveCustomerTodayItemDataToJson(
        ActiveCustomerTodayItemData instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'level': instance.level,
      'levelDesc': instance.levelDesc,
      'isAddCar': instance.isAddCar,
      'isPlaceOrder': instance.isPlaceOrder,
      'merchantName': instance.merchantName,
    };
