import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'active_customer_today_list_data.g.dart';

@JsonSerializable()
class ActiveCustomerTodayListData
    extends BaseModelV2<ActiveCustomerTodayListData> {
  dynamic isLastPage;
  List<ActiveCustomerTodayItemData>? row;

  ActiveCustomerTodayListData();

  factory ActiveCustomerTodayListData.fromJson(Map<String, dynamic> json) =>
      _$ActiveCustomerTodayListDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ActiveCustomerTodayListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ActiveCustomerTodayListDataToJson(this);
  }
}

@JsonSerializable()
class ActiveCustomerTodayItemData
    extends BaseModelV2<ActiveCustomerTodayItemData> {
  dynamic customerId;
  dynamic merchantId;
  dynamic level;
  dynamic levelDesc;

  dynamic isAddCar;
  dynamic isPlaceOrder;
  dynamic merchantName;

  ActiveCustomerTodayItemData();

  factory ActiveCustomerTodayItemData.fromJson(Map<String, dynamic> json) =>
      _$ActiveCustomerTodayItemDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$ActiveCustomerTodayItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ActiveCustomerTodayItemDataToJson(this);
  }
}
