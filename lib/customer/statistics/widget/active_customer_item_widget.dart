import 'package:XyyBeanSproutsFlutter/customer/statistics/data/active_customer_today_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:flutter/material.dart';

class ActiveCustomerItemWidget extends StatelessWidget {
  final ActiveCustomerTodayItemData itemMode;

  ActiveCustomerItemWidget(this.itemMode);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FunnelUtil.jumpMerchantInfo(itemMode.merchantId);
      },
      child: Container(
        margin: EdgeInsets.only(left: 10, right: 10, bottom: 10),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(4)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.only(left: 10, top: 16, bottom: 16, right: 10),
              width: 44,
              height: 44,
              child: Image.asset(
                "assets/images/customer/customer_statistics_active_icon.png",
                width: 44,
                height: 44,
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  SizedBox(
                    height: 14,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          itemMode.merchantName,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              color: const Color(0xff333333),
                              fontSize: 16,
                              fontWeight: FontWeight.normal),
                        ),
                      ),
                      SizedBox(
                        width: 15,
                      ),
                      Text(
                        itemMode.isAddCar == true ? "已加购" : "未加购",
                        style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.normal,
                            color: itemMode.isAddCar == true
                                ? const Color(0xff00b377)
                                : const Color(0xff9494a6)),
                      ),
                      SizedBox(
                        width: 10,
                      )
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            border: Border.all(
                                width: 0.5, color: const Color(0xff00b377)),
                            color: const Color(0xfff2fbf8)),
                        child: Text(
                          itemMode.levelDesc ?? "",
                          style: TextStyle(
                              fontSize: 12,
                              color: const Color(0xff00b377),
                              fontWeight: FontWeight.normal),
                        ),
                      ),
                      Expanded(child: Container()),
                      Image.asset(
                        itemMode.isPlaceOrder == true
                            ? "assets/images/customer/customer_statistics_active_has_order.png"
                            : "assets/images/customer/customer_statistics_active_no_order.png",
                        width: 55,
                        height: 44,
                      )
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
