import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/global_cache_manager.dart';
import 'package:flutter/material.dart';

class ActiveCustomerFilterWidget extends StatefulWidget {
  final bool? purchaseSelected;
  final bool? orderSelected;
  final CustomerLevelData? defaultLevel;
  final FilterSelectCallback? callback;

  ActiveCustomerFilterWidget({this.callback,
    this.purchaseSelected,
    this.orderSelected,
    this.defaultLevel});

  @override
  State<StatefulWidget> createState() {
    return ActiveCustomerFilterWidgetState();
  }
}

class ActiveCustomerFilterWidgetState
    extends State<ActiveCustomerFilterWidget> {
  bool? purchaseState;
  bool? orderState;
  CustomerLevelData? selectLevel;

  @override
  void initState() {
    super.initState();
    purchaseState = widget.purchaseSelected;
    orderState = widget.orderSelected;
    selectLevel = widget.defaultLevel;
  }

  @override
  Widget build(BuildContext context) {
    var globalKey = GlobalKey();
    return Container(
      height: 40,
      color: Colors.white,
      child: Row(
        children: [
          SizedBox(
            width: 10,
          ),
          buildActiveStateButton(0, purchaseState == true, "已加购"),
          SizedBox(
            width: 10,
          ),
          buildActiveStateButton(1, orderState == true, "已下单"),
          SizedBox(
            width: 10,
          ),
          buildActiveStateButton(2, orderState == false, "未下单"),
          Expanded(child: Container()),
          Container(
            padding: EdgeInsets.only(left: 10, right: 10),
            child: DropControllerButton(
              key: globalKey,
              title: completeDesc(selectLevel?.desc ?? "客户级别"),
              selectedText: completeDesc(selectLevel?.desc),
              selectedStyle: TextStyle(
                  color: Color(0xFF00B377),
                  fontSize: 14,
                  fontWeight: FontWeight.w600),
              onPressed: (controller) async {
                var customerLevelList =
                await GlobalCacheManager.instance.customerLevelList;
                if (customerLevelList == null || customerLevelList.isEmpty) {
                  XYYContainer.toastChannel.toast("客户等级列表为空，请刷新");
                  return;
                }
                await showCommonFilterPopup(
                  key: globalKey,
                  context: context,
                  pageBuilder: (distance) {
                    return CustomerSingleFilterPopup(
                      models: customerLevelList.map((e) {
                        return CustomerConditionModel()
                          ..code = e.code
                          ..text = completeDesc(e.desc);
                      }).toList(),
                      selectedCode: selectLevel?.code?.toString(),
                      selectAction: (value) {
                        selectLevel = customerLevelList.firstWhere((element) {
                          return element.code == value.code;
                        });

                        controller.setSelectText(value.text ?? "客户级别");

                        if (widget.callback != null) {
                          widget.callback!(
                              purchaseState, orderState, selectLevel);
                        }
                      },
                      distance: distance,
                    );
                  },
                );
                controller.setIsOpen(false);
              },
            ),
          )
        ],
      ),
    );
  }

  Widget buildActiveStateButton(int index, bool isSelected, String buttonText) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        switch (index) {
          case 0:
            purchaseState = purchaseState == null ? true : !purchaseState!;
            break;
          case 1:
            orderState = orderState == true ? null : true;
            break;
          case 2:
            orderState = orderState == false ? null : false;
            break;
        }
        if (widget.callback != null) {
          widget.callback!(purchaseState, orderState, selectLevel);
        }
        setState(() {});
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: isSelected
                ? const Color(0xffE4F8F1)
                : const Color(0xffF7F7F8)),
        alignment: Alignment.center,
        height: 30,
        width: 56,
        child: Text(
          buttonText,
          style: TextStyle(
              fontSize: 12,
              color: isSelected
                  ? const Color(0xff00B377)
                  : const Color(0xff676773),
              fontWeight: FontWeight.normal),
        ),
      ),
    );
  }

  dynamic completeDesc(dynamic desc) {
    if (desc is String &&
        ["S", "A", "B", "C", "D", "E"].contains(desc.toString())) {
      return "$desc级";
    }
    return desc;
  }

}

typedef FilterSelectCallback(bool? purchasedSelected, bool? orderSelected,
    CustomerLevelData? level);
