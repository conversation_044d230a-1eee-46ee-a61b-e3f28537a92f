import 'dart:ui';

import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/statistics/data/active_customer_today_list_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/statistics/widget/active_customer_filter_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/statistics/widget/active_customer_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ActiveCustomerTodayPage extends BasePage {
  final String? groupId;
  final String? searchUserId;

  ActiveCustomerTodayPage(this.groupId, this.searchUserId);

  @override
  BaseState<StatefulWidget> initState() {
    return ActiveCustomerTodayPageState();
  }
}

class ActiveCustomerTodayPageState extends BaseState<ActiveCustomerTodayPage> {
  EasyRefreshController _controller = EasyRefreshController();
  List<ActiveCustomerTodayItemData?>? dataSource;

  PageState pageState = PageState.Normal;

  Map<String, dynamic> params = {};

  int page = 1;

  bool? isLastPage = true;

  @override
  void onCreate() {
    super.onCreate();
    if (widget.groupId != null) {
      params['groupId'] = widget.groupId;
    } else if (widget.searchUserId != null) {
      params['searchUserId'] = widget.searchUserId;
    } else {
      params.clear();
    }
    showLoadingDialog();
    requestListData(true);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xffF7F7F8),
      child: Column(
        children: [buildFilterWidget(), buildTipsWidget(), buildListWidget()],
      ),
    );
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  String getTitleName() {
    return "今日活跃客户";
  }

  Widget buildFilterWidget() {
    return ActiveCustomerFilterWidget(
      callback: (purchasedSelected, orderSelected, selectLevel) {
        if (purchasedSelected == true) {
          params["addCarFlag"] = 1;
        } else {
          params.remove("addCarFlag");
        }
        params.remove("placeOrderFlag");
        if (orderSelected == true) {
          params["placeOrderFlag"] = 1;
        } else if (orderSelected == false) {
          params["placeOrderFlag"] = 0;
        }

        if (selectLevel != null && selectLevel.code != null) {
          params["level"] = selectLevel.code;
        } else {
          params.remove("level");
        }
        showLoadingDialog();
        requestListData(true);
      },
    );
  }

  Widget buildTipsWidget() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
      alignment: Alignment.centerLeft,
      child: Text(
        "仅统计今日有过登录的客户、加购和下单",
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
            color: const Color(0xff9494a6),
            fontSize: 12,
            fontWeight: FontWeight.normal),
      ),
    );
  }

  Widget buildListWidget() {
    return Expanded(
      child: EasyRefresh(
        controller: _controller,
        onRefresh: () async {
          return await this.requestListData(true);
        },
        onLoad: isLastPage != false
            ? null
            : () async {
                return await this.requestListData(false);
              },
        child: ListView.builder(
          itemCount: this.dataSource?.length ?? 0,
          itemBuilder: (context, index) {
            var itemData = this.dataSource?[index];
            if (itemData == null) {
              return Container();
            }
            return ActiveCustomerItemWidget(itemData);
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  Future<void> requestListData(bool isRefresh) async {
    params["limit"] = 20;
    if (isRefresh) {
      params["offset"] = 1;
    } else {
      params["offset"] = page + 1;
    }
    var value = await NetworkV2<ActiveCustomerTodayListData>(
            ActiveCustomerTodayListData())
        .requestDataV2("active/list",
            parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _controller.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          if (!isRefresh) {
            page++;
          }else{
            page=1;
          }
          var result = value.getData();
          if (result != null) {
            isLastPage = result.isLastPage;
            if (isRefresh) {
              if (result.row?.isNotEmpty == true) {
                dataSource = result.row!;
                pageState = PageState.Normal;
              } else {
                dataSource = [];
                pageState = PageState.Empty;
              }
            } else {
              if (result.row?.isNotEmpty == true) {
                if (dataSource == null) {
                  dataSource = [];
                }
                dataSource?.addAll(result.row!);
                pageState = PageState.Normal;
              }
            }
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
        _controller.finishRefresh();
        _controller.finishLoad();
      });
    }
  }

  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestListData(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }
}
