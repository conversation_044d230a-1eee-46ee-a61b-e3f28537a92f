import 'package:flutter/material.dart';

Future<T?> showHYSearchSelectPopover<T extends Object?>({
  required BuildContext context,
  required GlobalKey anchorKey,
  required List<String> models,
}) {
  dynamic renderBox = anchorKey.currentContext?.findRenderObject()!;
  double renderHeight = renderBox.size.height;
  Offset offset = renderBox.localToGlobal(Offset(0, renderHeight));
  return showGeneralDialog(
    context: context,
    barrierColor: Color(0x66000000),
    // 遮罩颜色
    barrierLabel: "",
    barrierDismissible: true,
    // 点击遮罩是否关闭对话框
    transitionDuration: const Duration(milliseconds: 200),
    // 对话框打开/关闭的动画时长
    pageBuilder: (
      // 构建对话框内部UI
      BuildContext ctx,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      return Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: offset.dy,
            left: offset.dx + 5,
            child: Container(
              child: CustomerSelectAuth(
                models: models,
              ),
            ),
          ),
        ],
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
  );
}

class CustomerSelectAuth extends StatelessWidget {
  final List<String> models;

  CustomerSelectAuth({required this.models});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Color(0xFFFFFFFF), borderRadius: BorderRadius.circular(3)),
      child: Column(
        children: this.getItems(context),
      ),
    );
  }

  List<Widget> getItems(BuildContext context) {
    return this
        .models
        .map((e) => GestureDetector(
              onTap: () {
                Navigator.of(context).pop(e);
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 40,
                width: 110,
                child: Center(
                  child: Text(
                    e,
                    style: TextStyle(color: Color(0xFF333333), fontSize: 15),
                  ),
                ),
              ),
            ))
        .toList();
  }
}
