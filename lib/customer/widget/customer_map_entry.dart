import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/drag/draggable_widget.dart';
import 'package:flutter/material.dart';

class YBMCustomerMapEntry {
  static Widget showEntry(
      {required BuildContext context, bool isFromPrivate = true}) {
    return Positioned(
      right: 10,
      bottom: 50,
      child: GestureDetector(
        onTap: () {
          if (isFromPrivate) {
            track("mc-customer-permap");
          } else {
            track("mc-customer-cusmap");
          }
          XYYContainer.open('/ybm_customer_map_page');
        },
        child: Image.asset(
          "assets/images/customer/customer_map_entrace.png",
          width: 65,
          height: 65,
        ),
      ),
    );
  }

  /// 可拖动的
  static Widget showDraggableEntry({bool isFromPrivate = true, double extraHeight = 45, bool usedAppBar = true}) {
    return DraggableWidget(
      initialPosition: AnchoringPosition.bottomRight,
      dragAnimationScale: 1,
      bottomMargin: 147,
      topMargin: 110,
      intialVisibility: true,
      horizontalSpace: 0,
      shadowBorderRadius: 50,
      usedAppBar: usedAppBar,
      extraHeight: extraHeight,
      child: GestureDetector(
        onTap: () {
          if (isFromPrivate) {
            track("mc-customer-permap");
          } else {
            track("mc-customer-cusmap");
          }
          XYYContainer.open('/ybm_customer_map_page');
        },
        child: Container(
          height: 64,
          width: 64,
          child: Image.asset('assets/images/customer/customer_map_entrace.png'),
        ),
      ),
    );
  }

  static void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }
}
