import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_visit_data.dart';
import 'package:flutter/material.dart';

class CustomerDetailVisitRecordWidget extends StatelessWidget {
  final CustomerDetailVisitData? visitData;
  final VoidCallback? moreClickCallback;

  CustomerDetailVisitRecordWidget(this.visitData, this.moreClickCallback);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.only(left: 10, right: 10, bottom: 15),
          width: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              buildTitleWidget(),
              isEmpty() ? buildEmptyWidget() : buildInfoWidget()
            ],
          ),
        ));
  }

  bool isEmpty() {
    return visitData?.visitDate == null || visitData?.customerId == "";
  }

  Widget buildEmptyWidget() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            "assets/images/base/state_empty_v2.png",
            width: 120,
            height: 120,
          ),
          Text(
            "该门店还没有被拜访过",
            style: TextStyle(
                color: const Color(0xff676773),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          )
        ],
      ),
    );
  }

  Widget buildTitleWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: Padding(
          padding: const EdgeInsets.only(top: 15, bottom: 10),
          child: Text(
            "拜访记录",
            style: TextStyle(
                color: const Color(0xff383841),
                fontSize: 14,
                fontWeight: FontWeight.w600),
          ),
        )),
        GestureDetector(
          onTap: moreClickCallback,
          behavior: HitTestBehavior.opaque,
          child: Container(
            padding: EdgeInsets.only(top: 14.5, bottom: 12.5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  "查看全部",
                  style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                      color: const Color(0xff9494a6)),
                ),
                SizedBox(
                  width: 5,
                ),
                Image.asset(
                  "assets/images/customer/customer_private_detail_arrow.png",
                  width: 4.5,
                  height: 7,
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget buildInfoWidget() {
    return Container(
      decoration: BoxDecoration(
          color: const Color(0xfff1f6f9),
          borderRadius: BorderRadius.circular(3),
          border: Border.all(color: const Color(0xffeeeff0), width: 0.5)),
      child: Column(
        children: [
          buildInfoItemWidget(
              "拜访人员", visitData?.visitOaUser?.toString() ?? "--"),
          Divider(
            height: 0.5,
            color: const Color(0xffeeeff0),
          ),
          buildInfoItemWidget("拜访时间", visitData?.visitDate?.toString() ?? "--"),
          Divider(
            height: 0.5,
            color: const Color(0xffeeeff0),
          ),
          buildInfoItemWidget(
              "拜访类型", visitData?.visitTypeDesc?.toString() ?? "--"),
          Divider(
            height: 0.5,
            color: const Color(0xffeeeff0),
          ),
          buildInfoItemWidget(
              "拜访目的", visitData?.visitPurpose?.toString() ?? "--"),
          Divider(
            height: 0.5,
            color: const Color(0xffeeeff0),
          ),
          buildInfoItemWidget(
              "拜访总结", visitData?.visitSummary?.toString() ?? "--"),
        ],
      ),
    );
  }

  Widget buildInfoItemWidget(String title, String content,
      {Color contentColor = const Color(0xff292933)}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 10, left: 19, right: 19),
          color: const Color(0xfff1f6f9),
          child: Text(
            title,
            style: TextStyle(
                color: const Color(0xff676773),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
        ),
        Container(width: 0.5, color: const Color(0xffeeeff0)),
        Expanded(
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
            child: Text(
              content,
              style: TextStyle(
                  color: contentColor,
                  fontSize: 12,
                  fontWeight: FontWeight.normal),
            ),
          ),
        )
      ],
    );
  }
}
