import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_history_manager.dart';
import 'package:XyyBeanSproutsFlutter/customer/page/customer_search_tab_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class YBMCustomerSearchHistoryPage extends BasePage {
  // 搜索关键字 如果为空则认为是第一次搜索
  final String? keyword;
  // 0: 私海  1: 公海
  final dynamic searchType;

  YBMCustomerSearchHistoryPage({
    required this.searchType,
    this.keyword,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return YBMCustomerSearchHistoryState();
  }
}

class YBMCustomerSearchHistoryState
    extends BaseState<YBMCustomerSearchHistoryPage> {
  TextEditingController _controller = TextEditingController();

  List<String> history = [];

  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    this.initHistoryList();
    this._controller.text = widget.keyword ?? "";
  }

  @override
  void dispose() {
    this._focusNode.unfocus();
    this._controller.dispose();
    super.dispose();
  }

  void initHistoryList() async {
    List<dynamic> result = await CustomerHistoryManager.queryHistoryKeyword();
    this.history = result.map((e) => '$e').toList();
    setState(() {});
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this._focusNode.unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 50,
              child: Row(
                children: [
                  Text(
                    '历史搜索',
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Spacer(),
                  Visibility(
                    visible: this.history.length > 0,
                    child: TextButton(
                      onPressed: this.deleteAllHoistory,
                      child: Image.asset(
                        'assets/images/base/history_delete_icon.png',
                        width: 14,
                        height: 14,
                      ),
                      style: ButtonStyle(
                        overlayColor: MaterialStateProperty.all<Color>(
                            Colors.transparent),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        minimumSize:
                            MaterialStateProperty.all<Size>(Size(30, 50)),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Visibility(
              visible: this.history.length <= 0,
              child: Expanded(
                child: Center(
                  child: PageStateWidget.pageEmpty(PageState.Empty,
                      stateText: "暂无历史搜索～"),
                ),
              ),
            ),
            Visibility(
              visible: this.history.length > 0,
              child: Wrap(
                alignment: WrapAlignment.start,
                spacing: 10,
                runSpacing: 10,
                children: this.historyItem(),
              ),
            )
          ],
        ),
      ),
    );
  }

  // 历史记录item
  List<Widget> historyItem() {
    return this
        .history
        .map((e) => GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                this.changeKeyword(e);
              },
              child: Container(
                padding: EdgeInsets.fromLTRB(9, 5, 9, 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(22),
                  color: Color(0xFFF7F7F8),
                ),
                child: Text(e,
                    style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 13,
                    )),
              ),
            ))
        .toList();
  }

  // 删除历史记录
  void deleteAllHoistory() {
    showCommonAlert(
      context: this.context,
      title: "提示",
      content: "是否删除全部历史搜索记录",
      actions: [
        CommonAlertAction(
          title: "否",
          style: CommonAlertActionStyle.cancle,
        ),
        CommonAlertAction(
          title: "是",
          onPressed: () {
            CustomerHistoryManager.clearHistoryKeyword();
            this.history = [];
            setState(() {});
          },
        ),
      ],
    );
  }

  // 增加搜索关键词
  void changeKeyword(String keyword) async {
    // 存储搜索历史
    await CustomerHistoryManager.setHistoryKeyword(keyword);

    // 收起键盘
    this._focusNode.unfocus();

    if (widget.keyword != null) {
      Navigator.of(context).pop(keyword);
    } else {
      Navigator.of(context).pushReplacement(CustomSearchRoute(
        builder: (context) => CustomerSearchTabPage(
          keyword: keyword,
          roleCode: 1,
          selectIndex: widget.searchType,
        ),
      ));
    }
  }

  @override
  String getTitleName() {
    return "搜索历史";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      focusNode: this._focusNode,
      hintText: "请输入客户名称/手机号/编号",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      controller: this._controller,
      radius: BorderRadius.circular(22),
      cancleStyle: TextStyle(color: Color(0xFF35C561), fontSize: 14),
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._controller.text = inputValue;
          showToast("请输入搜索内容");
          return;
        }
        this.changeKeyword(inputValue);
      },
    );
  }
}

class CustomSearchRoute extends MaterialPageRoute {
  CustomSearchRoute({required WidgetBuilder builder}) : super(builder: builder);

  @override
  Duration get transitionDuration => Duration(milliseconds: 0);

  Duration get reverseTransitionDuration => Duration(milliseconds: 400);
}
