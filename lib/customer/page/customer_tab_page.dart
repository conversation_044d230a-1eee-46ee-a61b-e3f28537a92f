import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/page/hy_customer_private_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/page/ybm_customer_private_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/page/hy_customer_public_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/page/ybm_customer_public_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_select_auth.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:flutter/material.dart';

class CustomerTabPage extends BasePage {
  int? customerExtraType = 0;

  CustomerTabPage({this.customerExtraType});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerTabPageState();
  }
}

class CustomerTabPageState extends BaseState<CustomerTabPage>
    with SingleTickerProviderStateMixin, EventBusObserver {
  List<UserAuthModel> authModels = [];

  GlobalKey _titleButtonKey = GlobalKey();

  // Tab标题数组
  List<String> titles = ["私海客户", "公海客户"];

  // Tab控制器
  late TabController _controller;

  // 导航按钮控制器
  ValueNotifier<int> _tabRightController = ValueNotifier(0);

  // 顶部标题控制器
  DropButtonController _titleController =
      DropButtonController(model: DropButtonModel(normalText: '药帮忙'));

  // 默认药帮忙
  UserAuthModel role = UserAuthModel()
    ..roleCode = "1"
    ..roleName = "药帮忙";

  // 状态保持
  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  void dispose() {
    this._controller.dispose();
    this._tabRightController.dispose();
    this.eventBus.removeListener(observer: this);
    super.dispose();
  }

  @override
  void initState() {
    this.getAuthType();
    this.initalTabController();
    this.addListenerNotification();
    super.initState();
  }

  // 订阅通知消息
  void addListenerNotification() {
    /// 公私海切换
    this.eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_CUSTOMER_TAB_CHANGE,
        callback: (index) {
          if (index is int) {
            if ("${this.role.roleCode}" != "1" && this.authModels.length > 1) {
              role = UserAuthModel()
                ..roleCode = "1"
                ..roleName = "药帮忙";
              this._titleController.value.normalText = role.roleName;
              setState(() {});
            }
            this._controller.index = index;
            // Future.delayed(Duration(milliseconds: 100), () {});
          }
        });
  }

  // 获取权限信息
  void getAuthType() async {
    try {
      String roleJSON = await UserAuthManager.getRoleJSONString();
      if (roleJSON.isNotEmpty) {
        List<dynamic> roleMaps = jsonDecode(roleJSON);
        if (roleMaps.length > 0) {
          this.authModels =
              roleMaps.map((e) => UserAuthModel().fromJsonMap(e)).toList();
          // 移除荷叶问诊
          this.authModels.removeWhere((element) =>
              "${element.roleCode}" == "2" && element.roleName == "灵芝问诊");
          if (this.authModels.length >= 1) {
            this.role = this.authModels.first;
            this._titleController.value.normalText = this.role.roleName;
          }
          setState(() {});
        }
      }
    } catch (e) {}
  }

  // 初始化Tab 控制器
  void initalTabController() {
    this._controller = TabController(
      length: this.titles.length,
      initialIndex: 0,
      vsync: this,
    );
    this._controller.addListener(() {
      if (!this._controller.indexIsChanging) {
        this._tabRightController.value = this._controller.index;
      }
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: TabBar(
              controller: this._controller,
              isScrollable: false,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: Color(0xFF00B377),
              indicatorWeight: 3,
              unselectedLabelColor: Color(0xFF666666),
              unselectedLabelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
              labelColor: Color(0xFF333333),
              labelStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
              tabs: tabs(),
            ),
          ),
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 1),
          Expanded(
            child: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: this._controller,
              children: this.tabViews(widget.customerExtraType!),
            ),
          )
        ],
      ),
    );
  }

  // Tab Item
  List<Widget> tabs() {
    return this
        .titles
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  // TabView Item
  List<Widget> tabViews(int customerExtraType) {
    if (this.role.roleCode == "3") {
      // 荷叶
      return [
        HYCustomerPrivatePage(),
        HYCustomerPublicPage(),
      ];
    } else {
      // 药帮忙
      return [
        YBMCustomerPrivatePage(),
        YBMCustomerPublicPage(customerExtraType: customerExtraType),
      ];
    }
  }

  // 导航
  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      leftType: LeftButtonType.none,
      leadingWidth: 100,
      titleWidget: this.getTitleWidget(),
      rightButtons: this.getRightButtons(),
    );
  }

  Widget getTitleWidget() {
    if (this.authModels.isEmpty || this.authModels.length == 1) {
      String title =
          this.authModels.isEmpty ? "药帮忙" : this.authModels.first.roleName;
      return Container(
        child: Text(
          title,
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else {
      return Container(
        child: DropControllerButton(
          key: _titleButtonKey,
          title: this.role.roleName,
          controller: this._titleController,
          normalStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          selectedStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          onPressed: (controller) {
            showAuthSelectPopover(
              context: context,
              anchorKey: _titleButtonKey,
              models: this.authModels,
              clickAction: (model) {
                controller.value.normalText = model.roleName;
                setState(() {
                  this.role = model;
                });
              },
            ).then((value) {
              controller.setIsOpen(false);
            });
          },
        ),
      );
    }
  }

  List<Widget> getRightButtons() {
    return [
      Container(
        padding: EdgeInsets.only(right: 10),
        width: 100,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: this.searchAction,
              child: Image.asset(
                'assets/images/customer/customer_tab_search.png',
                width: 22,
                height: 22,
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding:
                    MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            ValueListenableBuilder(
              valueListenable: this._tabRightController,
              builder: (context, value, child) {
                return Visibility(
                  visible: !(this.role.roleCode == "3" && value == 1),
                  child: TextButton(
                    onPressed: this.filterAction,
                    child: Image.asset(
                      'assets/images/customer/customer_tab_filter.png',
                      width: 22,
                      height: 33,
                    ),
                    style: ButtonStyle(
                      overlayColor:
                          MaterialStateProperty.all<Color>(Colors.transparent),
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          EdgeInsets.all(10)),
                      minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    ];
  }

  // 筛选事件
  void filterAction() {
    switch ("${this.role.roleCode}") {
      case "1":
        // 药帮忙
        if (this._controller.index == 0) {
          // 私海
          eventBus.sendMessage(CustomerEventBusName.YBM_PRIVATE_FILTER);
        } else {
          // 公海
          eventBus.sendMessage(CustomerEventBusName.YBM_PUBLIC_FILTER);
        }
        break;
      case "3":
        // 荷叶 - 仅私海
        eventBus.sendMessage(CustomerEventBusName.HY_PRIVATE_FILTER);
        break;
      default:
    }
  }

  // 搜索事件
  void searchAction() {
    dynamic roleCode = this.role.roleCode;
    dynamic selectIndex = this._controller.index;

    if ("$roleCode" == "1") {
      // 药帮忙 搜索增加历史记录
      XYYContainer.open('/ybm_search_history?searchType=$selectIndex');
    } else {
      XYYContainer.open(
          '/customer_search_tab_page?roleCode=$roleCode&selectIndex=$selectIndex');
    }
  }

  @override
  String getTitleName() {
    return "";
  }
}
