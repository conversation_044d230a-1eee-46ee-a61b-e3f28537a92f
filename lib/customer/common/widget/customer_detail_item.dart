import 'package:flutter/material.dart';

class CustomerDetailItemView extends StatelessWidget {
  final CustomerDetailItemData item;
  final GestureTapCallback onTap;

  CustomerDetailItemView({
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        children: [
          Image.asset(
            this.item.image,
            width: 25,
            height: 25,
          ),
          SizedBox(height: 9),
          Text(
            this.item.title,
            style: TextStyle(color: Color(0xFF333333), fontSize: 12),
          ),
        ],
      ),
    );
  }
}

class CustomerDetailItemData {
  final String image;
  final String title;
  final VoidCallback? onTapCallback;

  const CustomerDetailItemData(
      {required this.image, required this.title, this.onTapCallback});
}
