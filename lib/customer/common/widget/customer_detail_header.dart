import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_data.dart';
import 'package:flutter/material.dart';

class CustomerDetailHeaderView extends StatelessWidget {
  final CustomerDetailData model;

  final VoidCallback? statusOnTap;

  final VoidCallback? addressOnTap;

  final VoidCallback? merchantNameOnTap;

  final VoidCallback? callPhoneOnTap;

  final bool isFromPrivate;

  CustomerDetailHeaderView(
      {required this.model,
      this.merchantNameOnTap,
      this.statusOnTap,
      this.addressOnTap,
      this.isFromPrivate = false,
      this.callPhoneOnTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(10),
      padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 15),
      decoration: BoxDecoration(
          border: Border.all(color: Color(0xFFE7E7E7), width: 0.5),
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(2),
          boxShadow: [
            BoxShadow(
              blurRadius: 15,
              spreadRadius: 0,
              offset: Offset(0, 2),
              color: Color(0x0D292933),
            )
          ]),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: this.merchantNameOnTap,
                      behavior: HitTestBehavior.opaque,
                      child: Text(
                        this.model.merchantName,
                        style: TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    SizedBox(height: 6),
                    Row(
                      children: [
                        Text(
                          '所属蜂窝:${this.model.regionName}',
                          style: TextStyle(
                            color: Color(0xFF9494A6),
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(width: 20),
                        Text(
                          '客户编码:${this.model.merchantCode}',
                          style: TextStyle(
                            color: Color(0xFF9494A6),
                            fontSize: 12,
                          ),
                        )
                      ],
                    ),
                    SizedBox(height: 12),
                  ],
                ),
              ),
              Visibility(
                visible: this.isFromPrivate,
                child: GestureDetector(
                    onTap: callPhoneOnTap,
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      padding: EdgeInsets.only(left: 10),
                      child: Image.asset(
                        "assets/images/customer/customer_detail_phone.png",
                        width: 40,
                        height: 40,
                      ),
                    )),
              )
            ],
          ),
          Divider(
            height: 0,
            thickness: 1,
            color: Color(0xFFF6F6F6),
          ),
          SizedBox(height: 12),
          CustomerDetailEntryItem(title: '门店名称', content: this.model.shopName),
          SizedBox(height: 15),
          CustomerDetailEntryItem(title: '门店ID', content: this.model.shopId),
          SizedBox(height: 15),
          CustomerDetailEntryItem(
            title: '门店地址',
            content: this.model.shopAddress,
            image: 'assets/images/customer/customer_detail_location.png',
            onTap: this.addressOnTap,
          ),
          SizedBox(height: 15),
          CustomerDetailEntryItem(
            title: '门店状态',
            content: this.model.shopStatus,
            contentColor: this.model.shopStatusTextColor,
            image: 'assets/images/customer/customer_detail_arrow.png',
            onTap: this.statusOnTap,
          ),
        ],
      ),
    );
  }
}

class CustomerDetailEntryItem extends StatelessWidget {
  final String title;
  final String content;
  final Color? contentColor;
  final String? image;
  final VoidCallback? onTap;

  CustomerDetailEntryItem({
    required this.title,
    required this.content,
    this.contentColor,
    this.image,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 80,
            child: Text(
              this.title,
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 14,
                height: 1.1,
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                if (this.onTap != null) {
                  this.onTap!();
                }
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      this.content,
                      style: TextStyle(
                        color: this.contentColor ?? Color(0xFF292933),
                        fontSize: 14,
                        height: 1.1,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: this.image != null,
                    child: Image.asset(
                      this.image ?? "",
                      width: 15,
                      height: 15,
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
