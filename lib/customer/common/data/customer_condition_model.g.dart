// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_condition_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerConditionListModel _$CustomerConditionListModelFromJson(
    Map<String, dynamic> json) {
  return CustomerConditionListModel()
    ..customerType = (json['customerType'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..levelType = (json['levelType'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licenseType = (json['licenseType'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..sortType = (json['sortType'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..customerStatus = (json['customerStatus'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licensePrepare = (json['licensePrepare'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderCondition = (json['orderCondition'] as List<dynamic>?)
        ?.map((e) => CustomerConditionModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CustomerConditionListModelToJson(
        CustomerConditionListModel instance) =>
    <String, dynamic>{
      'customerType': instance.customerType,
      'levelType': instance.levelType,
      'licenseType': instance.licenseType,
      'sortType': instance.sortType,
      'customerStatus': instance.customerStatus,
      'licensePrepare': instance.licensePrepare,
      'orderCondition': instance.orderCondition,
    };

CustomerConditionModel _$CustomerConditionModelFromJson(
    Map<String, dynamic> json) {
  return CustomerConditionModel(
    code: json['code'],
    text: json['text'],
    selected: json['selected'],
  )
    ..name = json['name']
    ..id = json['id'];
}

Map<String, dynamic> _$CustomerConditionModelToJson(
        CustomerConditionModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'text': instance.text,
      'selected': instance.selected,
      'name': instance.name,
      'id': instance.id,
    };
