import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_condition_model.g.dart';

@JsonSerializable()
class CustomerConditionListModel
    extends BaseModelV2<CustomerConditionListModel> {
  // 客户类型
  List<CustomerConditionModel>? customerType;
  // 客户等级
  List<CustomerConditionModel>? levelType;
  // 资质条件
  List<CustomerConditionModel>? licenseType;
  // 排序条件
  List<CustomerConditionModel>? sortType;
  // 客户状态
  List<CustomerConditionModel>? customerStatus;
  // 资质筛选
  List<CustomerConditionModel>? licensePrepare;
  // 下单情况
  List<CustomerConditionModel>? orderCondition;

  @override
  CustomerConditionListModel fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerConditionListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerConditionListModelToJson(this);
  }
}

@JsonSerializable()
class CustomerConditionModel extends BaseModelV2<CustomerConditionModel> {
  // 编码
  dynamic code;
  // 展示类型
  dynamic text;
  // 是否选中
  dynamic selected;

  // 公海使用
  dynamic name;

  dynamic id;

  CustomerConditionModel({this.code, this.text, this.selected});

  @override
  CustomerConditionModel fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerConditionModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerConditionModelToJson(this);
  }

  factory CustomerConditionModel.fromJson(Map<String, dynamic> json) {
    return _$CustomerConditionModelFromJson(json);
  }
}
