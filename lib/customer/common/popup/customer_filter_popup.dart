import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

// 单选下拉 菜单
class CustomerSingleFilterPopup extends CommonFilterPopupBase {
  final List<CustomerConditionModel> models;
  final String? selectedCode;
  final ValueChanged<CustomerConditionModel> selectAction;

  CustomerSingleFilterPopup({
    required double distance,
    required this.models,
    required this.selectAction,
    this.selectedCode,
  }) : super(distance: distance);

  @override
  CommonFilterPopupBaseState createState() {
    return CustomerSingleFilterPopupState();
  }
}

class CustomerSingleFilterPopupState
    extends CommonFilterPopupBaseState<CustomerSingleFilterPopup> {
  String? selectedCode;

  @override
  void initState() {
    this.selectedCode = widget.selectedCode;
    super.initState();
  }

  @override
  Widget buildContent() {
    return Container(
      color: Color(0xFFFFFFFF),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: this.items(),
        ),
      ),
    );
  }

  List<Widget> items() {
    return widget.models
        .map((e) => GestureDetector(
              onTap: () {
                this.selectedCode = "${e.code}";
                setState(() {});
                widget.selectAction(e);
                EasyPopup.pop(context);
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                child: CustomerFilterItem(
                  title: "${e.text}",
                  isSelected: "${e.code}" == this.selectedCode,
                ),
              ),
            ))
        .toList();
  }
}

// 单选带重置下拉菜单
class CustomerSingleResetFilterPopup extends CommonFilterPopupBase {
  final List<CustomerConditionModel> models;
  final String? selectedCode;
  final ValueChanged<String?> selectAction;

  CustomerSingleResetFilterPopup({
    required double distance,
    required this.models,
    required this.selectAction,
    this.selectedCode,
  }) : super(distance: distance);

  @override
  CommonFilterPopupBaseState createState() {
    return CustomerSingleResetFilterPopupState();
  }
}

class CustomerSingleResetFilterPopupState
    extends CommonFilterPopupBaseState<CustomerSingleResetFilterPopup>
    with CommonFilterPopupBottomButton {
  String? selectedCode;

  @override
  void initState() {
    this.selectedCode = widget.selectedCode;
    super.initState();
  }

  double get contentHeight {
    int maxCount = widget.models.length > 5 ? 5 : widget.models.length;
    return maxCount * 44 + 50;
  }

  @override
  Widget buildContent() {
    return Container(
      color: Color(0xFFFFFFFF),
      height: this.contentHeight,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: widget.models.length,
              itemBuilder: (context, index) {
                CustomerConditionModel model = widget.models[index];
                return GestureDetector(
                  onTap: () {
                    var code = "${model.code}";
                    if (this.selectedCode == code) {
                      this.selectedCode = null;
                    } else {
                      this.selectedCode = code;
                    }
                    setState(() {});
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 44,
                    child: CustomerFilterItem(
                      title: "${model.text}",
                      isSelected: this.selectedCode == "${model.code}",
                    ),
                  ),
                );
              },
            ),
          ),
          bottomButton(),
        ],
      ),
    );
  }

  @override
  void determineAction() {
    widget.selectAction(this.selectedCode);
    this.closePopup();
  }

  @override
  void resetAction() {
    this.selectedCode = null;
    setState(() {});
  }
}

// 多选下拉菜单
class CustomerMultipleFilterPopup extends CommonFilterPopupBase {
  final List<CustomerConditionModel> models;
  final String? selectedCodes;
  final ValueChanged<String> selectAction;

  CustomerMultipleFilterPopup({
    required double distance,
    required this.models,
    required this.selectAction,
    this.selectedCodes,
  }) : super(distance: distance);

  @override
  CustomerMultipleFilterPopupState createState() {
    return CustomerMultipleFilterPopupState();
  }
}

class CustomerMultipleFilterPopupState
    extends CommonFilterPopupBaseState<CustomerMultipleFilterPopup>
    with CommonFilterPopupBottomButton {
  List<String> selectedCodes = [];

  @override
  void initState() {
    this.selectedCodes = widget.selectedCodes?.split(",") ?? [];
    super.initState();
  }

  double get contentHeight {
    int maxCount = widget.models.length > 5 ? 5 : widget.models.length;
    return maxCount * 44 + 50;
  }

  @override
  Widget buildContent() {
    return Container(
      color: Color(0xFFFFFFFF),
      height: this.contentHeight,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: widget.models.length,
              itemBuilder: (context, index) {
                CustomerConditionModel model = widget.models[index];
                return GestureDetector(
                  onTap: () {
                    if (this.selectedCodes.contains("${model.code}")) {
                      this.selectedCodes.remove("${model.code}");
                    } else {
                      this.selectedCodes.add("${model.code}");
                    }
                    setState(() {});
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    height: 44,
                    child: CustomerFilterItem(
                      title: "${model.text.toString().trim()}",
                      isSelected: this.selectedCodes.contains("${model.code}"),
                    ),
                  ),
                );
              },
            ),
          ),
          bottomButton(),
        ],
      ),
    );
  }

  @override
  void determineAction() {
    String selectCode = this.selectedCodes.join(",");
    widget.selectAction(selectCode);
    this.closePopup();
  }

  @override
  void resetAction() {
    this.selectedCodes = [];
    setState(() {});
  }
}

// 选项item
class CustomerFilterItem extends StatelessWidget {
  final String title;
  final bool isSelected;

  CustomerFilterItem({required this.title, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      padding: EdgeInsets.only(left: 15),
      child: Column(
        children: [
          Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: TextStyle(
                  color:
                      this.isSelected ? Color(0xFF35C561) : Color(0xFF666666),
                  fontSize: 14,
                ),
              ),
              Spacer(),
              Visibility(
                visible: this.isSelected,
                child: Image.asset(
                  'assets/images/customer/customer_filter_select_icon.png',
                  width: 22,
                  height: 22,
                ),
              ),
              SizedBox(width: 15),
            ],
          ),
          Spacer(),
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 0.5),
        ],
      ),
    );
  }
}
