import 'package:flutter/material.dart';

Future showCustomerMoreMenu({
  required BuildContext context,
  required GlobalKey targetKey,
  required List<CustomerMoreSourceItem> itemSource,
  ValueChanged<dynamic>? itemTap,
}) {
  dynamic renderBox = targetKey.currentContext?.findRenderObject();
  Offset offset = renderBox
      .localToGlobal(Offset(renderBox.size.width, renderBox.size.height));
  return showGeneralDialog(
    context: context,
    barrierLabel: "",
    barrierDismissible: true,
    // 点击遮罩是否关闭对话框
    transitionDuration: const Duration(milliseconds: 200),
    // 对话框打开/关闭的动画时长
    pageBuilder: (
      // 构建对话框内部UI
      BuildContext context,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      return Stack(
        children: [
          Positioned(
            child: CustomerDetailMorePopover(
              itemSource: itemSource,
              itemTap: itemTap,
            ),
            top: offset.dy,
            right: 10,
          )
        ],
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
  );
}

class CustomerDetailMorePopover extends StatelessWidget {
  final List<CustomerMoreSourceItem> itemSource;
  final ValueChanged<dynamic>? itemTap;

  CustomerDetailMorePopover({required this.itemSource, this.itemTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minWidth: 148),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            margin: EdgeInsets.only(right: 10),
            child: _CustomerTriangleView(),
          ),
          Container(
            padding: EdgeInsets.only(left: 10, right: 10),
            decoration: BoxDecoration(
              color: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Column(
              children: this.generateItems(context),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> generateItems(BuildContext context) {
    List<Widget> items = [];
    for (int index = 0; index < this.itemSource.length; index++) {
      CustomerMoreSourceItem data = this.itemSource[index];
      items.add(
        GestureDetector(
          onTap: () {
            if (this.itemTap != null) {
              this.itemTap!(data.id);
            }
            Navigator.of(context).pop();
          },
          behavior: HitTestBehavior.opaque,
          child: Container(
            height: 40,
            width: 140,
            alignment: Alignment.center,
            child: Text(
              data.title,
              style: TextStyle(color: Color(0xFF333333), fontSize: 14),
            ),
          ),
        ),
      );
      if (index < this.itemSource.length - 1) {
        items.add(Divider(color: Color(0xFFF6F6F6),height: 0.5,));
      }
    }
    return items;
  }
}

class _CustomerTriangleView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CustomerTrianglePainter(),
      child: Container(
        width: 10,
        height: 5,
      ),
    );
  }
}

class _CustomerTrianglePainter extends CustomPainter {
  _CustomerTrianglePainter();

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = Color(0xFFFFFFFF);
    paint.style = PaintingStyle.fill;

    Path path = Path()
      ..moveTo(5, 0)
      ..lineTo(10, 5)
      ..lineTo(0, 5);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class CustomerMoreSourceItem {
  String title;
  dynamic id;
  CustomerMoreSourceItem({required this.title, this.id});
}
