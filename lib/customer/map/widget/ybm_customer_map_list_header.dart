import 'package:flutter/material.dart';

class YBMCustomerMapListHeaderWidget extends StatelessWidget {
  final bool isTop;

  YBMCustomerMapListHeaderWidget({required this.isTop});

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: SizedB<PERSON>(
        height: this.isTop ? 10 : 25,
        child: Container(
          decoration: BoxDecoration(
              color: Color(0xFFF7F7F8),
              borderRadius: BorderRadius.vertical(top: Radius.circular(8))),
          alignment: Alignment.topCenter,
          child: Visibility(
            visible: !this.isTop,
            child: Container(
              height: 5,
              width: 35,
              margin: EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: Color(0xFFD5D5D5),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
