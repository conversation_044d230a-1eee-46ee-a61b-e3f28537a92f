import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_area_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_content_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

typedef YBMCustomerMapTopChangeFilter = void Function(
    Map<String, dynamic> params);

class YBMCustomerMapFilterWidget extends StatefulWidget {
  final YBMCustomerMapTopChangeFilter changeFilter;

  /// 是否存在外部传入的客户信息 如果外部未传入 则默认添加已注册选项 （降低地图标点加载延迟）
  final bool hasTargetMerchant;

  YBMCustomerMapFilterWidget({
    required this.changeFilter,
    this.hasTargetMerchant = false,
  });

  @override
  State<StatefulWidget> createState() {
    return YBMCustomerMapFilterWidgetState();
  }
}

class YBMCustomerMapFilterWidgetState
    extends State<YBMCustomerMapFilterWidget> {
  final List<_CustomerMapFilterData> filterList = [
    _CustomerMapFilterData(title: "新开业", option: 0),
    _CustomerMapFilterData(title: "已注册", option: 1),
    _CustomerMapFilterData(title: "未注册", option: 2),
    _CustomerMapFilterData(title: "已认领", option: 3),
    _CustomerMapFilterData(title: "未认领", option: 4),
  ];

  Map<String, dynamic> params = {};

  Map<String, String> filterParams = {};
  Map<String, dynamic> cacheParams = {};

  List<ValueNotifier<BackgroundButtonState>> selectController = [
    ValueNotifier<BackgroundButtonState>(BackgroundButtonState.normal),
    ValueNotifier<BackgroundButtonState>(BackgroundButtonState.normal),
    ValueNotifier<BackgroundButtonState>(BackgroundButtonState.normal),
    ValueNotifier<BackgroundButtonState>(BackgroundButtonState.normal),
    ValueNotifier<BackgroundButtonState>(BackgroundButtonState.normal),
  ];

  List<ValueNotifier<BackgroundButtonState>> flagArray = [];

  @override
  void initState() {
    /// 处理默认选中 已注册
    if (!widget.hasTargetMerchant) {
      this.selectController[1].value = BackgroundButtonState.selected;
      this.params['merchantStatus'] = '1';
      this.flagArray.add(this.selectController[1]);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 50,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: filterList.length,
                itemBuilder: (context, index) {
                  return Container(
                    padding: EdgeInsets.fromLTRB(
                        10, 10, index == filterList.length - 1 ? 10 : 0, 10),
                    child: BackgroundStateButton(
                      controller: this.selectController[index],
                      option: filterList[index].option,
                      alignment: Alignment.center,
                      panding: EdgeInsets.only(left: 10, right: 10),
                      title: filterList[index].title,
                      normalDecoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: Color(0xFFEEEEEE),
                      ),
                      selectDecoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: Color(0xFF00B377),
                      ),
                      textStyle:
                          TextStyle(color: Color(0xFF676773), fontSize: 12),
                      selectStyle: TextStyle(
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                          fontWeight: FontWeight.w500),
                      onPressed: onPressed,
                    ),
                  );
                },
              ),
            ),
          ),
          GestureDetector(
            onTap: jumpFilter,
            child: Container(
              height: 30,
              padding: EdgeInsets.only(right: 14, left: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 3,
                    spreadRadius: -2,
                    offset: Offset(-3, 0),
                    color: Color(0x22333333),
                  ),
                  BoxShadow(
                    blurRadius: 0,
                    spreadRadius: 0,
                    offset: Offset(0, -3),
                    color: Color(0xFFFFFFFFF),
                  ),
                  BoxShadow(
                    blurRadius: 0,
                    spreadRadius: 0,
                    offset: Offset(0, 3),
                    color: Color(0xFFFFFFFFF),
                  ),
                ],
              ),
              child: Image.asset(
                'assets/images/customer/customer_map_filter.png',
                width: 22,
                height: 22,
              ),
            ),
          )
        ],
      ),
    );
  }

  void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }

  void onPressed(
    ValueNotifier<BackgroundButtonState> controller,
    dynamic option,
  ) {
    /// 收起页面上的键盘
    FocusScope.of(context).requestFocus(FocusNode());

    bool selectedFlag = !this.flagArray.contains(controller);

    if (!selectedFlag) {
      controller.value = BackgroundButtonState.normal;
      this.flagArray.remove(controller);
    } else {
      this.flagArray.add(controller);
    }

    switch (option) {
      case 0:
        if (selectedFlag) {
          track("mc-map-new");
          this.params['poiRegisterFlag'] = 1;
        } else {
          this.params.remove('poiRegisterFlag');
        }
        break;
      case 1:
        if (selectedFlag) {
          track("mc-map-registered");
          this.params['merchantStatus'] = 1;
          this.selectController[2].value = BackgroundButtonState.normal;
          this.flagArray.remove(this.selectController[2]);
        } else {
          this.params.remove('merchantStatus');
        }
        break;
      case 2:
        if (selectedFlag) {
          track("mc-map-unregistered");
          this.params['merchantStatus'] = 2;
          this.selectController[1].value = BackgroundButtonState.normal;
          this.flagArray.remove(this.selectController[1]);
        } else {
          this.params.remove('merchantStatus');
        }
        break;
      case 3:
        if (selectedFlag) {
          track("mc-map-claimed");
          this.params['poiClaimed'] = 1;
          this.selectController[4].value = BackgroundButtonState.normal;
          this.flagArray.remove(this.selectController[4]);
        } else {
          this.params.remove('poiClaimed');
        }
        break;
      case 4:
        if (selectedFlag) {
          track("mc-map-unclaimed");
          this.params['poiClaimed'] = 2;
          this.selectController[3].value = BackgroundButtonState.normal;
          this.flagArray.remove(this.selectController[3]);
        } else {
          this.params.remove('poiClaimed');
        }
        break;
      default:
    }

    widget.changeFilter(this.getPageParams());
  }

  /// 跳转筛选页面
  void jumpFilter() async {
    /// 收起页面上的键盘
    FocusScope.of(context).requestFocus(FocusNode());

    dynamic result = await showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
          child: Container(
            height: MediaQuery.of(context).size.height - 88,
            child: ObjectFilterContentPage(
              filterParams: this.filterParams,
              cacheParams: this.cacheParams,
              isMapFilter: true,
            ),
          ),
        );
      },
    );

    if (result != null) {
      if (result is Map<String, dynamic>) {
        if (result.containsKey("filterParams")) {
          this.filterParams = result["filterParams"];
        }
        if (result.containsKey("cacheParams")) {
          this.cacheParams = result["cacheParams"];

          /// 如果存在 parentParam 则证明选择了区域，需要地图定位
          if (this.cacheParams.containsKey("parentParam")) {
            Map<String, dynamic> areaParams = {
              "parentParam": this.cacheParams["parentParam"],
              "areaParam": this.cacheParams["areaParam"]
            };
            SelectObjectAreaCallBackModel model =
                SelectObjectAreaCallBackModel.fromJsonMap(areaParams);
            String locationName = "";
            if (model.parentParam != null) {
              if (model.parentParam!.length > 1 &&
                  model.parentParam!.first.areaCode !=
                      model.parentParam!.last.areaCode) {
                locationName = model.parentParam!.first.areaName +
                    model.parentParam!.last.areaName;
              } else {
                locationName = model.parentParam!.first.areaName;
              }
            }
            this.filterParams['locationName'] = locationName;
            print("cache - $locationName");
          }
        }
        widget.changeFilter(this.getPageParams());
      }
    }
  }

  Map<String, dynamic> getPageParams() {
    Map<String, dynamic> pageParams = {};
    pageParams.addAll(this.params);
    pageParams.addAll(this.filterParams);
    return pageParams;
  }
}

class _CustomerMapFilterData {
  final String title;
  final dynamic option;

  _CustomerMapFilterData({required this.title, required this.option});
}
