import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/popover/common_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class YBMCustomerMapItemWidget extends StatelessWidget with EventBusObserver {
  final YBMCustomerPrivateItemModel model;
  final bool hasLocation;

  YBMCustomerMapItemWidget({required this.model, required this.hasLocation});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
      child: Container(
        padding: EdgeInsets.fromLTRB(10, 15, 10, model.isRevice ? 8 : 0),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            merchantNameWidget(),
            merchantTagWidget(),
            merchantSalesWidget(),
            getControlGoodsWidget(context),
            Visibility(
              visible: model.isRevice,
              child: getActionWidget(context),
            ),
          ],
        ),
      ),
    );
  }

  /// 客户名称
  Widget merchantNameWidget() {
    Color statusColor = Color(0xFF00B377);
    if (model.merchantStatusName == "已冻结") {
      statusColor = Color(0xFF8E8E93);
    } else if (model.merchantStatusName == "未注册" ||
        model.merchantStatusName == "沉默" ||
        model.merchantStatusName == "流失") {
      statusColor = Color(0xFFFF2021);
    }
    return Row(
      children: [
        Expanded(
          child: Text(
            this.model.getName(),
            style: TextStyle(
              color: Color(0xFF333333),
              fontSize: 17,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 10),
        Text(
          "${model.merchantStatusName}",
          style: TextStyle(color: statusColor, fontSize: 14),
        )
      ],
    );
  }

  // 客户标签
  Widget merchantTagWidget() {
    List<Widget> tags = [];
    // 资质状态
    if (this.model.licenseStatusName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.licenseStatusName}", Color(0xFFECA100)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 药店类型
    if (this.model.customerTypeName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.customerTypeName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 药店级别
    if (this.model.biLevelName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.biLevelName}" + "级别", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 生命周期
    if (this.model.biLifecycleName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.biLifecycleName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 审核状态
    if (this.model.statusName?.toString().isNotEmpty == true) {
      tags.add(
        getTagItem("${this.model.statusName}", Color(0xFF35C561)),
      );
      tags.add(SizedBox(width: 5));
    }
    // 是否新开业
    if ("${this.model.poiRegisterFlag}" == "1") {
      tags.add(
        getTagItem("NEW", Color(0xFFFF2021)),
      );
      tags.add(SizedBox(width: 5));
    }
    tags.add(Spacer());
    // 地址
    tags.add(merchantAddressWidget());
    return Container(
      padding: EdgeInsets.only(top: 5),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: tags,
      ),
    );
  }

  // 标签
  Widget getTagItem(String tag, Color color) {
    return Container(
      padding: EdgeInsets.fromLTRB(3.5, 0.5, 3.5, 0.5),
      decoration: BoxDecoration(
        border: Border.all(color: color, width: 0.5),
      ),
      child: Text(
        tag,
        style: TextStyle(color: color, fontSize: 10),
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr = this.hasLocation ? "距您${model.distance}" : "获取不到当前位置";
    return Container(
      padding: EdgeInsets.only(left: 10),
      child: Row(
        children: [
          Image.asset(
            'assets/images/customer/customer_map_location.png',
            width: 8,
            height: 11,
          ),
          SizedBox(width: 5),
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
        ],
      ),
    );
  }

  // 销售数据
  Widget merchantSalesWidget() {
    return Visibility(
      visible: model.isRevice,
      child: Container(
        padding: EdgeInsets.only(top: 10),
        child: Row(
          children: [
            salesWidget(
                "${model.thisMonthOrderAmt}".replaceAll('元', ""), '本月采购额'),
            SizedBox(width: 10),
            salesWidget(
                "${model.lastMonthOrderAmt}".replaceAll('元', ""), '上月采购额'),
          ],
        ),
      ),
    );
  }

  Widget salesWidget(String price, String title) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.only(top: 10, bottom: 10),
        decoration: BoxDecoration(
          color: Color(0xFFFEFCF6),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: [
            RichText(
              text: TextSpan(
                  text: price,
                  style: TextStyle(
                    color: Color(0xFFFF6E12),
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                  children: [
                    TextSpan(
                      text: '元',
                      style: TextStyle(
                        color: Color(0xFFFF6E12),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ]),
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              title,
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
            )
          ],
        ),
      ),
    );
  }

  /// 控销商品集入口
  Widget getControlGoodsWidget(BuildContext context) {
    List<Widget> list = [];

    list.addAll(this.model.bindSkuCollect?.map(
          (e) {
            if ("${e.receiveType}" == "1") {
              if (e.permissionClaimedFlag == true) {
                return getControlItem(context, e);
              } else {
                return getControlOtherRevice(e);
              }
            } else {
              return getControlItemRevice(context, e);
            }
          },
        ).toList() ??
        []);
    return Container(
      child: Column(
        children: list,
      ),
    );
  }

  Widget getControlItem(
      BuildContext context, CustomerSkuCollectData controlModel) {
    GlobalKey _authKey = GlobalKey();
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            this.jumpControlGoods(context, controlModel);
          },
          behavior: HitTestBehavior.opaque,
          child: Container(
            padding: EdgeInsets.only(top: 10, bottom: 10),
            child: Row(
              children: [
                Text(
                  "${controlModel.skuCollectName}",
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 5),
                Text(
                  "(还有${controlModel.bindCountdown ?? '-'}天掉落)",
                  style: TextStyle(
                    color: Color(0xFF8E8E93),
                    fontSize: 12,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    showPopoverTips(
                        context, _authKey, controlModel.tips ?? "--");
                  },
                  key: _authKey,
                  child: Image.asset(
                    'assets/images/funnel/funnel_tips_icon.png',
                    width: 12,
                    height: 12,
                  ),
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10, left: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                Spacer(),
                Image.asset(
                  'assets/images/base/icon_arrow_right.png',
                  width: 12,
                  height: 12,
                ),
              ],
            ),
          ),
        ),
        Divider(color: Color(0xFFEEEEEE), height: 0.5, thickness: 0.5),
      ],
    );
  }

  /// 商品集掉落规则弹窗
  void showPopoverTips(
      BuildContext context, GlobalKey anchorKey, String content) {
    showTipsPopover(
      context: context,
      anchorKey: anchorKey,
      content: Text(
        content,
        style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
      ),
    );
  }

  Widget getControlOtherRevice(CustomerSkuCollectData controlModel) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 10),
          child: Row(
            children: [
              Text(
                "${controlModel.skuCollectName}",
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 14,
                ),
              ),
              Spacer(),
              Text(
                'BD：${controlModel.oaUserName}',
                style: TextStyle(color: Color(0xFF333333), fontSize: 12),
              ),
            ],
          ),
        ),
        Divider(color: Color(0xFFEEEEEE), height: 0.5, thickness: 0.5),
      ],
    );
  }

  Widget getControlItemRevice(
      BuildContext context, CustomerSkuCollectData controlModel) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 10),
          child: Row(
            children: [
              Text(
                "${controlModel.skuCollectName}",
                style: TextStyle(
                  color: Color(0xFF333333),
                  fontSize: 14,
                ),
              ),
              Spacer(),
              TextButton(
                onPressed: () {
                  this.reviceAction(context, controlModel);
                },
                child: Container(
                  height: 25,
                  decoration: BoxDecoration(
                    color: Color(0xFF00B377),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  padding: EdgeInsets.only(left: 20, right: 20),
                  child: Center(
                    child: Text(
                      "认领",
                      style: TextStyle(
                          color: Color(0xFFFFFFFF),
                          fontSize: 12,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(left: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ],
          ),
        ),
        Divider(color: Color(0xFFEEEEEE), height: 0.5, thickness: 0.5),
      ],
    );
  }

  /// 认领商品集事件
  void reviceAction(BuildContext context, CustomerSkuCollectData controlModel) {
    showCommonAlert(
      context: context,
      title: "确认是否认领${controlModel.skuCollectName}",
      actions: [
        CommonAlertAction(
          title: '取消',
          style: CommonAlertActionStyle.cancle,
        ),
        CommonAlertAction(
          title: '确认',
          onPressed: () {
            this.requestReviceCustomer(
                context, "${controlModel.skuCollectCode}");
          },
        ),
      ],
    );
  }

  /// 认领请求
  void requestReviceCustomer(
      BuildContext context, String skuCollectCodes) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {"id": model.id, "skuCollectCodes": skuCollectCodes},
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      XYYContainer.toastChannel.toast("认领成功");
      eventBus.sendMessage(CustomerEventBusName.YBM_MAP_REVICE, arg: {
        'customerId': model.id,
        'collectCode': skuCollectCodes,
      });
    } else {
      if (result.code != null && result.code == 405) {
        showCommonAlert(
          context: context,
          title: '已达到私海数量最大限度，无法认领',
          actions: [
            CommonAlertAction(title: '确认'),
          ],
        );
      }
      XYYContainer.toastChannel.toast('${result.errorMsg ?? "认领客户失败"}');
    }
  }

  /// 底部按钮
  Widget getActionWidget(BuildContext context) {
    return Container(
      height: 50,
      child: Row(
        children: [
          Image.asset(
            'assets/images/customer/customer_private_visit_icon.png',
            width: 12,
          ),
          SizedBox(width: 4),
          Text(
            this.hasVisitTime ? '距上次拜访' : '未拜访',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '${this.model.overLastVisitDays}',
              style: TextStyle(color: Color(0xFF333333), fontSize: 12),
            ),
          ),
          Visibility(
            visible: this.hasVisitTime,
            child: Text(
              '天',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
          ),
          Spacer(),
          this.buttonFor('拨打电话', () {
            this.callPhoneAction(context);
          }),
          this.buttonFor('添加拜访', this.jumpToAddVisit),
        ],
      ),
    );
  }

  /// 是否拜访过
  bool get hasVisitTime {
    return this.model.overLastVisitDays != "-";
  }

  Widget buttonFor(String title, VoidCallback onPressed) {
    return TextButton(
      onPressed: onPressed,
      child: Container(
        height: 30,
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xFF8E8E93), width: 0.5),
          borderRadius: BorderRadius.circular(15),
        ),
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Center(
          child: Text(
            title,
            style: TextStyle(color: Color(0xFF333333), fontSize: 14),
          ),
        ),
      ),
      style: ButtonStyle(
        overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
        padding:
            MaterialStateProperty.all<EdgeInsets>(EdgeInsets.only(left: 10)),
        minimumSize: MaterialStateProperty.all<Size>(Size.zero),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  /// 跳转商品集
  void jumpControlGoods(
      BuildContext context, CustomerSkuCollectData controlModel) async {
    if ('${controlModel.skuCollectType}' == "1") {
      /// 普药商品集跳转 商品管理
      String router =
          "/goods_recommend_page?merchantId=${model.merchantId}&customerId=${model.id}&selectedIndex=4";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    } else {
      /// 其他控销商品集跳转 控销管理页面
      var router =
          "/control_manager_page?merchantId=${model.merchantId}&customerId=${model.id}";
      router = Uri.encodeFull(router);
      XYYContainer.open(router);
    }
  }

  /// 跳转添加拜访
  void jumpToAddVisit() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': model.id,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      }
    }
  }

  /// 拨打电话
  void callPhoneAction(BuildContext context) async {
    if (model.contactList == null || model.contactList?.length == 0) {
      XYYContainer.toastChannel.toast('联系人为空，请添加联系人');
      return;
    }
    YBMCustomerCallPhoneWidget.showContactListView(
      context: context,
      contactList: model.contactList ?? [],
      customerId: "${model.id ?? ""}",
      customerName: model.getName(),
    );
  }
}
