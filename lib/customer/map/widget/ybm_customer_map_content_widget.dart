import 'dart:ui';

import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_map_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/utils/ybm_customer_map_controller.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/customer_map_navigation_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_bottom.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_filter_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_list_header.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
// ignore: implementation_imports
import 'package:flutter_easyrefresh/src/widget/empty_widget.dart';

class YBMCustomerMapContentWidget extends StatefulWidget {
  final OnLoadCallback onLoadMore;

  final YBMCustomerMapTopChangeFilter changeFilter;

  final YBMCustomerMapController sourceController;

  final YBMCustomerMapMerchantValueNotifier merchantSource;

  final YBMCustomerMapListController listController;

  final CustomerMapTargetMerchantModel? targetModel;

  YBMCustomerMapContentWidget({
    required this.onLoadMore,
    required this.changeFilter,
    required this.sourceController,
    required this.merchantSource,
    required this.listController,
    this.targetModel,
  });

  @override
  State<StatefulWidget> createState() {
    return YBMCustomerMapContentWidgetState();
  }
}

class YBMCustomerMapContentWidgetState
    extends State<YBMCustomerMapContentWidget>
    with TickerProviderStateMixin, EventBusObserver {
  final double minTopDistance = 0;
  late double maxTopDistance;

  Drag? drag;
  DragStartDetails? startDetails;

  /// 客户列表是否可滚动
  bool listCanScroll = false;

  /// 客户列表是否 正在进行滑动动画
  bool bouncing = false;

  /// 当前列表距页面顶部距离
  double topDistance = 500;

  late ValueNotifier<double> distanceNotifier;

  /// 是否展示列表
  bool isShowListView = false;

  /// 是否展示收起列表按钮
  bool isShowHidden = false;

  /// 列表控制器
  ScrollController _scrollController = ScrollController();

  /// 地图选中标点的模型
  YBMCustomerMapTagModel? _merchantModel;

  /// 记录上一次是否有区域选择条件
  bool hasLocation = false;

  late AnimationController _controller;
  late Animation<double> _animation;
  late Animation<double> _curve;

  /// 页面整体高度
  double get pageHeight {
    return pageSize.height;
  }

  Size? _pageSize;
  Size get pageSize {
    if (_pageSize == null) {
      /// 屏幕大小
      Size screenSize = MediaQuery.of(context).size;
      EdgeInsets screenPadding = MediaQuery.of(context).viewPadding;

      /// 获取地图组件大小
      _pageSize = context.findRenderObject()?.paintBounds.size ??
          Size(screenSize.width,
              screenSize.height - screenPadding.top - 44 - 50);
    }
    return _pageSize!;
  }

  @override
  void initState() {
    /// 列表滑动监听
    _scrollController.addListener(() {
      if (_scrollController.offset <= 0) {
        _scrollController.jumpTo(0);
        if (this.listCanScroll) {
          setState(() {
            this.listCanScroll = false;
          });
        }
      } else {
        if (!this.isShowHidden) {
          setState(() {
            this.isShowHidden = true;
          });
        }
      }
    });

    double screenHeight = window.physicalSize.height / window.devicePixelRatio;
    double screenPaddingTop = window.viewPadding.top / window.devicePixelRatio;
    if (!this.isShowListView) {
      this.maxTopDistance = screenHeight - screenPaddingTop;
    } else {
      this.maxTopDistance = screenHeight - screenPaddingTop - 300;
    }

    /// 初始默认顶部间距为最大间距
    this.topDistance = this.maxTopDistance;

    this.distanceNotifier = ValueNotifier(this.topDistance);

    /// 创建动画管理器
    this.createAnimation();

    /// 设置外部调用方法
    this.configSorceController();

    /// 设置选中店铺调用方法
    widget.sourceController.selectedMerchant = this.selectedMerchant;

    widget.sourceController.showListAction = this.mapShowListAction;

    /// 订阅认领通知
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_MAP_REVICE,
        callback: (arg) async {
          var userInfo = await UserInfoUtil.getUserInfo();
          var dataSource = widget.merchantSource.value;
          var customerId = arg['customerId'];
          var collectionCode = arg['collectCode'];
          var model = dataSource.firstWhere(
            (element) => element.id == customerId,
            orElse: () => YBMCustomerPrivateItemModel(),
          );
          if (model.id != null) {
            model.bindSkuCollect?.forEach((element) {
              if (element.skuCollectCode == collectionCode) {
                element.receiveType = "1";
                element.permissionClaimedFlag = true;
                element.oaUserId = userInfo?.sysUserId;
                element.oaUserName = userInfo?.realName;
              }
            });
          }
          widget.merchantSource.value = dataSource;
        });

    super.initState();
  }

  /// 地图标点 选中店铺回调设置
  void selectedMerchant(YBMCustomerMapTagModel? model) {
    setState(() {
      this._merchantModel = model;
    });
  }

  /// 地图页面调用本地 展开列表方法
  void mapShowListAction(bool isShow) {
    if (isShow) {
      this.showListAction();
    } else {
      this.hiddenListAction();
    }
  }

  /// 设置外部调用方法
  void configSorceController() {
    widget.listController.showList = showListAction;
  }

  @override
  void dispose() {
    _scrollController.removeListener(() {});
    _scrollController.dispose();
    _controller.dispose();
    eventBus.removeListener(observer: this);
    super.dispose();
  }

  void createAnimation() {
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );
    _curve = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
    _animation =
        Tween<double>(begin: topDistance, end: topDistance).animate(_curve);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        YBMCustomerMapFilterWidget(
          hasTargetMerchant: widget.targetModel != null,
          changeFilter: (params) {
            /// 此处判断 是否存在区域筛选 存在则调用地图相关方法 重新定位地图中心点
            if (widget.sourceController.locationFilterChange != null) {
              if (params.containsKey('locationName')) {
                this.hasLocation = true;
                widget.sourceController
                    .locationFilterChange!("${params['locationName']}");
              } else {
                if (this.hasLocation) {
                  widget.sourceController.locationFilterChange!("");
                  this.hasLocation = false;
                }
              }
            }
            // 移除冗余的 locationName字段后 调用筛选条件变更方法
            Map<String, dynamic> allParams = params;
            allParams.remove("locationName");
            widget.changeFilter(allParams);

            /// 通知地图页面移除marker
            if (widget.sourceController.filterChange != null) {
              widget.sourceController.filterChange!();
            }
          },
        ),
        Expanded(
          child: Stack(
            alignment: Alignment.center,
            children: [
              YBMCustomerMapWidget(
                sourceController: widget.sourceController,
                targetModel: widget.targetModel,
              ),
              bottomShowContainer(),
              floatingContainer(),
              animatedListWidget(),
              bottomShowMapContainer(),
            ],
          ),
        ),
      ],
    );
  }

  Widget bottomShowContainer() {
    return Visibility(
      visible: !this.isShowListView,
      child: Positioned(
        bottom: MediaQuery.of(context).viewPadding.bottom <= 0
            ? 50
            : MediaQuery.of(context).viewPadding.bottom + 10,
        child: GestureDetector(
          onTap: showListAction,
          child: YBMCustomerMapBottomButtonWidget.showList(),
        ),
      ),
    );
  }

  Widget bottomShowMapContainer() {
    return Visibility(
      visible: this.isShowHidden,
      child: Positioned(
        bottom: MediaQuery.of(context).viewPadding.bottom <= 0
            ? 50
            : MediaQuery.of(context).viewPadding.bottom + 10,
        child: GestureDetector(
          onTap: hiddenListAction,
          child: YBMCustomerMapBottomButtonWidget.showMap(),
        ),
      ),
    );
  }

  /// 导航/上报错误
  Widget floatingContainer() {
    return Visibility(
      visible: this._merchantModel != null,
      child: Positioned(
        right: 10,
        bottom: 300,
        child: CustomerMapNavigationItem(
          navigationAction: () {
            // 导航
            customerNavigationAndReportError(this._merchantModel?.id,
                isNavigation: true);
          },
          reportAction: () {
            // 错误上报
            customerNavigationAndReportError(this._merchantModel?.id,
                isNavigation: false);
          },
        ),
      ),
    );
  }

  Widget animatedListWidget() {
    return ValueListenableBuilder<double>(
      valueListenable: this.distanceNotifier,
      builder: (context, value, child) {
        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, this.bouncing ? _animation.value : value),
              child: GestureDetector(
                child: getListView(),
                onVerticalDragStart: dragStart,
                onVerticalDragUpdate: dragEvent,
                onVerticalDragEnd: dragEnd,
                onVerticalDragCancel: dragCancel,
              ),
            );
          },
        );
      },
    );
  }

  Widget getListView() {
    return Container(
      color: Colors.white,
      child: ValueListenableBuilder<List<YBMCustomerPrivateItemModel>>(
        valueListenable: widget.merchantSource,
        builder: (context, value, child) {
          return EasyRefresh.builder(
            scrollController: _scrollController,
            onLoad: widget.merchantSource.isLastPage ? null : widget.onLoadMore,
            builder: (context, physics, header, footer) {
              List<Widget> slivers = [
                YBMCustomerMapListHeaderWidget(
                  isTop: this.topDistance <= this.minTopDistance,
                ),
              ];
              if (value.length > 0) {
                slivers.add(
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        YBMCustomerPrivateItemModel model = value[index];
                        return GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            this.jumpDetail(model);
                          },
                          child: YBMCustomerMapItemWidget(
                            hasLocation: true,
                            model: model,
                          ),
                        );
                      },
                      childCount: value.length,
                    ),
                  ),
                );
              } else {
                /// 空页面
                slivers.add(
                  EmptyWidget(
                    child: PageStateWidget(state: PageState.Empty),
                  ),
                );
              }
              if (footer != null) {
                slivers.add(footer);
              }
              return CustomScrollView(
                controller: _scrollController,
                physics: this.listCanScroll
                    ? physics
                    : NeverScrollableScrollPhysics(),
                slivers: slivers,
              );
            },
          );
        },
      ),
    );
  }

  /// 跳转详情
  void jumpDetail(YBMCustomerPrivateItemModel model) {
    jumpCustomerPageByCustomerId(model.id, canJumpChildPrivateForBDM: true);
  }

  /// 展示列表点击事件
  void showListAction() {
    if (this.isShowListView == false) {
      setState(() {
        this.isShowListView = true;
        this.maxTopDistance = this.pageHeight - 300;
        updateBaseViewPosition(this.topDistance, isShow: true);
      });
    }
  }

  /// 隐藏列表点击事件
  void hiddenListAction() {
    if (this.isShowListView == true) {
      setState(() {
        this.isShowListView = false;
        this.isShowHidden = false;
        this.listCanScroll = false;
        this.maxTopDistance = this.pageHeight;
        _scrollController.jumpTo(0);
        positionAnimation(topDistance, maxTopDistance);
        this.topDistance = this.pageHeight;
        this.distanceNotifier.value = this.topDistance;
      });
    }
  }

  void dragCancel() {
    drag?.cancel();
    updateBaseViewPosition(topDistance);
  }

  void dragStart(DragStartDetails details) {
    bouncing = false;
    this.isShowHidden = false;
    startDetails = details;
    if (this._scrollController.offset <= 0) {
      this.listCanScroll = false;
    }
  }

  void dragEnd(DragEndDetails details) {
    if (this.listCanScroll == true) {
      drag?.end(details);
    }
    updateBaseViewPosition(topDistance);
  }

  void dragEvent(DragUpdateDetails details) {
    // setState(() {
    double offsetY = details.delta.dy;
    double distance = this.topDistance + offsetY;
    this.drag = _scrollController.position.drag(startDetails!, () {});
    if (offsetY <= 0) {
      // 向上滑动
      if (distance <= minTopDistance) {
        // 滑动后位置高于 最小间距 则固定
        this.topDistance = minTopDistance;
        this.distanceNotifier.value = this.topDistance;
        // 设置列表可滚动
        this.listCanScroll = true;
        // 继续更新列表滚动距离
        _scrollController.jumpTo(_scrollController.offset - offsetY);
      } else {
        this.topDistance = distance;
        this.distanceNotifier.value = this.topDistance;
        this.listCanScroll = false;
        // 取消手势更新
        drag?.cancel();
      }
    } else {
      // 向下滑动
      if (distance <= minTopDistance) {
        // 这个条件好像不成立。
        // 滑动后位置高于 最小间距 则固定
        this.topDistance = minTopDistance;
        this.distanceNotifier.value = this.topDistance;
        // 继续更新列表滚动距离
        _scrollController.jumpTo(_scrollController.offset - offsetY);
      } else {
        if (this._scrollController.offset <= 0) {
          this.topDistance = distance;
          this.distanceNotifier.value = this.topDistance;
          // 取消手势更新
          drag?.cancel();
        } else {
          // 继续更新列表滚动距离
          _scrollController.jumpTo(_scrollController.offset - offsetY);
        }
      }
    }
    // });
  }

  void updateBaseViewPosition(double distance, {bool isShow = false}) {
    double flagDistance =
        (maxTopDistance - minTopDistance) / 2 + minTopDistance;
    bouncing = true;
    if (distance <= flagDistance) {
      this.topDistance = minTopDistance;
      this.distanceNotifier.value = this.topDistance;
      positionAnimation(distance, minTopDistance);
    } else if (distance <= maxTopDistance) {
      this.topDistance = maxTopDistance;
      this.distanceNotifier.value = this.topDistance;
      positionAnimation(distance, maxTopDistance);
    } else {
      if (!isShow) {
        this.topDistance = this.pageHeight;
        this.distanceNotifier.value = this.topDistance;
        positionAnimation(distance, this.pageHeight);
        this.isShowListView = false;
        setState(() {});
      } else {
        this.topDistance = maxTopDistance;
        this.distanceNotifier.value = this.topDistance;
        positionAnimation(distance, this.maxTopDistance);
      }
    }
  }

  void positionAnimation(double start, double end) {
    bouncing = true;
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );
    _curve = CurvedAnimation(parent: _controller, curve: Curves.easeIn);
    _animation = Tween(begin: start, end: end).animate(_curve);
    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed ||
          status == AnimationStatus.dismissed) {
        bouncing = false;
      }
    });
    _controller.forward();
  }
}

class YBMCustomerMapMerchantValueNotifier
    extends ValueNotifier<List<YBMCustomerPrivateItemModel>> {
  YBMCustomerMapMerchantValueNotifier(this._source) : super(_source);

  @override
  List<YBMCustomerPrivateItemModel> get value => _source;
  List<YBMCustomerPrivateItemModel> _source;

  @override
  set value(List<YBMCustomerPrivateItemModel> newValue) {
    _source = newValue;
    notifyListeners();
  }

  bool get isLastPage => _isLastPage;
  bool _isLastPage = false;

  set isLastPage(bool newValue) {
    _isLastPage = newValue;
    notifyListeners();
  }
}
