import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/location/location_manager.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_map_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/utils/ybm_customer_map_controller.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/utils/ybm_customer_map_marker_image.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/log_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';
import 'package:flutter_baidu_mapapi_map/flutter_baidu_mapapi_map.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
import 'package:collection/collection.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:keyboard_actions/external/platform_check/platform_check.dart';

class YBMCustomerMapWidget extends StatefulWidget {
  final YBMCustomerMapController sourceController;
  final CustomerMapTargetMerchantModel? targetModel;

  YBMCustomerMapWidget({
    required this.sourceController,
    this.targetModel,
  });

  @override
  State<StatefulWidget> createState() {
    return YBMCustomerMapWidgetState();
  }
}

class YBMCustomerMapWidgetState extends State<YBMCustomerMapWidget> {
  /// 生成marker图片类
  YBMCustomerMarkImageUtils _imageUtils = YBMCustomerMarkImageUtils();

  BMFMapOptions get mapOptions => BMFMapOptions(
        zoomLevel: 17,
        // 1:100
        overlookEnabled: false,
        // 设置不支持倾角
        rotateEnabled: false,
        // 设置不支持旋转
        logoPosition: BMFLogoPosition.RightBottom,
        compassEnabled: true,
        // 是否显示指南针
        compassPosition: BMFPoint(10, 200),
        // 指南针位置
        showMapPoi: false,
        // 展示POI标注
        showMapScaleBar: true,
        // 显示比例尺
        showZoomControl: false, // 不显示缩放图标 （Android）
        // mapScaleBarPosition: BMFPoint(10, mapSize.height - 30), // 比例尺位置
      );

  Size? _mapSize;

  Size get mapSize {
    if (_mapSize == null) {
      /// 屏幕大小
      Size screenSize = window.physicalSize;
      WindowPadding screenPadding = window.viewPadding;

      /// Android是物理像素，iOS是逻辑像素
      if (Platform.isAndroid) {
        _mapSize = Size(
            screenSize.width,
            screenSize.height -
                screenPadding.top -
                94 * window.devicePixelRatio);
      } else {
        /// 获取地图组件大小
        _mapSize = Size(
            screenSize.width / window.devicePixelRatio,
            screenSize.height / window.devicePixelRatio -
                screenPadding.top / window.devicePixelRatio -
                94);
      }
    }
    return _mapSize!;
  }

  // 当前用户位置
  BMFCoordinate? userCoordinate;

  // 当前选中的客户经纬度
  BMFCoordinate? merchantCoordinate;

  // 当前用户朝向
  BMFHeading? userHeading;

  late BMFMapController _controller;

  /// 标识当前是否是店铺marker  0-店铺 1-区域
  int flag = 0;

  /// 选中的覆盖物id （圆形区域）
  String overlyId = "";

  /// 缓存选中的marker参数
  Map<String, Object?>? cacheMarker;

  List<BMFMarker> markers = [];

  /// 缓存地图状态
  BMFMapStatus? mapStatus;

  /// 缓存筛选传递过来的区域信息
  String locationName = "";

  /// 判断是否是带客户信息的第一次打开
  bool merchantFlag = false;

  /// 记录mareker 设置时间 如果添加时与记录时间不一致 则放弃本次操作
  int? markerSettingTime;

  bool isMapLoadFinished = false;

  @override
  void initState() {
    this.configTargetModel();
    this.configSorceController();
    super.initState();
  }

  /// 设置外部传过来的参数
  void configTargetModel() {
    /// 设置店铺坐标
    if (widget.targetModel != null) {
      this.merchantFlag = true;
      double latitude =
          double.tryParse("${widget.targetModel!.latitude}") ?? 39.983913;
      double longitude =
          double.tryParse("${widget.targetModel!.longitude}") ?? 116.494816;
      this.merchantCoordinate = BMFCoordinate(latitude, longitude);
    }
    // 设置店铺选中
  }

  /// 设置外部调用方法
  void configSorceController() {
    widget.sourceController.changeMarker = settingMarker;
    widget.sourceController.locationFilterChange = locationFilterChange;
    widget.sourceController.settingSelectedMerchant = settingSelectedMerchant;
    widget.sourceController.searchMerchant = settingSearhResult;
    widget.sourceController.filterChange = filterChange;
  }

  /// 设置搜索结果
  void settingSearhResult(YBMCustomerPrivateItemModel model) {
    LogUtil.log("settingSearhResult");
    double latitude = double.tryParse("${model.poiLatitude}") ?? 39.983913;
    double longitude = double.tryParse("${model.poiLongitude}") ?? 116.494816;

    BMFCoordinate coordinate = BMFCoordinate(latitude, longitude);

    this._controller.setCenterCoordinate(coordinate, true);

    this.merchantCoordinate = coordinate;

    Future.delayed(Duration(milliseconds: 500), () {
      this.triggierMapChangeForRequest();
    });
  }

  /// 筛选条件变更调用 用来移除页面选中的marker
  void filterChange() {
    LogUtil.log("filterChange");

    /// 如果筛选条件变更 则清理覆盖物
    this.resetMapOverly();

    /// 设置选中marker的样式变更
    this.resetCacheMarkerStyle();
  }

  @override
  void dispose() {
    LocationManager().removeLocationCallback(this);
    LocationManager().removeUpdatingHeading(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.log("build");
    return BMFMapWidget(
      onBMFMapCreated: (controller) {
        _controller = controller;

        /// 设置地图相关回调
        this.settingMapCallback();
      },
      mapOptions: mapOptions,
    );
  }

  /// 设置地图变化回调
  void settingMapCallback() {
    LogUtil.log("settingMapCallback");

    /// 地图加载完成回调
    this._controller.setMapDidLoadCallback(callback: mapLoadFinished);

    /// 地图区域改变完成后回调
    this
        ._controller
        .setMapRegionDidChangeWithReasonCallback(callback: regionDidChange);

    this._controller.setMapRegionDidChangeCallback(callback: (mapStatus) {
      // 收起键盘
      widget.sourceController.dismissKeyboard();
      this.mapStatus = mapStatus;
    });

    /// 设置 marker点击回调
    this._controller.setMapClickedMarkerCallback(callback: clickMarker);
  }

  /// 地图加载完成
  void mapLoadFinished() {
    LogUtil.log("mapLoadFinished");
    isMapLoadFinished = true;

    /// 初次加载获取当前定位 并确定中心点
    this.requestLocation();

    /// 设置个人坐标点
    this.settingUserLocationIcon();

    /// 更新比例尺位置
    Future.delayed(Duration(milliseconds: 200), () {
      if (Platform.isIOS) {
        this._controller.updateMapOptions(
              BMFMapOptions(
                zoomLevel: 17,
                overlookEnabled: false,
                // 设置不支持倾角
                rotateEnabled: false,
                // 设置不支持旋转
                logoPosition: BMFLogoPosition.RightBottom,
                compassEnabled: true,
                // 是否显示指南针
                compassPosition: BMFPoint(10, 200),
                // 指南针位置
                showMapPoi: false,
                // 展示POI标注
                showMapScaleBar: true,
                // 显示比例尺
                mapScaleBarPosition: BMFPoint(15, mapSize.height - 40), // 比例尺位置
              ),
            );
      }
    });
  }

  /// 地图区域改变
  void regionDidChange(
    BMFMapStatus mapStatus,
    BMFRegionChangeReason regionChangeReason,
  ) async {
    // 收起键盘
    widget.sourceController.dismissKeyboard();

    this.mapStatus = mapStatus;
    if (regionChangeReason == BMFRegionChangeReason.Gesture &&
        isMapLoadFinished) {
      this.triggierMapChangeForRequest();
    }
    if (this.overlyId.isNotEmpty) {
      if (this.merchantCoordinate != null) {
        BMFPoint? point = await this
            ._controller
            .convertCoordinateToScreenPoint(this.merchantCoordinate!);
        if (point != null) {
          if (point.x < 0 || point.x > this.mapSize.width) {
            this.markerCrossBorder();
          }
          if (point.y < 0 || point.y > this.mapSize.height) {
            this.markerCrossBorder();
          }
        } else {
          this.markerCrossBorder();
        }
      }
    }
  }

  /// 选中mark越界处理
  void markerCrossBorder() {
    LogUtil.log("markerCrossBorder");
    this.resetMapOverly();
    this.merchantCoordinate = null;
    this.resetCacheMarkerStyle();
    if (widget.sourceController.showListAction != null) {
      widget.sourceController.showListAction!(false);
    }
  }

  /// 触发地图变化请求
  void triggierMapChangeForRequest() {
    var debounce = debounceCall(() {
      Map<String, dynamic> params = this.getParamsForDidChange();
      // 调用方法 传递参数
      widget.sourceController.mapChange(params);
    });

    debounce();
  }

  /// 获取参数
  Map<String, dynamic> getParamsForDidChange() {
    LogUtil.log("getParamsForDidChange");
    Map<String, dynamic> params = {};
    params["mapType"] = this.getMapSourceType(mapStatus?.fLevel ?? 15);
    params["latitudeTop"] =
        mapStatus?.coordinateBounds?.northeast.latitude ?? 40.02038555614921;
    params["longitudeTop"] =
        mapStatus?.coordinateBounds?.northeast.longitude ?? 116.52120191765269;
    params["latitudeBottom"] =
        mapStatus?.coordinateBounds?.southwest.latitude ?? 39.94741675030062;
    params["longitudeBottom"] =
        mapStatus?.coordinateBounds?.southwest.longitude ?? 116.46842646894149;
    if (overlyId.isEmpty) {
      // 如果没有 图层id 则代表没有选中的店铺， 此时传用地图中心点坐标
      params["latitude"] = mapStatus?.targetGeoPt?.latitude ?? 39.983913;
      params["longitude"] = mapStatus?.targetGeoPt?.longitude ?? 116.494816;
    } else {
      // 有选中的 则传递店铺坐标
      params["latitude"] = merchantCoordinate?.latitude ?? 39.983913;
      params["longitude"] = merchantCoordinate?.longitude ?? 116.494816;
    }

    // 用于计算店铺距离的经纬度
    params['sysLatitude'] = userCoordinate?.latitude ?? 39.983913;
    params['sysLongitude'] = userCoordinate?.longitude ?? 116.494816;
    print("guan1 getParamsForDidChange:${params.toString()}");
    return params;
  }

  /// 根据比例尺获取数据加载样式
  int getMapSourceType(double level) {
    if (level >= 15) {
      return 0;
    }
    if (level < 15 && level >= 12) {
      return 3;
    }
    if (level < 12 && level >= 11) {
      return 2;
    }
    return 1;
  }
}

/// Mark相关方法
extension _Marker on YBMCustomerMapWidgetState {
  void settingSelectedMerchant(YBMCustomerMapTagModel? model) {
    LogUtil.log("settingSelectedMerchant");
    if (model != null) {
      this.markers.forEach((element) {
        if (element.customMap is Map<String, dynamic>) {
          Map<String, dynamic> jsonMap =
              element.customMap as Map<String, dynamic>;
          YBMCustomerMapTagModel tagModel =
              YBMCustomerMapTagModel.fromJson(jsonMap);
          if (tagModel.id == model.id) {
            this.settingSelectedMarker(element);
            if (widget.sourceController.showListAction != null) {
              widget.sourceController.showListAction!(true);
            }
            this._controller.setCenterCoordinate(element.position, true);
          }
        }
      });
    }
  }

  /// 设置marker 数据
  void settingMarker(YBMCustomerMapModel model) async {
    LogUtil.log("settingMarker");
    if (this.flag != model.flag) {
      // 如果有变更级别 清空后重绘
      await this.resetMapMarkerAndOverly();
      this.markers = [];
    }

    // 存储
    this.flag = model.flag;

    var timeStamp = DateTime.now().millisecondsSinceEpoch;
    this.markerSettingTime = timeStamp;

    List<BMFMarker> marks = [];
    List<BMFMarker> deleteMarks = [];
    List<BMFMarker> curMarks = this.markers;
    BMFMarker? merchantMarker;
    if (model.flag == 0) {
      // 店铺标记
      List<YBMCustomerMapTagModel> deleteModels = [];
      if (curMarks.isNotEmpty) {
        curMarks.forEach((element) {
          if (element.customMap is Map<String, dynamic>) {
            Map<String, dynamic> jsonMap =
                element.customMap as Map<String, dynamic>;
            YBMCustomerMapTagModel tagModel =
                YBMCustomerMapTagModel.fromJson(jsonMap);
            var test =
                model.customerMapVos?.firstWhereOrNull((e) => e == tagModel);
            if (test == null) {
              deleteMarks.add(element);
            } else {
              deleteModels.add(test);
            }
          }
        });
      }
      if (deleteMarks.isNotEmpty) {
        this._controller.removeMarkers(deleteMarks);
      }

      for (YBMCustomerMapTagModel element in model.customerMapVos ?? []) {
        if (!deleteModels.contains(element)) {
          BMFMarker marker = await getMerchantMark(element);
          // 因仅在第一次加载时判断 是否有外部传入的店铺信息 所以只在此处处理
          if (this.merchantFlag &&
              "${element.id}" == "${widget.targetModel?.customerId}") {
            merchantMarker = marker;
            this.merchantFlag = false;
          }
          marks.add(marker);
        }
      }
    } else {
      List<YBMCustomerMapCountModel> deleteModels = [];
      if (curMarks.isNotEmpty) {
        curMarks.forEach((element) {
          if (element.customMap is Map<String, dynamic>) {
            Map<String, dynamic> jsonMap =
                element.customMap as Map<String, dynamic>;
            YBMCustomerMapCountModel tagModel =
                YBMCustomerMapCountModel.fromJson(jsonMap);
            var test = model.customerMapCountVos
                ?.firstWhereOrNull((e) => e == tagModel);
            if (test == null) {
              deleteMarks.add(element);
            } else {
              deleteModels.add(test);
            }
          }
        });
      }
      if (deleteMarks.isNotEmpty) {
        this._controller.removeMarkers(deleteMarks);
      }
      for (YBMCustomerMapCountModel element
          in model.customerMapCountVos ?? []) {
        if (!deleteModels.contains(element)) {
          BMFMarker marker = await getAreaMark(element);
          marks.add(marker);
        }
      }
    }

    /// 如果加载时 时间不同则直接放弃本次操作
    if (timeStamp != this.markerSettingTime) {
      return;
    }

    curMarks.removeWhere((element) => deleteMarks.contains(element));
    curMarks.addAll(marks);
    this.markers = curMarks;

    this._controller.addMarkers(marks);

    if (merchantMarker != null) {
      Future.delayed(Duration(milliseconds: 100), () {
        this.settingSelectedMarker(merchantMarker!);
        if (widget.sourceController.showListAction != null) {
          widget.sourceController.showListAction!(true);
        }
      });
    }
  }

  Future<BMFMarker> getMerchantMark(YBMCustomerMapTagModel element) async {
    LogUtil.log("getMerchantMark");
    Uint8List? pngBytes = await this._imageUtils.getMarkImage(
          merchantName: "${element.customerName ?? '-'}",
          isNew: element.newPoiFlag == true,
          isHit: element.filterFlag == null || element.filterFlag == true,
          isRevice: element.claimedFlag == true,
          isRegister: element.registerFlag == true,
        );
    double latitude =
        double.tryParse("${element.poiLatitude ?? "40.00193"}") ?? 40.00193;
    double longitude =
        double.tryParse("${element.poiLongitude ?? "116.487488"}") ??
            116.487488;
    BMFMarker marker = BMFMarker.iconData(
      identifier: 'flutter_marker',
      position: BMFCoordinate(latitude, longitude),
      canShowCallout: false,
      iconData: pngBytes,
      enabled: element.filterFlag == null || element.filterFlag == true,
      customMap: element.toJson(),
    );
    if (Platform.isAndroid) {
      marker.centerOffset = BMFPoint(0,
          element.filterFlag == null || element.filterFlag == true ? 100 : 20);
    } else {
      marker.centerOffset = BMFPoint(0,
          element.filterFlag == null || element.filterFlag == true ? 10 : 20);
    }
    return marker;
  }

  Future<BMFMarker> getAreaMark(YBMCustomerMapCountModel element) async {
    LogUtil.log("getAreaMark");
    Uint8List? pngBytes = await this._imageUtils.getCountImage(
          "${element.areaName ?? "-"}",
          "${element.poiCount ?? "0"}家",
        );
    double latitude =
        double.tryParse("${element.poiLatitude ?? "40.00193"}") ?? 40.00193;
    double longitude =
        double.tryParse("${element.poiLongitude ?? "116.487488"}") ??
            116.487488;
    BMFMarker marker = BMFMarker.iconData(
      identifier: 'flutter_marker',
      position: BMFCoordinate(latitude, longitude),
      canShowCallout: false,
      iconData: pngBytes,
      customMap: element.toJson(),
    );
    return marker;
  }

  /// marker点击方法
  void clickMarker(BMFMarker marker) async {
    LogUtil.log("clickMarker");
    if (this.flag == 0) {
      this.merchantMarkerClick(marker);
    } else {
      this.clickAreaMarker(marker);
    }
  }

  /// 点击的是店铺类型的 marker
  void merchantMarkerClick(BMFMarker marker) async {
    LogUtil.log("merchantMarkerClick");

    /// 设置选中marker样式
    this.settingSelectedMarker(marker);

    /// 传递选中参数
    widget.sourceController.clickMarker(this.getParamsForDidChange());
  }

  /// 设置选中marker样式
  void settingSelectedMarker(BMFMarker marker) async {
    LogUtil.log("settingSelectedMarker");
    if (this.overlyId.isNotEmpty) {
      this._controller.removeOverlay(this.overlyId);
    }

    bool isSelected = marker.Id == cacheMarker?["id"];
    if (isSelected) {
      this.overlyId = "";
      _controller.deselectMarker(marker);
      // 清理选中店铺经纬度
      this.merchantCoordinate = null;
      // 清理缓存的marker
      this.cacheMarker = null;
      // 传递选中店铺参数
      if (widget.sourceController.selectedMerchant != null) {
        widget.sourceController.selectedMerchant!(null);
      }
    } else {
      /// 如果存在选中的 maker 则更新图片
      this.resetCacheMarkerStyle();

      /// 设置覆盖物
      settingOverLayer(marker.position);

      /// 设置marker选中
      _controller.selectMarker(marker);

      // 设置选中店铺经纬度
      this.merchantCoordinate = marker.position;

      // 缓存选中的marker
      this.cacheMarker = marker.toMap();
      this.cacheMarker?.remove("iconData");

      // 传递选中店铺参数
      if (widget.sourceController.selectedMerchant != null) {
        YBMCustomerMapTagModel tagModel = YBMCustomerMapTagModel()
          ..poiLatitude = marker.customMap?['poiLatitude']
          ..poiLongitude = marker.customMap?['poiLongitude']
          ..id = marker.customMap?['id']
          ..merchantId = marker.customMap?['merchantId']
          ..poiId = marker.customMap?['poiId']
          ..customerName = marker.customMap?['customerName'];
        widget.sourceController.selectedMerchant!(tagModel);
      }
    }

    /// 更新图片
    if (marker.customMap != null) {
      this.updateMarker(marker.Id,
          customMap: marker.customMap, isSelected: !isSelected);
    }
  }

  /// 设置overlayer
  void settingOverLayer(BMFCoordinate position) {
    LogUtil.log("settingOverLayer");

    /// 设置500米范围覆盖物
    BMFCircle circle = BMFCircle(
      center: position,
      radius: 500,
      fillColor: Color(0x45B0FFE1),
      strokeColor: Color(0xE300BC7D),
      width: 1,
    );
    this.overlyId = circle.Id;
    _controller.addCircle(circle);
  }

  /// 点击的是区域的count
  void clickAreaMarker(BMFMarker marker) async {
    LogUtil.log("clickAreaMarker");

    if (this.markers.isNotEmpty) {
      this._controller.removeMarkers(this.markers);
      this.markers = [];
    }

    double level = mapStatus?.fLevel ?? 15;

    if (level < 11) {
      this.mapStatus?.fLevel = 11;
      this.mapStatus?.targetGeoPt = marker.position;
      if (this.mapStatus != null) {
        this._controller.setNewMapStatus(mapStatus: this.mapStatus!);
      }
    } else if (level < 12) {
      this.mapStatus?.fLevel = 12;
      this.mapStatus?.targetGeoPt = marker.position;
      if (this.mapStatus != null) {
        this._controller.setNewMapStatus(mapStatus: this.mapStatus!);
      }
    } else if (level < 15) {
      this.mapStatus?.fLevel = 15;
      this.mapStatus?.targetGeoPt = marker.position;
      if (this.mapStatus != null) {
        this._controller.setNewMapStatus(mapStatus: this.mapStatus!);
      }
    }

    Future.delayed(Duration(milliseconds: 200), () {
      // 触发请求
      this.triggierMapChangeForRequest();
    });
  }

  /// 重置选中marker样式
  void resetCacheMarkerStyle() {
    LogUtil.log("resetCacheMarkerStyle");
    if (this.cacheMarker != null) {
      BMFMarker marker = BMFMarker.fromMap(this.cacheMarker!);
      if (marker.customMap != null) {
        this.updateMarker(marker.Id,
            customMap: marker.customMap, isSelected: false);
      }
      _controller.deselectMarker(marker);
      this.cacheMarker = null;
    }
  }

  /// 更新marker状态
  void updateMarker(
    String id, {
    Map<dynamic, dynamic>? customMap,
    bool isSelected = false,
  }) async {
    LogUtil.log("updateMarker");
    bool isNew = customMap?["newPoiFlag"] == true;
    bool isHit =
        customMap?["filterFlag"] == null || customMap?["filterFlag"] == true;
    bool isRevice = customMap?["claimedFlag"] == true;
    bool isRegister = customMap?["registerFlag"] == true;
    String name = customMap?["customerName"] ?? "-";
    Uint8List? pngBytes = await this._imageUtils.getMarkImage(
          merchantName: name,
          isNew: isNew,
          isHit: isHit,
          isRevice: isRevice,
          isRegister: isRegister,
          isSelected: isSelected,
        );
    _controller.updateMarkerMember({
      "id": id,
      "member": "iconData",
      "value": pngBytes,
    });
  }

  /// 重置地图标点及覆盖物
  Future<void> resetMapMarkerAndOverly() async {
    LogUtil.log("resetMapMarkerAndOverly");
    await this._controller.cleanAllMarkers();
    this.merchantCoordinate = null;
    await this.resetMapOverly();
    return;
  }

  Future<void> resetMapOverly() async {
    LogUtil.log("resetMapOverly");
    if (this.overlyId.isNotEmpty) {
      this._controller.removeOverlay(this.overlyId);
      this.overlyId = "";
    }
    // 传递选中店铺参数
    if (widget.sourceController.selectedMerchant != null) {
      widget.sourceController.selectedMerchant!(null);
    }
    return;
  }
}

/// 定位相关方法
extension _Location on YBMCustomerMapWidgetState {
  /// 区域筛选变更调用 重新定位地图中心点
  void locationFilterChange(String locationName) {
    LogUtil.log("locationFilterChange");
    this.locationName = locationName;
    if (this.locationName.isEmpty) {
      if (this.merchantCoordinate != null) {
        this._controller.setCenterCoordinate(this.merchantCoordinate!, true);
        this.triggierMapChangeForRequest();
      } else if (this.userCoordinate != null) {
        this._controller.setCenterCoordinate(this.userCoordinate!, true);
        this.triggierMapChangeForRequest();
      }
    } else {
      /// 设置地理位置检索回调
      LocationManager().searchLocation(locationName, (result, errorCode) {
        if (errorCode == BMFSearchErrorCode.NO_ERROR) {
          if (result.location != null) {
            this._controller.setCenterCoordinate(result.location!, true);
            this.triggierMapChangeForRequest();
          }
        }
      });
    }
  }

  /// 设置个人坐标点
  void settingUserLocationIcon() async {
    LogUtil.log("settingUserLocationIcon");
    double devicePixelRatio =
        MediaQuery.of(context).devicePixelRatio < 2.5 || Platform.isAndroid
            ? 2.0
            : 3.0;
    // 定制样式
    BMFUserLocationDisplayParam userlocationDisplayParam =
        BMFUserLocationDisplayParam(
      userTrackingMode: BMFUserTrackingMode.Heading,
      // Android独有
      isAccuracyCircleShow: false,
      // 精度圈不显示 iOS独有
      canShowCallOut: false,
      // 不展示气泡 iOS独有
      locationViewImage: devicePixelRatio > 2.5 && Platform.isIOS
          ? "assets/images/customer/map/3.0x/customer_map_user.png"
          : "assets/images/customer/map/2.0x/customer_map_user.png",
      isRotateAngleValid: true,
      locationViewHierarchy:
          BMFLocationViewHierarchy.LOCATION_VIEW_HIERARCHY_TOP, // 显示在最上层
    );
    await this
        ._controller
        .updateLocationViewWithParam(userlocationDisplayParam);

    // 设定定位模式
    if (Platform.isIOS) {
      await this._controller.setUserTrackingMode(BMFUserTrackingMode.Heading);
    }

    // 设置显示定位图层
    await this._controller.showUserLocation(true);
  }

  Future<BMFCoordinate?> getCacheLocation() async {
    double? latitude;
    double? longitude;
    var cacheLocationTimestamp = await XYYContainer.storageChannel
        .getValue("customer_map_location_timestamp");
    if (cacheLocationTimestamp != null && cacheLocationTimestamp is int) {}
    var cacheDateTime =
        DateTime.fromMillisecondsSinceEpoch(cacheLocationTimestamp);
    if ((DateTime.now().difference(cacheDateTime)).inMinutes < 10) {
      // 十分钟内缓存有效
      String? latitudeStr =
          await XYYContainer.storageChannel.getValue("customer_map_latitude");
      String? longitudeStr =
          await XYYContainer.storageChannel.getValue("customer_map_longitude");
      if (latitudeStr != null && longitudeStr != null) {
        latitude = double.tryParse(latitudeStr) ?? 39.913607;
        longitude = double.tryParse(longitudeStr) ?? 116.404269;
      }
    }
    if (latitude == null || longitude == null) {
      return null;
    } else {
      return BMFCoordinate(latitude, longitude);
    }
  }

  /// 请求定位信息
  Future<void> requestLocation({bool userCache = true}) async {
    LogUtil.log("requestLocation");
    double? latitude;
    double? longitude;
    if (userCache) {
      var cacheLocation = await getCacheLocation();
      if (cacheLocation != null) {
        latitude = cacheLocation.latitude;
        longitude = cacheLocation.longitude;
      }
    }
    if (latitude == null || longitude == null) {
      var result = await XYYContainer.locationChannel.locate();
      if (result.isSuccess == true &&
          result.latitude != null &&
          result.longitude != null) {
        latitude = double.tryParse(result.latitude ?? "39.913607") ?? 39.913607;
        longitude =
            double.tryParse(result.longitude ?? "116.404269") ?? 116.404269;

        /// 缓存此次获取的位置信息
        this.cacheLocation(result.longitude ?? "", result.latitude ?? "");
      } else {
        String? latitudeStr =
            await XYYContainer.storageChannel.getValue("customer_map_latitude");
        String? longitudeStr = await XYYContainer.storageChannel
            .getValue("customer_map_longitude");
        if (latitudeStr != null && longitudeStr != null) {
          latitude = double.tryParse(latitudeStr) ?? 39.913607;
          longitude = double.tryParse(longitudeStr) ?? 116.404269;
        }
      }
    } else if (!userCache) {
      Future.delayed(Duration(milliseconds: 1000), () {
        requestLocation(userCache: false);
      });
    }

    if (latitude != null && longitude != null) {
      BMFCoordinate coordinate = BMFCoordinate(latitude, longitude);

      if (widget.targetModel == null) {
        /// 设置地图中心点为当前获取到的位置信息
        await _controller.setCenterCoordinate(coordinate, true);
      } else {
        /// 设置地图中心点为外部传入的位置信息
        await _controller.setCenterCoordinate(
            this.merchantCoordinate ?? coordinate, true);
      }

      /// 缓存获取到的经纬度
      this.userCoordinate = coordinate;

      /// 设置当前用户坐标
      this.updateUserLocation(coordinate, this.userHeading);

      /// 设置连续定位
      this.settingGetLocation();
      Future.delayed(Duration(milliseconds: 500), () {
        /// 获取定位后 触发请求
        this.triggierMapChangeForRequest();
      });
    } else {
      XYYContainer.toastChannel.toast("获取位置信息失败，请检查权限设置");
    }

    return;
  }

  /// 缓存当前定位信息 - 方便下次未获取定位时使用
  void cacheLocation(String longitude, String latitude) {
    LogUtil.log("cacheLocation");
    XYYContainer.storageChannel.put("customer_map_longitude", longitude);
    XYYContainer.storageChannel.put("customer_map_latitude", latitude);
    XYYContainer.storageChannel.put("customer_map_location_timestamp",
        DateTime.now().millisecondsSinceEpoch);
  }

  /// 设置用户当前位置信息
  void updateUserLocation(BMFCoordinate? coordinate, BMFHeading? heading) {
    LogUtil.log("updateUserLocation");
    if (coordinate != null) {
      BMFLocation location = BMFLocation(
          coordinate: coordinate,
          course: Platform.isAndroid ? heading?.trueHeading : null);
      BMFUserLocation userLocation = BMFUserLocation(
        location: location,
        heading: heading,
      );
      this._controller.updateLocationData(userLocation);
    }
  }

  /// 设置获取定位
  void settingGetLocation() async {
    LogUtil.log("settingGetLocation");

    /// 设置定位回调
    LocationManager().setLocationCallback(this, (result) {
      if (result.latitude != null && result.longitude != null) {
        BMFCoordinate coordinate =
            BMFCoordinate(result.latitude!, result.longitude!);

        /// 缓存获取到的经纬度
        this.userCoordinate = coordinate;
        this.updateUserLocation(coordinate, this.userHeading);
      }
    });

    /// 设置定位朝向监听
    LocationManager().setUpdateHeadingCallback(this, (result) {
      BMFHeading heading = BMFHeading.fromMap(result.getMap());
      this.userHeading = heading;
      this.updateUserLocation(this.userCoordinate, heading);
    });

    /// 开启定位
    LocationManager().startLocation();

    /// 开启设备朝向监听
    LocationManager().startUpdatingHeading();
  }
}
