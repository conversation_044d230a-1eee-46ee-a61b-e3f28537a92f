import 'package:flutter/material.dart';

class CustomerMapNavigationItem extends StatelessWidget {
  final VoidCallback navigationAction;
  final VoidCallback reportAction;

  CustomerMapNavigationItem({
    required this.navigationAction,
    required this.reportAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 8, right: 8),
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x1F005035),
            offset: Offset(0, 3),
            blurRadius: 8.0, // 模糊
            spreadRadius: 0.0, // 延展
          )
        ],
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          _MapButton(
            title: '导航',
            imagePath: 'assets/images/customer/map_navigation_icon.png',
            onPressed: navigationAction,
          ),
          Divider(color: Color(0xFFEEEEEE), height: 0.5, thickness: 0.5),
          _MapButton(
            title: '报错',
            imagePath: 'assets/images/customer/map_report_icon.png',
            onPressed: reportAction,
          ),
        ],
      ),
    );
  }
}

class _MapButton extends StatelessWidget {
  final String title;
  final String imagePath;
  final VoidCallback onPressed;

  _MapButton({
    required this.title,
    required this.imagePath,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.fromLTRB(0, 6, 0, 6),
        child: Column(
          children: [
            Image.asset(imagePath, width: 19, height: 19),
            Text(
              title,
              style: TextStyle(color: Color(0xFF333333), fontSize: 10),
            )
          ],
        ),
      ),
    );
  }
}
