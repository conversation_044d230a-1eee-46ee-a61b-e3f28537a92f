import 'dart:math';

import 'package:flutter/material.dart';

class YBMCustomerMapBottomButtonWidget extends StatelessWidget {
  final String title;

  final bool _isShowButton;

  YBMCustomerMapBottomButtonWidget.showList()
      : title = '门店列表',
        _isShowButton = false;

  YBMCustomerMapBottomButtonWidget.showMap()
      : title = "收起列表",
        _isShowButton = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      width: 160,
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            blurRadius: 8,
            spreadRadius: 0,
            offset: Offset(0, 3),
            color: Color(0x1F005035),
          )
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              color: Color(0xFF292933),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 5),
          Transform.rotate(
            angle: pi * (_isShowButton ? 1 : 0),
            child: Image.asset(
              'assets/images/customer/customer_map_show.png',
              width: 11,
              height: 6.5,
            ),
          ),
        ],
      ),
    );
  }
}
