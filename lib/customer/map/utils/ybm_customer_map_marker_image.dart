import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class YBMCustomerMarkImageUtils {
  YBMCustomerMarkImageUtils();

  double getRealSize(double size) {
    double devicePixelRatio = ui.window.devicePixelRatio < 2.5 ? 2.0 : 3.0;
    return size * devicePixelRatio;
  }

  Future<Uint8List?> getMarkImage({
    bool isNew = true,
    bool isSelected = false, // 是否选中
    bool isHit = true, // 是否是筛选出来的结果
    bool isRegister = true, // 是否注册
    bool isRevice = true, // 是否认领
    String? merchantName,
  }) async {
    ui.Image iconImage;
    double devicePixelRatio = ui.window.devicePixelRatio < 2.5 ? 2.0 : 3.0;
    if (isHit) {
      /// 筛选条件命中
      if (isRegister) {
        if (isSelected) {
          /// 选中
          iconImage = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_hit_icon.png'
              : 'assets/images/customer/map/2.0x/customer_map_hit_icon.png');
        } else {
          /// 未选中
          iconImage = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_icon.png'
              : 'assets/images/customer/map/2.0x/customer_map_icon.png');
        }
      } else {
        if (isSelected) {
          /// 选中
          iconImage = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_revice_select_icon.png'
              : 'assets/images/customer/map/2.0x/customer_map_revice_select_icon.png');
        } else {
          /// 未选中
          iconImage = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_revice_icon.png'
              : 'assets/images/customer/map/2.0x/customer_map_revice_icon.png');
        }
      }
    } else {
      /// 筛选条件未命中
      iconImage = await _loadImage(devicePixelRatio > 2.5
          ? 'assets/images/customer/map/3.0x/customer_map_not_hit_icon.png'
          : 'assets/images/customer/map/2.0x/customer_map_not_hit_icon.png');
    }

    /// 生成文字
    Size labelSize = Size(0, 0);
    TextPainter? labelTp;
    if (merchantName != null && isHit) {
      labelTp = TextPainter(
        text: TextSpan(
          text: merchantName,
          style: TextStyle(
            color: Color(0xFF292933),
            fontSize: getRealSize(12),
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
        maxLines: 2,
        ellipsis: "...",
      );
      labelTp.layout(maxWidth: getRealSize(62));

      labelSize = Size(labelTp.width, labelTp.height + 2);
    }

    /// 创建画布
    final recorder = new ui.PictureRecorder();
    Offset size;
    double offsetX = 0;
    if (isHit) {
      if (isNew) {
        size = Offset(getRealSize(62), getRealSize(40) + labelSize.height);
      } else {
        if (isRevice) {
          double width = getRealSize(29);
          if (width < labelSize.width) {
            offsetX = (labelSize.width - width) / 2;
            width = labelSize.width;
          }
          size = Offset(width, getRealSize(30) + labelSize.height);
        } else {
          double width = getRealSize(23);
          if (width < labelSize.width) {
            offsetX = (labelSize.width - width) / 2;
            width = labelSize.width;
          }
          size = Offset(width, getRealSize(30) + labelSize.height);
        }
      }
    } else {
      size = Offset(getRealSize(15), getRealSize(19));
    }

    final canvas = new Canvas(recorder, Rect.fromPoints(Offset(0, 0), size));

    /// 画笔
    Paint paint = Paint();

    /// 绘制图片
    if (isHit) {
      if (isNew) {
        canvas.drawImage(iconImage,
            Offset(getRealSize(20) + offsetX, getRealSize(11)), paint);
        ui.Image newIcon = await _loadImage(devicePixelRatio > 2.5
            ? 'assets/images/customer/map/3.0x/customer_map_new_icon.png'
            : 'assets/images/customer/map/2.0x/customer_map_new_icon.png');
        canvas.drawImage(newIcon, Offset(getRealSize(38) + offsetX, 0), paint);
        if (isRevice) {
          ui.Image registerIcon = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_angle_icon.png'
              : 'assets/images/customer/map/3.0x/customer_map_angle_icon.png');
          canvas.drawImage(registerIcon,
              Offset(getRealSize(31) + offsetX, getRealSize(25)), paint);
        }
      } else {
        if (isRevice) {
          canvas.drawImage(
              iconImage, Offset(getRealSize(3) + offsetX, 0), paint);
          ui.Image registerIcon = await _loadImage(devicePixelRatio > 2.5
              ? 'assets/images/customer/map/3.0x/customer_map_angle_icon.png'
              : 'assets/images/customer/map/2.0x/customer_map_angle_icon.png');
          canvas.drawImage(registerIcon,
              Offset(getRealSize(15) + offsetX, getRealSize(15)), paint);
        } else {
          canvas.drawImage(iconImage, Offset(offsetX, 0), paint);
        }
      }
    } else {
      canvas.drawImage(iconImage, Offset(0, 0), paint);
    }

    /// 绘制文字
    if (merchantName != null && isHit) {
      double labelOffsetX = 0;
      if (labelSize.width < size.dx) {
        labelOffsetX = (size.dx - labelSize.width) / 2;
      }
      labelTp?.paint(
          canvas, Offset(labelOffsetX, size.dy - labelSize.height + 2));
    }

    /// 获取图片
    final picture = recorder.endRecording();

    ui.Image finalUiImage =
        await picture.toImage(size.dx.toInt(), size.dy.toInt());

    ByteData? finalByteData =
        await finalUiImage.toByteData(format: ui.ImageByteFormat.png);
    Uint8List? finalPngBytes = finalByteData?.buffer.asUint8List();

    return finalPngBytes;
  }

  Future<Uint8List?> getCountImage(String name, String count) async {
    /// 创建画布
    final recorder = new ui.PictureRecorder();
    Rect rect = Rect.fromLTWH(0, 0, getRealSize(84), getRealSize(84));
    final canvas = new Canvas(recorder, rect);
    // 画笔
    Paint paint = Paint();
    paint.color = Color(0xFF00B377);
    // 画背景
    canvas.drawArc(rect, 0, pi * 2, true, paint);
    // 画文字
    TextPainter titleTp = TextPainter(
      text: TextSpan(
          text: name,
          style: TextStyle(
            color: Color(0xFFFFFFFF),
            fontSize: getRealSize(12),
          )),
      textAlign: TextAlign.left,
      textDirection: TextDirection.ltr,
      maxLines: 1,
      ellipsis: "...",
    );
    titleTp.layout(maxWidth: rect.width - 10);
    Size titleSize = Size(titleTp.width, titleTp.height);

    TextPainter countTp = TextPainter(
      text: TextSpan(
          text: count,
          style: TextStyle(
            color: Color(0xFFFFFFFF),
            fontSize: getRealSize(15),
            fontWeight: FontWeight.w500,
          )),
      textAlign: TextAlign.left,
      textDirection: TextDirection.ltr,
      maxLines: 1,
      ellipsis: "...",
    );
    countTp.layout(maxWidth: rect.width - 10);
    Size countSize = Size(countTp.width, countTp.height);

    double totalHeight = titleSize.height + 1 + countSize.height;
    double offsetT = (rect.height - totalHeight) / 2;
    double offsetW = (rect.width - titleSize.width) / 2;
    double offsetC = (rect.width - countSize.width) / 2;

    titleTp.paint(canvas, Offset(offsetW, offsetT));

    countTp.paint(canvas, Offset(offsetC, offsetT + titleSize.height + 1));

    /// 获取图片
    final picture = recorder.endRecording();

    ui.Image finalUiImage =
        await picture.toImage(rect.width.toInt(), rect.height.toInt());

    ByteData? finalByteData =
        await finalUiImage.toByteData(format: ui.ImageByteFormat.png);
    Uint8List? finalPngBytes = finalByteData?.buffer.asUint8List();

    return finalPngBytes;
  }

  Future<ui.Image> _loadImage(String path) async {
    ImageStream stream = AssetImage(path).resolve(ImageConfiguration.empty);
    Completer<ui.Image> completer = Completer<ui.Image>();

    ImageStreamListener listener =
        ImageStreamListener((ImageInfo frame, bool synchronousCall) {
      final ui.Image image = frame.image;
      if (!completer.isCompleted) {
        completer.complete(image);
      }
    });
    stream.addListener(listener);
    ui.Image image = await completer.future;
    stream.removeListener(listener);
    return image;
  }
}
