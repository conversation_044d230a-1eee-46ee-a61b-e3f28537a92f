import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_map_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bmflocation/flutter_bmflocation.dart';

typedef YBMCustomerMapChange = void Function(Map<String, dynamic> params);

typedef YBMCustomerMapMarkerChange = void Function(YBMCustomerMapModel model);

typedef YBMCustomerMapSelectedMerchant = void Function(
    YBMCustomerMapTagModel? model);

typedef YBMCustomerMapListShow = void Function(bool isShow);

typedef YBMCustomerFilterLocation = void Function(String locationName);

typedef YBMCustomerSearchMerchantAction = void Function(
    YBMCustomerPrivateItemModel model);

class YBMCustomerMapController {
  /// 地图变更状态回调
  final YBMCustomerMapChange mapChange;

  /// 点击地图标记回调
  final YBMCustomerMapChange clickMarker;

  /// 设置地图标记点
  YBMCustomerMapMarkerChange? changeMarker;

  /// 选中店铺回调
  YBMCustomerMapSelectedMerchant? selectedMerchant;

  /// 设置选中店铺
  YBMCustomerMapSelectedMerchant? settingSelectedMerchant;

  /// 区域筛选变更调用
  YBMCustomerFilterLocation? locationFilterChange;

  /// 调用展开客户列表方法
  YBMCustomerMapListShow? showListAction;

  /// 搜索客户后调用
  YBMCustomerSearchMerchantAction? searchMerchant;

  /// 外部筛选条件变更后调用 用来移除地图标点的选中
  VoidCallback? filterChange;

  VoidCallback dismissKeyboard;

  YBMCustomerMapController({
    required this.mapChange,
    required this.clickMarker,
    required this.dismissKeyboard,
  });
}

class YBMCustomerMapListController {
  /// 展示列表回调
  VoidCallback? showList;

  YBMCustomerMapListController();
}

class YBMCustomerMapLocationOptions {
  /// Android定位参数
  static BaiduLocationAndroidOption initAndroidOptions() {
    BaiduLocationAndroidOption options = BaiduLocationAndroidOption(
// 定位模式，可选的模式有高精度、仅设备、仅网络。默认为高精度模式
        locationMode: BMFLocationMode.hightAccuracy,
// 是否需要返回地址信息
        isNeedAddress: false,
// 是否需要返回海拔高度信息
        isNeedAltitude: false,
// 是否需要返回周边poi信息
        isNeedLocationPoiList: false,
// 是否需要返回新版本rgc信息
        isNeedNewVersionRgc: false,
// 是否需要返回位置描述信息
        isNeedLocationDescribe: false,
// 是否使用gps
        openGps: true,
// 坐标系
        coordType: BMFLocationCoordType.bd09ll,
// 设置发起定位请求的间隔，int类型，单位ms
// 如果设置为0，则代表单次定位，即仅定位一次，默认为0
        scanspan: 4000);
    return options;
  }

  /// iOS定位参数
  static BaiduLocationIOSOption initIOSOptions() {
    BaiduLocationIOSOption options = BaiduLocationIOSOption(
      // 坐标系
      coordType: BMFLocationCoordType.bd09ll,
      // 位置获取超时时间
      locationTimeout: 10,
      // 获取地址信息超时时间
      reGeocodeTimeout: 10,
      // 应用位置类型 默认为automotiveNavigation
      activityType: BMFActivityType.automotiveNavigation,
      // 设置预期精度参数 默认为best
      desiredAccuracy: BMFDesiredAccuracy.best,
      // 是否需要最新版本rgc数据
      isNeedNewVersionRgc: false,
      // 指定定位是否会被系统自动暂停
      pausesLocationUpdatesAutomatically: false,
      // 指定是否允许后台定位,
      // 允许的话是可以进行后台定位的，但需要项目
// 配置允许后台定位，否则会报错，具体参考开发文档
      allowsBackgroundLocationUpdates: false,
// 设定定位的最小更新距离
      distanceFilter: 10,
    );
    return options;
  }
}
