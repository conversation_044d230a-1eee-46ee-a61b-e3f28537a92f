import 'dart:async';
import 'dart:ui';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_map_merchant_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_map_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/utils/ybm_customer_map_controller.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/widget/ybm_customer_map_content_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class YBMCustomerMapPage extends BasePage {
  final dynamic customerId;
  final dynamic latitude;
  final dynamic longitude;

  YBMCustomerMapPage({
    this.customerId,
    this.latitude,
    this.longitude,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return YBMCustomerMapPageState();
  }
}

class YBMCustomerMapPageState extends BaseState<YBMCustomerMapPage>
    with TickerProviderStateMixin {
  late YBMCustomerMapController mapSourceController;

  Map<String, dynamic> mapParams = {};

  Map<String, dynamic> filterParams = {};

  YBMCustomerMapMerchantValueNotifier merchantSource =
      YBMCustomerMapMerchantValueNotifier([]);

  YBMCustomerMapListController listController = YBMCustomerMapListController();

  int page = 0;

  String? keyword;

  CustomerMapTargetMerchantModel? _targetModel;

  bool isFirstSearchKeyword = false;

  FocusNode searchFocusNode = FocusNode();

  @override
  void initState() {
    mapSourceController = YBMCustomerMapController(
      mapChange: mapChangeParams,
      clickMarker: clickMarker,
      dismissKeyboard: () {
        searchFocusNode.unfocus();
      },
    );

    /// 处理外部传入选中店铺的信息
    if (widget.customerId != null &&
        widget.latitude != null &&
        widget.longitude != null) {
      this._targetModel = CustomerMapTargetMerchantModel(
          customerId: widget.customerId,
          latitude: widget.latitude,
          longitude: widget.longitude);
    } else {
      /// 如果外部未传入客户 则默认添加已注册筛选
      this.filterParams['merchantStatus'] = "1";
    }

    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTapDown: (details) {
        print("guan2 onTapDown");
        searchFocusNode.unfocus();
      },
      child: YBMCustomerMapContentWidget(
        changeFilter: filterChangeParams,
        onLoadMore: loadMoreMerchantData,
        sourceController: mapSourceController,
        merchantSource: this.merchantSource,
        listController: this.listController,
        targetModel: this._targetModel,
      ),
    );
  }

  @override
  bool get resizeToAvoidBottomInset => false;

  /// 筛选条件变更回调
  void filterChangeParams(Map<String, dynamic> params) {
    print(params.toString());
    this.filterParams = params;
    this.requestMapData();
    this.refreshMerchantData();
  }

  /// 地图变化的回调
  void mapChangeParams(Map<String, dynamic> params) {
    print(params.toString());
    this.mapParams = params;
    this.requestMapData();
    this.refreshMerchantData();
  }

  /// 点击地图上标点的回调
  void clickMarker(Map<String, dynamic> params) {
    this.mapParams = params;
    this.refreshMerchantData();
    if (this.listController.showList != null) {
      this.listController.showList!();
    }
  }

  /// 请求地图marker数据
  void requestMapData() async {
    Map<String, dynamic> params = {};
    if (this.keyword?.isNotEmpty == true) {
      params['keyword'] = this.keyword;
    }
    params.addAll(this.filterParams);
    params.addAll(this.mapParams);
    var result = await NetworkV2<YBMCustomerMapModel>(YBMCustomerMapModel())
        .requestDataV2(
      'customerV2/map',
      parameters: params,
    );

    if (mounted) {
      if (result.isSuccess == true) {
        YBMCustomerMapModel? data = result.getData();
        if (data != null) {
          if (this.mapSourceController.changeMarker != null) {
            this.mapSourceController.changeMarker!(data);
          }
        } else {
          showToast('获取信息失败, 请尝试拖动地图重新请求');
        }
      } else {
        showToast('获取信息失败, 请尝试拖动地图重新请求');
      }
    }
  }

  /// 请求客户列表数据
  Future<void> refreshMerchantData() async {
    this.page = 0;
    return this.requestMerchantData();
  }

  Future<void> loadMoreMerchantData() async {
    return this.requestMerchantData();
  }

  Future<void> requestMerchantData() async {
    Map<String, dynamic> params = {
      'offset': this.page,
      'limit': 10,
    };
    if (this.keyword?.isNotEmpty == true) {
      params['keyword'] = this.keyword;
    }
    params.addAll(this.filterParams);
    params.addAll(this.mapParams);

    Completer<void> completer = Completer();

    await NetworkV2<YBMCustomerMapMerchantModel>(YBMCustomerMapMerchantModel())
        .requestDataV2("customerV2/mapList", parameters: params)
        .then((value) {
      if (mounted) {
        if (value.isSuccess == true) {
          var data = value.getData();
          if (data != null) {
            var source = data.list ?? [];
            if (this.page == 0) {
              this.merchantSource.value = source;

              if (this.isFirstSearchKeyword) {
                /// 判断第一次 搜索的结果
                if (this.keyword?.isNotEmpty == true && source.length > 0) {
                  this.settingSearchResult(source.first);
                } else {
                  /// 如果搜索无结果 直接请求标点数据
                  this.requestMapData();
                }
              }
            } else {
              var totalSource = this.merchantSource.value;
              totalSource.addAll(source);
              this.merchantSource.value = totalSource;
            }
            if (data.isLastPage == false) {
              this.merchantSource.isLastPage = false;
              this.page += 1;
            } else {
              this.merchantSource.isLastPage = true;
            }
          }
        }
        completer.complete();
      }
    });

    return completer.future;
  }

  void settingSearchResult(YBMCustomerPrivateItemModel model) {
    // Future.delayed(Duration(milliseconds: 100), () {
    //   if (data.flag == 0 && this.keyword?.isNotEmpty == true) {
    //     if (this.mapSourceController.settingSelectedMerchant != null) {
    //       if (data.customerMapVos != null && data.customerMapVos!.length > 0) {
    //         YBMCustomerMapTagModel first = data.customerMapVos!.first;
    //         this.mapSourceController.settingSelectedMerchant!(first);
    //       }
    //     }
    //   }
    // });
    if (this.mapSourceController.searchMerchant != null) {
      this.mapSourceController.searchMerchant!(model);
    }
  }

  @override
  String getTitleName() {
    return "客户地图页面";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      hintText: "请输入客户名称/手机号/编号",
      hintTextStyle: TextStyle(color: Color(0xFFA4A4A4), fontSize: 13),
      showLeading: false,
      focusNode: searchFocusNode,
      hideCancel: false,
      backgroundColor: Color(0xFFEEEEEE),
      radius: BorderRadius.circular(17),
      cancleText: "返回",
      cancleStyle: TextStyle(color: Color(0xFF676773), fontSize: 15),
      onSearch: (value) async {
        if (value is String) {
          var result = value.trim();
          if (result.isNotEmpty) {
            this.keyword = result;
            // this.requestMapData();
            this.isFirstSearchKeyword = true;
            await this.refreshMerchantData();
            this.isFirstSearchKeyword = false;

            /// 通知地图页面移除marker
            if (this.mapSourceController.filterChange != null) {
              this.mapSourceController.filterChange!();
            }
          } else {
            showToast('请输入正确搜索内容');
          }
        }
      },
      onClear: () {
        this.keyword = null;
        this.requestMapData();
        this.refreshMerchantData();

        /// 通知地图页面移除marker
        if (this.mapSourceController.filterChange != null) {
          this.mapSourceController.filterChange!();
        }
      },
      onCancel: () {
        // XYYContainer.close(context);
      },
    );
  }
}
