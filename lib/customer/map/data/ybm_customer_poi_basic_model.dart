import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_poi_basic_model.g.dart';

@JsonSerializable()
class YBMCustomerPoiBasicModel extends BaseModelV2<YBMCustomerPoiBasicModel> {
  dynamic customerId;
  dynamic poiId;
  dynamic customerName;
  dynamic address;
  dynamic poiAuditStatus;
  dynamic poiLatitude;
  dynamic poiLongitude;

  YBMCustomerPoiBasicModel();

  factory YBMCustomerPoiBasicModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerPoiBasicModelFromJson(json);
  }

  @override
  YBMCustomerPoiBasicModel fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerPoiBasicModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerPoiBasicModelToJ<PERSON>(this);
  }
}
