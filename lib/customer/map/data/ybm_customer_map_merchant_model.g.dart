// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ybm_customer_map_merchant_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

YBMCustomerMapMerchantModel _$YBMCustomerMapMerchantModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerMapMerchantModel()
    ..total = json['total']
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) =>
            YBMCustomerPrivateItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$YBMCustomerMapMerchantModelToJson(
        YBMCustomerMapMerchantModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };
