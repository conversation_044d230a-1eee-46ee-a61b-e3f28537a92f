// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ybm_customer_map_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

YBMCustomerMapModel _$YBMCustomerMapModelFromJson(Map<String, dynamic> json) {
  return YBMCustomerMapModel()
    ..flag = json['flag']
    ..customerMapCountVos = (json['customerMapCountVos'] as List<dynamic>?)
        ?.map(
            (e) => YBMCustomerMapCountModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..customerMapVos = (json['customerMapVos'] as List<dynamic>?)
        ?.map((e) => YBMCustomerMapTagModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$YBMCustomerMapModelToJson(
        YBMCustomerMapModel instance) =>
    <String, dynamic>{
      'flag': instance.flag,
      'customerMapCountVos': instance.customerMapCountVos,
      'customerMapVos': instance.customerMapVos,
    };

YBMCustomerMapCountModel _$YBMCustomerMapCountModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerMapCountModel()
    ..areaName = json['areaName']
    ..areaLevel = json['areaLevel']
    ..poiLongitude = json['poiLongitude']
    ..poiLatitude = json['poiLatitude']
    ..poiCount = json['poiCount'];
}

Map<String, dynamic> _$YBMCustomerMapCountModelToJson(
        YBMCustomerMapCountModel instance) =>
    <String, dynamic>{
      'areaName': instance.areaName,
      'areaLevel': instance.areaLevel,
      'poiLongitude': instance.poiLongitude,
      'poiLatitude': instance.poiLatitude,
      'poiCount': instance.poiCount,
    };

YBMCustomerMapTagModel _$YBMCustomerMapTagModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerMapTagModel()
    ..id = json['id']
    ..poiId = json['poiId']
    ..merchantId = json['merchantId']
    ..customerName = json['customerName']
    ..poiLongitude = json['poiLongitude']
    ..poiLatitude = json['poiLatitude']
    ..newPoiFlag = json['newPoiFlag']
    ..filterFlag = json['filterFlag']
    ..claimedFlag = json['claimedFlag']
    ..registerFlag = json['registerFlag'];
}

Map<String, dynamic> _$YBMCustomerMapTagModelToJson(
        YBMCustomerMapTagModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'poiId': instance.poiId,
      'merchantId': instance.merchantId,
      'customerName': instance.customerName,
      'poiLongitude': instance.poiLongitude,
      'poiLatitude': instance.poiLatitude,
      'newPoiFlag': instance.newPoiFlag,
      'filterFlag': instance.filterFlag,
      'claimedFlag': instance.claimedFlag,
      'registerFlag': instance.registerFlag,
    };
