import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_map_model.g.dart';

@JsonSerializable()
class YBMCustomerMapModel extends BaseModelV2 {
  /// 当前返回的类型 0-地图标记；1-地图数量
  dynamic flag;

  List<YBMCustomerMapCountModel>? customerMapCountVos;

  List<YBMCustomerMapTagModel>? customerMapVos;

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerMapModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerMapModelToJson(this);
  }
}

@JsonSerializable()
class YBMCustomerMapCountModel extends BaseModelV2 {
  // 区域名称
  dynamic areaName;
  // 区域级别
  dynamic areaLevel;

  // 经纬度信息
  dynamic poiLongitude;
  dynamic poiLatitude;

  // 数量
  dynamic poiCount;

  YBMCustomerMapCountModel();

  bool operator ==(other) {
    if (runtimeType != other.runtimeType) return false;
    if (other is YBMCustomerMapCountModel) {
      YBMCustomerMapCountModel otherModel = other;
      return this.areaName == otherModel.areaName &&
          this.areaLevel == otherModel.areaLevel &&
          this.poiLongitude == otherModel.poiLongitude &&
          this.poiLatitude == otherModel.poiLatitude &&
          this.poiCount == otherModel.poiCount;
    } else {
      return false;
    }
  }

  @override
  int get hashCode => 18;

  factory YBMCustomerMapCountModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerMapCountModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerMapCountModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerMapCountModelToJson(this);
  }
}

@JsonSerializable()
class YBMCustomerMapTagModel extends BaseModelV2 {
  dynamic id;

  dynamic poiId;

  dynamic merchantId;

  dynamic customerName;

  dynamic poiLongitude;

  dynamic poiLatitude;

  // 新门店
  dynamic newPoiFlag;

  // 是否筛选选中
  dynamic filterFlag;

  // 是否已认领
  dynamic claimedFlag;

  // 是否已注册
  dynamic registerFlag;

  YBMCustomerMapTagModel();

  bool operator ==(other) {
    if (runtimeType != other.runtimeType) return false;
    if (other is YBMCustomerMapTagModel) {
      YBMCustomerMapTagModel otherModel = other;
      return this.merchantId == otherModel.merchantId &&
          this.merchantId == otherModel.merchantId &&
          this.poiLongitude == otherModel.poiLongitude &&
          this.poiLatitude == otherModel.poiLatitude &&
          this.newPoiFlag == otherModel.newPoiFlag &&
          this.filterFlag == otherModel.filterFlag &&
          this.claimedFlag == otherModel.claimedFlag &&
          this.registerFlag == otherModel.registerFlag;
    } else {
      return false;
    }
  }

  @override
  int get hashCode => 17;

  factory YBMCustomerMapTagModel.fromJson(Map<String, dynamic> json) {
    return _$YBMCustomerMapTagModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerMapTagModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerMapTagModelToJson(this);
  }
}

class CustomerMapTargetMerchantModel {
  dynamic customerId;
  dynamic latitude;
  dynamic longitude;

  CustomerMapTargetMerchantModel({
    this.customerId,
    this.latitude,
    this.longitude,
  });
}
