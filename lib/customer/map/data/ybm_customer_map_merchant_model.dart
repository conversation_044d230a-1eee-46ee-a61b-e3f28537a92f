import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_map_merchant_model.g.dart';

@JsonSerializable()
class YBMCustomerMapMerchantModel extends BaseModelV2 {
  dynamic total;

  dynamic isLastPage;

  List<YBMCustomerPrivateItemModel>? list;

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$YBMCustomerMapMerchantModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$YBMCustomerMapMerchantModelToJson(this);
  }
}
