import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/location/location_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_param.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_private_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_map_entry.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/buildLocaRequest.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class YBMCustomerPrivatePage extends CustomerBasePage {
  final ValueNotifier<String?>? searchChangeText;

  YBMCustomerPrivatePage({this.searchChangeText});

  @override
  CustomerBasePageState initState() {
    return YBMCustomerPrivatePageState();
  }
}

class YBMCustomerPrivatePageState
    extends CustomerBasePageState<YBMCustomerPrivatePage>
    with EventBusObserver {
  // 筛选参数
  CustomerConditionListModel? conditionModel;

  // 接口入参存储
  YBMCustomerPrivateParam paramModel = YBMCustomerPrivateParam();

  List<YBMCustomerPrivateItemModel> dataSource = [];
  String? locationLat;
  String? locationLong;

  /// 用来判断是否是第一次加载
  bool isFirstLoad = true;

  // 排序类型 下拉框筛选控制器
  DropButtonController sortController = DropButtonController(
    model: DropButtonModel(
      normalText: '最近新增',
      selectText: "距离排序",
    ),
  );

  // 客户状态 下拉框筛选控制器
  DropButtonController customerTypeController = DropButtonController(
    model: DropButtonModel(
      normalText: '客户状态',
    ),
  );

  // 资质状态 下拉框筛选控制器
  DropButtonController licenceTypeController = DropButtonController(
    model: DropButtonModel(
      normalText: '资质',
    ),
  );

  bool isLastPage = true;

  get licenseCode => null;

  @override
  void initState() {
    if (widget.searchChangeText == null) {
      this.refreshList(type: 'init');
    }
    this.addNotification();

    Future.delayed(Duration(milliseconds: 100), () {
      // YBMCustomerMapEntry.showEntry(context: context);
      YBMCustomerMapEntry.showDraggableEntry();
    });
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        super.buildWidget(context),
        // YBMCustomerMapEntry.showEntry(context: context),
        YBMCustomerMapEntry.showDraggableEntry(),
      ],
    );
  }

  // 订阅事件
  void addNotification() {
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_PRIVATE_FILTER,
        callback: (_) {
          this.jumpFilter();
        });
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_PUBLIC_RECEIVE,
        callback: (_) {
          /// 这里直接请求第一页数据， 触发刷新会导致公海页面同时进行下拉动作，不知道为什么
          this.page = 0;
          this.requestListData();
        });
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.YBM_PRIVATE_SHOW,
        callback: (arg) {
          if (arg is Map) {
            this.paramModel = YBMCustomerPrivateParam();

            var licenseStatus = arg['licenseStatus'];
            var merchantStatus = arg['merchantStatus'];
            this.paramModel.licenseCode = "$licenseStatus";
            this.paramModel.merchantStatus = "$merchantStatus";

            if (arg.containsKey('searchUserId')) {
              this.paramModel.filterParams?['searchUserId'] =
                  arg['searchUserId'];
            } else if (arg.containsKey('groupId')) {
              this.paramModel.filterParams?['groupId'] = arg['groupId'];
            }
            if (arg.containsKey('name')) {
              this.paramModel.cacheParams?['name'] = arg['name'];
            }
            this.customerTypeController.setSelectText('客户状态');
            this.licenceTypeController.setSelectText('资质');
            setState(() {});
            if (!this.isFirstLoad) {
              this.refreshController.callRefresh();
            }
          }
        });

    // 监听输入的事件
    widget.searchChangeText?.addListener(() {
      this.paramModel.keyword = widget.searchChangeText?.value;
      this.refreshController.callRefresh();
    });
  }

  @override
  void dispose() {
    eventBus.removeListener(observer: this);
    super.dispose();
  }

  // 筛选项
  @override
  List<CommonDropConfigModel> get filterConfig {
    return [
      CommonDropConfigModel(
        defaultTitle: "最近新增",
        selectTitle: "距离排序",
        paramKey: "sortCode",
        controller: this.sortController,
      ),
      CommonDropConfigModel(
        defaultTitle: "客户状态",
        paramKey: "merchantStatus",
        controller: this.customerTypeController,
      ),
      CommonDropConfigModel(
        defaultTitle: "客户级别",
        paramKey: "customerLevelCode",
      ),
      CommonDropConfigModel(
        defaultTitle: "资质",
        paramKey: "licenseCode",
        controller: this.licenceTypeController,
      ),
    ];
  }

  // 筛选弹窗
  @override
  void showDropPopupView(GlobalKey authKey, CommonDropConfigModel model,
      DropButtonController controller) async {
    switch (model.paramKey) {
      case "sortCode":
        // 排序
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: this.conditionModel?.sortType ?? [],
              selectedCode: this.paramModel.sortCode,
              selectAction: (value) {
                trackSortFilterEvent(value.code?.toString());
                // 选择了距离排序 并且没有经纬度 则提示错误
                if ("${value.code}" == "2" && !this.hasLocation) {
                  showToast('未获取到当前位置信息，无法使用距离排序');
                  return;
                }
                this.paramModel.sortCode = "${value.code}";
                controller.setSelectText("${value.text}");
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "merchantStatus":
        // 客户状态
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleResetFilterPopup(
              models: this.conditionModel?.customerStatus ?? [],
              selectedCode: this.paramModel.merchantStatus,
              distance: distance,
              selectAction: (value) {
                if (value != null) {
                  controller.setSelectText("客户状态");
                } else {
                  controller.setSelectText(null);
                }
                this.paramModel.merchantStatus = value;
                this.refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "customerLevelCode":
        // 客户级别
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerMultipleFilterPopup(
              models: this.conditionModel?.levelType ?? [],
              selectedCodes: this.paramModel.customerLevelCode,
              distance: distance,
              selectAction: (value) {
                trackLevelFilterEvent(value);
                if (value.length > 0) {
                  controller.setSelectText("客户级别");
                } else {
                  controller.setSelectText(null);
                }
                this.paramModel.customerLevelCode = value;
                this.refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "licenseCode":
        // 资质
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerMultipleFilterPopup(
              models: this.conditionModel?.licenseType ?? [],
              selectedCodes: this.paramModel.licenseCode,
              distance: distance,
              selectAction: (value) {
                if (value.length > 0) {
                  controller.setSelectText("资质");
                } else {
                  controller.setSelectText(null);
                }
                this.paramModel.licenseCode = value == '' ? null : value;
                this.refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      default:
    }
  }

  void trackSortFilterEvent(String? code) {
    switch (code) {
      case "2":
        track("mc-customer-distance"); // 距离排序
        break;
      case "1":
        track("mc-customer-newly"); // 最近新增
        break;
      case "5":
        track("mc-customer-drop"); // 掉公海时长
        break;
    }
  }

  void trackLevelFilterEvent(String? code) {
    switch (code) {
      case "1":
        track("mc-customer-gradeS"); // S
        break;
      case "5":
        track("mc-customer-gradeA"); // A
        break;
      case "9":
        track("mc-customer-gradeB"); // B
        break;
      case "13":
        track("mc-customer-gradeC"); // C
        break;
      case "14":
        track("mc-customer-gradeD"); // D
        break;
    }
  }

  @override
  Future<void> refreshList({String? type}) async {
    this.page = 0;
    // 没有数据时 刷新增加loading
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }

    // 每次刷新时 重新请求定位信息
    await this.requestLocation(type);

    // 如果本地没有筛选条件则先请求筛选
    if (this.conditionModel == null && !this.isSearch) {
      await this.requestCondition();
    }

    // 设置排序条件
    this.configSortCondition();

    // 请求列表数据
    this.requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  Future<void> requestLocation(String? type) async {
    if (isFirstLoad) {
      /// 延迟100毫秒 进行定位请求，预防首页跳转过来传参未接收
      await Future.delayed(Duration(milliseconds: 100));
    }

    LocationData result;
    if (BuildLocaRequest.latitude == null || BuildLocaRequest.longitude == '' || type == null) {
      result = await XYYContainer.locationChannel.locate().timeout(
        Duration(seconds: 3),
        onTimeout: () {
          return LocationData();
        },
      );
    }else {
      result = LocationData(
        latitude: BuildLocaRequest.latitude,
        longitude: BuildLocaRequest.longitude,
      );
    }
    this.paramModel.poiLatitude = result.latitude;
    this.paramModel.poiLongitude = result.longitude;
    locationLat = result.latitude;
    locationLong = result.longitude;
    XYYContainer.storageChannel.put("customer_map_longitude", result.longitude);
    XYYContainer.storageChannel.put("customer_map_latitude", result.latitude);
    XYYContainer.storageChannel.put("customer_map_location_timestamp",
        DateTime.now().millisecondsSinceEpoch);
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return this.paramModel.poiLatitude?.isEmpty == false &&
        this.paramModel.poiLongitude?.isEmpty == false;
  }

  /// 请求私海列表数据
  void requestListData() async {
    
    var params = this.paramModel.toJson();
    // 移除荷叶使用的参数
    params.remove("sortType");
    params['limit'] = 10;
    params['offset'] = this.page;
   
    var result = await NetworkV2<YBMCustomerPrivatePageModel>(
            YBMCustomerPrivatePageModel())
        .requestDataV2(
      'customerV2/privateList',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
      showErrorToast:false,
    );
    if (this.isFirstLoad != true && this.page == 0) {
      this.dataSource = [];
    }

    /// 设置第一次加载完成
    this.isFirstLoad = false;

    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.rows;
        if (rows != null) {
          if (this.page == 0) {
            this.dataSource = rows;
          } else {
            this.dataSource.addAll(rows);
          }
        } else {
          this.dataSource = [];
        }
        this.isLastPage = data?.lastPage == true;
        this.page = this.isLastPage ? this.page : this.page + 1;
        setState(() {});
      }
    }
  }

  // 请求筛选条件
  Future<void> requestCondition() async {
    var result = await NetworkV2<CustomerConditionListModel>(
            CustomerConditionListModel())
        .requestDataV2('customerV2/list/condition');
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (data != null) {
          this.conditionModel = data;
          setState(() {});
        }
      }
    }
    return;
  }

  void configSortCondition() {
    // 设置排序默认选项
    if (this.paramModel.sortCode == null) {
      String defaultSortText = "距离排序";
      if (this.hasLocation) {
        CustomerConditionModel? defaultSort =
            this.conditionModel?.sortType?.firstWhere(
                  (element) => "${element.selected}" == "1",
                  orElse: () => CustomerConditionModel()
                    ..text = "距离排序"
                    ..code = 2,
                );
        this.paramModel.sortCode = "${defaultSort?.code ?? 2}";
        defaultSortText = "${defaultSort?.text ?? "距离排序"}";
      } else {
        this.paramModel.sortCode = "1";
        defaultSortText = "最近新增";
      }
      this.sortController.setSelectText(defaultSortText);
    } else {
      if (!this.hasLocation && this.paramModel.sortCode == "2") {
        this.paramModel.sortCode = "1";
        this.sortController.setSelectText("最近新增");
      }
    }
  }

  @override
  bool get isSearch => widget.searchChangeText != null;

  @override
  bool get isNoMore => this.isLastPage;

  @override
  int get itemCount => this.dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        YBMCustomerPrivateItemModel model = this.dataSource[index];
        String licenseCode = this.paramModel.licenseCode != null
            ? this.paramModel.licenseCode.toString()
            : "";

        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
            jumpToDetail(model);
          },
          behavior: HitTestBehavior.opaque,
          child: YBMCustomerPrivateItem(
            model,
            hasLocation: this.hasLocation,
            serviceInterface: this.interface,
            licenseCode: licenseCode,
            locationLat: locationLat,
            locationLong: locationLong,
          ),
        );
      };

  // Action 跳转
  /// 跳转客户详情
  void jumpToDetail(YBMCustomerPrivateItemModel model) {
// var lat = "30.461284";
// var lng = "114.428373";
//     Navigator.of(context).pushNamed("/add_visit_recommendation_page?lat=$lat&lng=$lng");
    String router =
        "/customer_private_detail_page?customerId=${model.id}&distributable=${model.distributable}&licenseValidateMust=${model.licenseValidateMust}&licenseValidateIssue=${model.licenseValidateIssue}&merchantStatusName=${model.merchantStatusName}";
    Navigator.of(context).pushNamed(router).then((resultData) {
      print('resultData - $resultData');
      // 私海详情释放 商品集后回调参数 刷新页面数据
      if (resultData is Map &&
          resultData.containsKey("release") == true &&
          resultData["release"] == "1") {
        this.refreshController.callRefresh();
      }
    });
  }

  /// 跳转筛选
  /// 跳转筛选页面
  void jumpFilter() async {
    track("mc-customer-screen");
    dynamic result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ObjectFilterPage(
          filterParams: this.paramModel.filterParams ?? {},
          cacheParams: this.paramModel.cacheParams ?? {},
        ),
        fullscreenDialog: true,
      ),
    );
    if (result != null) {
      if (result is Map<String, dynamic>) {
        if (result.containsKey("filterParams")) {
          this.paramModel.filterParams = result["filterParams"];
          this.paramModel.customerTypeCode = null;
          this.customerTypeController.setSelectText(null);
        }
        if (result.containsKey("cacheParams")) {
          this.paramModel.cacheParams = result["cacheParams"];
        }
        this.refreshController.callRefresh();
      }
    }
  }
}
