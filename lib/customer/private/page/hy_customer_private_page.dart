import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/page/customer_base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_param.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/hy_customer_private_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/object_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class HYCustomerPrivatePage extends CustomerBasePage {
  final ValueNotifier<String?>? searchChangeText;

  final ValueNotifier<int>? searchType;

  HYCustomerPrivatePage({this.searchChangeText, this.searchType});

  @override
  CustomerBasePageState initState() {
    return HYCustomerPrivatePageState();
  }
}

class HYCustomerPrivatePageState
    extends CustomerBasePageState<HYCustomerPrivatePage> with EventBusObserver {
  // 筛选参数
  CustomerConditionListModel? conditionModel;

  // 接口入参存储
  YBMCustomerPrivateParam paramModel = YBMCustomerPrivateParam();

  List<YBMCustomerPrivateItemModel> dataSource = [];

  // 客户类型 下拉框筛选控制器
  DropButtonController customerTypeController = DropButtonController(
    model: DropButtonModel(
      normalText: '客户类型',
    ),
  );

  bool isLastPage = true;

  @override
  void initState() {
    // 清除无用排序参数
    this.paramModel.sortCode = null;

    this.refreshList();
    this.addNotification();

    super.initState();
  }

  // 订阅事件
  void addNotification() {
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.HY_PRIVATE_FILTER,
        callback: (_) {
          this.jumpFilter();
        });
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.HY_PUBLIC_RECEIVE,
        callback: (_) {
          /// 这里直接请求第一页数据， 触发刷新会导致公海页面同时进行下拉动作，不知道为什么
          this.page = 0;
          this.requestListData();
        });
    eventBus.addListener(
        observer: this,
        eventName: CustomerEventBusName.HY_PRIVATE_ALLOC,
        callback: (_) {
          this.refreshController.callRefresh();
        });

    // 监听输入的事件
    widget.searchChangeText?.addListener(() {
      this.paramModel.keyword = widget.searchChangeText?.value;
      this.refreshController.callRefresh();
    });
  }

  @override
  void dispose() {
    eventBus.removeListener(observer: this);
    super.dispose();
  }

  @override
  List<CommonDropConfigModel> get filterConfig {
    // 设置排序默认选项
    String defaultSortText = "距离排序";
    if (this.paramModel.sortType == null) {
      this.paramModel.sortType = "0";
    }
    return [
      CommonDropConfigModel(
        defaultTitle: "最近新增",
        selectTitle: defaultSortText,
        paramKey: "sortType",
      ),
      CommonDropConfigModel(
        defaultTitle: "客户类型",
        paramKey: "customerTypeList",
        controller: this.customerTypeController,
      ),
      CommonDropConfigModel(
        defaultTitle: "客户级别",
        paramKey: "customerLevelCode",
      ),
    ];
  }

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey,
      CommonDropConfigModel model, DropButtonController controller) async {
    switch (model.paramKey) {
      case "sortType":
        // 排序
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '距离排序', code: "1"),
                CustomerConditionModel(text: '最近新增', code: "2"),
              ],
              selectedCode: this.paramModel.sortType,
              selectAction: (value) {
                this.paramModel.sortType = "${value.code}";
                controller.setSelectText("${value.text}");
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "customerTypeList":
        // 客户类型
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerMultipleFilterPopup(
              models: this.conditionModel?.customerType ?? [],
              selectedCodes: this.paramModel.customerTypeList,
              distance: distance,
              selectAction: (value) {
                if (value.length > 0) {
                  controller.setSelectText("客户类型");
                } else {
                  controller.setSelectText(null);
                }
                this.paramModel.customerTypeList = value;
                // 客户类型 匹配处理 筛选页面的
                this.paramModel.filterParams?['customerTypeCode'] =
                    value.length > 0 ? value : '-1';
                this.refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      case "customerLevelCode":
        // 客户级别
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerMultipleFilterPopup(
              models: this.conditionModel?.levelType ?? [],
              selectedCodes: this.paramModel.customerLevelCode,
              distance: distance,
              selectAction: (value) {
                if (value.length > 0) {
                  controller.setSelectText("客户级别");
                } else {
                  controller.setSelectText(null);
                }
                this.paramModel.customerLevelCode = value;
                this.refreshController.callRefresh();
              },
            );
          },
        );
        controller.setIsOpen(false);
        break;
      default:
    }
  }

  @override
  bool get isNoMore => this.isLastPage;

  @override
  int get itemCount => this.dataSource.length;

  @override
  bool get isSearch => widget.searchChangeText != null;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
        var model = this.dataSource[index];
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
            jumpToDetail(model);
          },
          behavior: HitTestBehavior.opaque,
          child: HYCustomerPrivateItem(
            model,
            hasLocation: this.hasLocation,
            serviceInterface: this.interface,
          ),
        );
      };

  /// 跳转客户详情
  void jumpToDetail(YBMCustomerPrivateItemModel model) {
    String router =
        "xyy://crm-app.ybm100.com/customer/hy_private_detail?shopId=${model.id}&hyId=${model.hyId ?? ""}&distributable=${model.distributable}&visitType=1";
    router = Uri.encodeFull(router);
    XYYContainer.open(
      router,
      callback: (resultData) {},
    );
  }

  @override
  Future<void> refreshList() async {
    this.page = 0;
    // 没有数据时 刷新增加loading
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    // 如果本地没有筛选条件则先请求筛选
    if (this.conditionModel == null && !this.isSearch) {
      await this.requestCondition();
    }
    // 每次刷新时 重新请求定位信息
    await this.requestLocation();
    // 请求列表数据
    this.requestListData();
  }

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  Future<void> requestLocation() async {
    var result = await XYYContainer.locationChannel.locate();
    this.paramModel.poiLatitude = result.latitude;
    this.paramModel.poiLongitude = result.longitude;
    return;
  }

  // 当前页面是否获取经纬度
  bool get hasLocation {
    return this.paramModel.poiLatitude?.isEmpty == false &&
        this.paramModel.poiLongitude?.isEmpty == false;
  }

  /// 请求私海列表数据
  void requestListData() async {
    var params = this.paramModel.toJson();
    // 移除药帮忙筛选过来的参数，已处理了冗余代码
    if (params.containsKey('customerTypeCode')) {
      params.remove('customerTypeCode');
    }

    /// 共用药帮忙私海请求类， 处理keyWord
    if (params.containsKey('keyword')) {
      if (widget.searchType?.value == 1) {
        // 搜索POI信息
        params['hyId'] = params['keyword'];
      } else {
        // 搜索关键词
        params['keyWord'] = params['keyword'];
      }
      params.remove('keyword');
    }
    params['limit'] = 10;
    params['offset'] = this.page;

    var result =
        await NetworkV2<YBMCusomerPrivateModel>(YBMCusomerPrivateModel())
            .requestDataV2(
      'hyjk/privateSeaList',
      contentType: RequestContentType.FORM,
      parameters: params,
      method: RequestMethod.POST,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        var rows = data?.pageData?.rows;
        if (rows != null) {
          if (this.page == 0) {
            this.dataSource = rows;
          } else {
            this.dataSource.addAll(rows);
          }
        } else {
          this.dataSource = [];
        }
        this.isLastPage = data?.pageData?.lastPage == true;
        this.page = this.isLastPage ? this.page : this.page + 1;
        setState(() {});
      }
    }
  }

  // 请求筛选条件
  Future<void> requestCondition() async {
    var result = await NetworkV2<CustomerConditionListModel>(
            CustomerConditionListModel())
        .requestDataV2('customer/private/list/condition');
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (data != null) {
          this.conditionModel = data;
          setState(() {});
        }
      }
    }
    return;
  }

  /// 跳转筛选
  /// 跳转筛选页面
  void jumpFilter() async {
    dynamic result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ObjectFilterPage(
          filterParams: this.paramModel.filterParams ?? {},
          cacheParams: this.paramModel.cacheParams ?? {},
        ),
        fullscreenDialog: true,
      ),
    );
    if (result != null) {
      if (result is Map<String, dynamic>) {
        if (result.containsKey("filterParams")) {
          this.paramModel.filterParams = result["filterParams"];

          /// 处理包含客户类型的逻辑
          if (this.paramModel.filterParams?.containsKey('customerTypeCode') ==
              true) {
            this.paramModel.customerTypeList =
                this.paramModel.filterParams?['customerTypeCode'];
            this.customerTypeController.setSelectText('客户类型');
          } else {
            this.paramModel.customerTypeList = null;
            this.customerTypeController.setSelectText(null);
          }
        }
        if (result.containsKey("cacheParams")) {
          this.paramModel.cacheParams = result["cacheParams"];
        }
        this.refreshController.callRefresh();
      }
    }
  }
}
