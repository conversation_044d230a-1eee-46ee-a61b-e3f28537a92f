import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_detail_data.g.dart';

@JsonSerializable()
class CustomerDetailData extends BaseModelV2 {
  dynamic id;

  /// 客户名称
  dynamic customerName;

  /// 地址
  dynamic address;

  /// 经度
  dynamic poiLongitude;

  /// 纬度
  dynamic poiLatitude;

  /// ec注册名称
  dynamic ecCustomerName;

  /// 是否注册标识 1是，2否 默认注册
  dynamic registerFlag;

  /// poiId
  dynamic poiId;

  /// 客户编号
  dynamic merchantId;

  /// 审核状态 1待审核 2审核通过 3驳回
  dynamic poiAuditStatus;

  /// 所属蜂窝名称
  dynamic combName;

  /// 审核id
  dynamic poiAuditId;

  /// 商品集列表
  List<CustomerSkuCollectData>? bindSkuCollect;

  /// ============================私海详情特有字段 start==========================

  /// 联系人列表，用于打电话拜访
  List<YBMCustomerContactModel>? contactList;

  /// 电话拜访使用，当未注册时默认取该电话
  dynamic poiMobilePhone;

  /// 是否可释放1是，2否
  dynamic unbindFlag;

  /// ============================公海详情特有字段 start==========================
  /// 暂无

  CustomerDetailData();

  /// 展示的客户名称
  @JsonKey(ignore: true)
  String get merchantName {
    String name = "";
    if (this.ecCustomerName != null &&
        this.ecCustomerName.toString().isNotEmpty) {
      name = "${this.ecCustomerName}";
    } else if (this.customerName != null) {
      print("customerName = ${this.customerName}");
      name = "${this.customerName}";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 展示的蜂窝名称
  @JsonKey(ignore: true)
  String get regionName {
    String name = "";
    if (this.combName != null) {
      name = "${this.combName}";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 展示的客户编码
  @JsonKey(ignore: true)
  String get merchantCode {
    String name = "";
    if (this.merchantId != null) {
      name = "${this.merchantId}";
    }
    return name.isNotEmpty && name != "-1" ? name : "-";
  }

  /// 展示的门店名称
  @JsonKey(ignore: true)
  String get shopName {
    String name = "";
    if (this.customerName != null) {
      name = "${this.customerName}";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 展示的门店id
  @JsonKey(ignore: true)
  String get shopId {
    String name = "";
    if (this.poiId != null) {
      name = "${this.poiId}";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 展示的门店地址
  @JsonKey(ignore: true)
  String get shopAddress {
    String name = "";
    if (this.address != null) {
      name = "${this.address}";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 展示的门店审核状态
  @JsonKey(ignore: true)
  String get shopStatus {
    String name = "";
    switch (this.poiAuditStatus) {
      case 1:
        name = "待审核";
        break;
      case 2:
        name = "审核通过";
        break;
      case 3:
        name = "已驳回";
        break;
      default:
        name = "未知审核状态";
    }
    return name.isNotEmpty ? name : "-";
  }

  /// 审核状态颜色
  @JsonKey(ignore: true)
  Color get shopStatusTextColor {
    Color color = Color(0xFFFF2121);
    switch (this.poiAuditStatus) {
      case 1:
        color = Color(0xFFFF7200);
        break;
      case 2:
        color = Color(0xFF00B377);
        break;
      case 3:
        color = Color(0xFFFF2121);
        break;
      default:
        color = Color(0xFFFF2121);
    }
    return color;
  }

  factory CustomerDetailData.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailDataToJson(this);
  }
}
