import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_contact_model.g.dart';

@JsonSerializable()
class YBMCustomerContactModel {
  /// 创建人id
  dynamic creator;

  /// 生日
  dynamic contactBirth;

  /// 岗位
  dynamic contactJob;

  /// 岗位名称仅用于页面回显
  dynamic contactJobName;

  /// 是否kp
  dynamic isEffective;

  /// 手机号
  dynamic contactMobile;

  /// 姓名
  dynamic contactName;

  /// 性别
  dynamic contactSex;

  /// 性别名称仅用于回显
  @JsonKey(ignore: true)
  String get contactSexName {
    if (contactSex.toString().length > 0) {
      return contactSex.toString() == "0" ? "女" : "男";
    }
    return "";
  }

  /// 标签
  dynamic contactTag;

  /// 客户id
  dynamic merchantId;

  /// 客户名称
  dynamic merchantName;

  // dynamic sysUserId;

  /// 编辑的情况下 需要传递当前联系人id
  dynamic id;

  dynamic kp;

  YBMCustomerContactModel();

  Map<String, dynamic> toJson() {
    return _$YBMCustomerContactModelToJson(this);
  }

  factory YBMCustomerContactModel.fromJson(Map<String, dynamic> json) =>
      _$YBMCustomerContactModelFromJson(json);
}
