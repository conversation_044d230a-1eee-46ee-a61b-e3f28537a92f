import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_detail_purchase_trend_data.g.dart';

@JsonSerializable()
class CustomerDetailPurchaseTrendData extends BaseModelV2<CustomerDetailPurchaseTrendData> {

  dynamic orderCount;
  dynamic refundOrderCount;
  dynamic couponCount;
  List<GMVItem>? gmvList;

  CustomerDetailPurchaseTrendData();


  factory CustomerDetailPurchaseTrendData.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailPurchaseTrendDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailPurchaseTrendDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailPurchaseTrendDataToJson(this);
  }
}

@JsonSerializable()
class GMVItem extends BaseModelV2<GMVItem> {

  dynamic dateStr;
  dynamic payGmv;
  dynamic highGrossGmv;
  dynamic marketControlGmv;

  dynamic selectionGrossGmv;//甄选GMV

  GMVItem();


  factory GMVItem.fromJson(Map<String, dynamic> json) =>
      _$GMVItemFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$GMVItemFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$GMVItemToJson(this);
  }
}
