// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ybm_customer_private_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

YBMCusomerPrivateModel _$YBMCusomerPrivateModelFromJson(
    Map<String, dynamic> json) {
  return YBMCusomerPrivateModel()
    ..pageData = json['pageData'] == null
        ? null
        : YBMCustomerPrivatePageModel.fromJson(
            json['pageData'] as Map<String, dynamic>);
}

Map<String, dynamic> _$YBMCusomerPrivateModelToJson(
        YBMCusomerPrivateModel instance) =>
    <String, dynamic>{
      'pageData': instance.pageData,
    };

YBMCustomerPrivatePageModel _$YBMCustomerPrivatePageModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerPrivatePageModel()
    ..rows = (json['rows'] as List<dynamic>?)
            ?.map((e) =>
                YBMCustomerPrivateItemModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        []
    ..lastPage = json['lastPage'];
}

Map<String, dynamic> _$YBMCustomerPrivatePageModelToJson(
        YBMCustomerPrivatePageModel instance) =>
    <String, dynamic>{
      'rows': instance.rows,
      'lastPage': instance.lastPage,
    };

YBMCustomerPrivateItemModel _$YBMCustomerPrivateItemModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerPrivateItemModel()
    ..address = json['address']
    ..biLevelName = json['biLevelName']
    ..cartNum = json['cartNum'] ?? 0
    ..customerName = json['customerName']
    ..customerTypeName = json['customerTypeName']
    ..biLifecycle = json['biLifecycle']
    ..biLifecycleName = json['biLifecycleName']
    ..distance = json['distance']
    ..distributable = json['distributable']
    ..id = json['id']
    ..poiId = json['poiId']
    ..hyId = json['hyId']
    ..licenseRecoveryFlag = json['licenseRecoveryFlag']
    ..licenseValidateFlag = json['licenseValidateFlag']
    ..licenseStatusName = json['licenseStatusName']
    ..merchantId = json['merchantId']
    ..mobile = json['mobile']
    ..oaUserLevel = json['oaUserLevel']
    ..overLastVisitDays = json['overLastVisitDays'] ?? '-'
    ..poiLatitude = json['poiLatitude']
    ..poiLongitude = json['poiLongitude']
    ..registerFlag = json['registerFlag']
    ..thisMonthOrderAmt = json['thisMonthOrderAmt'] ?? ''
    ..thisMonthOrderNum = json['thisMonthOrderNum'] ?? ''
    ..thisMonthOrderCount = json['thisMonthOrderCount'] ?? ''
    ..thisMonthOrderSkuCount = json['thisMonthOrderSkuCount'] ?? '0'
    ..lastMonthOrderSkuCount = json['lastMonthOrderSkuCount'] ?? '0'
    ..lastMonthOrderNum = json['lastMonthOrderNum'] ?? ''
    ..lastMonthOrderAmt = json['lastMonthOrderAmt'] ?? ''
    ..ecAddress = json['ecAddress']
    ..ecCustomerName = json['ecCustomerName'] ?? ''
    ..merchantStatusName = json['merchantStatusName'] ?? ''
    ..status = json['status']
    ..statusName = json['statusName']
    ..relaseDays = json['relaseDays']
    ..customerStatusName = json['customerStatusName']
    ..bindCountdown = json['bindCountdown']
    ..poiRegisterFlag = json['poiRegisterFlag']
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map(
            (e) => YBMCustomerContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..serviceLines = (json['serviceLines'] as List<dynamic>?)
        ?.map((e) =>
            YBMCustomerServiceLinesModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..bindSkuCollect = (json['bindSkuCollect'] as List<dynamic>?)
        ?.map(
            (e) => CustomerSkuCollectData.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..licenseValidateMust = json['licenseValidateMust']
    ..licenseValidateIssue = json['licenseValidateIssue']
    ..inPlanFlag = json['inPlanFlag'] ?? false;
}

Map<String, dynamic> _$YBMCustomerPrivateItemModelToJson(
        YBMCustomerPrivateItemModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'biLevelName': instance.biLevelName,
      'cartNum': instance.cartNum,
      'customerName': instance.customerName,
      'customerTypeName': instance.customerTypeName,
      'biLifecycle': instance.biLifecycle,
      'biLifecycleName': instance.biLifecycleName,
      'distance': instance.distance,
      'distributable': instance.distributable,
      'id': instance.id,
      'poiId': instance.poiId,
      'hyId': instance.hyId,
      'licenseRecoveryFlag': instance.licenseRecoveryFlag,
      'licenseValidateFlag': instance.licenseValidateFlag,
      'licenseStatusName': instance.licenseStatusName,
      'merchantId': instance.merchantId,
      'mobile': instance.mobile,
      'oaUserLevel': instance.oaUserLevel,
      'overLastVisitDays': instance.overLastVisitDays,
      'poiLatitude': instance.poiLatitude,
      'poiLongitude': instance.poiLongitude,
      'registerFlag': instance.registerFlag,
      'thisMonthOrderAmt': instance.thisMonthOrderAmt,
      'thisMonthOrderNum': instance.thisMonthOrderNum,
      'thisMonthOrderCount': instance.thisMonthOrderCount,
      'thisMonthOrderSkuCount': instance.thisMonthOrderSkuCount,
      'lastMonthOrderSkuCount': instance.lastMonthOrderSkuCount,
      'lastMonthOrderNum': instance.lastMonthOrderNum,
      'lastMonthOrderAmt': instance.lastMonthOrderAmt,
      'ecAddress': instance.ecAddress,
      'ecCustomerName': instance.ecCustomerName,
      'merchantStatusName': instance.merchantStatusName,
      'status': instance.status,
      'statusName': instance.statusName,
      'relaseDays': instance.relaseDays,
      'customerStatusName': instance.customerStatusName,
      'bindCountdown': instance.bindCountdown,
      'poiRegisterFlag': instance.poiRegisterFlag,
      'contactList': instance.contactList,
      'serviceLines': instance.serviceLines,
      'bindSkuCollect': instance.bindSkuCollect,
      'licenseValidateMust': instance.licenseValidateMust,
      'licenseValidateIssue': instance.licenseValidateIssue,
      'inPlanFlag': instance.inPlanFlag,
    };

YBMCustomerServiceLinesModel _$YBMCustomerServiceLinesModelFromJson(
    Map<String, dynamic> json) {
  return YBMCustomerServiceLinesModel()
    ..code = json['code']
    ..name = json['name']
    ..lighten = json['lighten']
    ..iOSIcon = json['iOSIcon'];
}

Map<String, dynamic> _$YBMCustomerServiceLinesModelToJson(
        YBMCustomerServiceLinesModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'name': instance.name,
      'lighten': instance.lighten,
      'iOSIcon': instance.iOSIcon,
    };
