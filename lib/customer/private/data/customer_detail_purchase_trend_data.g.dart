// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_detail_purchase_trend_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerDetailPurchaseTrendData _$CustomerDetailPurchaseTrendDataFromJson(
    Map<String, dynamic> json) {
  return CustomerDetailPurchaseTrendData()
    ..orderCount = json['orderCount']
    ..refundOrderCount = json['refundOrderCount']
    ..couponCount = json['couponCount']
    ..gmvList = (json['gmvList'] as List<dynamic>?)
        ?.map((e) => GMVItem.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CustomerDetailPurchaseTrendDataToJson(
        CustomerDetailPurchaseTrendData instance) =>
    <String, dynamic>{
      'orderCount': instance.orderCount,
      'refundOrderCount': instance.refundOrderCount,
      'couponCount': instance.couponCount,
      'gmvList': instance.gmvList,
    };

GMVItem _$GMVItemFromJson(Map<String, dynamic> json) {
  return GMVItem()
    ..dateStr = json['dateStr']
    ..payGmv = json['payGmv']
    ..highGrossGmv = json['highGrossGmv']
    ..marketControlGmv = json['marketControlGmv']
    ..selectionGrossGmv = json['selectionGrossGmv'];
}

Map<String, dynamic> _$GMVItemToJson(GMVItem instance) => <String, dynamic>{
      'dateStr': instance.dateStr,
      'payGmv': instance.payGmv,
      'highGrossGmv': instance.highGrossGmv,
      'marketControlGmv': instance.marketControlGmv,
      'selectionGrossGmv': instance.selectionGrossGmv,
    };
