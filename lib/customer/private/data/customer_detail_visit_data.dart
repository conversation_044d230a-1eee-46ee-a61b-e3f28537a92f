import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_detail_visit_data.g.dart';

@JsonSerializable()
class CustomerDetailVisitData extends BaseModelV2<CustomerDetailVisitData> {

  dynamic visitDate;
  dynamic visitOaUser;
  dynamic visitPurpose;
  dynamic visitTypeDesc;
  dynamic visitSummary;
  dynamic customerId;

  CustomerDetailVisitData();


  factory CustomerDetailVisitData.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailVisitDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailVisitDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailVisitDataToJson(this);
  }
}
