// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_detail_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerDetailData _$CustomerDetailDataFromJson(Map<String, dynamic> json) {
  return CustomerDetailData()
    ..id = json['id']
    ..customerName = json['customerName']
    ..address = json['address']
    ..poiLongitude = json['poiLongitude']
    ..poiLatitude = json['poiLatitude']
    ..ecCustomerName = json['ecCustomerName']
    ..registerFlag = json['registerFlag']
    ..poiId = json['poiId']
    ..merchantId = json['merchantId']
    ..poiAuditStatus = json['poiAuditStatus']
    ..combName = json['combName']
    ..poiAuditId = json['poiAuditId']
    ..bindSkuCollect = (json['bindSkuCollect'] as List<dynamic>?)
        ?.map(
            (e) => CustomerSkuCollectData.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map(
            (e) => YBMCustomerContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..poiMobilePhone = json['poiMobilePhone']
    ..unbindFlag = json['unbindFlag'];
}

Map<String, dynamic> _$CustomerDetailDataToJson(CustomerDetailData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerName': instance.customerName,
      'address': instance.address,
      'poiLongitude': instance.poiLongitude,
      'poiLatitude': instance.poiLatitude,
      'ecCustomerName': instance.ecCustomerName,
      'registerFlag': instance.registerFlag,
      'poiId': instance.poiId,
      'merchantId': instance.merchantId,
      'poiAuditStatus': instance.poiAuditStatus,
      'combName': instance.combName,
      'poiAuditId': instance.poiAuditId,
      'bindSkuCollect': instance.bindSkuCollect,
      'contactList': instance.contactList,
      'poiMobilePhone': instance.poiMobilePhone,
      'unbindFlag': instance.unbindFlag,
    };
