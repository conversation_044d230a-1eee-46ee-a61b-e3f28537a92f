import 'package:json_annotation/json_annotation.dart';

part 'ybm_customer_private_param.g.dart';

@JsonSerializable(createFactory: false)
class YBMCustomerPrivateParam {
  // 排序条件 - 默认最近新增
  String? sortCode;
  // 排序条件 - 荷叶使用 默认距离排序
  String? sortType = "1";
  // 客户等级
  String? customerLevelCode;
  // 客户资质
  String? licenseCode;
  // 客户类型
  String? customerTypeCode;
  // 客户状态
  String? merchantStatus;
  // 客户类型 - 荷叶使用
  String? customerTypeList;

  // 关键词 - 搜索页面使用
  String? keyword;

  /// 筛选页面参数
  @JsonKey(ignore: true)
  Map<String, String>? filterParams = {};

  /// 筛选页面缓存
  @JsonKey(ignore: true)
  Map<String, dynamic>? cacheParams = {};

  String? poiLatitude;
  String? poiLongitude;

  YBMCustomerPrivateParam();

  Map<String, dynamic> toJson() {
    var map = _$YBMCustomerPrivateParamToJson(this);
    if (this.filterParams != null) {
      map.addAll(this.filterParams!);
    }
    map.removeWhere((key, value) =>
        value == "-1" || value == -1 || value == null || value == "");
    return map;
  }
}
