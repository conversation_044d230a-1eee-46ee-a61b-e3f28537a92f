import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/header_footer/header_footer_helper.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_schedule_item_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_schedule_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ScheduleRecordListPage extends BasePage {
  final String type; // "0":私海，"1"：公海
  final String customerId;

  ScheduleRecordListPage(this.type, this.customerId);

  @override
  BaseState<StatefulWidget> initState() {
    return ScheduleRecordListPageState();
  }
}

const PAGE_SIZE = 10;

class ScheduleRecordListPageState extends BaseState<ScheduleRecordListPage> {
  var _controller = EasyRefreshController();

  List<CustomerScheduleItemData?>? listData;
  int offset = 0;
  PageState pageState = PageState.Empty;
  bool lastPage = false;

  @override
  void onCreate() {
    super.onCreate();
    requestRecordList(true);
    if (Platform.isAndroid) {
      XYYContainer.bridgeCall("handle_call_record",
          parameters: {"source": "visit_record"});
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xffefeff4),
      child: EasyRefresh.custom(
          controller: _controller,
          enableControlFinishLoad: true,
          enableControlFinishRefresh: true,
          header: HeaderFooterHelp().getHeader(),
          footer: HeaderFooterHelp().getFooter(),
          onRefresh: () async {
            requestRecordList(true);
          },
          onLoad: () async {
            requestRecordList(false);
          },
          emptyWidget: getEmptyWidget(),
          slivers: [
            SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                if (listData == null || (listData?.isEmpty ?? true)) {
                  return Container();
                }
                var itemData = listData?[index];
                if (itemData == null) {
                  return Container();
                }
                return GestureDetector(
                  onTap: () {
                    if (itemData.perfect != 1) {
                      var router =
                          '/visit_detail_page?scheduleId=${itemData.id}';
                      XYYContainer.open(router);
                    } else {
                      showToast("拜访信息未完善，无法查看详情");
                    }
                  },
                  child: Container(
                    height: 116,
                    margin: EdgeInsets.only(top: 10, left: 10, right: 10),
                    child: Stack(
                      children: [
                        // 卡片主体
                        Container(
                          padding: EdgeInsets.all(10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Image.asset(
                                    itemData.scheduleType == "1"
                                        ? 'assets/images/customer/customer_schedule_type_phone.png'
                                        : 'assets/images/customer/customer_schedule_type_site.png',
                                    width: 30,
                                    height: 30,
                                  ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                      child: Container(
                                    alignment: Alignment.centerLeft,
                                    height: 30,
                                    child: Text(
                                      itemData.scheduleTypeName ?? "",
                                      style: TextStyle(
                                          color: const Color(0xff292933),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16),
                                    ),
                                  )),
                                  Visibility(
                                    visible: 
                                        itemData.perfect == 1,
                                    child: GestureDetector(
                                      onTap: () {
                                        Map<String, dynamic> perfectMap = {};
                                        perfectMap["visitId"] =
                                            itemData.id.toString();
                                        perfectMap["merchantName"] =
                                            itemData.merchantName;
                                        perfectMap["contactor"] =
                                            itemData.contactor;
                                        perfectMap["isEffective"] =
                                            itemData.isEffective.toString();
                                        perfectMap["merchantId"] =
                                            itemData.poiId.toString();
                                        perfectMap["customerId"] =
                                            itemData.poiId.toString();
                                        perfectMap["mobile"] =
                                            itemData.mobile.toString();
                                        perfectMap["talkTime"] =
                                            itemData.talkTime.toString();
                                        perfectMap["isRegisterFlag"] =
                                            itemData.registerFlag.toString();
                                        var jsonString =
                                            json.encode(perfectMap);
                                        var router =
                                            "/add_perfect_visit_page?perfectJSON=$jsonString";
                                        router = Uri.encodeFull(router);
                                        XYYContainer.open(router,
                                            callback: (result) {
                                          if (result != null &&
                                              result['success'] == 'true') {
                                            requestRecordList(true);
                                          }
                                        });
                                      },
                                      child: Container(
                                        width: 65,
                                        decoration: BoxDecoration(
                                            color: const Color(0xff00b377),
                                            borderRadius:
                                                BorderRadius.circular(3)),
                                        height: 26,
                                        alignment: Alignment.center,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              '去完善',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14),
                                              strutStyle:
                                                  StrutStyle(leading: 0.2),
                                            ),
                                            SizedBox(
                                              width: 2,
                                            ),
                                            Image.asset(
                                              'assets/images/customer/customer_schedule_complete_arrow.png',
                                              width: 7,
                                              height: 7,
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Divider(
                                height: 1,
                                color: const Color(0xfff7f7f8),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    '拜访人：',
                                    style: TextStyle(
                                        color: const Color(0xff9494a6),
                                        fontWeight: FontWeight.normal,
                                        fontSize: 14),
                                  ),
                                  Text(
                                    itemData.userName ?? "",
                                    style: TextStyle(
                                        color: const Color(0xff292933),
                                        fontWeight: FontWeight.normal,
                                        fontSize: 14),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    '拜访时间：',
                                    style: TextStyle(
                                        color: const Color(0xff9494a6),
                                        fontWeight: FontWeight.normal,
                                        fontSize: 14),
                                  ),
                                  Text(
                                    itemData.createTime != null
                                        ? TimeUtils()
                                            .formatTime(itemData.createTime!)
                                        : "",
                                    style: TextStyle(
                                        color: const Color(0xff292933),
                                        fontWeight: FontWeight.normal,
                                        fontSize: 14),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        // 有效标签
                        Visibility(
                          visible:
                              visibilityEfIcon(itemData),
                          child: Positioned(
                            top: 36,
                            right: 6,
                            child: Image.asset(
                              'assets/images/customer/customer_schedule_effective.png',
                              height: 65,
                              width: 65,
                            ),
                          ),
                        )
                      ],
                    ),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(2),
                        boxShadow: [
                          BoxShadow(
                            color: Color(0x0D292933),
                            offset: Offset(0, 0.5),
                            blurRadius: 1,
                          )
                        ]),
                  ),
                );
              }, childCount: listData?.length ?? 0),
            )
          ]),
    );
  }

  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestRecordList(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(PageState.Empty);
      default:
        return null;
    }
  }

  bool visibilityEfIcon(itemData){
     var result = itemData.effective == 1 && widget.type == "0";
     if(itemData.scheduleTypeName == "电话拜访" && itemData.perfect.toString() == "1") {
       result =  false;
     }
    return result;
  }

  requestRecordList(bool isRefresh) {
    if (isRefresh && (listData?.isEmpty ?? true)) {
      showLoadingDialog(isMask: true, dismissOnTap: true);
    }
    NetworkV2<CustomerScheduleListData>(CustomerScheduleListData())
        .requestDataV2("merchant/v300/followInRecords",
            parameters: {
              "limit": PAGE_SIZE,
              "merchantId": widget.customerId,
              "offset": isRefresh ? 0 : offset
            },
            method: RequestMethod.GET)
        .then((value) {
      dismissLoadingDialog();
      if (mounted) {
        setState(() {
          if (value.isSuccess ?? false) {
            var result = value.getData();
            if (result != null) {
              lastPage = result.lastPage ?? false;
              if (isRefresh) {
                listData = result.rows;
                offset = 0;
              } else {
                listData?.addAll(result.rows ?? List.empty());
                if (PAGE_SIZE * offset < (result.total ?? 0)) {
                  offset = offset + 1;
                }
              }
              if (this.listData?.isEmpty ?? true) {
                pageState = PageState.Empty;
              } else {
                pageState = PageState.Normal;
              }
            }
          } else {
            pageState = PageState.Error;
          }
        });
        if (isRefresh) {
          _controller.resetLoadState();
        }
        _controller.finishRefresh();
        _controller.finishLoad(noMore: value.getData()?.lastPage ?? false);
      }
    });
  }

  @override
  String getTitleName() {
    return "拜访记录";
  }
}
