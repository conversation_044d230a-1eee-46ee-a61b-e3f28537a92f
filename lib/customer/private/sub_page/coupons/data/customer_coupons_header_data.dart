import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_coupons_header_data.g.dart';

@JsonSerializable()
class CustomerCouponsHeaderModel
    extends BaseModelV2<CustomerCouponsHeaderModel> {
  dynamic unusedCount;
  dynamic usedCount;
  dynamic expiredCount;

  @override
  CustomerCouponsHeaderModel fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerCouponsHeaderModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerCouponsHeaderModelToJson(this);
  }
}
