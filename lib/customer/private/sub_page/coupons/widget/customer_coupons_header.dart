import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:flutter/material.dart';

class CustomerCouponsHeader extends StatefulWidget {
  final ValueChanged<dynamic> typeChanged;

  final List<String> titles;

  CustomerCouponsHeader({
    required this.typeChanged,
    required this.titles,
  });

  @override
  State<StatefulWidget> createState() {
    return _CustomerCouponsHeaderState();
  }
}

class _CustomerCouponsHeaderState extends State<CustomerCouponsHeader> {
  ValueNotifier<BackgroundButtonState>? controller =
      ValueNotifier(BackgroundButtonState.selected);

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _CustomerCouponsHeaderDelegate(
        child: Container(
          padding: EdgeInsets.all(10),
          color: Color(0xFFFFFFFF),
          child: Row(
            children: [
              this.getItemWidget(
                widget.titles[0],
                1,
                changeSelected,
                this.controller,
              ),
              SizedBox(width: 12),
              this.getItemWidget(
                widget.titles[1],
                2,
                changeSelected,
                null,
              ),
              SizedBox(width: 12),
              this.getItemWidget(
                widget.titles[2],
                3,
                changeSelected,
                null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getItemWidget(
      String title,
      dynamic option,
      BackgroundButtonPressed onPressed,
      ValueNotifier<BackgroundButtonState>? controller) {
    return Expanded(
      child: Container(
        height: 30,
        child: BackgroundStateButton(
          title: title,
          option: option,
          alignment: Alignment.center,
          controller: controller,
          textStyle: TextStyle(color: Color(0xFF676773), fontSize: 12),
          selectStyle: TextStyle(
            color: Color(0xFF00B377),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          normalDecoration: BoxDecoration(
            color: Color(0xFFF7F7F8),
            borderRadius: BorderRadius.circular(15),
          ),
          selectDecoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            border: Border.all(color: Color(0xFF00B377), width: 0.5),
            borderRadius: BorderRadius.circular(15),
          ),
          onPressed: onPressed,
        ),
      ),
    );
  }

  void changeSelected(
      ValueNotifier<BackgroundButtonState> controller, dynamic option) {
    widget.typeChanged(option);
    this.controller?.value = BackgroundButtonState.normal;
    this.controller = controller;
  }
}

class _CustomerCouponsHeaderDelegate extends SliverPersistentHeaderDelegate {
  _CustomerCouponsHeaderDelegate({
    required this.child,
  });

  final Widget child;

  @override
  double get minExtent => 50;

  @override
  double get maxExtent => 50;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    List<BoxShadow> list = [];
    if (shrinkOffset > 0) {
      list.add(
        BoxShadow(
          color: Color(0x14000000),
          offset: Offset(0, 1),
          blurRadius: 2.0, // 模糊
          spreadRadius: 0.0, // 延展
        ),
      );
    }
    return Container(
      decoration: BoxDecoration(boxShadow: list),
      child: child,
    );
  }

  @override
  bool shouldRebuild(_CustomerCouponsHeaderDelegate oldDelegate) {
    return child != oldDelegate.child;
  }
}
