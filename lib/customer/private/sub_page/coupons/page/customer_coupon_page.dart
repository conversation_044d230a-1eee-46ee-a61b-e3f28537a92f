import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/coupons/data/customer_coupons_header_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/coupons/widget/customer_coupons_header.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/coupons_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
// ignore: implementation_imports
import 'package:flutter_easyrefresh/src/widget/empty_widget.dart';

class CustomerCouponsPage extends BasePage {
  final dynamic merchantId;

  CustomerCouponsPage({this.merchantId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerCoupnsPageState();
  }
}

class CustomerCoupnsPageState extends BaseState<CustomerCouponsPage> {
  int page = 1;
  dynamic type = 1;

  EasyRefreshController _controller = EasyRefreshController();

  List<CouponsListDataModel> dataSource = [];
  CustomerCouponsHeaderModel? headerModel;

  bool isLastPage = true;

  @override
  void initState() {
    this.refreshList();
    super.initState();
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: EasyRefresh(
        onRefresh: refreshList,
        onLoad: this.isLastPage ? null : loadMoreList,
        controller: _controller,
        child: CustomScrollView(
          slivers: [
            this.getHeaderWidget(),
            this.getListWidget(),
          ],
        ),
      ),
    );
  }

  Widget getHeaderWidget() {
    return CustomerCouponsHeader(
      titles: [
        "未使用" + "(${this.headerModel?.unusedCount ?? '-'})",
        "已使用" + "(${this.headerModel?.usedCount ?? '-'})",
        "已过期" + "(${this.headerModel?.expiredCount ?? '-'})",
      ],
      typeChanged: (type) {
        this.type = type;
        _controller.callRefresh();

        switch (type) {
          case 1:
            track('mc-client-couponUnused');
            break;
          case 2:
            track("mc-client-couponUsed");
            break;
          case 3:
            track("mc-client-couponExpired");
            break;
          default:
        }
      },
    );
  }

  Widget getListWidget() {
    if (this.dataSource.length == 0) {
      return EmptyWidget(child: PageStateWidget(state: PageState.Empty));
    }
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          var model = this.dataSource[index];
          // 未使用正常展示，已使用/已过期展示灰色
          if ("${model.status}" == "1") {
            return CouponsListItem.detail(model: model);
          }
          return CouponsListItem.gary(model: model);
        },
        childCount: this.dataSource.length,
      ),
    );
  }

  Future<void> refreshList() async {
    this.page = 1;
    await Future.wait([this.requestList(), this.requestHeaderData()]);
    return;
  }

  Future<void> loadMoreList() async {
    await this.requestList();
    return;
  }

  Future<void> requestHeaderData() async {
    var result = await NetworkV2<CustomerCouponsHeaderModel>(
            CustomerCouponsHeaderModel())
        .requestDataV2(
      'coupon/merchant/couponCount',
      parameters: {
        'merchantId': widget.merchantId,
      },
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );

    if (mounted) {
      if (result.isSuccess == true) {
        this.headerModel = result.getData();
        setState(() {});
      }
    }
    return;
  }

  Future<void> requestList() async {
    Map<String, dynamic> param = {
      'offset': this.page,
      'limit': "10",
      'merchantId': widget.merchantId,
      'type': this.type,
    };
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<CouponsListDataPageModel>(CouponsListDataPageModel())
            .requestDataV2(
      'coupon/merchant/couponList',
      parameters: param,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (this.page == 1) {
          this.dataSource = data?.list ?? [];
        } else {
          this.dataSource.addAll(data?.list ?? []);
        }
        this.isLastPage = data?.isLastPage == true;
        this.page += 1;
        setState(() {});
      }
    }
    return;
  }

  @override
  String getTitleName() {
    return "优惠券";
  }
}
