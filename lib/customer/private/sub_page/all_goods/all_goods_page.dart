import 'dart:io';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AllGoodsPage extends BasePage {
  final dynamic customerId;

  AllGoodsPage({this.customerId});

  @override
  BaseState<StatefulWidget> initState() {
    return AllGoodsPageState();
  }
}

class AllGoodsPageState extends BaseState<AllGoodsPage> {
  AndroidView? androidView;

  @override
  bool isSubPage() => true;

  @override
  bool needKeepAlive() => true;

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: this.getContentView(),
    );
  }

  Widget getContentView() {
    if (Platform.isIOS) {
      return UiKitView(
        viewType: "all_goods_view_id",
        creationParams: {
          "merchantId": "${widget.customerId}",
        },
        creationParamsCodec: StandardMessageCodec(),
        gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>[
          new Factory<OneSequenceGestureRecognizer>(
            () => new _CustomEagerGestureRecognizer(),
          ),
        ].toSet(),
      );
    } else {
      if (androidView == null) {
        androidView = AndroidView(
          viewType: 'all_goods_view_id',
          creationParams: {
            "merchantId": "${widget.customerId}",
          },
          creationParamsCodec: StandardMessageCodec(),
        );
        Future.delayed(Duration(milliseconds: 200), () {
          setState(() {});
        });
        return Container(
          color: Colors.white,
        );
      } else {
        return androidView!;
      }
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "全部商品";
  }
}

class _CustomEagerGestureRecognizer extends OneSequenceGestureRecognizer {
  _CustomEagerGestureRecognizer({PointerDeviceKind? kind}) : super(kind: kind);

  @override
  void addAllowedPointer(PointerDownEvent event) {
    // We call startTrackingPointer as this is where OneSequenceGestureRecognizer joins the arena.
    startTrackingPointer(event.pointer, event.transform);
    resolve(GestureDisposition.accepted);
    stopTrackingPointer(event.pointer);
  }

  @override
  String get debugDescription => 'eager';

  @override
  void didStopTrackingLastPointer(int pointer) {}

  @override
  void handleEvent(PointerEvent event) {}
}
