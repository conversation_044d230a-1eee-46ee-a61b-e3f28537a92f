import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/all_goods/all_goods_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/page/customer_often_buy_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/page/customer_search_word.dart';
import 'package:XyyBeanSproutsFlutter/goods/carts/shopping_carts_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/commodity_rank_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';

class GoodsRecommendPage extends BasePage {
  final dynamic merchantId;
  final dynamic customerId;
  final dynamic selectedIndex;

  GoodsRecommendPage({
    this.merchantId,
    this.customerId,
    this.selectedIndex = 0,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return GoodsRecommendPageState();
  }
}

class GoodsRecommendPageState extends BaseState<GoodsRecommendPage>
    with SingleTickerProviderStateMixin {
  // Tab标题数组
  List<String> titles = ["购物车", "常购品", "搜索品", "热销品", "全部商品"];

  // Tab控制器
  late TabController _controller;

  ValueNotifier<int> selectedIndexNotifier = ValueNotifier(0);

  @override
  void initState() {
    this.initalTabController();
    super.initState();
  }

  // 初始化Tab 控制器
  void initalTabController() {
    int initialIndex = int.tryParse("${widget.selectedIndex}") ?? 0;

    this.selectedIndexNotifier.value = initialIndex;

    this._controller = TabController(
      length: this.titles.length,
      initialIndex: initialIndex,
      vsync: this,
    );
    this._controller.addListener(() {
      if (!this._controller.indexIsChanging) {
        this.selectedIndexNotifier.value = this._controller.index;
      }
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: TabBar(
              controller: this._controller,
              isScrollable: true,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: Color(0xFF00B377),
              indicatorWeight: 3,
              unselectedLabelColor: Color(0xFF676773),
              unselectedLabelStyle: TextStyle(fontSize: 14),
              labelColor: Color(0xFF292933),
              labelStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              tabs: tabs(),
            ),
          ),
          // Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 1),
          Expanded(
            child: TabBarView(
              controller: this._controller,
              physics: NeverScrollableScrollPhysics(),
              children: this.tabViews(),
            ),
          )
        ],
      ),
    );
  }

  // Tab Item
  List<Widget> tabs() {
    return this
        .titles
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e,
              ),
            ))
        .toList();
  }

  // TabView Item
  List<Widget> tabViews() {
    return [
      ShoppingCartsPage(
        merchantId: widget.merchantId,
        customerId: widget.customerId,
      ),
      CustomerOftenBuyPage(
        merchantId: widget.merchantId,
        customerId: widget.customerId,
      ),
      CustomerSearchWordPage(merchantId: widget.merchantId),
      CommodityRankPage(
        merchantId: widget.merchantId,
        customerId: widget.customerId,
        isSubPage: true,
      ),
      AllGoodsPage(customerId: widget.customerId),
    ];
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        ValueListenableBuilder<int>(
          valueListenable: this.selectedIndexNotifier,
          builder: (context, value, child) {
            return Visibility(
              visible: value == 4,
              child: TextButton(
                onPressed: this.searchAction,
                child: Image.asset(
                  'assets/images/customer/customer_tab_search.png',
                  width: 22,
                  height: 22,
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            );
          },
        )
      ],
    );
  }

  @override
  String getTitleName() {
    return "商品推荐";
  }

  void searchAction() {
    var router =
        "xyy://crm-app.ybm100.com/goods_search_history?customerId=${widget.customerId}&searchType=searchdetailGoodsManagerList&androidSearchType=7";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
}
