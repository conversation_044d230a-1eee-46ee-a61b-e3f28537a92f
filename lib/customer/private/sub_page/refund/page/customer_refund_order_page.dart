import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/refund/data/customer_refund_order_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_refund_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerRefundOrderPage extends BasePage {
  final String? merchantId;

  CustomerRefundOrderPage({this.merchantId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerRefundOrderPageState();
  }
}

class CustomerRefundOrderPageState extends BaseState<CustomerRefundOrderPage> {
  EasyRefreshController _refreshController = EasyRefreshController();

  List<OrderListItemData>? dataSource;

  PageState pageState = PageState.Empty;

  int page = 0;

  Map<String, dynamic> params = {};

  bool? isLastPage = true;

  String? smallImageHost;

  @override
  void onCreate() {
    super.onCreate();
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        smallImageHost = value["image_host"];
        setState(() {});
      }
    });
    params["merchantId"]=widget.merchantId;
    showLoadingDialog(msg: "加载中...");
    requestListData(true);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      child: buildListWidget(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  Widget buildListWidget() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: EasyRefresh(
        controller: _refreshController,
        onRefresh: () async {
          return await requestListData(true);
        },
        onLoad: isLastPage != false
            ? null
            : () async {
                return await requestListData(false);
              },
        child: ListView.builder(
          itemCount: this.dataSource?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            OrderListItemData? model = this.dataSource?[index];
            if (model == null) {
              return Container();
            }
            return GestureDetector(
              onTap: () {
                handleItemClick(model);
              },
              behavior: HitTestBehavior.opaque,
              child: OrderRefundListItemWidget(smallImageHost, model),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog(msg: "加载中...");
          requestListData(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  void handleItemClick(OrderListItemData itemData) {
    Navigator.of(context).pushNamed("/OrderDetailRefundPage",
        arguments: {"orderId": itemData.id, "merchantId": itemData.merchantId});
  }

  Future<void> requestListData(bool isRefresh) async {
    params["limit"] = 20;
    if (isRefresh) {
      params["offset"] = 0;
    } else {
      params["offset"] = page + 1;
    }
    var value =
        await NetworkV2<CustomerRefundOrderParentData>(CustomerRefundOrderParentData())
            .requestDataV2("refund/merchantRefundOrderList",
                parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      _refreshController.finishRefresh();
      setState(() {
        if (value.isSuccess == true) {
          if (!isRefresh) {
            page++;
          }else{
            page=0;
          }
          var result = value.getData()?.page;
          if (result != null) {
            isLastPage = result.isLastPage;
            if (isRefresh) {
              if (result.list?.isNotEmpty == true) {
                dataSource = result.list!;
                pageState = PageState.Normal;
              } else {
                dataSource = [];
                pageState = PageState.Empty;
              }
            } else {
              if (result.list?.isNotEmpty == true) {
                if (dataSource == null) {
                  dataSource = [];
                }
                dataSource?.addAll(result.list!);
                pageState = PageState.Normal;
              }
            }
          } else {
            pageState = PageState.Empty;
          }
        } else {
          pageState = PageState.Error;
        }
        _refreshController.finishRefresh();
        _refreshController.finishLoad();
      });
    }
  }

  @override
  String getTitleName() {
    return "退款单";
  }
}
