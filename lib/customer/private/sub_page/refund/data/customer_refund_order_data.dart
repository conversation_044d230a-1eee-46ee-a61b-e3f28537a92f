import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_refund_order_data.g.dart';

@JsonSerializable()
class CustomerRefundOrderData extends BaseModelV2<CustomerRefundOrderData> {

  List<OrderListItemData>? list;

  bool? isLastPage;

  CustomerRefundOrderData();

  Map<String, dynamic> toJson() {
    return _$CustomerRefundOrderDataToJson(this);
  }

  factory CustomerRefundOrderData.fromJson(Map<String, dynamic> json) =>
      _$CustomerRefundOrderDataFromJson(json);

  @override
  CustomerRefundOrderData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerRefundOrderDataFromJson(json);
  }
}



@JsonSerializable()
class CustomerRefundOrderParentData extends BaseModelV2<CustomerRefundOrderParentData> {

  CustomerRefundOrderData? page;


  CustomerRefundOrderParentData();

  Map<String, dynamic> toJson() {
    return _$CustomerRefundOrderParentDataToJson(this);
  }

  factory CustomerRefundOrderParentData.fromJson(Map<String, dynamic> json) =>
      _$CustomerRefundOrderParentDataFromJson(json);

  @override
  CustomerRefundOrderParentData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerRefundOrderParentDataFromJson(json);
  }
}