// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_refund_order_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerRefundOrderData _$CustomerRefundOrderDataFromJson(
    Map<String, dynamic> json) {
  return CustomerRefundOrderData()
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => OrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..isLastPage = json['isLastPage'] as bool?;
}

Map<String, dynamic> _$CustomerRefundOrderDataToJson(
        CustomerRefundOrderData instance) =>
    <String, dynamic>{
      'list': instance.list,
      'isLastPage': instance.isLastPage,
    };

CustomerRefundOrderParentData _$CustomerRefundOrderParentDataFromJson(
    Map<String, dynamic> json) {
  return CustomerRefundOrderParentData()
    ..page = json['page'] == null
        ? null
        : CustomerRefundOrderData.fromJson(
            json['page'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerRefundOrderParentDataToJson(
        CustomerRefundOrderParentData instance) =>
    <String, dynamic>{
      'page': instance.page,
    };
