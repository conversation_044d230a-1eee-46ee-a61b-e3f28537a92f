import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/data/customer_basic_info_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/data/customer_basic_info_group_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/widget/customer_basic_info_group_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:intl/intl.dart';

class CustomerBasicInfoPage extends BasePage {
  final String? customerId;
  final String? registerFlag;

  CustomerBasicInfoPage({this.customerId, this.registerFlag});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerBasicInfoPageState();
  }
}

class CustomerBasicInfoPageState extends BaseState<CustomerBasicInfoPage> {
  EasyRefreshController _controller = EasyRefreshController();

  PageState pageState = PageState.Empty;

  List<CustomerBasicInfoGroupData> groupDataList = [];

  CustomerBasicInfoData? customerBasicInfoData;

  @override
  void onCreate() {
    super.onCreate();
    requestBasicInfoData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff1f6f9),
      child: EasyRefresh(
        controller: _controller,
        onRefresh: () async {
          return await requestBasicInfoData();
        },
        onLoad: null,
        child: buildInfoListWidget(),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestBasicInfoData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(PageState.Empty);
      default:
        return null;
    }
  }

  bool isRegistered() {
    return widget.registerFlag?.toString() == "1";
  }

  @override
  String getTitleName() {
    return "客户信息";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    if (isRegistered()) {
      return null;
    } else {
      return super.getTitleBar(context);
    }
  }

  Widget buildInfoListWidget() {
    List<Widget> childWidgetList = [];
    childWidgetList.addAll(
        groupDataList.map((e) => CustomerBasicInfoGroupWidget(e)).toList());
    childWidgetList.add(SizedBox(
      height: 10,
    ));
    return SingleChildScrollView(
      child: Column(children: childWidgetList),
    );
  }

  Future<void> requestBasicInfoData() async {
    showLoadingDialog();
    var result = await NetworkV2<CustomerBasicInfoData>(CustomerBasicInfoData())
        .requestDataV2(
            isRegistered()
                ? "customer/private/basic"
                : "customerV2/noRegisterBasic",
            method: RequestMethod.GET,
            parameters: {
          isRegistered() ? "merchantId" : "customerId": widget.customerId
        });
    dismissLoadingDialog();
    if (mounted) {
      pageState = PageState.Error;
      if (result.isSuccess == true) {
        customerBasicInfoData = result.getData();
        if (customerBasicInfoData == null) {
          pageState = PageState.Empty;
        } else {
          pageState = PageState.Normal;
          groupDataList = buildGroupDataList(customerBasicInfoData!);
        }
      }
      setState(() {});
    }
  }

  List<CustomerBasicInfoGroupData> buildGroupDataList(
      CustomerBasicInfoData basicInfoData) {
    List<CustomerBasicInfoGroupData> result = [];
    if (isRegistered()) {
      result.add(buildMerchantGroupData(basicInfoData.merchant)); // 客户信息
      result.add(buildStoreGroupData(basicInfoData.poiInfoVo)); // 门店信息
      result.add(
          buildShippingAddressGroupData(basicInfoData.shippingAddress)); // 收货信息
      result.add(buildBDInfoGroupData(basicInfoData.bindSkuCollect,
          basicInfoData.merchant?.followPersons)); // 关联销售
      result.add(buildOperatingStateGroupData(basicInfoData.basicInfo)); // 经营状态
    } else {
      result.add(buildNoRegisterStoreGroupData(basicInfoData.poiInfoVo));
      result.add(buildBDInfoGroupData(basicInfoData.bindSkuCollect,
          basicInfoData.merchant?.followPersons)); // 关联销售
    }
    return result;
  }

  /// 客户信息
  CustomerBasicInfoGroupData buildMerchantGroupData(
      MerchantData? merchantData) {
    return CustomerBasicInfoGroupData(
        iconAssetPath:
            "assets/images/customer/customer_basic_info_merchant.png",
        title: "客户信息",
        itemInfoList: [
          ItemInfoData(
              title: "注册时间", content: merchantData?.getFormatCreateTime()),
          ItemInfoData(title: "责任人", content: merchantData?.nickname),
          ItemInfoData(
              title: "联系电话", content: merchantData?.mobile, isCanCopy: true),
          ItemInfoData(
              title: "客户注册类型", content: merchantData?.customerTypeName),
        ]);
  }

  /// 门店信息
  CustomerBasicInfoGroupData buildStoreGroupData(PoiInfo? poiInfo) {
    return CustomerBasicInfoGroupData(
        iconAssetPath: "assets/images/customer/customer_basic_info_store.png",
        title: "门店信息",
        itemInfoList: [
          ItemInfoData(title: "门店名称", content: poiInfo?.poiName?.toString()),
          ItemInfoData(title: "门店ID", content: poiInfo?.poiId?.toString()),
          ItemInfoData(
              title: "工商登记时间", content: poiInfo?.poiRegisterDate?.toString()),
          ItemInfoData(
              title: "门店状态",
              content: poiInfo?.poiStatusText?.toString(),
              contentColor: const Color(0xff00B377)),
          ItemInfoData(title: "连锁品牌", content: poiInfo?.chainBrand?.toString()),
          ItemInfoData(
              title: "连锁门店数", content: poiInfo?.chainCount?.toString()),
        ]);
  }

  /// 门店信息(未注册)
  CustomerBasicInfoGroupData buildNoRegisterStoreGroupData(PoiInfo? poiInfo) {
    return CustomerBasicInfoGroupData(
        iconAssetPath: "assets/images/customer/customer_basic_info_store.png",
        title: "门店信息",
        itemInfoList: [
          ItemInfoData(title: "门店名称", content: poiInfo?.poiName?.toString()),
          ItemInfoData(title: "门店ID", content: poiInfo?.poiId?.toString()),
          ItemInfoData(
              title: "工商登记时间", content: poiInfo?.poiRegisterDate?.toString()),
          ItemInfoData(
              title: "门店状态",
              content: poiInfo?.poiStatusText?.toString(),
              contentColor: const Color(0xff00B377)),
          ItemInfoData(
            title: "门店电话",
            content: poiInfo?.poiPhone?.toString(),
            moreActionWidget: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                // 打电话
                var poiPhone = poiInfo?.poiPhone?.toString();
                if (poiPhone != null && poiPhone.isNotEmpty) {
                  XYYContainer.bridgeCall("call_phone", parameters: {
                    "mobile": poiPhone,
                    "isOnlyCallPhone": true
                  });
                }
              },
              child: Container(
                padding: EdgeInsets.all(8),
                child: Image.asset(
                  "assets/images/customer/customer_basic_info_phone.png",
                  width: 13,
                  height: 13,
                ),
              ),
            ),
          ),
          ItemInfoData(
            title: "手机号码",
            content: poiInfo?.poiContactMobile?.toString(),
            moreActionWidget: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                // 打电话
                var poiContactMobile = poiInfo?.poiContactMobile?.toString();
                if (poiContactMobile != null && poiContactMobile.isNotEmpty) {
                  XYYContainer.bridgeCall("call_phone", parameters: {
                    "mobile": poiContactMobile,
                    "isOnlyCallPhone": true
                  });
                }
              },
              child: Container(
                padding: EdgeInsets.all(8),
                child: Image.asset(
                  "assets/images/customer/customer_basic_info_phone.png",
                  width: 13,
                  height: 13,
                ),
              ),
            ),
          ),
        ]);
  }

  /// 收货信息
  CustomerBasicInfoGroupData buildShippingAddressGroupData(
      ShippingAddress? shippingAddress) {
    return CustomerBasicInfoGroupData(
        iconAssetPath:
            "assets/images/customer/customer_basic_info_shipping_address.png",
        title: "收货信息",
        tipsWidget: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (shippingAddress?.tips != null) {
              showCommonAlert(
                context: context,
                title: shippingAddress?.tips?.toString() ?? "--",
                actions: [
                  CommonAlertAction(title: '确认'),
                ],
              );
            }
          },
          child: Container(
            padding: EdgeInsets.all(5),
            child: Image.asset(
              "assets/images/base/select_object_tips_icon.png",
              width: 12,
              height: 12,
            ),
          ),
        ),
        itemInfoList: [
          ItemInfoData(title: "收货人", content: shippingAddress?.contactor),
          ItemInfoData(title: "联系电话", content: shippingAddress?.mobile),
          ItemInfoData(title: "收货地址", content: shippingAddress?.fullAddress),
        ]);
  }

  /// 关联销售
  CustomerBasicInfoGroupData buildBDInfoGroupData(
      List<CustomerSkuCollectData>? skuCollectData,
      List<PersonInfo>? followPersons) {
    List<ItemInfoData> itemInfoList = [];
    // 商品集
    skuCollectData?.forEach((element) {
      itemInfoList.add(ItemInfoData(
          title: element.skuCollectName, content: element.oaUserName));
    });
    // 跟进人
    if (followPersons != null && followPersons.isNotEmpty) {
      var followNameStr =
          followPersons.map((e) => e.realName?.toString() ?? "-").join(",");
      itemInfoList.add(ItemInfoData(title: "跟进人", content: followNameStr));
    }
    return CustomerBasicInfoGroupData(
        iconAssetPath: "assets/images/customer/customer_basic_info_bd_info.png",
        title: "关联销售",
        moreActionWidget: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            // 认领记录
            XYYContainer.open(
                "/customer_claim_record_page?merchantId=${customerBasicInfoData?.merchant?.id}&customerId=${widget.customerId}");
          },
          child: Container(
              padding: EdgeInsets.symmetric(vertical: 5, horizontal: 7),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(23),
                  border:
                      Border.all(color: const Color(0xff00b377), width: 0.5)),
              child: Text(
                "认领记录",
                style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xff00b377),
                    fontWeight: FontWeight.normal),
              )),
        ),
        itemInfoList: itemInfoList);
  }

  /// 经营状态
  CustomerBasicInfoGroupData buildOperatingStateGroupData(
      BasicInfo? basicInfo) {
    return CustomerBasicInfoGroupData(
        iconAssetPath:
            "assets/images/customer/customer_basic_info_operating_state.png",
        title: "经营状态",
        itemInfoList: [
          ItemInfoData(
              title: "是否可用医保",
              content:
                  basicInfo?.medicalInsurance?.toString() == "1" ? "是" : "否"),
          ItemInfoData(
              title: "客户面积(平方米)",
              content: NumberFormat("0.00").format(basicInfo?.areaSize ?? 0)),
          ItemInfoData(title: "客户成员数(人)", content: basicInfo?.clerkNum),
          ItemInfoData(title: "周边环境", content: basicInfo?.aroundEnvName),
          ItemInfoData(title: "主要消费人群", content: basicInfo?.buyersTypeName),
          ItemInfoData(
              title: "客户流量(人/天)", content: basicInfo?.buyersAmountText),
          ItemInfoData(title: "客户需求SKU数", content: basicInfo?.buySkusName),
          ItemInfoData(
              title: "主要消费结构", content: basicInfo?.mainlyConsumeMedTypesName),
          ItemInfoData(
              title: "是否需要动销",
              content: basicInfo?.needPullSales?.toString() == "1" ? "是" : "否"),
          ItemInfoData(
              title: "是否需要门店诊断",
              content: basicInfo?.needMerchantDiagnose?.toString() == "1"
                  ? "是"
                  : "否"),
          ItemInfoData(
              title: "是否需要店员培训",
              content:
                  basicInfo?.needClerkTrains?.toString() == "1" ? "是" : "否"),
          ItemInfoData(
              title: "月销售额(元)",
              content:
                  NumberFormat("0.00").format(basicInfo?.monthlySales ?? 0)),
          ItemInfoData(title: "缺失品种", content: basicInfo?.shortOfTypes),
          ItemInfoData(title: "核心供应商", content: basicInfo?.purchaseWay),
          ItemInfoData(title: "客户需求", content: basicInfo?.merchantDemand),
        ]);
  }
}
