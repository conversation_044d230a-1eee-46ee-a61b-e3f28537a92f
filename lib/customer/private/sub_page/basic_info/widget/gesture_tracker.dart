import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FlutterGestureTracker extends StatelessWidget {
  FlutterGestureTracker({Key? key, required this.child}) : super(key: key);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return RawGestureDetector(
      behavior: HitTestBehavior.translucent,
      gestures: {
        _PointerTracker: GestureRecognizerFactoryWithHandlers<_PointerTracker>(
          () => _PointerTracker(), //constructor
          (_PointerTracker instance) {
            //initializer
          },
        )
      },
      child: child,
    );
  }
}

class _PointerTracker extends PanGestureRecognizer {
  bool _flutterGestureIsWorking = false;

  @override
  void rejectGesture(int pointer) {
    super.rejectGesture(pointer);
    _flutterGestureIsWorking = true;
    _notify();
  }

  @override
  void acceptGesture(int pointer) {
    super.acceptGesture(pointer);
    _flutterGestureIsWorking = false;
    _notify();
  }

  void _notify() {
    GestureConflict.flutterGestureStateChanged(_flutterGestureIsWorking);
  }
}

class GestureConflict {
  /// 通信
  static late MethodChannel? _methodChannel;

  GestureConflict() {
    _methodChannel = const MethodChannel("flutterGestureStateChanged");
  }

  static flutterGestureStateChanged(bool flutterGestureIsWorking) {
    if (_methodChannel == null) {
      GestureConflict();
    }
    _methodChannel?.invokeMapMethod(
        "flutterGestureStateChanged", {"isWorking": flutterGestureIsWorking});
  }
}
