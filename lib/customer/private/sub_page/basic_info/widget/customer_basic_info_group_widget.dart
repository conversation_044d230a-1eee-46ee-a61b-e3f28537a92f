import 'package:XyyBeanSproutsFlutter/common/copy_widget/copy_action_sheet.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/data/customer_basic_info_group_data.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class CustomerBasicInfoGroupWidget extends StatelessWidget {
  final CustomerBasicInfoGroupData groupData;

  CustomerBasicInfoGroupWidget(this.groupData);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10, top: 10),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8), color: Colors.white),
      padding: EdgeInsets.only(left: 10, bottom: 15, right: 10),
      child: Column(
        children: [
          buildHeaderWidget(),
          Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                border: Border.all(width: 0.5, color: const Color(0xffeeeff0))),
            child: Column(
              children: buildInfoListWidget(context),
            ),
          )
        ],
      ),
    );
  }

  Widget buildHeaderWidget() {
    return Container(
      alignment: Alignment.center,
      height: 46,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            groupData.iconAssetPath,
            width: 17,
            height: 17,
          ),
          SizedBox(
            width: 5,
          ),
          Text(
            groupData.title,
            style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xff333333)),
          ),
          groupData.tipsWidget ?? SizedBox(),
          Expanded(child: Container()),
          groupData.moreActionWidget ?? SizedBox(),
        ],
      ),
    );
  }

  List<Widget> buildInfoListWidget(BuildContext context) {
    List<Widget> result = [];
    groupData.itemInfoList?.forEachIndexed((index, element) {
      if (index != 0) {
        result.add(Divider(
          height: 0.5,
          color: const Color(0xffeeeff0),
        ));
      }
      result.add(buildInfoItemWidget(context, element));
    });
    return result;
  }

  Widget buildInfoItemWidget(BuildContext context, ItemInfoData itemInfoData) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 7, vertical: 5),
          width: 90,
          alignment: Alignment.center,
          constraints: BoxConstraints(minHeight: 40),
          color: const Color(0xfff1f6f9),
          child: Text(
            itemInfoData.title ?? "--",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: const Color(0xff676773),
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              if (itemInfoData.isCanCopy) {
                showCopyActionSheet(context,
                    message: itemInfoData.content?.toString() ?? "--");
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 15),
              alignment: Alignment.centerLeft,
              child: Text(
                itemInfoData.content?.toString() ?? "--",
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color:
                        itemInfoData.contentColor ?? const Color(0xff292933)),
              ),
            ),
          ),
        ),
        itemInfoData.moreActionWidget ?? SizedBox()
      ],
    );
  }
}
