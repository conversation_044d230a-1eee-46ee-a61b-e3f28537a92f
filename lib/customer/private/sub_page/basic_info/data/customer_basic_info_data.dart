import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/basic_info/data/customer_basic_info_group_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:date_format/date_format.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_basic_info_data.g.dart';

@JsonSerializable()
class CustomerBasicInfoData extends BaseModelV2<CustomerBasicInfoData> {
  MerchantData? merchant;
  BasicInfo? basicInfo;
  List<YBMCustomerContactModel>? contactList;
  ShippingAddress? shippingAddress;
  List<CustomerSkuCollectData>? bindSkuCollect;
  PoiInfo? poiInfoVo;
  List<FollowPersonInfo>? followPersions;

  CustomerBasicInfoData();

  factory CustomerBasicInfoData.fromJson(Map<String, dynamic> json) =>
      _$CustomerBasicInfoDataFromJson(json);

  @override
  CustomerBasicInfoData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerBasicInfoDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerBasicInfoDataToJson(this);
  }
}

@JsonSerializable()
class MerchantData extends BaseModelV2<MerchantData> {
  dynamic realName;
  dynamic mobile;
  dynamic nickname;
  dynamic createTime;
  dynamic customerTypeName;
  dynamic id; // merchantId
  List<PersonInfo>? followPersons;

  MerchantData();

  String getFormatCreateTime() {
    return formatDate(
      DateTime.fromMillisecondsSinceEpoch(createTime),
      [yyyy, '.', mm, '.', dd, ' ', HH, ':', nn, ':', ss],
    );
  }

  factory MerchantData.fromJson(Map<String, dynamic> json) =>
      _$MerchantDataFromJson(json);

  @override
  MerchantData fromJsonMap(Map<String, dynamic> json) {
    return _$MerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MerchantDataToJson(this);
  }
}

@JsonSerializable()
class BasicInfo extends BaseModelV2<BasicInfo> {
  dynamic areaSize;
  dynamic purchaseWay;
  dynamic clerkNum;
  dynamic merchantDemand;
  dynamic monthlySales;
  dynamic medicalInsurance;
  dynamic needClerkTrains;
  dynamic needMerchantDiagnose;
  dynamic needPullSales;
  dynamic shortOfTypes;
  dynamic aroundEnvName;
  dynamic buySkusName;
  dynamic mainlyConsumeMedTypesName;
  dynamic buyersAmountText;
  dynamic buyersTypeName;

  BasicInfo();

  factory BasicInfo.fromJson(Map<String, dynamic> json) =>
      _$BasicInfoFromJson(json);

  @override
  BasicInfo fromJsonMap(Map<String, dynamic> json) {
    return _$BasicInfoFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$BasicInfoToJson(this);
  }
}

@JsonSerializable()
class ShippingAddress extends BaseModelV2<ShippingAddress> {
  dynamic fullAddress;
  dynamic contactor;
  dynamic mobile;
  dynamic tips;

  ShippingAddress();

  factory ShippingAddress.fromJson(Map<String, dynamic> json) =>
      _$ShippingAddressFromJson(json);

  @override
  ShippingAddress fromJsonMap(Map<String, dynamic> json) {
    return _$ShippingAddressFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ShippingAddressToJson(this);
  }
}

@JsonSerializable()
class PersonInfo extends BaseModelV2<PersonInfo> {
  dynamic realName;

  PersonInfo();

  factory PersonInfo.fromJson(Map<String, dynamic> json) =>
      _$PersonInfoFromJson(json);

  @override
  PersonInfo fromJsonMap(Map<String, dynamic> json) {
    return _$PersonInfoFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PersonInfoToJson(this);
  }
}

@JsonSerializable()
class FollowPersonInfo extends BaseModelV2<FollowPersonInfo> {
  dynamic oaName;

  FollowPersonInfo();

  factory FollowPersonInfo.fromJson(Map<String, dynamic> json) =>
      _$FollowPersonInfoFromJson(json);

  @override
  FollowPersonInfo fromJsonMap(Map<String, dynamic> json) {
    return _$FollowPersonInfoFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FollowPersonInfoToJson(this);
  }
}

@JsonSerializable()
class PoiInfo extends BaseModelV2<PoiInfo> {
  dynamic poiName;
  dynamic poiId;
  dynamic poiRegisterDate;
  dynamic chainBrand;
  dynamic chainCount;
  dynamic poiStatus;
  dynamic poiStatusText;
  dynamic poiPhone;
  dynamic poiContactMobile;

  PoiInfo();

  factory PoiInfo.fromJson(Map<String, dynamic> json) =>
      _$PoiInfoFromJson(json);

  @override
  PoiInfo fromJsonMap(Map<String, dynamic> json) {
    return _$PoiInfoFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PoiInfoToJson(this);
  }
}
