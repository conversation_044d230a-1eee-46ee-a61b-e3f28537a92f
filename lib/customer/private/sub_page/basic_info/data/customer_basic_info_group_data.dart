import 'package:flutter/cupertino.dart';

/// 客户信息模块数据
class CustomerBasicInfoGroupData {
  final String iconAssetPath;
  final String title;
  final Widget? tipsWidget;
  final Widget? moreActionWidget;

  final List<ItemInfoData>? itemInfoList;

  CustomerBasicInfoGroupData(
      {required this.iconAssetPath,
      required this.title,
      this.tipsWidget,
      this.moreActionWidget,
      this.itemInfoList});
}

class ItemInfoData {
  final String? title;
  final dynamic content;
  final Color? contentColor;
  final Widget? moreActionWidget;
  final bool isCanCopy;

  ItemInfoData({
    this.title,
    this.content,
    this.contentColor,
    this.moreActionWidget,
    this.isCanCopy = false,
  });
}
