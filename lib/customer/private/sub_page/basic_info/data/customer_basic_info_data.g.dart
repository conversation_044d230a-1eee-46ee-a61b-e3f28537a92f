// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_basic_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerBasicInfoData _$CustomerBasicInfoDataFromJson(
    Map<String, dynamic> json) {
  return CustomerBasicInfoData()
    ..merchant = json['merchant'] == null
        ? null
        : MerchantData.fromJson(json['merchant'] as Map<String, dynamic>)
    ..basicInfo = json['basicInfo'] == null
        ? null
        : BasicInfo.fromJson(json['basicInfo'] as Map<String, dynamic>)
    ..contactList = (json['contactList'] as List<dynamic>?)
        ?.map(
            (e) => YBMCustomerContactModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..shippingAddress = json['shippingAddress'] == null
        ? null
        : ShippingAddress.fromJson(
            json['shippingAddress'] as Map<String, dynamic>)
    ..bindSkuCollect = (json['bindSkuCollect'] as List<dynamic>?)
        ?.map(
            (e) => CustomerSkuCollectData.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..poiInfoVo = json['poiInfoVo'] == null
        ? null
        : PoiInfo.fromJson(json['poiInfoVo'] as Map<String, dynamic>)
    ..followPersions = (json['followPersions'] as List<dynamic>?)
        ?.map((e) => FollowPersonInfo.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CustomerBasicInfoDataToJson(
        CustomerBasicInfoData instance) =>
    <String, dynamic>{
      'merchant': instance.merchant,
      'basicInfo': instance.basicInfo,
      'contactList': instance.contactList,
      'shippingAddress': instance.shippingAddress,
      'bindSkuCollect': instance.bindSkuCollect,
      'poiInfoVo': instance.poiInfoVo,
      'followPersions': instance.followPersions,
    };

MerchantData _$MerchantDataFromJson(Map<String, dynamic> json) {
  return MerchantData()
    ..realName = json['realName']
    ..mobile = json['mobile']
    ..nickname = json['nickname']
    ..createTime = json['createTime']
    ..customerTypeName = json['customerTypeName']
    ..id = json['id']
    ..followPersons = (json['followPersons'] as List<dynamic>?)
        ?.map((e) => PersonInfo.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$MerchantDataToJson(MerchantData instance) =>
    <String, dynamic>{
      'realName': instance.realName,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'createTime': instance.createTime,
      'customerTypeName': instance.customerTypeName,
      'id': instance.id,
      'followPersons': instance.followPersons,
    };

BasicInfo _$BasicInfoFromJson(Map<String, dynamic> json) {
  return BasicInfo()
    ..areaSize = json['areaSize']
    ..purchaseWay = json['purchaseWay']
    ..clerkNum = json['clerkNum']
    ..merchantDemand = json['merchantDemand']
    ..monthlySales = json['monthlySales']
    ..medicalInsurance = json['medicalInsurance']
    ..needClerkTrains = json['needClerkTrains']
    ..needMerchantDiagnose = json['needMerchantDiagnose']
    ..needPullSales = json['needPullSales']
    ..shortOfTypes = json['shortOfTypes']
    ..aroundEnvName = json['aroundEnvName']
    ..buySkusName = json['buySkusName']
    ..mainlyConsumeMedTypesName = json['mainlyConsumeMedTypesName']
    ..buyersAmountText = json['buyersAmountText']
    ..buyersTypeName = json['buyersTypeName'];
}

Map<String, dynamic> _$BasicInfoToJson(BasicInfo instance) => <String, dynamic>{
      'areaSize': instance.areaSize,
      'purchaseWay': instance.purchaseWay,
      'clerkNum': instance.clerkNum,
      'merchantDemand': instance.merchantDemand,
      'monthlySales': instance.monthlySales,
      'medicalInsurance': instance.medicalInsurance,
      'needClerkTrains': instance.needClerkTrains,
      'needMerchantDiagnose': instance.needMerchantDiagnose,
      'needPullSales': instance.needPullSales,
      'shortOfTypes': instance.shortOfTypes,
      'aroundEnvName': instance.aroundEnvName,
      'buySkusName': instance.buySkusName,
      'mainlyConsumeMedTypesName': instance.mainlyConsumeMedTypesName,
      'buyersAmountText': instance.buyersAmountText,
      'buyersTypeName': instance.buyersTypeName,
    };

ShippingAddress _$ShippingAddressFromJson(Map<String, dynamic> json) {
  return ShippingAddress()
    ..fullAddress = json['fullAddress']
    ..contactor = json['contactor']
    ..mobile = json['mobile']
    ..tips = json['tips'];
}

Map<String, dynamic> _$ShippingAddressToJson(ShippingAddress instance) =>
    <String, dynamic>{
      'fullAddress': instance.fullAddress,
      'contactor': instance.contactor,
      'mobile': instance.mobile,
      'tips': instance.tips,
    };

PersonInfo _$PersonInfoFromJson(Map<String, dynamic> json) {
  return PersonInfo()..realName = json['realName'];
}

Map<String, dynamic> _$PersonInfoToJson(PersonInfo instance) =>
    <String, dynamic>{
      'realName': instance.realName,
    };

FollowPersonInfo _$FollowPersonInfoFromJson(Map<String, dynamic> json) {
  return FollowPersonInfo()..oaName = json['oaName'];
}

Map<String, dynamic> _$FollowPersonInfoToJson(FollowPersonInfo instance) =>
    <String, dynamic>{
      'oaName': instance.oaName,
    };

PoiInfo _$PoiInfoFromJson(Map<String, dynamic> json) {
  return PoiInfo()
    ..poiName = json['poiName']
    ..poiId = json['poiId']
    ..poiRegisterDate = json['poiRegisterDate']
    ..chainBrand = json['chainBrand']
    ..chainCount = json['chainCount']
    ..poiStatus = json['poiStatus']
    ..poiStatusText = json['poiStatusText']
    ..poiPhone = json['poiPhone']
    ..poiContactMobile = json['poiContactMobile'];
}

Map<String, dynamic> _$PoiInfoToJson(PoiInfo instance) => <String, dynamic>{
      'poiName': instance.poiName,
      'poiId': instance.poiId,
      'poiRegisterDate': instance.poiRegisterDate,
      'chainBrand': instance.chainBrand,
      'chainCount': instance.chainCount,
      'poiStatus': instance.poiStatus,
      'poiStatusText': instance.poiStatusText,
      'poiPhone': instance.poiPhone,
      'poiContactMobile': instance.poiContactMobile,
    };
