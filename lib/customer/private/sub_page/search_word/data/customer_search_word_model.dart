import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_search_word_model.g.dart';

@JsonSerializable()
class CustomerSearchWordModel extends BaseModelV2 {
  /// 标题
  dynamic title;

  /// 关键词
  List<dynamic>? words;

  CustomerSearchWordModel();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerSearchWordModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerSearchWordModelToJson(this);
  }
}
