import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/data/customer_search_word_model.dart';
import 'package:flutter/material.dart';

class CustomerSearchWordItem extends StatelessWidget {
  final bool isTop;
  final bool isBottom;

  final CustomerSearchWordModel model;

  CustomerSearchWordItem({
    this.isTop = false,
    this.isBottom = false,
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: this.baseRadius(),
      ),
      child: CustomPaint(
        painter: _KeywordBasePainter(
          isTop: this.isTop,
          isBottom: this.isBottom,
        ),
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(color: Color(0xFF00B377), width: 2),
                    ),
                    height: 9,
                    width: 9,
                  ),
                  SizedBox(width: 15),
                  Text("${model.title}",
                      style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 15,
                          fontWeight: FontWeight.w500))
                ],
              ),
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.only(left: 24),
                child: LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                    return Wrap(
                      runSpacing: 12,
                      spacing: 12,
                      children: this.getItems(constraints),
                    );
                  },
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> getItems(BoxConstraints constraints) {
    if (model.words != null) {
      return model.words!
          .map(
            (e) => Container(
              padding: EdgeInsets.only(left: 7, right: 7,top: 5,bottom: 5),
              decoration: BoxDecoration(
                color: Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(2),
              ),
              child: Text(
                "$e",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(color: Color(0xFF666666), fontSize: 14,fontWeight: FontWeight.normal),
              ),
            ),
          )
          .toList();
    }
    return [];
  }

  BorderRadiusGeometry? baseRadius() {
    if (this.isTop) {
      return BorderRadius.vertical(top: Radius.circular(8));
    }
    if (this.isBottom) {
      return BorderRadius.vertical(bottom: Radius.circular(8));
    }
    return null;
  }
}

class _KeywordBasePainter extends CustomPainter {
  final bool isTop;
  final bool isBottom;

  _KeywordBasePainter({this.isTop = false, this.isBottom = false});

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = Color(0xFFF0F0F0);
    paint.strokeWidth = 1;

    canvas.drawLine(
      Offset(19, this.isTop ? 24 : 0),
      Offset(19, this.isBottom ? 24 : size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
