import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/data/customer_search_word_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/search_word/widget/customer_search_word_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerSearchWordPage extends BasePage {
  final dynamic merchantId;

  CustomerSearchWordPage({required this.merchantId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerSearchWordPageState();
  }
}

class CustomerSearchWordPageState extends BaseState<CustomerSearchWordPage> {
  List<CustomerSearchWordModel> dataSource = [];

  @override
  bool needKeepAlive() => true;

  @override
  bool isSubPage() {
    return true;
  }

  @override
  void initState() {
    this.requestList();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.all(10),
      child: EasyRefresh(
        onRefresh: requestList,
        emptyWidget: getEmptyWidget(),
        child: ListView.builder(
          itemCount: dataSource.length,
          cacheExtent: 999,
          itemBuilder: (context, index) {
            return CustomerSearchWordItem(
              isTop: index == 0,
              isBottom: index == dataSource.length - 1,
              model: this.dataSource[index],
            );
          },
        ),
      ),
    );
  }

  Future<void> requestList() async {
    Map<String, dynamic> param = {
      'merchantId': widget.merchantId,
    };
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<CustomerSearchWordModel>(CustomerSearchWordModel())
            .requestDataV2(
      'customerV2/historyWords',
      parameters: param,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getListData();
        this.dataSource = data ?? [];
        setState(() {});
      }
    }
    return;
  }

  /// 空页面
  Widget? getEmptyWidget() {
    return this.dataSource.length > 0
        ? null
        : PageStateWidget.pageEmpty(PageState.Empty);
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "搜索品";
  }
}
