import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/data/customer_often_buy_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/widget/customer_often_buy_filter.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/widget/customer_often_buy_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerOftenBuyPage extends BasePage {
  final dynamic merchantId;
  final dynamic customerId;

  CustomerOftenBuyPage({this.merchantId,this.customerId});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerOftenBuyPageState();
  }
}

class CustomerOftenBuyPageState extends BaseState<CustomerOftenBuyPage> {
  List<CustomerOftenBuyModel> dataSource = [];

  EasyRefreshController refreshController = EasyRefreshController();

  Map<String, dynamic> fliterParams = {
    "timeType": "1",
  };

  @override
  void initState() {
    this.requestList();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        children: [
          CustomerOftenBuyFilter(
            changeFilter: (params) {
              this.fliterParams = params;
              this.refreshController.callRefresh();
            },
          ),
          Expanded(
            child: EasyRefresh(
              onRefresh: requestList,
              emptyWidget: getEmptyWidget(),
              controller: refreshController,
              child: ListView.builder(
                itemCount: dataSource.length,
                cacheExtent: 999,
                itemBuilder: (context, index) {
                  CustomerOftenBuyModel model = dataSource[index];
                  return GestureDetector(
                    onTap: () {
                      this.jumpToDetailListPage(model);
                    },
                    child: CustomerOftenBuyItem(
                      model: model,
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void jumpToDetailListPage(CustomerOftenBuyModel model) {
    var router = '/commodity_detail_list?';
    if (widget.merchantId != null) {
      router += "merchantId=${widget.merchantId}";
    }
    if (widget.customerId != null) {
      router += "&customerId=${widget.customerId}";
    }
    router += "&searchSkuId=${model.searchSkuId}";
    router += "&searchType=${model.searchType}";
    router += "&goodsName=${model.name}";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  Future<void> requestList() async {
    Map<String, dynamic> param = {
      'merchantId': widget.merchantId,
    };
    param.addAll(this.fliterParams);
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    var result = await NetworkV2<CustomerOftenBuyModel>(CustomerOftenBuyModel())
        .requestDataV2(
      'merchant/oftenBuy/list',
      parameters: param,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getListData();
        this.dataSource = data ?? [];
        setState(() {});
      }
    }
    return;
  }

  /// 空页面
  Widget? getEmptyWidget() {
    return this.dataSource.length > 0
        ? null
        : PageStateWidget.pageEmpty(PageState.Empty);
  }

  @override
  bool needKeepAlive() => true;

  @override
  bool isSubPage() => true;

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) => null;

  @override
  String getTitleName() {
    return "常购品";
  }
}
