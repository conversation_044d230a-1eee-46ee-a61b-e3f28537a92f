import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/often_buy/data/customer_often_buy_model.dart';
import 'package:flutter/material.dart';

class CustomerOftenBuyItem extends StatelessWidget {
  final CustomerOftenBuyModel model;

  CustomerOftenBuyItem({required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          this.goodsImageWidget(),
          SizedBox(width: 10),
          Expanded(child: this.goodsContentWidget()),
        ],
      ),
    );
  }

  /// 商品图片
  Widget goodsImageWidget() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFF9F9F9),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: ImageCatchWidget(
          url: "${model.productImg}",
          w: 100,
          h: 100,
        ),
      ),
    );
  }

  /// 商品信息部分
  Widget goodsContentWidget() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(top: 5),
            child: Text(
              "${model.name}",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292933),
              ),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '${model.spec}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 11,
                color: const Color(0xFF676773),
                fontWeight: FontWeight.normal),
          ),
          SizedBox(height: 6),
          this.getBuyCountNumber(),
          SizedBox(height: 6),
          this.getBuyRecordWidget()
        ],
      ),
    );
  }

  /// 购买数量
  Widget getBuyCountNumber() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Text(
          "累计购买${model.totalBuyNum}${model.unit}",
          style: TextStyle(
              color: Color(0xFFFF2121),
              fontSize: 12,
              fontWeight: FontWeight.w500),
        ),
        Spacer(),
        Text(
          "查看在售商品",
          style: TextStyle(color: Color(0xFF949498), fontSize: 12),
        ),
        Image.asset(
          'assets/images/commodity/commodity_rank_arrow.png',
          width: 12,
          height: 12,
        )
      ],
    );
  }

  /// 购买记录
  Widget getBuyRecordWidget() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFAFBFC),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: EdgeInsets.fromLTRB(5, 4, 5, 5),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          RichText(
            text: TextSpan(
              text: "上次购买记录：",
              style: TextStyle(color: Color(0xFF9494A6), fontSize: 11),
              children: [
                TextSpan(
                  text: "${model.latestBuyDate}",
                  style: TextStyle(color: Color(0xFF00B377), fontSize: 11),
                ),
                WidgetSpan(child: SizedBox(width: 2)),
                TextSpan(
                  text: "购买",
                  style: TextStyle(color: Color(0xFF292933), fontSize: 11),
                ),
                TextSpan(
                  text: "${model.latestBuyNum}${model.unit}",
                  style: TextStyle(color: Color(0xFF00B377), fontSize: 11),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
