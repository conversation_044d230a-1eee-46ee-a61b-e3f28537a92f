import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/popover/common_popover.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class CustomerOftenBuyFilter extends StatefulWidget {
  final ValueChanged<Map<String, dynamic>> changeFilter;

  CustomerOftenBuyFilter({required this.changeFilter});
  @override
  State<StatefulWidget> createState() {
    return CustomerOftenBuyFilterState();
  }
}

class CustomerOftenBuyFilterState extends State<CustomerOftenBuyFilter> {
  GlobalKey dropKey = GlobalKey();

  final List<_OftenBuyButtonOptionModel> filterList = [
    _OftenBuyButtonOptionModel(title: "全部", option: -1),
    _OftenBuyButtonOptionModel(title: "优选品", option: 1),
    _OftenBuyButtonOptionModel(title: "控销品", option: 2),
    _OftenBuyButtonOptionModel(title: "甄选品", option: 3)
  ];

  ValueNotifier<BackgroundButtonState> curController =
      ValueNotifier<BackgroundButtonState>(BackgroundButtonState.selected);

  DropButtonController controller =
      DropButtonController(model: DropButtonModel(normalText: "近一年"));

  Map<String, dynamic> params = {
    "timeType": "1",
  };

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      color: Color(0xFFFFFFFF),
      child: Row(
        children: this.getFilterItems(),
      ),
    );
  }

  List<Widget> getFilterItems() {
    List<Widget> child = [];
    child.addAll(filterList
        .mapIndexed(
          (index, element) => Container(
            padding: EdgeInsets.fromLTRB(10, 10, 0, 10),
            child: BackgroundStateButton(
              onPressed: selectItem,
              title: element.title,
              option: element.option,
              controller: index == 0 ? this.curController : null,
              alignment: Alignment.center,
              panding: EdgeInsets.only(left: 15, right: 15),
              selectStyle: TextStyle(
                color: Color(0xFF00B377),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textStyle: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
              ),
              normalDecoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: Color(0xFFF7F7F8),
              ),
              selectDecoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Color(0xFF00B377), width: 0.5),
                color: Color(0xFFFFFFFF),
              ),
            ),
          ),
        )
        .toList());
    child.add(Spacer());
    child.add(DropControllerButton(
      key: dropKey,
      title: "近一年",
      controller: this.controller,
      normalImage: Image.asset(
        "assets/images/customer/often_buy_down.png",
        width: 7,
        height: 5,
      ),
      selectImage: Image.asset(
        "assets/images/customer/often_buy_down.png",
        width: 7,
        height: 5,
      ),
      normalStyle: TextStyle(color: Color(0xFF676773), fontSize: 14),
      selectedStyle: TextStyle(
          color: Color(0xFF00B377), fontSize: 14, fontWeight: FontWeight.w500),
      onPressed: selectTimerFilter,
    ));
    child.add(SizedBox(width: 15));
    return child;
  }

  void selectItem(
      ValueNotifier<BackgroundButtonState> controller, dynamic option) {
    if (controller == curController) {
      return;
    }
    curController.value = BackgroundButtonState.normal;
    curController = controller;
    if (option == -1) {
      this.params.remove("queryType");
    } else {
      this.params["queryType"] = option;
    }

    widget.changeFilter(this.params);
  }

  void selectTimerFilter(DropButtonController controller) async {
    String? timeType = await showRightTipsPopover<String>(
      context: context,
      anchorKey: dropKey,
      backgroundColor: Color(0xFF292933),
      contentRadus: BorderRadius.circular(6),
      content: Container(
        height: 70,
        width: 90,
        child: _OftenBuyTimeFilterContent(),
      ),
    );
    controller.setIsOpen(false);
    if (timeType != null) {
      if (timeType == "1") {
        controller.setSelectText(null);
      } else {
        controller.setSelectText("去年同期");
      }
      this.params["timeType"] = timeType;
      widget.changeFilter(this.params);
    }
  }
}

class _OftenBuyTimeFilterContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop("1");
                },
                behavior: HitTestBehavior.opaque,
                child: Text(
                  "近一年",
                  style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13),
                ),
              ),
            ),
          ),
          Divider(color: Color(0x33FFFFFF), height: 1, thickness: 0.5),
          Expanded(
            child: Container(
              alignment: Alignment.centerLeft,
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).pop("2");
                },
                behavior: HitTestBehavior.opaque,
                child: Text(
                  "去年同期",
                  style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _OftenBuyButtonOptionModel {
  final String title;
  final dynamic option;
  _OftenBuyButtonOptionModel({required this.title, this.option});
}
