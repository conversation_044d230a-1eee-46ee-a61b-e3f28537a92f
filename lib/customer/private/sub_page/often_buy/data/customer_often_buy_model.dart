import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_often_buy_model.g.dart';

@JsonSerializable()
class CustomerOftenBuyModel extends BaseModelV2 {
  dynamic skuId;

  dynamic spec;

  dynamic name;

  dynamic searchSkuId;

  //  1：标准库ID 2：pid
  dynamic searchType;

  dynamic productImg;

  dynamic totalBuyNum;

  dynamic latestBuyNum;

  /// 单位
  dynamic unit;

  dynamic latestBuyDate;

  CustomerOftenBuyModel();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerOftenBuyModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerOftenBuyModelToJson(this);
  }
}
