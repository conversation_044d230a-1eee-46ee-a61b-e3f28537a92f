import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_detail_more_popover.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/widget/customer_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CustomerPrivateDetailPage extends BasePage {
  final String? customerId; // 原merchantId
  final String? distributable;

  CustomerPrivateDetailPage(
      {@required this.customerId, @required this.distributable});

  @override
  BaseState<StatefulWidget> initState() {
    return CustomerPrivateDetailPageState();
  }
}

class CustomerPrivateDetailPageState
    extends BaseState<CustomerPrivateDetailPage> {
  String pdcHost = "";

  /// 数据
  CustomerDetailData sourceData = CustomerDetailData();

  /// 菜单Source
  List<CustomerDetailItemData> menuSource = [];

  /// 刷新控制器
  EasyRefreshController _controller = EasyRefreshController();

  /// 用来保存NavigationBar右侧按钮
  GlobalKey _anchorKey = GlobalKey();

  @override
  void initState() {
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          pdcHost = value['h5_host'];
        });
      }
    });
    this.requestDetailData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    this.menuSource = this.generateMenusSource();
    return Column(
      children: [
        Expanded(
          child: EasyRefresh(
            controller: _controller,
            onRefresh: () async {
              this.requestDetailData();
            },
            child: ListView(
              children: [
                // 头部视图
                CustomerDetailHeaderView(
                  model: this.sourceData,
                  merchantNameOnTap: this.showCopyActionSheet,
                  addressOnTap: this.jumpAddressPage,
                  statusOnTap: this.jumpStatusPage,
                  isFromPrivate: true,
                  callPhoneOnTap: this.callPhoneOnTap,
                ),
                // 列表
                GridView.builder(
                  shrinkWrap: true,
                  // 解决无限高度问题
                  physics: NeverScrollableScrollPhysics(),
                  // 禁用滚动
                  padding:
                      EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 30),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 50.0 / 50.0,
                    mainAxisSpacing: 20,
                    crossAxisSpacing: 0,
                  ),
                  itemCount: this.menuSource.length,
                  itemBuilder: (context, index) {
                    CustomerDetailItemData item = this.menuSource[index];
                    return CustomerDetailItemView(
                      item: item,
                      onTap: () {
                        track("mc-private-detail-item-$index");
                        if (sourceData.customerName != null &&
                            item.onTapCallback != null) {
                          item.onTapCallback!();
                        }
                      },
                    );
                  },
                )
              ],
            ),
          ),
        ),
        Visibility(
          visible: sourceData.customerName != null,
          child: Container(
            margin: EdgeInsets.only(
              left: 15,
              right: 15,
              top: 10,
              bottom: 10 + MediaQuery.of(context).viewPadding.bottom,
            ),
            decoration: BoxDecoration(
              color: Color(0xFF00B377),
              borderRadius: BorderRadius.circular(2),
            ),
            constraints: BoxConstraints(minWidth: double.infinity),
            child: TextButton(
              onPressed: this.addVisitAction,
              style: ButtonStyle(
                  overlayColor: MaterialStateProperty.all(Colors.transparent)),
              child: Text(
                '添加拜访',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  void requestDetailData() async {
    if (this.sourceData.customerName == null) {
      showLoadingDialog();
    }
    var result = await NetworkV2<CustomerDetailData>(CustomerDetailData())
        .requestDataV2('customer/private/detail',
            parameters: {'customerId': widget.customerId, 'isMerchantId': 0},
            method: RequestMethod.GET);
    dismissLoadingDialog();
    _controller.finishRefresh();
    if (result.isSuccess == true) {
      this.sourceData = result.getData() ?? CustomerDetailData();
      if (mounted) {
        setState(() {});
      }
    } else {
      showToast('${result.errorMsg ?? "获取客户详情异常"}');
    }
  }

  @override
  String getTitleName() {
    return "客户详情";
  }

  /// 生成菜单项
  List<CustomerDetailItemData> generateMenusSource() {
    return [
      CustomerDetailItemData(
          title: "基本信息",
          image: "assets/images/customer/customer_menu_basic.png",
          onTapCallback: () {
            XYYContainer.open(
                "/customer_basic_info_page?customerId=${widget.customerId}&registerFlag=${sourceData.registerFlag}");
          }),
      CustomerDetailItemData(
          title: "订单记录",
          image: "assets/images/customer/customer_menu_order.png",
          onTapCallback: () {
            XYYContainer.open(
                "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${widget.customerId}&position=0&merchantName=${Uri.encodeFull(sourceData.merchantName)}&registerFlag=${sourceData.registerFlag}");
          }),
      CustomerDetailItemData(
          title: "优惠券",
          image: "assets/images/customer/customer_menu_coupon.png",
          onTapCallback: () {
            XYYContainer.open(
                "/customer_coupons_page?merchantId=${this.sourceData.merchantId}");
          }),
      CustomerDetailItemData(
          title: "商品管理",
          image: "assets/images/customer/customer_menu_product.png",
          onTapCallback: () {
            XYYContainer.open(
                "xyy://crm-app.ybm100.com/customer/goodcontrol?shopid=${widget.customerId}&isFromCustomer=1");
          }),
      CustomerDetailItemData(
          title: "客户资质",
          image: "assets/images/customer/customer_menu_license.png",
          onTapCallback: () {
            XYYContainer.open(
                "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${widget.customerId}&position=1&merchantName=${Uri.encodeFull(sourceData.merchantName)}&registerFlag=${sourceData.registerFlag}");
          }),
      CustomerDetailItemData(
          title: "客户透视",
          image: "assets/images/customer/customer_menu_fluoroscopy.png",
          onTapCallback: () {
            XYYContainer.open(
                "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${widget.customerId}&position=2&merchantName=${Uri.encodeFull(sourceData.merchantName)}&registerFlag=${sourceData.registerFlag}");
          }),
      CustomerDetailItemData(
          title: "拜访记录",
          image: "assets/images/customer/customer_menu_visit.png",
          onTapCallback: () {
            XYYContainer.open(
                "/customer_schedule_record_page?type=0&customerId=${widget.customerId}");
          }),
      CustomerDetailItemData(
          title: "认领记录",
          image: "assets/images/customer/customer_menu_revice.png",
          onTapCallback: () {
            XYYContainer.open(
                "/customer_claim_record_page?merchantId=${sourceData.merchantId}&customerId=${widget.customerId}");
          }),
      CustomerDetailItemData(
          title: "发票类型",
          image: "assets/images/customer/customer_menu_receipt_type.png",
          onTapCallback: () {
            XYYContainer.open(
                "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${widget.customerId}&position=3&merchantName=${Uri.encodeFull(sourceData.merchantName)}&registerFlag=${sourceData.registerFlag}");
          }),
      CustomerDetailItemData(
          title: "热销榜单",
          image: "assets/images/customer/customer_menu_rank.png",
          onTapCallback: () {
            var url = "/commodity_rank_page";
            if (sourceData.registerFlag == 1) {
              url +=
                  "?merchantId=${sourceData.merchantId}&customerId=${widget.customerId}";
            }
            XYYContainer.open(url);
          }),
      CustomerDetailItemData(
          title: "控销管理",
          image: "assets/images/customer/customer_menu_control.png",
          onTapCallback: () {
            XYYContainer.open(
                "/control_manager_page?merchantId=${sourceData.merchantId}&customerId=${widget.customerId}");
          })
    ];
  }

  /// 添加拜访
  void addVisitAction() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': widget.customerId,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      }
    }
  }

  /// 跳转地图页
  void jumpAddressPage() {
    if (this.sourceData.id == null) {
      return;
    }
    String router = "xyy://crm-app.ybm100.com/customer/customer_map?";
    router = router + "id=${this.sourceData.id}&";
    router = router + "poiLatitude=${this.sourceData.poiLatitude}&";
    router = router + "poiLongitude=${this.sourceData.poiLongitude}&";
    router = router + "address=${this.sourceData.address}&";
    router = router + "customerName=${this.sourceData.customerName}&";
    router = router + "poiId=${this.sourceData.poiId}&";
    router = router + "type=false&";
    router = router + "changePoi=true&";
    router = router + "changeMap=true";
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 展示复制弹窗
  void showCopyActionSheet() {
    showCupertinoModalPopup<void>(
      context: this.context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          message: Text(
              '${this.sourceData.merchantName}（客户编号:${this.sourceData.merchantCode}）'),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Clipboard.setData(
                  ClipboardData(
                      text:
                          '${this.sourceData.merchantName}（客户编号:${this.sourceData.merchantCode}）'),
                );
                Navigator.of(context).pop();
                showToast('复制成功');
              },
              child: Text('复制'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('取消'),
          ),
        );
      },
    );
  }

  /// 跳转pdc审核详情
  void jumpStatusPage() {
    if (this.sourceData.poiAuditId == null) {
      showToast("门店审核id为空");
      return;
    }
    String pdcUrl = this.pdcHost +
        "/record/addDetail?poiAuditId=${this.sourceData.poiAuditId}&source=5";
    pdcUrl = Uri.encodeComponent(pdcUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
    XYYContainer.open(router);
  }

  /// 打电话
  void callPhoneOnTap() {
    if (sourceData.customerName == null) {
      return;
    }
    if (sourceData.registerFlag == 2) {
      // 未注册，直接拨打poi电话
      if (sourceData.poiMobilePhone == null ||
          (sourceData.poiMobilePhone?.toString().isEmpty ?? true)) {
        showToast("暂无电话");
        return;
      }
      debounce(() {
        XYYContainer.bridgeCall('call_phone', parameters: {
          'mobile': '${sourceData.poiMobilePhone}',
          'merchantId': '${widget.customerId}',
          'merchantName': '${sourceData.customerName}',
          'addSource': enumToString(CallAddSource.privateDetail),
        });
      });
    } else {
      // 已注册，展示联系人列表
      if (sourceData.contactList?.isEmpty ?? true) {
        showToast("联系人为空，请添加联系人");
        return;
      }
      YBMCustomerCallPhoneWidget.showContactListView(
        context: context,
        contactList: sourceData.contactList ?? [],
        customerId: "${widget.customerId ?? ""}",
        customerName: sourceData.merchantName,
      );
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      rightButtons: [
        IconButton(
          key: this._anchorKey,
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          icon: Image.asset(
            'assets/images/customer/customer_detail_more.png',
            width: 24,
            height: 24,
          ),
          onPressed: () {
            showCustomerMoreMenu(
              context: this.context,
              targetKey: this._anchorKey,
              itemSource: getCustomerMoreList(),
              itemTap: this.jumpMoreItem,
            );
          },
        )
      ],
    );
  }

  List<CustomerMoreSourceItem> getCustomerMoreList() {
    var sourceList = <CustomerMoreSourceItem>[];
    if (widget.distributable == "1") {
      sourceList
          .add(CustomerMoreSourceItem(title: '分配至BD', id: "distributeToBD"));
    }
    sourceList.addAll([
      CustomerMoreSourceItem(title: '释放至公海', id: "releaseToPublic"),
      CustomerMoreSourceItem(title: '客户错误信息上报', id: "uploadError")
    ]);
    return sourceList;
  }

  void jumpMoreItem(dynamic id) {
    if (sourceData.customerName == null) {
      return;
    }
    debounce(() {
      switch (id) {
        case "distributeToBD":
          distributeToBD();
          break;
        case "releaseToPublic":
          releaseToPublic();
          break;
        case "uploadError":
          uploadError();
          break;
      }
    });
  }

  /// 分配至BD
  void distributeToBD() async {
    var sysUserId = await UserInfoUtil.sysUserId();
    // 重置商品集选中信息
    sourceData.bindSkuCollect?.forEach((element) {
      element.isCheck = false;
    });
    List<CustomerSkuCollectData> list = sourceData.bindSkuCollect
            ?.where((element) => element.oaUserId?.toString() == sysUserId)
            .toList() ??
        [];
    track("Event-PrivateSeaDetail-Distribute");
    if (list.length > 1) {
      List<CustomerSkuCollectData>? result =
          await CustomerSkuCollectDialog.showSkuCollectDialog(context, list,
              title: "选择要分配的商品集",
              rightText: "下一步",
              canSelectIgnoreReceiveType: true);
      if (result != null && result.length > 0) {
        this.jumpToSelectPeople(result);
      }
    } else if (list.length == 1) {
      this.jumpToSelectPeople(list);
    } else {
      XYYContainer.toastChannel.toast("暂无可分配商品集");
    }
  }

  /// 跳转选择分配人员
  void jumpToSelectPeople(List<CustomerSkuCollectData> result) {
    String routerPath =
        "xyy://crm-app.ybm100.com/alloc_people?type=1&isAccompanySelect=1";
    XYYContainer.open(routerPath, callback: (value) {
      if (value != null) {
        String? peopleId = value["selectdId"];
        String skuCollectCodes = result.map((e) => e.skuCollectCode).join(',');
        if (peopleId != null && skuCollectCodes.length > 0) {
          this.requestAlloc(peopleId, skuCollectCodes);
        }
      }
    });
  }

  /// 请求分配
  void requestAlloc(String bindUserId, String skuCollectCodes) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'customer/private/bind/user',
      parameters: {
        'bindUserId': bindUserId,
        'skuCollectCodes': skuCollectCodes,
        'customerId': widget.customerId,
      },
      method: RequestMethod.POST,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      track("Event-PrivateSeaDetail-Distribute-Success");
      XYYContainer.toastChannel.toast("分配成功");
      // 分配成功后刷新页面数据
      showLoadingDialog();
      requestDetailData();
    }
  }

  /// 释放至公海
  void releaseToPublic() async {
    if (widget.customerId == null) {
      showToast("customerId字段错误");
      return;
    }
    if (sourceData.unbindFlag?.toString() != "1") {
      showToast("无权限释放该客户");
      return;
    }
    track("Event-PrivateSeaDetail-Release");
    var sysUserId = await UserInfoUtil.sysUserId();
    // 重置商品集选中信息
    sourceData.bindSkuCollect?.forEach((element) {
      element.isCheck = false;
    });
    List<CustomerSkuCollectData> list = sourceData.bindSkuCollect
            ?.where((element) => element.oaUserId?.toString() == sysUserId)
            .toList() ??
        [];
    if (list.length > 1) {
      List<CustomerSkuCollectData>? result =
          await CustomerSkuCollectDialog.showSkuCollectDialog(context, list,
              title: "选择要释放商品集",
              rightText: "释放",
              canSelectIgnoreReceiveType: true);
      if (result != null && result.length > 0) {
        this.releaseToPublicReal(result);
      }
    } else if (list.length == 1) {
      this.releaseToPublicReal(list);
    } else {
      XYYContainer.toastChannel.toast("暂无可释放商品集");
    }
  }

  /// 释放至公海（调用接口）
  void releaseToPublicReal(List<CustomerSkuCollectData> skuCollectList) async {
    showMessageDialog2(
        title: "",
        message: "是否释放该商品集到公海",
        callBack: () async {
          showLoadingDialog();
          var result = await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2())
              .requestDataV2('customer/private/unBindCustomer',
                  parameters: {
                    'skuCollectCodes':
                        skuCollectList.map((e) => e.skuCollectCode).join(','),
                    'id': widget.customerId,
                  },
                  method: RequestMethod.GET);
          dismissLoadingDialog();
          if (result.isSuccess == true) {
            track("Event-PrivateSeaDetail-Release-Success");
            XYYContainer.toastChannel.toast("释放到公海成功");
            XYYContainer.close(context, resultData: {"release": "1"});
          }
        });
  }

  /// 错误信息上报
  void uploadError() {
    track("Event-PrivateSeaDetail-UploadError");
    if (this.sourceData.poiAuditStatus != null) {
      if (this.sourceData.poiAuditStatus.toString() == "1") {
        showToast("客户信息审核中，无法提交审核");
        return;
      }
      if (this.sourceData.poiAuditStatus.toString() == "3") {
        showToast("客户信息审核驳回，无法操作");
        return;
      }
    }
    track("Event-PrivateSeaDetail-UploadError-Success");
    String pdcUrl =
        this.pdcHost + "/dataError?poiId=${this.sourceData.poiId}&source=5";
    pdcUrl = Uri.encodeComponent(pdcUrl);
    String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
    XYYContainer.open(router);
  }
}
