import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/hy_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class HYCustomerPrivateItem extends StatelessWidget with EventBusObserver {
  final YBMCustomerPrivateItemModel model;
  final bool hasLocation;
  final String serviceInterface;

  HYCustomerPrivateItem(
    this.model, {
    this.hasLocation = true,
    this.serviceInterface = '',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFEFEFF4),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(7),
        ),
        child: Column(
          children: [
            this.merchantNameWidget(),
            this.getMerchatIds(),
            this.getVisitDays(),
            this.merchantAddressWidget(),
            this.getCooperationWidget(),
            this.getReleaseDays(),
            this.getActionWidget(context),
          ],
        ),
      ),
    );
  }

  // 客户名称及状态
  Widget merchantNameWidget() {
    Color statusColor = Color(0xFF35C561);
    if (model.customerStatusName == "已合作") {
      statusColor = Color(0xFF35C561);
    } else {
      statusColor = Color(0xFFFE3D3D);
    }
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10),
      child: Row(
        children: [
          Expanded(
            child: Text(
              this.model.getName(),
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 10),
          Text(
            "${model.customerStatusName}",
            style: TextStyle(color: statusColor, fontSize: 14),
          )
        ],
      ),
    );
  }

  // POI ID  荷叶门店ID
  Widget getMerchatIds() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            'POI ID：${model.poiId}',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
          ),
          Visibility(
            visible: model.hyId != null && "${model.hyId}".length > 0,
            child: Text(
              'POI ID：${model.hyId}',
              style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  // 拜访天数
  Widget getVisitDays() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      width: double.infinity,
      child: Text(
        model.overLastVisitDays != null
            ? '距离上次拜访${model.overLastVisitDays}天'
            : '未拜访',
        style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
      ),
    );
  }

  // 距离及位置
  Widget merchantAddressWidget() {
    String locationStr = this.hasLocation ? "距您${model.distance}" : "获取不到当前位置";
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 9),
      child: Row(
        children: [
          Text(
            locationStr,
            style: TextStyle(color: Color(0xFF666666), fontSize: 12),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              model.getAddress(),
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 合作状态
  Widget getCooperationWidget() {
    // ImageCatchWidget
    List<String> imagePaths = this
            .model
            .serviceLines
            ?.map((e) => this.serviceInterface + e.iOSIcon)
            .toList() ??
        [];
    List<Widget> items = [];
    imagePaths.forEach((e) {
      items.add(ImageCatchWidget(
        url: e,
        h: 14,
        fit: BoxFit.fill,
      ));
      items.add(SizedBox(width: 5));
    });
    items.add(Spacer());
    return Container(
      padding: EdgeInsets.only(left: 10, top: 10),
      child: Row(
        children: items,
      ),
    );
  }

  // 释放天数
  Widget getReleaseDays() {
    return Container(
      height: 37,
      width: double.infinity,
      color: Color(0xFFFAFAFA),
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(top: 9),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: RichText(
        text: TextSpan(
            text: '距离释放',
            style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12),
            children: [
              TextSpan(
                text: '${model.relaseDays ?? '--'}天',
                style: TextStyle(color: Color(0xFF333333), fontSize: 12),
              )
            ]),
      ),
    );
  }

  /// 底部按钮
  Widget getActionWidget(BuildContext context) {
    return Container(
      height: 50,
      child: Row(
        children: [
          Spacer(),
          Visibility(
            visible: "${model.distributable}" != "0",
            child: this.buttonFor('分配', () {
              this.allocCollectAction(context);
            }),
          ),
          this.buttonFor('拨打电话', () {
            this.callPhoneAction(context);
          }),
          this.buttonFor('添加拜访', this.jumpToAddVisit),
        ],
      ),
    );
  }

  Widget buttonFor(String title, VoidCallback onPressed) {
    return TextButton(
        onPressed: onPressed,
        child: Container(
          height: 30,
          decoration: BoxDecoration(
            border: Border.all(color: Color(0xFF8E8E93), width: 0.5),
            borderRadius: BorderRadius.circular(15),
          ),
          padding: EdgeInsets.only(left: 10, right: 10),
          child: Center(
            child: Text(
              title,
              style: TextStyle(color: Color(0xFF333333), fontSize: 14),
            ),
          ),
        ),
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding:
              MaterialStateProperty.all<EdgeInsets>(EdgeInsets.only(right: 10)),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ));
  }

  /// 分配
  void allocCollectAction(BuildContext context) async {
    /// 跳转选择分配人员
    String routerPath =
        "xyy://crm-app.ybm100.com/alloc_people?type=1&isAccompanySelect=1";
    XYYContainer.open(routerPath, callback: (value) {
      if (value != null) {
        String? popleId = value["selectdId"];
        if (popleId != null) {
          this.requestAlloc(popleId);
        }
      }
    });
  }

  /// 请求分配
  void requestAlloc(String bindUserId) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'hyjk/assignCustomer',
      parameters: {
        'bindUserId': bindUserId,
        'id': model.id,
      },
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      XYYContainer.toastChannel.toast("分配成功");
      // 发送分配成功通知
      eventBus.sendMessage(CustomerEventBusName.HY_PRIVATE_ALLOC);
    }
  }

  /// 拨打电话
  void callPhoneAction(BuildContext context) async {
    if (model.contactList?.length == 0) {
      XYYContainer.toastChannel.toast('联系人为空，请添加联系人');
      return;
    }
    HYCustomerCallPhoneWidget.showContactListView(
      context: context,
      contactList: model.contactList ?? [],
      customerId: "${model.id ?? ""}",
    );
  }

  /// 跳转添加拜访
  void jumpToAddVisit() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/hy/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': model.id,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr&isHeyeVisit=1';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr&isHeyeVisit=1';
        XYYContainer.open(router);
      }
    }
  }
}
