import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

enum CallAddSource {
  baseInfo, // 基本信息
  contactDialog, // 客户详情
  privateList, // 私海列表
  privateDetail, // 私海详情
}

class HYCustomerCallPhoneWidget extends StatefulWidget {
  final List<YBMCustomerContactModel> contactList;
  final String customerId;
  final CallAddSource addSource;

  HYCustomerCallPhoneWidget({
    required this.contactList,
    required this.customerId,
    required this.addSource,
  });

  static Future<T?> showContactListView<T>({
    required BuildContext context,
    required List<YBMCustomerContactModel> contactList,
    required dynamic customerId,
    CallAddSource addSource = CallAddSource.privateList,
  }) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height -
              233 +
              MediaQuery.of(context).viewInsets.bottom,
          child: HYCustomerCallPhoneWidget(
            contactList: contactList,
            customerId: customerId,
            addSource: addSource,
          ),
        );
      },
    );
  }

  @override
  State<StatefulWidget> createState() {
    return HYCustomerCallPhoneWidgetState();
  }
}

class HYCustomerCallPhoneWidgetState extends State<HYCustomerCallPhoneWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Column(
        children: [
          Container(
            height: 40,
            child: Row(
              children: [
                SizedBox(width: 30),
                Expanded(
                  child: Text(
                    '拨打电话',
                    style: TextStyle(color: Color(0xFF333333), fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Image.asset(
                    'assets/images/titlebar/icon_close.png',
                    width: 20,
                    height: 20,
                  ),
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: Container(
              child: ListView.builder(
                padding: EdgeInsets.zero,
                itemCount: widget.contactList.length,
                itemBuilder: (context, index) {
                  YBMCustomerContactModel model = widget.contactList[index];
                  return HYCustomerCallPhoneItem(
                    model: model,
                    customerId: widget.customerId,
                    addSource: widget.addSource,
                  );
                },
              ),
            ),
          )
        ],
      ),
    );
  }
}

class HYCustomerCallPhoneItem extends StatelessWidget {
  final YBMCustomerContactModel model;
  final dynamic customerId;
  final CallAddSource addSource;

  HYCustomerCallPhoneItem({
    required this.model,
    required this.customerId,
    this.addSource = CallAddSource.privateList,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      child: Column(
        children: [
          Spacer(),
          Row(
            children: [
              Container(
                padding: EdgeInsets.only(left: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          "${model.contactName??"--"}",
                          style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Visibility(
                          visible: "${model.contactName}" != "注册电话",
                          child: Text(
                            "(${model.contactJobName})",
                          ),
                        )
                      ],
                    ),
                    Text(
                      '${model.contactMobile}',
                      style: TextStyle(color: Color(0xFF8E8E93), fontSize: 13),
                    ),
                  ],
                ),
              ),
              Spacer(),
              TextButton(
                onPressed: callPhoneForMerchant,
                child: Image.asset(
                  'assets/images/customer/customer_detail_phone.png',
                  width: 38,
                  height: 38,
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              )
            ],
          ),
          Spacer(),
          Divider(color: Color(0xFFECECEC), height: 0.5, thickness: 0.5),
        ],
      ),
    );
  }

  void callPhoneForMerchant() {
    debounce(() {
      XYYContainer.bridgeCall('call_phone', parameters: {
        'mobile': '${model.contactMobile}',
        'merchantId': '${this.customerId}',
        'merchantName': '${model.merchantName}',
        'addSource': enumToString(this.addSource),
      });
    });
  }
}
