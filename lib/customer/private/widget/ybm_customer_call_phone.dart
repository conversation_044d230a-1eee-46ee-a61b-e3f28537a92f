import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

enum CallAddSource {
  baseInfo, // 基本信息
  contactDialog, // 客户详情
  privateList, // 私海列表
  privateDetail, // 私海详情
}

class YBMCustomerCallPhoneWidget extends StatefulWidget {
  final List<YBMCustomerContactModel> contactList;
  final String customerId;
  final CallAddSource addSource;
  final String customerName;

  YBMCustomerCallPhoneWidget({
    required this.contactList,
    required this.customerId,
    required this.addSource,
    required this.customerName,
  });

  static Future<T?> showContactListView<T>({
    required BuildContext context,
    required List<YBMCustomerContactModel> contactList,
    required dynamic customerId,
    required String customerName,
    CallAddSource addSource = CallAddSource.privateList,
  }) {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        double maxHeight = MediaQuery.of(context).size.height -
            233 +
            MediaQuery.of(context).viewPadding.bottom;
        double curHeight = paddingBottom(context) + 104;
        curHeight += contactList.length * 70;
        if (curHeight > maxHeight) {
          curHeight = maxHeight;
        }
        return Container(
          height: curHeight,
          child: YBMCustomerCallPhoneWidget(
            contactList: contactList,
            customerId: customerId,
            addSource: addSource,
            customerName: customerName,
          ),
        );
      },
    );
  }

  static double paddingBottom(BuildContext context) {
    var bottom = MediaQuery.of(context).viewPadding.bottom;
    if (bottom == 0) {
      return 20;
    }
    return bottom;
  }

  @override
  State<StatefulWidget> createState() {
    return YBMCustomerCallPhoneWidgetState();
  }
}

class YBMCustomerCallPhoneWidgetState
    extends State<YBMCustomerCallPhoneWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFF1F6F9),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(18),
          topRight: Radius.circular(18),
        ),
      ),
      child: Column(
        children: [
          Container(
            height: 50,
            child: Row(
              children: [
                SizedBox(width: 30),
                Expanded(
                  child: Text(
                    '客户联系人',
                    style: TextStyle(
                        color: Color(0xFF383841),
                        fontSize: 17,
                        fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Image.asset(
                    'assets/images/customer/customer_phone_close.png',
                    width: 20,
                    height: 20,
                  ),
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                )
              ],
            ),
          ),
          Expanded(
            child: Container(
              child: ListView.builder(
                padding: EdgeInsets.zero,
                itemCount: widget.contactList.length,
                physics: BouncingScrollPhysics(),
                itemBuilder: (context, index) {
                  YBMCustomerContactModel model = widget.contactList[index];
                  model.merchantName = widget.customerName;
                  return YBMCustomerCallPhoneItem(
                    model: model,
                    customerId: widget.customerId,
                    addSource: widget.addSource,
                  );
                },
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(
                10, 10, 10, YBMCustomerCallPhoneWidget.paddingBottom(context)),
            child: GestureDetector(
              onTap: addContactAction,
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: Color(0xFF00B377),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Text(
                  '添加联系人',
                  style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 添加联系人
  void addContactAction() {
    Navigator.of(context).pop();
    debounce(() {
      Map<String, dynamic> param = {
        "merchantId": widget.customerId,
        "merchantName": widget.customerName
      };
      String jsonStr = json.encode(param);
      var routerPath = "/add_contact_page?contactJSON=$jsonStr";
      routerPath = Uri.encodeFull(routerPath);
      Navigator.of(context).pushNamed(routerPath);
    });
  }
}

class YBMCustomerCallPhoneItem extends StatelessWidget {
  final YBMCustomerContactModel model;
  final dynamic customerId;
  final CallAddSource addSource;

  YBMCustomerCallPhoneItem({
    required this.model,
    required this.customerId,
    this.addSource = CallAddSource.privateList,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
        height: 60,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Text(
                        "${model.contactName??"--"}",
                        style: TextStyle(
                          color: Color(0xFF383841),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Visibility(
                        visible: "${model.contactName}" != "注册电话",
                        child: Text(
                          "(${model.contactJobName}${model.kp?.toString()=="true"?"kp":""})",
                          style:
                              TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 5),
                  Text(
                    '电话：${model.contactMobile}',
                    style: TextStyle(color: Color(0xFF383841), fontSize: 12),
                  ),
                ],
              ),
            ),
            Spacer(),
            TextButton(
              onPressed: () {
                callPhoneForMerchant(context);
              },
              child: Image.asset(
                'assets/images/customer/customer_link_phone.png',
                width: 25,
                height: 35,
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(
                    EdgeInsets.only(right: 15)),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            TextButton(
              onPressed: () {
                editContactAction(context);
              },
              child: Image.asset(
                'assets/images/customer/customer_link_edit.png',
                width: 25,
                height: 35,
              ),
              style: ButtonStyle(
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 编辑联系人
  void editContactAction(BuildContext context) {
    Navigator.of(context).pop();
    debounce(() {
      Map<String, dynamic> param = this.model.toJson();
      String jsonStr = json.encode(param);
      var routerPath = "/add_contact_page?contactJSON=$jsonStr";
      routerPath = Uri.encodeFull(routerPath);
      Navigator.of(context).pushNamed(routerPath);
    });
  }

  void callPhoneForMerchant(BuildContext context) {
    Navigator.of(context).pop();
    debounce(() {
      XYYContainer.bridgeCall('call_phone', parameters: {
        'mobile': '${model.contactMobile}',
        'merchantId': '${this.customerId}',
        'merchantName': '${model.merchantName}',
        'addSource': enumToString(this.addSource),
      });
    });
  }
}
