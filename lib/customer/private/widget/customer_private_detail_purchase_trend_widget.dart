import 'package:XyyBeanSproutsFlutter/customer/private/data/customer_detail_purchase_trend_data.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'dart:math' as math;

class CustomerPrivateDetailPurchaseTrendWidget extends StatefulWidget {
  final ValueNotifier<CustomerDetailPurchaseTrendData?>? purchaseTrendNotifier;
  final VoidCallback? moreClickCallback;

  CustomerPrivateDetailPurchaseTrendWidget(
      {this.purchaseTrendNotifier, this.moreClickCallback});

  @override
  State<StatefulWidget> createState() {
    return CustomerPrivateDetailPurchaseTrendWidgetState();
  }
}

class CustomerPrivateDetailPurchaseTrendWidgetState
    extends State<CustomerPrivateDetailPurchaseTrendWidget> {
  GMVDataType get selectDataType => selectDataTypeNotifier.value;
  ValueNotifier<GMVDataType> selectDataTypeNotifier =
      ValueNotifier(GMVDataType.ActuallyPaid);

  Map<GMVDataType, Map<String, dynamic>> config = {
    GMVDataType.ActuallyPaid: {
      "title": "实付GMV",
      "color": const Color(0xff00b377),
    },
    GMVDataType.Control: {
      "title": "控销GMV",
      "color": const Color(0xff1649FB),
    },
    GMVDataType.HighGross: {
      "title": "优选GMV",
      "color": const Color(0xffff2121),
    },
    GMVDataType.CuratedSelect: {
      "title": "甄选GMV",
      "color": const Color(0xFFFF7A00),
    },
  };

  List<String> chartXData = [];

  Map<GMVDataType, List<double>> chartYData = {};

  @override
  void initState() {
    super.initState();
    widget.purchaseTrendNotifier?.addListener(initChartData);
    initChartData();
  }

  @override
  void dispose() {
    super.dispose();
    widget.purchaseTrendNotifier?.removeListener(initChartData);
  }

  void initChartData() {
    var gmvList = widget.purchaseTrendNotifier?.value?.gmvList;
    if (gmvList?.isNotEmpty == true) {
      chartXData.clear();
      chartYData.clear();
      List<double> actuallyPaidYList = [];
      List<double> controlYList = [];
      List<double> highGrossYList = [];
      List<double> curatedSelectYList = [];//甄选
      gmvList?.forEach((element) {
        chartXData.add(element.dateStr?.toString() ?? "--");
        actuallyPaidYList.add(convertToDouble(element.payGmv));
        controlYList.add(convertToDouble(element.marketControlGmv));
        highGrossYList.add(convertToDouble(element.highGrossGmv));
        curatedSelectYList.add(convertToDouble(element.selectionGrossGmv));//甄选GMV
      });
      chartYData[GMVDataType.ActuallyPaid] = actuallyPaidYList;
      chartYData[GMVDataType.Control] = controlYList;
      chartYData[GMVDataType.HighGross] = highGrossYList;
      chartYData[GMVDataType.CuratedSelect] = curatedSelectYList;//甄选
    } else {
      initDefaultChartData();
    }
    setState(() {});
  }

  double convertToDouble(dynamic value) {
    try {
      if (value is double) {
        return value;
      }
      return double.tryParse(value?.toString() ?? "0") ?? 0;
    } catch (e) {
      return 0;
    }
  }

  void initDefaultChartData() {
    chartXData = ["上上月", "上月", "本月"];
    chartYData = {
      GMVDataType.ActuallyPaid: [0, 0, 0],
      GMVDataType.Control: [0, 0, 0],
      GMVDataType.HighGross: [0, 0, 0],
      GMVDataType.CuratedSelect: [0, 0, 0],
    };
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.only(left: 10, right: 10, bottom: 15),
          width: double.infinity,
          color: Colors.white,
          child: Column(
            children: [buildTitleWidget(), buildChartGroupWidget()],
          ),
        ));
  }

  Widget buildTitleWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: Container(
          padding: EdgeInsets.only(
            top: 15,
          ),
          child: Text(
            "采购走势",
            style: TextStyle(
                color: const Color(0xff383841),
                fontSize: 14,
                fontWeight: FontWeight.w600),
          ),
        )),
        Visibility(
          visible: false, // 暂时隐藏子页面入口
          child: GestureDetector(
            onTap: widget.moreClickCallback,
            behavior: HitTestBehavior.opaque,
            child: Container(
              padding: EdgeInsets.only(top: 16, bottom: 1),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "查看全部",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        color: const Color(0xff9494a6)),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Image.asset(
                    "assets/images/customer/customer_private_detail_arrow.png",
                    width: 4.5,
                    height: 7,
                  )
                ],
              ),
            ),
          ),
        )
      ],
    );
  }

  /// 图表容器
  Widget buildChartGroupWidget() {
    return ValueListenableBuilder(
        valueListenable: selectDataTypeNotifier,
        builder: (context, value, child) {
          return Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildRadioItemWidget(GMVDataType.ActuallyPaid),
                  buildRadioItemWidget(GMVDataType.Control),
                  buildRadioItemWidget(GMVDataType.HighGross),
                  buildRadioItemWidget(GMVDataType.CuratedSelect),
                ],
              ),
              SizedBox(
                height: 15,
              ),
              buildChartWidget()
            ],
          );
        });
  }

  List<double>? getGMVDataRange() {
    double? maxData;
    double? minData;
    chartYData.forEach((key, value) {
      value.forEach((element) {
        if (maxData == null) {
          maxData = element;
        }
        if (minData == null) {
          minData = element;
        }
        maxData = math.max(maxData!, element);
        minData = math.min(minData!, element);
      });
    });
    if (minData == null || maxData == null) {
      return [0, 1000];
    }
    if (maxData!.abs() - minData!.abs() <= 100) {
      return [minData! - 100, maxData! + 100];
    }
    return [minData!, maxData!];
    print("guan --------------max-------------");
    var fixedMaxData = getFixedMaxData(minData!, maxData!).toDouble();
    print("guan --------------min-------------");
    var fixedMinData =
        getFixedMinData(minData!, maxData!, fixedMaxData).toDouble();

    //  数据对齐，最大值向上取整，最小值向下取整，取整步长为数据范围的10%

    return [fixedMinData, fixedMaxData];
  }

  // 最大膨胀率
  var maxExpandRate = 0.4;

  // 最小膨胀率
  var minExpandRate = 0.1;

  double getFixedMaxData(double minData, double maxData) {
    print("guan maxData ${maxData}");
    var distance = maxData - minData;
    print("guan distance ${distance}");
    var ceilExpandDistance = distance * maxExpandRate + maxData;
    print("guan maxExpandDistance ${ceilExpandDistance}");
    var floorExpandDistance = distance * minExpandRate + maxData;
    print("guan minExpandDistance ${floorExpandDistance}");
    var firstNum = getFirstNum(ceilExpandDistance);
    print("guan firstNum ${firstNum}");
    var zeroNumCount = math.max(0, getNumCount(ceilExpandDistance) - 1);
    print("guan zeroNumCount ${zeroNumCount}");

    // 获取一个比最大膨胀率稍微大一点的预设值,
    double tempFixedMaxData = 0;
    int i = 1;
    do {
      tempFixedMaxData = (firstNum + i) * math.pow(10, zeroNumCount).toDouble();
      i++;
    } while (tempFixedMaxData < ceilExpandDistance);
    print("guan tempFixedMaxData ${tempFixedMaxData}");
    // 减少的步长
    double decreaseDistance =
        zeroNumCount >= 0 ? math.pow(10, zeroNumCount).toDouble() : 1;
    print("guan decreaseDistance ${decreaseDistance}");
    // 预设值逐步减少，直到小于最大膨胀率
    while (true) {
      tempFixedMaxData -= decreaseDistance;
      print("guan while tempFixedMaxData ${tempFixedMaxData}");
      if (tempFixedMaxData < ceilExpandDistance &&
          tempFixedMaxData > floorExpandDistance) {
        print("guan while tempFixedMaxData result ${tempFixedMaxData}");
        // 小于最大膨胀，大于最小膨胀，满足要求
        return tempFixedMaxData;
      }
      if (tempFixedMaxData < floorExpandDistance) {
        print(
            "guan while tempFixedMaxData min ${tempFixedMaxData} ${floorExpandDistance} ${decreaseDistance}");
        // 直接小于了最小膨胀，步长需要减小
        if (decreaseDistance == 1) {
          // 步长已经是1了，不能再小了,直接原数据+1返回
          return maxData;
        }
        // 恢复上一步的数据
        tempFixedMaxData += decreaseDistance;
        // 减小步长
        zeroNumCount -= 1;
        decreaseDistance = zeroNumCount >= 0
            ? math.pow(10, zeroNumCount).toDouble()
            : 1;
        print("guan while decreaseDistance min ${decreaseDistance} ");
        continue;
      }
    }
  }

  double getFixedMinData(double minData, double maxData, double fixedMaxData) {
    print("guan minData ${minData}");
    var distance = maxData - minData;
    print("guan distance ${distance}");
    var ceilExpandDistance = minData - distance * minExpandRate;
    print("guan ceilExpandDistance ${ceilExpandDistance}");
    var floorExpandDistance = minData - distance * maxExpandRate;
    print("guan floorExpandDistance ${floorExpandDistance}");
    var firstNum = getFirstNum(floorExpandDistance);
    print("guan firstNum ${firstNum}");
    var zeroNumCount = math.max(0, getNumCount(floorExpandDistance) - 1);
    print("guan zeroNumCount ${zeroNumCount}");

    // 获取一个比下限值稍微小一点的预设值,
    double tempFixedMinData = 0;
    int i = 1;
    do {
      tempFixedMinData = (firstNum - i) * math.pow(10, zeroNumCount).toDouble();
      i++;
    } while (tempFixedMinData > floorExpandDistance);

    print("guan tempFixedMinData ${tempFixedMinData}");
    // 步长
    double decreaseDistance =
        zeroNumCount >= 0 ? math.pow(10, zeroNumCount).toDouble() : 1;
    print("guan decreaseDistance ${decreaseDistance}");
    // 预设值逐步增加，直到大于下限值
    while (true) {
      tempFixedMinData += decreaseDistance;
      print("guan while tempFixedMinData ${tempFixedMinData}");
      if (tempFixedMinData < ceilExpandDistance &&
          tempFixedMinData > floorExpandDistance &&
          ((fixedMaxData - tempFixedMinData) / 4) % 10 == 0) {
        print("guan while tempFixedMinData result ${tempFixedMinData}");
        // 小于上限，大于下限，满足要求
        return tempFixedMinData;
      }
      if (tempFixedMinData > ceilExpandDistance) {
        print(
            "guan while tempFixedMinData min ${tempFixedMinData} ${ceilExpandDistance} ${decreaseDistance}");
        // 直接大于了上限，步长需要减小
        if (decreaseDistance == 1) {
          // 步长已经是1了，不能再大了,直接原数据-1返回
          return minData;
        }
        // 恢复上一步的数据
        tempFixedMinData -= decreaseDistance;
        // 减小步长
        zeroNumCount -= 1;
        decreaseDistance = zeroNumCount >= 0
            ? math.pow(10, zeroNumCount).toDouble()
            : 1;
        print("guan while tempFixedMinData min ${decreaseDistance}");
        continue;
      }
    }
  }

  int getFirstNum(double distance) {
    double tempDistance = distance;
    while (tempDistance.abs() >= 10) {
      tempDistance = (tempDistance / 10).floor().toDouble();
    }
    return tempDistance.toInt();
  }

  int getNumCount(double distance) {
    int count = 0;
    while (distance.abs() > 0) {
      count++;
      distance = (distance.abs() / 10).floor().toDouble();
    }
    return count;
  }

  double getGMVDataInterval(List<double>? gmvDataRange) {
    if (gmvDataRange != null) {
      return (gmvDataRange[1] - gmvDataRange[0]) / 4.0;
    } else {
      return 100;
    }
  }

  final double chartHeight = 180;
  final double leftBarLinePadding = 10;
  final double barLinePadding = 30;
  final double gridLinePadding = 10;
  final double bottomTitlePadding = 10;

  Widget buildChartWidget() {
    var gmvDataRange = getGMVDataRange();
    return Stack(children: [
      buildBackgroundChartWidget(gmvDataRange),
      buildRealChartWidget(gmvDataRange)
    ]);
  }

  Widget buildBackgroundChartWidget(List<double>? gmvDataRange) {
    return Container(
      padding: EdgeInsets.only(left: leftBarLinePadding),
      height: chartHeight,
      width: double.infinity,
      child: LineChart(
        LineChartData(
          maxY: gmvDataRange?[1] ?? 1000,
          minY: gmvDataRange?[0] ?? 0,
          lineTouchData: LineTouchData(enabled: false),
          gridData: FlGridData(
              drawVerticalLine: false,
              horizontalInterval: getGMVDataInterval(gmvDataRange),
              getDrawingHorizontalLine: (value) {
                return FlLine(
                    color: const Color(0xffdddddd),
                    strokeWidth: 0.5,
                    dashArray: [4, 4]);
              }),
          borderData: FlBorderData(show: false),
          lineBarsData: [
            buildVirtualChartBarData(GMVDataType.ActuallyPaid),
            buildVirtualChartBarData(GMVDataType.Control),
            buildVirtualChartBarData(GMVDataType.HighGross),
            buildVirtualChartBarData(GMVDataType.CuratedSelect)
          ],
          titlesData: FlTitlesData(
            leftTitles: SideTitles(
              margin: gridLinePadding,
              showTitles: true,
              getTitles: (value) {
                return "";
              },
              interval: getGMVDataInterval(gmvDataRange),
            ),
            bottomTitles: SideTitles(
                showTitles: true,
                margin: bottomTitlePadding,
                getTitles: (value) {
                  return "";
                }),
          ),
        ),
      ),
    );
  }

  /// 图表
  Widget buildRealChartWidget(List<double>? gmvDataRange) {
    var lineBarsData = {
      GMVDataType.ActuallyPaid: buildChartBarData(GMVDataType.ActuallyPaid),
      GMVDataType.Control: buildChartBarData(GMVDataType.Control),
      GMVDataType.HighGross: buildChartBarData(GMVDataType.HighGross),
      GMVDataType.CuratedSelect: buildChartBarData(GMVDataType.CuratedSelect)
    };
    var selectedLineBarData = lineBarsData[selectDataType]!;
    lineBarsData.remove(selectDataType);
    lineBarsData[selectDataType] = selectedLineBarData;
    return Container(
      height: chartHeight,
      padding: EdgeInsets.only(left: leftBarLinePadding, right: barLinePadding),
      width: double.infinity,
      child: LineChart(
        LineChartData(
          maxY: gmvDataRange?[1] ?? 1000,
          minY: gmvDataRange?[0] ?? 0,
          backgroundColor: Colors.transparent,
          showingTooltipIndicators:
              chartYData[selectDataType]?.mapIndexed((index, element) {
            return ShowingTooltipIndicators([
              LineBarSpot(selectedLineBarData, 0,
                  lineBarsData[selectDataType]!.spots[index]),
            ]);
          }).toList(),
          lineTouchData: LineTouchData(
              enabled: false,
              touchTooltipData: LineTouchTooltipData(
                  tooltipPadding:
                      EdgeInsets.symmetric(vertical: 3, horizontal: 4.5),
                  tooltipRoundedRadius: 14,
                  tooltipMargin: 10,
                  tooltipBgColor: config[selectDataType]?["color"]!,
                  getTooltipItems: (touchedSpots) {
                    return touchedSpots.map((LineBarSpot touchedSpot) {
                      final textStyle = TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      );
                      return LineTooltipItem(
                          touchedSpot.y.toString(), textStyle);
                    }).toList();
                  })),
          gridData: FlGridData(
              drawVerticalLine: false,
              drawHorizontalLine: false,
              horizontalInterval: getGMVDataInterval(gmvDataRange),
              getDrawingHorizontalLine: (value) {
                return FlLine(
                    color: const Color(0xffdddddd),
                    strokeWidth: 0.5,
                    dashArray: [4, 4]);
              }),
          borderData: FlBorderData(show: false),
          lineBarsData: lineBarsData.values.toList(),
          titlesData: FlTitlesData(
            leftTitles: SideTitles(
              margin: barLinePadding + gridLinePadding,
              showTitles: true,
              interval: getGMVDataInterval(gmvDataRange),
              getTextStyles: (context, value) {
                return TextStyle(
                    color: const Color(0xff9494a6),
                    fontSize: 11,
                    fontWeight: FontWeight.normal);
              },
            ),
            bottomTitles: SideTitles(
                margin: bottomTitlePadding,
                showTitles: true,
                getTextStyles: (context, value) {
                  return TextStyle(
                      color: const Color(0xff676773),
                      fontSize: 11,
                      fontWeight: FontWeight.normal);
                },
                getTitles: (value) {
                  return chartXData[value.toInt()];
                }),
          ),
        ),
      ),
    );
  }

  LineChartBarData buildVirtualChartBarData(GMVDataType gmvDataType) {
    return LineChartBarData(
        spots: chartYData[gmvDataType]
            ?.mapIndexed((index, element) =>
                FlSpot(index.toDouble(), element.toDouble()))
            .toList(),
        colors: [Colors.transparent],
        barWidth: 1,
        dotData: FlDotData(show: false));
  }

  LineChartBarData buildChartBarData(GMVDataType gmvDataType) {
    var color = config[gmvDataType]?["color"]!;
    if (selectDataType == gmvDataType) {
      //选中
      return LineChartBarData(
          spots: chartYData[gmvDataType]
              ?.mapIndexed((index, element) =>
                  FlSpot(index.toDouble(), element.toDouble()))
              .toList(),
          belowBarData: BarAreaData(
            show: true,
            spotsLine: BarAreaSpotsLine(
                show: true,
                flLineStyle:
                    FlLine(color: color.withOpacity(0.4), strokeWidth: 1)),
            gradientFrom: Offset(0, 0),
            gradientTo: Offset(0, 1),
            colors: [color.withOpacity(0.14), Colors.white.withOpacity(0.14)],
          ),
          colors: [color],
          isCurved: true,
          barWidth: 2,
          dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                    radius: 2.5,
                    color: Colors.white,
                    strokeWidth: 4,
                    strokeColor: color);
              }));
    } else {
      // 未选中
      return LineChartBarData(
          spots: chartYData[gmvDataType]
              ?.mapIndexed((index, element) =>
                  FlSpot(index.toDouble(), element.toDouble()))
              .toList(),
          dashArray: [6, 6],
          colors: [color],
          isCurved: true,
          barWidth: 1,
          dotData: FlDotData(show: false));
    }
  }

  Widget buildRadioItemWidget(GMVDataType gmvDataType) {
    String text = config[gmvDataType]?["title"]?.toString() ?? "";
    Color color = config[gmvDataType]?["color"];
    return GestureDetector(
      onTap: () {
        selectDataTypeNotifier.value = gmvDataType;
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5),
        child: Row(
          children: [
            Transform.scale(
              scale: 0.5,
              child: Radio(
                groupValue: selectDataType,
                value: gmvDataType,
                onChanged: onGMVDataTypeChanged,
                activeColor: color,
                visualDensity: VisualDensity(horizontal: -4),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            Text(
              text,
              style: TextStyle(
                  color: color,
                  fontSize: 11,
                  fontWeight: selectDataType == gmvDataType
                      ? FontWeight.w500
                      : FontWeight.normal),
            )
          ],
        ),
      ),
    );
  }

  void onGMVDataTypeChanged(GMVDataType? value) {
    if (value != null) {
      selectDataTypeNotifier.value = value;
    }
  }
}

enum GMVDataType { ActuallyPaid, Control, HighGross,CuratedSelect }
