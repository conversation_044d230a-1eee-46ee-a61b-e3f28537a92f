import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomerPrivateDetailMenuWidget extends StatelessWidget {
  final VoidCallback? frequentGoodsClickCallback;
  final VoidCallback? searchRecordClickCallback;
  final VoidCallback? hotSellClickCallback;

  CustomerPrivateDetailMenuWidget(
      {this.frequentGoodsClickCallback,
      this.searchRecordClickCallback,
      this.hotSellClickCallback});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Container(
        color: Colors.white,
        child: Row(
          children: [
            buildItem(
                "assets/images/customer/customer_private_detail_menu_frequent.png",
                "常购商品",
                frequentGoodsClickCallback),
            Container(
              height: 26,
              width: 0.5,
              color: const Color(0xffdddddd),
            ),
            buildItem(
                "assets/images/customer/customer_private_detail_menu_search.png",
                "搜索记录",
                searchRecordClickCallback),
            Container(
              height: 26,
              width: 0.5,
              color: const Color(0xffdddddd),
            ),
            buildItem(
                "assets/images/customer/customer_private_detail_menu_hot.png",
                "热销榜单",
                hotSellClickCallback),
          ],
        ),
      ),
    );
  }

  Widget buildItem(String icon, String title, VoidCallback? clickCallback) {
    return Expanded(
      child: GestureDetector(
        onTap: clickCallback,
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 18),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                icon,
                width: 19,
                height: 19,
              ),
              SizedBox(
                width: 5,
              ),
              Text(
                title,
                style: TextStyle(
                    color: const Color(0xff333333),
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              )
            ],
          ),
        ),
      ),
    );
  }
}
