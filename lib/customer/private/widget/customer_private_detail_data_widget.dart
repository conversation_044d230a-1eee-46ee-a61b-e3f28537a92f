import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/tools/tools.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/widget/ybm_customer_call_phone.dart';
import 'package:XyyBeanSproutsFlutter/utils/click_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CustomerPrivateDetailDataWidget extends StatelessWidget {
  final dynamic orderCount;
  final dynamic refundOrderCount;
  final dynamic couponCount;

  final VoidCallback? orderClickCallback;
  final VoidCallback? refundOrderClickCallback;
  final VoidCallback? couponClickCallback;

  CustomerPrivateDetailDataWidget(
      {this.orderCount,
      this.refundOrderCount,
      this.couponCount,
      this.orderClickCallback,
      this.refundOrderClickCallback,
      this.couponClickCallback});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        buildItem(
            "assets/images/customer/customer_private_detail_menu_order.png",
            "订单",
            "进行中：${orderCount ?? "--"}",
            orderClickCallback),
        SizedBox(
          width: 5,
        ),
        buildItem(
            "assets/images/customer/customer_private_detail_menu_refund_order.png",
            "退单",
            "退单中：${refundOrderCount ?? "--"}",
            refundOrderClickCallback),
        SizedBox(
          width: 5,
        ),
        buildItem(
            "assets/images/customer/customer_private_detail_menu_coupon.png",
            "优惠券",
            "未使用：${couponCount ?? "--"}",
            couponClickCallback),
      ],
    );
  }

  Widget buildItem(
      String icon, String title, String content, VoidCallback? clickCallback) {
    return Expanded(
      child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: GestureDetector(
            onTap: clickCallback,
            behavior: HitTestBehavior.opaque,
            child: Container(
              color: Colors.white,
              padding: EdgeInsets.only(top: 9.5, left: 10, right: 8, bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xff333333)),
                        ),
                      ),
                      Image.asset(
                        icon,
                        width: 19,
                        height: 19,
                      )
                    ],
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  Text(
                    content,
                    style: TextStyle(
                        color: const Color(0xff9494a6),
                        fontSize: 11,
                        fontWeight: FontWeight.normal),
                  )
                ],
              ),
            ),
          )),
    );
  }
}
