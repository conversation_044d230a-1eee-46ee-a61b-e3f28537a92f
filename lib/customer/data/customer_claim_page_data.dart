import 'package:XyyBeanSproutsFlutter/customer/data/customer_claim_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_claim_page_data.g.dart';

@JsonSerializable()
class CustomerClaimPageData extends BaseModelV2<CustomerClaimPageData>{

  String? tips;
  CustomerClaimListData? pageInfo;


  CustomerClaimPageData();

  factory CustomerClaimPageData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerClaimPageDataFromJson(json!);


  @override
  CustomerClaimPageData fromJsonMap(Map<String, dynamic> json) {
    return CustomerClaimPageData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerClaimPageDataToJson(this);
  }
}
