// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_claim_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerClaimListData _$CustomerClaimListDataFromJson(
    Map<String, dynamic> json) {
  return CustomerClaimListData()
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => e == null
            ? null
            : CustomerClaimItemData.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..isLastPage = json['isLastPage'] as bool?;
}

Map<String, dynamic> _$CustomerClaimListDataToJson(
        CustomerClaimListData instance) =>
    <String, dynamic>{
      'list': instance.list,
      'isLastPage': instance.isLastPage,
    };
