import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_sku_collect_data.g.dart';

@JsonSerializable()
class CustomerSkuCollectData extends BaseModelV2<CustomerSkuCollectData> {
  String? skuCollectCode;
  String? skuCollectName;

  /// 认领状态
  dynamic receiveType;

  dynamic oaUserId;
  String? oaUserName;

  @JsonKey(defaultValue: '-')
  dynamic bindCountdown;

  /// 商品集类型  1-普药，2-控销
  dynamic skuCollectType;

  /// 商品集是否被当前账号或其子账号认领 地图模式下使用
  dynamic permissionClaimedFlag;

  dynamic tips;

  bool? isCheck = false;

  CustomerSkuCollectData();

  factory CustomerSkuCollectData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerSkuCollectDataFromJson(json!);

  @override
  CustomerSkuCollectData fromJsonMap(Map<String, dynamic> json) {
    return CustomerSkuCollectData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerSkuCollectDataToJson(this);
  }
}
