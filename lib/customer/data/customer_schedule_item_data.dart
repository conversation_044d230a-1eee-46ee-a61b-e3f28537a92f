import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_schedule_item_data.g.dart';

@JsonSerializable()
class CustomerScheduleItemData extends BaseModelV2<CustomerScheduleItemData>{

  int? id;
  String? scheduleType;
  String? scheduleTypeName;
  String? scheduleTheme;
  int? userId;
  String? userName;
  int? createTime;
  int? merchantId;
  String? merchantName;
  int? effective;
  int? isKp;
  int? perfect;
  String? contactor;
  int? isEffective;
  int? talkTime;

  int? registerFlag;
  int? poiId;
  String? mobile;

  CustomerScheduleItemData();

  factory CustomerScheduleItemData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerScheduleItemDataFromJson(json!);


  @override
  CustomerScheduleItemData fromJsonMap(Map<String, dynamic> json) {
    return CustomerScheduleItemData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerScheduleItemDataToJson(this);
  }
}
