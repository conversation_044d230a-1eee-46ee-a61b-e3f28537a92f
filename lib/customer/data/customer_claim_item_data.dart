import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_claim_item_data.g.dart';

@JsonSerializable()
class CustomerClaimItemData extends BaseModelV2<CustomerClaimItemData>{

  int? id;
  int? poiId;
  int? customerId;
  int? merchantId;
  String? customerName;
  String? ecCustomerName;
  int? oaId;
  String? oaName;
  int? customerRegisterFlag;//是否已注册 1:是 2:否
  int? bindType;//绑定类型 1:BD绑定 2:销运分配 3:BDM分配 4:POI注册自动认领 5:BD释放 6:销运释放 7:系统释放
  int? creatorOaId;
  String? creatorOaName;
  int? createTime;
  int? operationType;
  String? operationName;

  String? skuCollectCode;
  String? skuCollectName;


  CustomerClaimItemData();

  factory CustomerClaimItemData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerClaimItemDataFromJson(json!);


  @override
  CustomerClaimItemData fromJsonMap(Map<String, dynamic> json) {
    return CustomerClaimItemData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerClaimItemDataToJson(this);
  }
}
