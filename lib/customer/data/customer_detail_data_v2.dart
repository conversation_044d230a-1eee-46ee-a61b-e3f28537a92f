import 'package:XyyBeanSproutsFlutter/customer/data/customer_sku_collect_data.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_contact_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_detail_data_v2.g.dart';

@JsonSerializable()
class CustomerDetailDataV2 extends BaseModelV2 {
  dynamic id;

  /// 客户名称
  dynamic customerName;

  /// 客户编号
  dynamic merchantId;

  /// 地址
  dynamic address;

  /// 所属蜂窝名称
  dynamic combName;

  /// 是否连锁
  dynamic chainFlag;

  /// 发票类型
  dynamic invoiceTypeName;

  /// 资质状态
  dynamic licenseStatusName;

  /// 上次采购时间
  dynamic lastPurchaseDays;

  /// 上次拜访时间
  dynamic overLastVisitDays;

  /// 联系人列表，用于打电话拜访
  List<YBMCustomerContactModel>? contactList;

  @JsonKey(name: "registerFlag")
  dynamic isRegister;

  /// 是否注册标识  默认注册
  dynamic get registerFlag => isRegister == true ? 1 : 2;

  /// poiId
  dynamic poiId;

  /// 是否可释放1是，2否
  dynamic unbindFlag;

  /// ec注册名称
  dynamic ecCustomerName;

  /// 是否可分配
  dynamic allocationFlag;

  /// 商品集列表
  List<CustomerSkuCollectData>? bindSkuCollect;

  /// 经度
  dynamic poiLongitude;

  /// 纬度
  dynamic poiLatitude;

  /// 电话拜访使用，当未注册时默认取该电话
  dynamic poiMobilePhone;

  /// 审核状态
  dynamic poiAuditStatus;

  /// 客户等级
  CustomerLevel? crmCustomerLevel;

  CustomerDetailDataV2();

  factory CustomerDetailDataV2.fromJson(Map<String, dynamic> json) =>
      _$CustomerDetailDataV2FromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerDetailDataV2FromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerDetailDataV2ToJson(this);
  }
}

@JsonSerializable()
class CustomerLevel extends BaseModelV2 {
  dynamic customerId;
  dynamic merchantId;
  dynamic tips;
  dynamic lastMonthLevel;
  dynamic lastMonthLevelName;
  dynamic currentLevel;
  dynamic currentLevelName;
  dynamic currentLevelTips;
  dynamic purchaseHistory;
  dynamic thisDifference;
  dynamic rulePay;

  CustomerLevel();

  factory CustomerLevel.fromJson(Map<String, dynamic> json) =>
      _$CustomerLevelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerLevelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerLevelToJson(this);
  }
}
