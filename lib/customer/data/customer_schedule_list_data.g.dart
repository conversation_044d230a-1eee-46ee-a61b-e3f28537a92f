// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_schedule_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerScheduleListData _$CustomerScheduleListDataFromJson(
    Map<String, dynamic> json) {
  return CustomerScheduleListData()
    ..limit = json['limit'] as int?
    ..offset = json['offset'] as int?
    ..total = json['total'] as int?
    ..lastPage = json['lastPage'] as bool?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => e == null
            ? null
            : CustomerScheduleItemData.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$CustomerScheduleListDataToJson(
        CustomerScheduleListData instance) =>
    <String, dynamic>{
      'limit': instance.limit,
      'offset': instance.offset,
      'total': instance.total,
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };
