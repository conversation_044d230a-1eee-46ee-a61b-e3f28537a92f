import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_add_plan_single_time_data.g.dart';

@JsonSerializable()
class CustomerAddPlanSingleTimeData extends BaseModelV2<CustomerAddPlanSingleTimeData> {
  @override
  CustomerAddPlanSingleTimeData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerAddPlanSingleTimeDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerAddPlanSingleTimeDataToJson(this);
  }
}
