import 'package:XYYContainer/XYYContainer.dart';

const String CustomerHistoryKeywordKey = 'customer_history_keyword_key';

class CustomerHistoryManager {
  static Future<List<String>> queryHistoryKeyword() async {
    List<dynamic> history =
        await XYYContainer.storageChannel.getValue(CustomerHistoryKeywordKey) ??
            [];
    return history.map((e) => "$e").toList();
  }

  static Future<bool?> setHistoryKeyword(String keyword) async {
    List<String> historyList =
        await CustomerHistoryManager.queryHistoryKeyword();
    if (historyList.contains(keyword)) {
      historyList.remove(keyword);
    }
    if (historyList.length >= 20) {
      historyList = historyList.sublist(0, 19);
    }
    historyList.insert(0, keyword);
    return XYYContainer.storageChannel
        .put(CustomerHistoryKeywordKey, historyList);
  }

  static void clearHistoryKeyword() {
    XYYContainer.storageChannel.delete(CustomerHistoryKeywordKey);
  }
}
