// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_schedule_item_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerScheduleItemData _$CustomerScheduleItemDataFromJson(
    Map<String, dynamic> json) {
  return CustomerScheduleItemData()
    ..id = json['id'] as int?
    ..scheduleType = json['scheduleType'] as String?
    ..scheduleTypeName = json['scheduleTypeName'] as String?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..userId = json['userId'] as int?
    ..userName = json['userName'] as String?
    ..createTime = json['createTime'] as int?
    ..merchantId = json['merchantId'] as int?
    ..merchantName = json['merchantName'] as String?
    ..effective = json['effective'] as int?
    ..isKp = json['isKp'] as int?
    ..perfect = json['perfect'] as int?
    ..contactor = json['contactor'] as String?
    ..isEffective = json['isEffective'] as int?
    ..talkTime = json['talkTime'] as int?
    ..registerFlag = json['registerFlag'] as int?
    ..poiId = json['poiId'] as int?
    ..mobile = json['mobile'] as String?;
}

Map<String, dynamic> _$CustomerScheduleItemDataToJson(
        CustomerScheduleItemData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'scheduleType': instance.scheduleType,
      'scheduleTypeName': instance.scheduleTypeName,
      'scheduleTheme': instance.scheduleTheme,
      'userId': instance.userId,
      'userName': instance.userName,
      'createTime': instance.createTime,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'effective': instance.effective,
      'isKp': instance.isKp,
      'perfect': instance.perfect,
      'contactor': instance.contactor,
      'isEffective': instance.isEffective,
      'talkTime': instance.talkTime,
      'registerFlag': instance.registerFlag,
      'poiId': instance.poiId,
      'mobile': instance.mobile,
    };
