import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

import 'customer_schedule_item_data.dart';

part 'customer_schedule_list_data.g.dart';

@JsonSerializable()
class CustomerScheduleListData extends BaseModelV2<CustomerScheduleListData> {

  int? limit;
  int? offset;
  int? total;
  bool? lastPage;
  List<CustomerScheduleItemData?>? rows;

  CustomerScheduleListData();

  factory CustomerScheduleListData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerScheduleListDataFromJson(json!);


  @override
  CustomerScheduleListData fromJsonMap(Map<String, dynamic> json) {
    return CustomerScheduleListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerScheduleListDataToJson(this);
  }
}
