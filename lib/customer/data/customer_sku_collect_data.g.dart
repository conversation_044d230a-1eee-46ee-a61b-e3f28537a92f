// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_sku_collect_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerSkuCollectData _$CustomerSkuCollectDataFromJson(
    Map<String, dynamic> json) {
  return CustomerSkuCollectData()
    ..skuCollectCode = json['skuCollectCode'] as String?
    ..skuCollectName = json['skuCollectName'] as String?
    ..receiveType = json['receiveType']
    ..oaUserId = json['oaUserId']
    ..oaUserName = json['oaUserName'] as String?
    ..bindCountdown = json['bindCountdown'] ?? '-'
    ..skuCollectType = json['skuCollectType']
    ..permissionClaimedFlag = json['permissionClaimedFlag']
    ..tips = json['tips']
    ..isCheck = json['isCheck'] as bool?;
}

Map<String, dynamic> _$CustomerSkuCollectDataToJson(
        CustomerSkuCollectData instance) =>
    <String, dynamic>{
      'skuCollectCode': instance.skuCollectCode,
      'skuCollectName': instance.skuCollectName,
      'receiveType': instance.receiveType,
      'oaUserId': instance.oaUserId,
      'oaUserName': instance.oaUserName,
      'bindCountdown': instance.bindCountdown,
      'skuCollectType': instance.skuCollectType,
      'permissionClaimedFlag': instance.permissionClaimedFlag,
      'tips': instance.tips,
      'isCheck': instance.isCheck,
    };
