
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_schedule_init_data.g.dart';

@JsonSerializable()
class CustomerScheduleInitData extends BaseModelV2<CustomerScheduleInitData>{

  String? visitId;
  String? merchantId;
  String? merchantName;
  String? contactor;
  String? mobile;
  String? isEffective;
  String? talkTime;
  String? isRegisterFlag;



  CustomerScheduleInitData();

  factory CustomerScheduleInitData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerScheduleInitDataFromJson(json!);


  @override
  CustomerScheduleInitData fromJsonMap(Map<String, dynamic> json) {
    return CustomerScheduleInitData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerScheduleInitDataToJson(this);
  }
}
