// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_claim_item_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerClaimItemData _$CustomerClaimItemDataFromJson(
    Map<String, dynamic> json) {
  return CustomerClaimItemData()
    ..id = json['id'] as int?
    ..poiId = json['poiId'] as int?
    ..customerId = json['customerId'] as int?
    ..merchantId = json['merchantId'] as int?
    ..customerName = json['customerName'] as String?
    ..ecCustomerName = json['ecCustomerName'] as String?
    ..oaId = json['oaId'] as int?
    ..oaName = json['oaName'] as String?
    ..customerRegisterFlag = json['customerRegisterFlag'] as int?
    ..bindType = json['bindType'] as int?
    ..creatorOaId = json['creatorOaId'] as int?
    ..creatorOaName = json['creatorOaName'] as String?
    ..createTime = json['createTime'] as int?
    ..operationType = json['operationType'] as int?
    ..operationName = json['operationName'] as String?
    ..skuCollectCode = json['skuCollectCode'] as String?
    ..skuCollectName = json['skuCollectName'] as String?;
}

Map<String, dynamic> _$CustomerClaimItemDataToJson(
        CustomerClaimItemData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'poiId': instance.poiId,
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'customerName': instance.customerName,
      'ecCustomerName': instance.ecCustomerName,
      'oaId': instance.oaId,
      'oaName': instance.oaName,
      'customerRegisterFlag': instance.customerRegisterFlag,
      'bindType': instance.bindType,
      'creatorOaId': instance.creatorOaId,
      'creatorOaName': instance.creatorOaName,
      'createTime': instance.createTime,
      'operationType': instance.operationType,
      'operationName': instance.operationName,
      'skuCollectCode': instance.skuCollectCode,
      'skuCollectName': instance.skuCollectName,
    };
