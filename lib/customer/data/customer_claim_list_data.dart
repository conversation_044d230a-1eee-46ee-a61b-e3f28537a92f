import 'package:XyyBeanSproutsFlutter/customer/data/customer_claim_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_claim_list_data.g.dart';

@JsonSerializable()
class CustomerClaimListData extends BaseModelV2<CustomerClaimListData>{

  List<CustomerClaimItemData?>? list;
  bool? isLastPage;


  CustomerClaimListData();

  factory CustomerClaimListData.fromJson(Map<String, dynamic>? json) =>
      _$CustomerClaimListDataFromJson(json!);


  @override
  CustomerClaimListData fromJsonMap(Map<String, dynamic> json) {
    return CustomerClaimListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerClaimListDataToJson(this);
  }
}
