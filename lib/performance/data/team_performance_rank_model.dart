import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'team_performance_rank_model.g.dart';

@JsonSerializable()
class TeamPerformanceRankModel extends BaseModelV2<TeamPerformanceRankModel> {

  dynamic oaId;
  dynamic rankLevel;
  dynamic rankGmv;
  dynamic gmvDiff;
  dynamic levelDiff;
  dynamic postType;
  dynamic gmvText;

  @JsonKey(defaultValue: [])
  List<TeamPerformanceRankItemModel>? rankList;

  TeamPerformanceRankModel();

  @override
  TeamPerformanceRankModel fromJsonMap(Map<String, dynamic> json) {
    return _$TeamPerformanceRankModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TeamPerformanceRankModelToJson(this);
  }

  factory TeamPerformanceRankModel.fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceRankModelFromJson(json);
  }

}

@JsonSerializable()
class TeamPerformanceRankItemModel extends BaseModelV2<TeamPerformanceRankItemModel> {

  /// 排行
  dynamic rank;

  dynamic oaId;
  /// 展示名称
  dynamic userDisplayName;

  dynamic deptPath;

  dynamic gmvAmount;

  TeamPerformanceRankItemModel();

  @override
  TeamPerformanceRankItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$TeamPerformanceRankItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TeamPerformanceRankItemModelToJson(this);
  }

  factory TeamPerformanceRankItemModel.fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceRankItemModelFromJson(json);
  }

}