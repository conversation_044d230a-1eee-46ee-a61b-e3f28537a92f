import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:json_annotation/json_annotation.dart';

part 'team_performance_rank_list_params.g.dart';


/// 团队业绩列表接口参数
@JsonSerializable()
class TeamPerformanceRankListParams {

  /// 1-今天，3-本月
  String timeType = TIME_TYPE_TODAY;

  /// 0-总(实付)gmv，1-优选，2-控销
  String? rankGmvType = SORT_FIELD_REAL_PAY_GMV;

  /// 1-BD,2-KA
  String? rankUserType = USER_TYPE_BD;

  /// 当前用户类型 0-m，1-bd，2-ka
  String? postType = USER_TYPE_M;

  Map<String, dynamic> toJson() {
    return _$TeamPerformanceRankListParamsToJson(this);
  }

  static TeamPerformanceRankListParams fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceRankListParamsFromJson(json);
  }


}