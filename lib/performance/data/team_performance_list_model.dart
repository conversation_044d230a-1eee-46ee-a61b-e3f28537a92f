import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'team_performance_list_model.g.dart';

@JsonSerializable()
class TeamPerformanceListDataModel extends BaseModelV2<TeamPerformanceListDataModel> {

  /// 是否CM，PM，RM账号
  dynamic mangerFlag;

  /// 是否是最后一级
  dynamic lastPageFlag;

  /// 是否有下一页
  dynamic hasNextPage;

  // true-有筛选项；false-无筛选项
  bool? gmvFilter;

  @JsonKey(defaultValue: [])
  List<TeamPerformanceListItemModel>? nodeList;

  TeamPerformanceListDataModel();

  @override
  TeamPerformanceListDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$TeamPerformanceListDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TeamPerformanceListDataModelToJson(this);
  }

  factory TeamPerformanceListDataModel.fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceListDataModelFromJson(json);
  }

}

@JsonSerializable()
class TeamPerformanceListItemModel extends BaseModelV2<TeamPerformanceListItemModel> {

  /// 部门code
  dynamic groupId;
  /// 展示名称
  @JsonKey(defaultValue: '')
  dynamic displayName;
  /// 部门路径或部门负责人
  @JsonKey(defaultValue: '')
  dynamic smallText;
  /// 人员oaId
  dynamic userOaId;
  /// 是否有子节点
  @JsonKey(defaultValue: false)
  dynamic haveChild;

  @JsonKey(defaultValue: [])
  List<TeamPerformanceListTypeModel>? gmvList;

  TeamPerformanceListItemModel();

  @override
  TeamPerformanceListItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$TeamPerformanceListItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TeamPerformanceListItemModelToJson(this);
  }

  factory TeamPerformanceListItemModel.fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceListItemModelFromJson(json);
  }
}

@JsonSerializable()
class TeamPerformanceListTypeModel extends BaseModelV2<TeamPerformanceListTypeModel> {
  /// gmv类型；0-实付，1-优选。2-控销
  dynamic gmvType;
  /// gmv名称
  dynamic gmvText;
  /// gmv
  dynamic gmvAmount;
  /// 同比
  dynamic gmvRate;
  /// 是否选中排序
  dynamic sortFlag;

  TeamPerformanceListTypeModel();

  @override
  TeamPerformanceListTypeModel fromJsonMap(Map<String, dynamic> json) {
    return _$TeamPerformanceListTypeModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TeamPerformanceListTypeModelToJson(this);
  }

  factory TeamPerformanceListTypeModel.fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceListTypeModelFromJson(json);
  }
  
}