import 'package:json_annotation/json_annotation.dart';

part 'team_performance_list_params.g.dart';

/// 事件类型
const FILTER_TYPE_DATE = '0';

/// 时间类型 - 当日
const String TIME_TYPE_TODAY = '1';
/// 时间类型 - 本月
const String TIME_TYPE_THIS_MOUTH = '3';

/// GMV类型 - 实付GMV
const String SORT_FIELD_REAL_PAY_GMV = '0';
/// GMV类型 - 优选GMV
const String SORT_FIELD_GROSS_GMV = '1';
/// GMV类型 - 控销GMV
const String SORT_FIELD_CONTROL_GMV = '2';

/// GMV类型 - 甄选GMV
const String SORT_FIELD_SELECT_GMV = '3';

/// 排序类型 - 升序
const String SORT_TYPE_ASCE = '0';
/// 排序类型 - 降序
const String SORT_TYPE_DESC = '1';

/// 用户类型 - M级
const String USER_TYPE_M = '0';
/// 用户类型 - BD
const String USER_TYPE_BD = '1';
/// 用户类型 - KA
const String USER_TYPE_KA = '2';
/// 用户类型 - 电销
const String USER_TYPE_DX = '3';


/// 团队业绩列表接口参数
@JsonSerializable()
class TeamPerformanceListParams {
  /// 部门code
  String? groupId;

  /// 1-今天，3-本月
  String timeType = TIME_TYPE_TODAY;

  /// 0-总(实付)gmv，1-优选，2-控销
  String? sortField = SORT_FIELD_REAL_PAY_GMV;

  /// 0-正序，1-倒序
  String? sortType = SORT_TYPE_DESC;

  /// 1-BD,2-KA
  String? userType;

  /// 页码
  String? offset;

  /// 不传或0-全量业绩；1-属地业绩
  String? gmvFilter;

  Map<String, dynamic> toJson() {
    var map = _$TeamPerformanceListParamsToJson(this);
    if (groupId == null) {
      map.remove('groupId');
    }
    if (sortField == null) {
      map.remove('sortField');
    }
    if (sortType == null) {
      map.remove('sortType');
    }
    if (userType == null) {
      map.remove('userType');
    }
    if (offset == null) {
      map.remove('offset');
    }
    if (gmvFilter == null) {
      map.remove('gmvFilter');
    }
    return map;
  }

  static TeamPerformanceListParams fromJson(Map<String, dynamic> json) {
    return _$TeamPerformanceListParamsFromJson(json);
  }


}