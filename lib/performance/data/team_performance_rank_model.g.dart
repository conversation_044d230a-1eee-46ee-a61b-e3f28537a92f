// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_performance_rank_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamPerformanceRankModel _$TeamPerformanceRankModelFromJson(
    Map<String, dynamic> json) {
  return TeamPerformanceRankModel()
    ..oaId = json['oaId']
    ..rankLevel = json['rankLevel']
    ..rankGmv = json['rankGmv']
    ..gmvDiff = json['gmvDiff']
    ..levelDiff = json['levelDiff']
    ..postType = json['postType']
    ..gmvText = json['gmvText']
    ..rankList = (json['rankList'] as List<dynamic>?)
            ?.map((e) => TeamPerformanceRankItemModel.fromJson(
                e as Map<String, dynamic>))
            .toList() ??
        [];
}

Map<String, dynamic> _$TeamPerformanceRankModelToJson(
        TeamPerformanceRankModel instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'rankLevel': instance.rankLevel,
      'rankGmv': instance.rankGmv,
      'gmvDiff': instance.gmvDiff,
      'levelDiff': instance.levelDiff,
      'postType': instance.postType,
      'gmvText': instance.gmvText,
      'rankList': instance.rankList,
    };

TeamPerformanceRankItemModel _$TeamPerformanceRankItemModelFromJson(
    Map<String, dynamic> json) {
  return TeamPerformanceRankItemModel()
    ..rank = json['rank']
    ..oaId = json['oaId']
    ..userDisplayName = json['userDisplayName']
    ..deptPath = json['deptPath']
    ..gmvAmount = json['gmvAmount'];
}

Map<String, dynamic> _$TeamPerformanceRankItemModelToJson(
        TeamPerformanceRankItemModel instance) =>
    <String, dynamic>{
      'rank': instance.rank,
      'oaId': instance.oaId,
      'userDisplayName': instance.userDisplayName,
      'deptPath': instance.deptPath,
      'gmvAmount': instance.gmvAmount,
    };
