// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_performance_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TeamPerformanceListDataModel _$TeamPerformanceListDataModelFromJson(
    Map<String, dynamic> json) {
  return TeamPerformanceListDataModel()
    ..mangerFlag = json['mangerFlag']
    ..lastPageFlag = json['lastPageFlag']
    ..hasNextPage = json['hasNextPage']
    ..gmvFilter = json['gmvFilter'] as bool?
    ..nodeList = (json['nodeList'] as List<dynamic>?)
            ?.map((e) => TeamPerformanceListItemModel.fromJson(
                e as Map<String, dynamic>))
            .toList() ??
        [];
}

Map<String, dynamic> _$TeamPerformanceListDataModelToJson(
        TeamPerformanceListDataModel instance) =>
    <String, dynamic>{
      'mangerFlag': instance.mangerFlag,
      'lastPageFlag': instance.lastPageFlag,
      'hasNextPage': instance.hasNextPage,
      'gmvFilter': instance.gmvFilter,
      'nodeList': instance.nodeList,
    };

TeamPerformanceListItemModel _$TeamPerformanceListItemModelFromJson(
    Map<String, dynamic> json) {
  return TeamPerformanceListItemModel()
    ..groupId = json['groupId']
    ..displayName = json['displayName'] ?? ''
    ..smallText = json['smallText'] ?? ''
    ..userOaId = json['userOaId']
    ..haveChild = json['haveChild'] ?? false
    ..gmvList = (json['gmvList'] as List<dynamic>?)
            ?.map((e) => TeamPerformanceListTypeModel.fromJson(
                e as Map<String, dynamic>))
            .toList() ??
        [];
}

Map<String, dynamic> _$TeamPerformanceListItemModelToJson(
        TeamPerformanceListItemModel instance) =>
    <String, dynamic>{
      'groupId': instance.groupId,
      'displayName': instance.displayName,
      'smallText': instance.smallText,
      'userOaId': instance.userOaId,
      'haveChild': instance.haveChild,
      'gmvList': instance.gmvList,
    };

TeamPerformanceListTypeModel _$TeamPerformanceListTypeModelFromJson(
    Map<String, dynamic> json) {
  return TeamPerformanceListTypeModel()
    ..gmvType = json['gmvType']
    ..gmvText = json['gmvText']
    ..gmvAmount = json['gmvAmount']
    ..gmvRate = json['gmvRate']
    ..sortFlag = json['sortFlag'];
}

Map<String, dynamic> _$TeamPerformanceListTypeModelToJson(
        TeamPerformanceListTypeModel instance) =>
    <String, dynamic>{
      'gmvType': instance.gmvType,
      'gmvText': instance.gmvText,
      'gmvAmount': instance.gmvAmount,
      'gmvRate': instance.gmvRate,
      'sortFlag': instance.sortFlag,
    };
