import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_item_config_order.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// 业绩列表item
class TeamPerformanceListItem extends StatefulWidget {
  final int index;
  final TeamPerformanceListItemModel itemModel;
  final bool isShowContent;

  TeamPerformanceListItem(this.index, this.itemModel, {this.isShowContent = true});

  @override
  State<StatefulWidget> createState() => TeamPerformanceListItemState();
}

class TeamPerformanceListItemState extends State<TeamPerformanceListItem> {
  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(9),
      ),
      elevation: 0,
      margin: EdgeInsets.only(left: 10, right: 10, top: 10),
      clipBehavior: Clip.hardEdge,
      child: Container(
        child: Column(
          children: [
            _getHeader(),
            _getContent(),
          ],
        ),
      ),
    );
  }

  Widget _getHeader() {
    var configOrder = _getHeadConfigOrder(widget.index);
    final itemModel = this.widget.itemModel;
    return Container(
        height: 50,
        padding: EdgeInsets.only(left: 10),
        decoration: BoxDecoration(
            gradient: LinearGradient(
          colors: [Color(configOrder.colorLeft), Color(configOrder.colorRight)],
        )),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(right: 48),
              child: Row(
                children: [
                  Spacer(),
                  Image.asset(
                    'assets/images/performance/team_performance_item_bg.png',
                    width: 104,
                    height: 48,
                  ),
                ],
              ),
            ),
            Row(
              children: [
                Container(
                  width: 32,
                  margin: EdgeInsets.only(right: 12),
                  child: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      Image.asset(
                        configOrder.orderIcon,
                        width: 26,
                        height: 32,
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 5),
                        child: Text(
                          _getRankNum(widget.index),
                          style: TextStyle(color: Color(0xFFF7F7F8), fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      itemModel.displayName,
                      style: TextStyle(color: Color(configOrder.titleColor), fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    Visibility(
                      visible: !(itemModel.smallText == null || itemModel.smallText == ''),
                      child: Text(
                        itemModel.smallText,
                        style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 11,
                        ),
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Padding(
                  padding: EdgeInsets.only(right: 18),
                  child: Visibility(
                    visible: itemModel.haveChild,
                    child: Image.asset(
                      'assets/images/performance/team_performance_entry_arrow.png',
                      width: 22,
                      height: 22,
                      alignment: Alignment.centerRight,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ));
  }

  Widget _getContent() {
    if (!this.widget.isShowContent) {
      return Container();
    }
    List<TeamPerformanceListTypeModel>? gmvList = this.widget.itemModel.gmvList;
    if (gmvList == null || gmvList.length < 3) {
      return Spacer();
    }
    return Container(
      margin: EdgeInsets.fromLTRB(10, 0, 10, 0),
      // height: 90,
      padding: EdgeInsets.fromLTRB(0, 12, 0, 8),
      decoration: BoxDecoration(
        color: Color(0xFFFEFEFE),
      ),
      child: Column(children: [
        Row(
          children: [
            _getContentItem(gmvList[0]),
            Container(
              height: 60, // 设置高度
              child: VerticalDivider(
                width: 1,
                thickness: 1,
                color: Color(0xFFF5F5F5),
                indent: 12,
                endIndent: 12,
              ),
            ),
            _getContentItem(gmvList[1]),
          ],
        ),
        Row(
          children: [
            _getContentItem(gmvList[2]),
            Container(
              height: 60, // 设置高度
              child: VerticalDivider(
                width: 1,
                thickness: 1,
                color: Color(0xFFF5F5F5),
                indent: 12,
                endIndent: 12,
              ),
            ),
            _getContentItem(gmvList[3]),
          ],
        ),
      ]),
    );
  }

  Widget _getContentItem(TeamPerformanceListTypeModel gmvModel) {
    return Expanded(
      flex: 1,
      child: Padding(
        padding: const EdgeInsets.only(left: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              child: Row(children: [
                Container(
                    height: 18,
                    padding: const EdgeInsets.only(left: 5, right: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(9)),
                      color: gmvModel.sortFlag ? Color(0xFF00B377) : Color(0xFFFFFFFF),
                    ),
                    child: Text(
                      gmvModel.gmvText,
                      style: TextStyle(
                        fontSize: 12,
                        color: gmvModel.sortFlag ? Color(0xFFFEFEFE) : Color(0xFF676773),
                        fontWeight: FontWeight.w500,
                      ),
                    )),
                SizedBox(width: 4),
                Text(_generateGmvRateValue(gmvModel.gmvRate), style: TextStyle(color: Color(_generateGmvRateColor(gmvModel.gmvRate)), fontSize: 11)),
                SizedBox(width: 4),
                if (gmvModel.gmvRate != 0)
                  Transform.translate(
                    offset: Offset(0, -4),
                    child: Image.asset(
                      _generateGmvRateImgUrl(gmvModel.gmvRate),
                      width: 8,
                      height: 11,
                    ),
                  )
              ]),
            ),
            Container(
              margin: const EdgeInsets.only(top: 8, bottom: 9, left: 5),
              child: Text(
                gmvModel.gmvAmount,
                style: TextStyle(color: Color(0xFF0D0E10), fontSize: 18, fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _generateGmvRateImgUrl(num rate) {
    if (rate > 0) return 'assets/images/performance/team_performance_up.png';
    if (rate < 0) return 'assets/images/performance/team_performance_down.png';
    return '';
  }

  String _generateGmvRateValue(num rate) {
    if (rate > 0) return '+$rate%';
    if (rate < 0) return '$rate%';
    return '—';
  }

  int _generateGmvRateColor(num rate) {
    if (rate > 0) return 0xFF00B377;
    if (rate < 0) return 0xFFFF2121;
    return 0xFFFF7200;
  }

  TeamPerformanceListItemConfigOrder _getHeadConfigOrder(int index) {
    switch (index) {
      case 0:
        return TeamPerformanceListItemConfigOrder('assets/images/performance/team_performance_order1.png', 0xFFD02834, 0xFFFFE6E3, 0xFFFFFFFF);
      case 1:
        return TeamPerformanceListItemConfigOrder('assets/images/performance/team_performance_order2.png', 0xFFD9851B, 0xFFFFEBD6, 0xFFFFFFFF);
      case 2:
        return TeamPerformanceListItemConfigOrder('assets/images/performance/team_performance_order3.png', 0xFF4A71D4, 0xFFDFEBFF, 0xFFFFFFFF);
      default:
        return TeamPerformanceListItemConfigOrder('assets/images/performance/team_performance_order_other.png', 0xFF0D0E10, 0xFFE9E9E9, 0xFFFFFFFF);
    }
  }

  String _getRankNum(int index) {
    if (index < 9)
      return '0${index + 1}';
    else
      return '${index + 1}';
  }
}
