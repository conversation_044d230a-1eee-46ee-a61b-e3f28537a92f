import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

typedef OnFooterClickCallback = void Function();

/// 团队业绩列表footer
class TeamPerformanceListFooterToRankWidget extends StatefulWidget {
  /// 排行榜回调
  OnFooterClickCallback? onFooterClickRankCallback;

  TeamPerformanceListFooterToRankWidget({this.onFooterClickRankCallback});

  @override
  State<StatefulWidget> createState() {
    return TeamPerformanceListFooterToRankState();
  }
}

class TeamPerformanceListFooterToRankState extends State<TeamPerformanceListFooterToRankWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 49,
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          Divider(
            height: 0.5,
            color: Color(0xFFE5E5E5),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  if (widget.onFooterClickRankCallback != null) {
                    widget.onFooterClickRankCallback!();
                  }
                },
                child: RichText(
                  text: TextSpan(style: DefaultTextStyle.of(context).style, children: [
                    TextSpan(
                        text: '排行榜',
                        style: TextStyle(color: Color(0xFF292933), fontSize: 14, fontWeight: FontWeight.w500)),
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Padding(
                        padding: EdgeInsets.only(left: 2),
                        child: Image.asset(
                          'assets/images/item_arrow.png',
                          width: 12,
                          height: 12,
                        ),
                      ),
                    ),
                  ]),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
