import 'package:XyyBeanSproutsFlutter/home/<USER>/home_user_level_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// 排行榜item头部
class TeamPerformanceRankItemHeadWidget extends StatefulWidget {

  /// KA 或 BD
  String userType;

  /// 筛选类型
  String filterType;

  TeamPerformanceRankItemHeadWidget({required this.userType, required this.filterType});

  @override
  State<StatefulWidget> createState() {
    return TeamPerformanceRankItemHeadState();
  }
}

class TeamPerformanceRankItemHeadState extends State<TeamPerformanceRankItemHeadWidget> {
  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4)),
      ),
      margin: EdgeInsets.zero,
      elevation: 0,
      clipBehavior: Clip.hardEdge,
      child: Container(
        height: 32,
        child: Row(
          children: [
            Container(
              height: 32,
              width: 66,
              alignment: Alignment.center,
              child: Text(
                '排名',
                style: TextStyle(fontSize: 12, color: Color(0xFF676773)),
              ),
            ),
            Expanded(
              child: Container(
                height: 32,
                alignment: Alignment.centerLeft,
                child: Text(
                  _getUserName(this.widget.userType),
                  style: TextStyle(fontSize: 12, color: Color(0xFF676773)),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(right: 15),
              alignment: Alignment.center,
              child: Text(
                _getFilterName(this.widget.filterType),
                style: TextStyle(fontSize: 12, color: Color(0xFF676773)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getUserName(String userType) {
    switch(userType) {
      case USER_TYPE_BD:
        return 'BD';
      default:
        return 'KA';
    }
  }

  String _getFilterName(String filterType) {
    switch(filterType) {
      case SORT_FIELD_REAL_PAY_GMV:
        return '实付GMV';
      case SORT_FIELD_GROSS_GMV:
        return '优选GMV';
      default:
        return '控销GMV';
    }
  }
}
