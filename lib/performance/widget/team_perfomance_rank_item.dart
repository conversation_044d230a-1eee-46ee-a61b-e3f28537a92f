import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_item_config_order.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_rank_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// 排行榜item
class TeamPerformanceRankListItemWidget extends StatefulWidget {
  final int index;
  final TeamPerformanceRankItemModel itemModel;

  TeamPerformanceRankListItemWidget(this.index, this.itemModel);

  @override
  State<StatefulWidget> createState() {
    return TeamPerformanceRankListItemState();
  }
}

class TeamPerformanceRankListItemState extends State<TeamPerformanceRankListItemWidget> {
  @override
  Widget build(BuildContext context) {
    var itemModel = this.widget.itemModel;
    var rankIndex = itemModel.rank - 1;
    var configOrder = _getHeadConfigOrder(rankIndex);
    return Container(
      height: 55,
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          gradient: LinearGradient(
        colors: [Color(configOrder.colorLeft), Color(configOrder.colorRight)],
      )),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _getRankNum(itemModel.rank, configOrder.orderIcon),
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      itemModel.userDisplayName,
                      style: TextStyle(color: Color(configOrder.titleColor), fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    Text(
                      itemModel.deptPath,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Color(0xFF676773),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 20,
              ),
              Padding(
                padding: EdgeInsets.only(right: 18),
                child: Text(
                  itemModel.gmvAmount,
                  textAlign: TextAlign.end,
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF0D0E10),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          Visibility(
            visible: itemModel.rank > 3,
            child: Divider(
              height: 1,
              thickness: 1,
              color: Color(0xfff5f5f5),
              indent: 15,
              endIndent: 15,
            ),
          ),
        ],
      ),
    );
  }
}

Widget _getRankNum(int rank, String orderIcon) {
  if (rank < 4) {
    return Container(
      width: 32,
      margin: EdgeInsets.only(left: 22, right: 12),
      alignment: Alignment.centerLeft,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Image.asset(
            orderIcon,
            width: 26,
            height: 32,
          ),
          Container(
            margin: EdgeInsets.only(top: 5),
            child: Text(
              '$rank',
              style: TextStyle(color: Color(0xFFF7F7F8), fontSize: 14),
            ),
          ),
        ],
      ),
    );
  } else {
    return Container(
        width: 66,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '$rank',
              style: TextStyle(
                fontSize: 20,
                color: Color(0xFFADADAD),
                fontWeight: FontWeight.w600
              ),
            ),
            Container(
              height: 2,
              width: 10,
              margin: EdgeInsets.only(top: 5),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(0.5),
                color: Color(0xFFADADAD),
              ),
            ),
          ],
        ));
  }
}

TeamPerformanceListItemConfigOrder _getHeadConfigOrder(int index) {
  switch (index) {
    case 0:
      return TeamPerformanceListItemConfigOrder(
          'assets/images/performance/team_performance_order1.png', 0xFFD02834, 0xFFFFE6E3, 0xFFFFFFFF);
    case 1:
      return TeamPerformanceListItemConfigOrder(
          'assets/images/performance/team_performance_order2.png', 0xFFD9851B, 0xFFFFEBD6, 0xFFFFFFFF);
    case 2:
      return TeamPerformanceListItemConfigOrder(
          'assets/images/performance/team_performance_order3.png', 0xFF4A71D4, 0xFFDFEBFF, 0xFFFFFFFF);
    default:
      return TeamPerformanceListItemConfigOrder(
          'assets/images/performance/team_performance_order_other.png', 0xFF0D0E10, 0xFFFFFFFF, 0xFFFFFFFF);
  }
}
