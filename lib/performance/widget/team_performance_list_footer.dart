import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

typedef OnFooterClickCallback = void Function();

/// 团队业绩列表footer
class TeamPerformanceListFooterWidget extends StatefulWidget {

  /// BD回调
  OnFooterClickCallback? onFooterClickBDCallback;
  /// KA回调
  OnFooterClickCallback? onFooterClickKACallback;

  TeamPerformanceListFooterWidget({this.onFooterClickBDCallback, this.onFooterClickKACallback});

  @override
  State<StatefulWidget> createState() {
    return TeamPerformanceListFooterState();
  }
}

class TeamPerformanceListFooterState extends State<TeamPerformanceListFooterWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 49,
      child: Column(
        children: [
          Divider(
            height: 0.5,
            color: Color(0xFFE5E5E5),
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Color(0xFFFFFFFF),
                  ),
                  child: GestureDetector(
                    onTap: (){
                      if (widget.onFooterClickBDCallback != null) {
                        widget.onFooterClickBDCallback!();
                      }
                    },
                    child: RichText(
                      text: TextSpan(style: DefaultTextStyle.of(context).style, children: [
                        TextSpan(
                            text: '查看下属所有BD',
                            style: TextStyle(
                              color: Color(0xFF292933),
                              fontSize: 14,
                            )
                        ),
                        WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: Padding(
                            padding: EdgeInsets.only(left: 2),
                            child: Image.asset(
                              'assets/images/item_arrow.png',
                              width: 12,
                              height: 12,
                            ),
                          ),
                        ),
                      ]),
                    ),
                  ),
                ),
              ),
              VerticalDivider(
                width: 1,
                thickness: 1,
                color: Color(0xFFE5E5E5),
                indent: 15,
                endIndent: 15,
              ),
              Expanded(
                child: Container(
                  height: 48,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Color(0xFFFFFFFF),
                  ),
                  child: GestureDetector(
                    onTap: (){
                      if (widget.onFooterClickKACallback != null) {
                        widget.onFooterClickKACallback!();
                      }
                    },
                    child: RichText(
                      text: TextSpan(style: DefaultTextStyle.of(context).style, children: [
                        TextSpan(
                            text: '查看下属所有KA',
                            style: TextStyle(
                              color: Color(0xFF292933),
                              fontSize: 14,
                            )
                        ),
                        WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: Padding(
                            padding: EdgeInsets.only(left: 2),
                            child: Image.asset(
                              'assets/images/item_arrow.png',
                              width: 12,
                              height: 12,
                            ),
                          ),
                        ),
                      ]),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
