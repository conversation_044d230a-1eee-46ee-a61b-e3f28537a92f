import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_rank_model.dart';
import 'package:flutter/cupertino.dart';

/// 业绩排行榜footer
class TeamPerformanceRankListFooterWidget extends StatefulWidget {
  TeamPerformanceRankModel rankModel;

  TeamPerformanceRankListFooterWidget({required this.rankModel});

  @override
  State<StatefulWidget> createState() {
    return TeamPerformanceRankListFooterState();
  }
}

class TeamPerformanceRankListFooterState extends State<TeamPerformanceRankListFooterWidget> {
  @override
  Widget build(BuildContext context) {
    var rankModel = this.widget.rankModel;
    return Container(
      height: 62,
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(color: Color(0xFFFFFFFF)),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            margin: EdgeInsets.only(left: 13),
            transform: Matrix4.translationValues(0, -7, 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              color: Color(0xFF00B377),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  '${rankModel.rankLevel}',
                  style: TextStyle(
                    fontSize: 20,
                    color: Color(0xFFFFFFFF),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '我的排名',
                  style: TextStyle(
                    fontSize: 10,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 9),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        rankModel.gmvText,
                        style: TextStyle(
                          fontSize: 15,
                          color: Color(0xFF0D0E10),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 25),
                        child: Text(
                          '${rankModel.rankGmv}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Color(0xFF0D0E10),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 3),
                    child: _getBottomDes(rankModel),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getBottomDes(TeamPerformanceRankModel rankModel) {
    var diffGMV = rankModel.gmvDiff;
    var diffGMVStr = (diffGMV as num).abs();
    var diffLevel = rankModel.levelDiff;
    var diffLevelStr = (diffLevel as num).abs();
    var preText;
    if (rankModel.rankLevel == 1) {
      preText = '领先第2名 ';
    } else {
      preText = '距第${rankModel.rankLevel-1}名还差 ';
    }
    var postText;
    if (diffLevel > 0) {
      postText = ' 比昨天上升';
    } else if (diffLevel < 0) {
      postText = ' 比昨天下降';
    } else {
      postText = ' 与昨天排名相同';
    }
    return RichText(
      text: TextSpan(
        style: DefaultTextStyle.of(context).style,
        children: [
          TextSpan(text: preText, style: TextStyle(color: Color(0xFF676773), fontSize: 12),),
          TextSpan(text: '$diffGMVStr元', style: TextStyle(color: Color(0xFFFF0000), fontSize: 12, fontWeight: FontWeight.w500),),
          TextSpan(text: ',', style: TextStyle(color: Color(0xFF676773), fontSize: 12),),
          TextSpan(text: postText, style: TextStyle(color: Color(0xFF676773), fontSize: 12),),
          TextSpan(
              text: diffLevel == 0 ? '' : '$diffLevelStr名',
              style: TextStyle(color: Color(diffGMV > 0 ? 0xFF00B377 : 0xFFFF0000), fontSize: 12, fontWeight: FontWeight.w500),),
        ],
      ),
    );
  }
}
