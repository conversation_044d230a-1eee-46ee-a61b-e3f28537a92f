import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/button/sort_button_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_multi_status_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_select_auth.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/base/team_performance_base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

/// 团队业绩列表
abstract class TeamPerformanceListBasePage extends TeamPerformanceBasePage {
  String? paramsModelJson;

  @override
  TeamPerformanceBasePageState initState();
}

abstract class TeamPerformanceListBasePageState<T extends TeamPerformanceListBasePage> extends TeamPerformanceBasePageState<T> {
  /// 是否是 最后一页
  bool isLastPage = true;

  // true-有筛选项；false-无筛选项
  bool gmvFilterShow = false;

  /// 数据源
  List<dynamic> dataSource = [];

  /// 请求相关参数
  TeamPerformanceListParams paramsModel = TeamPerformanceListParams();

  var offset = 1;

  GlobalKey _titleButtonKey = GlobalKey();
  String get title => getTitleName();  // 使用 getter
  // DropButtonController _titleController = DropButtonController(model: DropButtonModel(normalText: title));
  DropButtonController get _titleController => DropButtonController(
      model: DropButtonModel(normalText: title)
  );

  List<UserAuthModel> authModels = [
    UserAuthModel()
      ..roleCode = '0'
      ..roleName = '全量业绩',
    UserAuthModel()
      ..roleCode = '1'
      ..roleName = '属地业绩',
  ];


  @override
  void onCreate() {
    super.onCreate();
    if (this.widget.paramsModelJson != null) {
      paramsModel = TeamPerformanceListParams.fromJson(json.decode(this.widget.paramsModelJson!));
      multiFilterConfig = _generateConfigFilters(paramsModel);
    }
    showLoadingDialog();
    this.requestListData();
  }

  List<dynamic> _generateConfigFilters(TeamPerformanceListParams params) {
    var resultList = <dynamic>[];
    return resultList
      ..add(CommonDropMultiConfigModel(
          defaultTitle: params.timeType == TIME_TYPE_TODAY && isShowDateFilter ? '今天' : '本月',
          paramKey: FILTER_TYPE_DATE,
          isShowImage: isShowDateFilter,
          selectTitle: params.timeType == TIME_TYPE_TODAY && isShowDateFilter ? '今天' : '本月'))
      ..add(CommonDropConfigMultiStatusModel(
        paramKey: SORT_FIELD_REAL_PAY_GMV,
        title: "实付",
        isSelected: _isSelected(params, SORT_FIELD_REAL_PAY_GMV),
        type: _getSortType(params, SORT_FIELD_REAL_PAY_GMV),
      ))
      ..add(CommonDropConfigMultiStatusModel(
        paramKey: SORT_FIELD_GROSS_GMV,
        title: "优选",
        isSelected: _isSelected(params, SORT_FIELD_GROSS_GMV),
        type: _getSortType(params, SORT_FIELD_GROSS_GMV),
      ))
      ..add(CommonDropConfigMultiStatusModel(
        paramKey: SORT_FIELD_CONTROL_GMV,
        title: "控销",
        isSelected: _isSelected(params, SORT_FIELD_CONTROL_GMV),
        type: _getSortType(params, SORT_FIELD_CONTROL_GMV),
      ))
      ..add(CommonDropConfigMultiStatusModel(
        paramKey: SORT_FIELD_SELECT_GMV,
        title: "甄选",
        isSelected: _isSelected(params, SORT_FIELD_SELECT_GMV),
        type: _getSortType(params, SORT_FIELD_SELECT_GMV),
      ));
  }

  SortButtonWidgetType _getSortType(TeamPerformanceListParams params, String sortFiled) {
    if (_isSelected(params, sortFiled)) {
      return params.sortType == SORT_TYPE_ASCE ? SortButtonWidgetType.asce : SortButtonWidgetType.desc;
    }
    return SortButtonWidgetType.normal;
  }

  bool _isSelected(TeamPerformanceListParams params, String sortFiled) {
    return params.sortField == sortFiled;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return super.buildWidget(context);
  }


  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      // getTitleName(),
      "",
      titleWidget: getTitleWidget(),
      leftType: LeftButtonType.back,
      rightButtons: [_getRightButton()],
    );
  }

  Widget getTitleWidget() {
    if (!gmvFilterShow) {
      return Container(
        child: Text(
          getTitleName(),
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else {
      return Container(
        child: DropControllerButton(
          key: _titleButtonKey,
          title: '',
          controller: _titleController,
          maxTextWidth: 200,
          normalStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          selectedStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          onPressed: (controller) {
            showAuthSelectPopover(
              context: context,
              anchorKey: _titleButtonKey,
              models: authModels,
              clickAction: (model) {
                 controller.value.normalText = model.roleName;
                 paramsModel.gmvFilter = model.roleCode;
                 refreshController.callRefresh();
              },
            ).then((value) {
              controller.setIsOpen(false);
            });
          },
        ),
      );
    }
  }



  Widget _getRightButton() {
    return Container(
      padding: EdgeInsets.only(right: 10),
      child: TextButton(
        onPressed: filterClick,
        child: Image.asset(
          'assets/images/customer/customer_tab_filter.png',
          width: 22,
          height: 22,
        ),
        style: ButtonStyle(
          overlayColor: MaterialStateProperty.all<Color>(Colors.transparent),
          padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.all(10)),
          minimumSize: MaterialStateProperty.all<Size>(Size.zero),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }

  /// 过滤事件
  void filterClick() {}

  List<dynamic> multiFilterConfig = [
    CommonDropMultiConfigModel(defaultTitle: "今日", paramKey: FILTER_TYPE_DATE, selectTitle: '今日'),
    CommonDropConfigMultiStatusModel(paramKey: SORT_FIELD_REAL_PAY_GMV, title: "实付", isSelected: true, type: SortButtonWidgetType.desc),
    CommonDropConfigMultiStatusModel(paramKey: SORT_FIELD_GROSS_GMV, title: "优选"),
    CommonDropConfigMultiStatusModel(paramKey: SORT_FIELD_CONTROL_GMV, title: "控销"),
    CommonDropConfigMultiStatusModel(paramKey: SORT_FIELD_SELECT_GMV, title: "甄选"),
  ];

  @override
  List<dynamic> get filterConfig => multiFilterConfig;

  @override
  bool get isNoMore => isLastPage;

  @override
  bool get isSearch => false;

  @override
  int get itemCount => dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => onItemClick;

  Widget onItemClick(BuildContext context, int index);

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  @override
  Future<void> refreshList() async {
    this.requestListData();
  }

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey, CommonDropMultiConfigModel model, DropButtonController controller) async {
    switch (model.paramKey) {

      /// 日期
      case FILTER_TYPE_DATE:
        if (!isShowDateFilter) return;
        await showCommonFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '今日', code: TIME_TYPE_TODAY),
                CustomerConditionModel(text: '本月', code: TIME_TYPE_THIS_MOUTH),
              ],
              selectedCode: paramsModel.timeType,
              selectAction: (value) {
                paramsModel.timeType = "${value.code}";
                controller.setSelectText("${value.text}");
                if (value.code == TIME_TYPE_TODAY) {
                  paramsModel.timeType = '1';
                } else
                  paramsModel.timeType = '3';
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
    }
  }

  /// 是否显示时间下拉
  get isShowDateFilter;

  @override
  void multiFilterSelected(CommonDropConfigMultiStatusModel model) {
    switch (model.type) {

      /// 升序
      case SortButtonWidgetType.asce:
        paramsModel.sortType = SORT_TYPE_ASCE;
        break;

      /// 降序
      case SortButtonWidgetType.desc:
        paramsModel.sortType = SORT_TYPE_DESC;
        break;

      /// 默认未选中
      default:
        paramsModel.sortField = null;
        paramsModel.sortType = null;
        return;
    }
    switch (model.paramKey) {

      /// 实付GMV
      case SORT_FIELD_REAL_PAY_GMV:
        paramsModel.sortField = SORT_FIELD_REAL_PAY_GMV;
        break;

      /// 优选GMV
      case SORT_FIELD_GROSS_GMV:
        paramsModel.sortField = SORT_FIELD_GROSS_GMV;
        break;

      ///控销GMV
      case SORT_FIELD_CONTROL_GMV:
        paramsModel.sortField = SORT_FIELD_CONTROL_GMV;
        break;

      case SORT_FIELD_SELECT_GMV:
        paramsModel.sortField = SORT_FIELD_SELECT_GMV;
        break;
    }
    this.refreshController.callRefresh();
  }

  /// 请求数据
  void requestListData() async {
    var result = await NetworkV2<TeamPerformanceListDataModel>(TeamPerformanceListDataModel())
        .requestDataV2(getRequestUrl(), contentType: RequestContentType.FORM, parameters: getRequestParams(), method: RequestMethod.GET);
    if (mounted) {
      if (result.isSuccess == true) {
        var rows = result.getData()?.nodeList;
        gmvFilterShow = result.getData()?.gmvFilter ?? false;
        var preOffset = offset;
        onRequestListData(result.getData());
        if (rows != null) {
          if (preOffset == 1) {
            this.dataSource = rows;
          } else {
            this.dataSource.addAll(rows);
          }
        } else {
          this.dataSource = [];
        }
        setState(() {});
      }
    }
    dismissLoadingDialog();
  }

  void onRequestListData(TeamPerformanceListDataModel? model);

  String getRequestUrl();

  Map<String, dynamic> getRequestParams();
}
