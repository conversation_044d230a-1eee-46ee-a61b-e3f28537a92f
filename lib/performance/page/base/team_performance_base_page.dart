import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_multi_status_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

abstract class TeamPerformanceBasePage extends BasePage {
  @override
  BaseState initState();
}

// 抽象类 1.不能实例化  2.有抽象方法和普通方法 3.子类必须实现抽象方法：
abstract class TeamPerformanceBasePageState<T extends TeamPerformanceBasePage> extends BaseState<T> {
  // 分页参数
  int page = 0;

  // 筛选项
  List<dynamic> get filterConfig;

  // 状态保持
  @override
  bool needKeepAlive() => true;

  // 设置子页面
  @override
  bool isSubPage() => true;

  // 设置导航为空
  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) => null;

  EasyRefreshController refreshController = EasyRefreshController();

  ScrollController _scrollController = ScrollController();

  late String interface;

  @override
  void dispose() {
    refreshController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void requestInterface() async {
    var result = await XYYContainer.bridgeCall('app_host');
    if (result is Map) {
      this.interface = result['interface'] ?? "";
    }
  }

  @override
  void initState() {
    this.requestInterface();
    _scrollController.addListener(() {
      FocusScope.of(context).requestFocus(FocusNode());
    });
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: Color(0xFFEFEFF4),
        child: Column(
          children: [
            Visibility(
              visible: !this.isSearch,
              child: Container(
                height: 44,
                width: double.infinity,
                // 顶部筛选栏
                child: CommonDropFilterMultiStatusBar(
                  configs: filterConfig,
                  action: this.showDropPopupView,
                  multiAction: this.multiFilterSelected,
                  triggerAction: this.triggerFilterSelected,
                ),
              ),
            ),
            Visibility(
              visible: !this.isSearch,
              child: Divider(
                  color: Color(0xFFEFEFF4), height: 0.5, thickness: 0.5),
            ),
            onListHeader(),
            Expanded(
              child: EasyRefresh(
                onRefresh: refreshList,
                onLoad: this.isNoMore ? null : loadMoreList,
                controller: refreshController,
                emptyWidget: this.getEmptyWidget(),
                child: this.getContentListView(),
              ),
            ),
            onListFooter(),
          ],
        ),
      ),
    );
  }

  Widget onListHeader() {
    return Container(
      width: 0,
      height: 0,
    );
  }
  Widget onListFooter() {
    return Container(
      width: 0,
      height: 0,
    );
  }

  ListView getContentListView() {
    return ListView.builder(
      controller: _scrollController,
      itemCount: itemCount,
      cacheExtent: MediaQuery.of(context).size.height + 500,
      itemBuilder: itembuild,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  // 筛选弹窗
  void showDropPopupView(GlobalKey authKey, CommonDropMultiConfigModel model,
      DropButtonController controller){}

  // 多状态点击
  void multiFilterSelected(CommonDropConfigMultiStatusModel model){}

  //切换状态选中
  void triggerFilterSelected(TextStateConfigModel model){}

  bool get isNoMore;

  bool get isNetWorkError => false;

  bool get isSearch;

  Widget? getEmptyWidget() {
    if (this.isNetWorkError && this.isNoMore) {
      return PageStateWidget(
        state: PageState.Error,
        errorClick: () {
          this.refreshList();
        },
      );
    }
    return (itemCount == 0) ? PageStateWidget(state: PageState.Empty) : null;
  }

  // 列表长度
  int get itemCount;
  // 列表item构建方法
  IndexedWidgetBuilder get itembuild;
  // 刷新方法
  Future<void> refreshList();
  // 加载方法
  Future<void> loadMoreList();
}