import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/button/trigger_text_state_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_multi_status_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/customer/widget/customer_select_auth.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_rank_list_params.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_rank_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/page/base/team_performance_base_page.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_perfomance_rank_item.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_rank_item_header.dart';
import 'package:XyyBeanSproutsFlutter/performance/widget/team_performance_rank_list_footer.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

/// 团队业绩列表
class TeamPerformanceRankListPage extends TeamPerformanceBasePage {
  String userLevel;
  String rankUserType;

  TeamPerformanceRankListPage({required this.userLevel, this.rankUserType = USER_TYPE_BD});

  @override
  TeamPerformanceBasePageState initState() {
    return TeamPerformanceRankListPageState();
  }
}

class TeamPerformanceRankListPageState extends TeamPerformanceBasePageState<TeamPerformanceRankListPage> {
  /// 是否是 最后一页
  bool isLastPage = true;

  /// 数据源
  List<dynamic> dataSource = [];

  /// 排名数据
  dynamic rankModel = TeamPerformanceRankModel();

  /// 是否显示Header
  bool isShowHeader = false;

  /// 是否显示Footer
  bool isShowFooter = false;

  /// 排行榜参数
  TeamPerformanceRankListParams rankListParams = TeamPerformanceRankListParams();

  GlobalKey _titleButtonKey = GlobalKey();

  DropButtonController _titleController = DropButtonController(model: DropButtonModel(normalText: 'BD排行榜'));

  List<UserAuthModel> authModels = [
    UserAuthModel()
      ..roleCode = USER_TYPE_BD
      ..roleName = 'BD排行榜',
    UserAuthModel()
      ..roleCode = USER_TYPE_KA
      ..roleName = 'KA排行榜',
    UserAuthModel()
      ..roleCode = USER_TYPE_DX
      ..roleName = '电销排行榜'
  ];

  @override
  void onCreate() {
    super.onCreate();
    track("me-homepage-chartlist");
    rankListParams.postType = this.widget.userLevel;
    rankListParams.rankUserType = this.widget.rankUserType;
    showLoadingDialog();
    this.requestListData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return super.buildWidget(context);
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      '',
      leftType: LeftButtonType.back,
      titleWidget: getTitleWidget(),
      rightButtons: [
        Container(
          width: 50,
        ),
      ],
    );
  }

  String getTitleText(String userType) {
    switch (userType) {
      case USER_TYPE_BD:
        return "BD排行榜";
      case USER_TYPE_KA:
        return "KA排行榜";
      case USER_TYPE_DX:
        return "电销排行榜";
      default:
        return "";
    }
  }


  Widget getTitleWidget() {
    if (rankListParams.postType != USER_TYPE_M) {
      return Container(
        child: Text(
          getTitleText(widget.rankUserType),
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else {
      return Container(
        child: DropControllerButton(
          key: _titleButtonKey,
          title: getTitleText(widget.rankUserType),
          controller: this._titleController,
          maxTextWidth: 100,
          normalStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          selectedStyle: TextStyle(
            color: Color(0xFF333333),
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
          onPressed: (controller) {
            showAuthSelectPopover(
              context: context,
              anchorKey: _titleButtonKey,
              models: this.authModels,
              clickAction: (model) {
                controller.value.normalText = model.roleName;
                _switchUserType(model.roleCode);
              },
            ).then((value) {
              controller.setIsOpen(false);
            });
          },
        ),
      );
    }
  }

  /// 切换用户类型
  void _switchUserType(String userType) {
    rankListParams = TeamPerformanceRankListParams()..rankUserType = userType;
    filterConfig.forEach((element) {
      if (element is CommonDropMultiConfigModel) {
        element.controller?.setSelectText('今日');
      } else if (element is TextStateConfigModel) {
        element.controller?.value = element.paramKey == SORT_FIELD_REAL_PAY_GMV
            ? TriggerTextButtonState.selected
            : TriggerTextButtonState.normal;
      }
    });
    refreshController.callRefresh();
  }

  @override
  Widget onListHeader() {
    if (isShowHeader) {
      return Container(
        margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: TeamPerformanceRankItemHeadWidget(
          userType: rankListParams.rankUserType!,
          filterType: rankListParams.rankGmvType!,
        ),
      );
    } else
      return Container();
  }

  @override
  Widget onListFooter() {
    if (isShowFooter) {
      return TeamPerformanceRankListFooterWidget(rankModel: rankModel);
    } else
      return Container();
  }

  @override
  String getTitleName() => '';

  List<dynamic> multiFilterConfig = [
    CommonDropMultiConfigModel(
        defaultTitle: "今日",
        paramKey: FILTER_TYPE_DATE,
        selectTitle: '今日',
        controller: DropButtonController(model: DropButtonModel(selectText: '今日'))),
    TextStateConfigModel(
        paramKey: SORT_FIELD_REAL_PAY_GMV, title: "实付", controller: ValueNotifier(TriggerTextButtonState.selected)),
    TextStateConfigModel(paramKey: SORT_FIELD_GROSS_GMV, title: "优选"),
    TextStateConfigModel(paramKey: SORT_FIELD_CONTROL_GMV, title: "控销"),
    TextStateConfigModel(paramKey: SORT_FIELD_SELECT_GMV, title: "甄选"),
  ];

  @override
  List<dynamic> get filterConfig => multiFilterConfig;

  @override
  bool get isNoMore => isLastPage;

  @override
  bool get isSearch => false;

  @override
  int get itemCount => dataSource.length;

  @override
  IndexedWidgetBuilder get itembuild => (BuildContext context, int index) {
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10, top: index == 0 ? 10 : 0),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(index == 0 ? 4 : 0),
            topRight: Radius.circular(index == 0 ? 4 : 0),
          ),
        ),
        margin: EdgeInsets.zero,
        elevation: 0,
        clipBehavior: Clip.hardEdge,
        child: TeamPerformanceRankListItemWidget(index, dataSource[index]),
      ),
    );
  };

  @override
  Future<void> loadMoreList() async {
    this.requestListData();
  }

  @override
  Future<void> refreshList() async {
    this.requestListData();
  }

  @override
  void showDropPopupView(GlobalKey<State<StatefulWidget>> authKey, CommonDropMultiConfigModel model,
      DropButtonController controller) async {
    switch (model.paramKey) {

      /// 日期
      case FILTER_TYPE_DATE:
        await showCommodityFilterPopup(
          key: authKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: [
                CustomerConditionModel(text: '今日', code: TIME_TYPE_TODAY),
                CustomerConditionModel(text: '本月', code: TIME_TYPE_THIS_MOUTH),
              ],
              selectedCode: rankListParams.timeType,
              selectAction: (value) {
                rankListParams.timeType = "${value.code}";
                controller.setSelectText("${value.text}");
                this.refreshController.callRefresh();
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
        break;
    }
  }

  @override
  void triggerFilterSelected(TextStateConfigModel model) {
    rankListParams.rankGmvType = model.paramKey;
    setState(() {});
    refreshController.callRefresh();
  }

  /// 请求数据
  void requestListData() async {
    var params = rankListParams.toJson();
    var result = await NetworkV2<TeamPerformanceRankModel>(TeamPerformanceRankModel()).requestDataV2(
        'group/gmv/rankList',
        contentType: RequestContentType.FORM,
        parameters: params,
        method: RequestMethod.GET);
    if (mounted) {
      if (result.isSuccess == true) {
        rankModel = result.getData();
        var rows = result.getData()?.rankList;
        if (rows != null) {
          this.dataSource = rows;
        } else {
          this.dataSource = [];
        }
        // isShowHeader = this.dataSource.isNotEmpty;
        isShowFooter = this.dataSource.isNotEmpty && rankListParams.postType != USER_TYPE_M;
        this.isLastPage = true;
        setState(() {});
      }
    }
    dismissLoadingDialog();
  }
}
