library flutter_datetime_picker;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';

import 'date_model.dart';
import 'datetime_picker_theme.dart';

typedef DateChangedCallback(DateTime? time);
typedef DateCancelledCallback();
typedef String? StringAtIndexCallBack(int index);

class DatePicker {
  ///
  /// Display date picker bottom sheet.
  ///
  static Future<DateTime?> showDatePicker(
    BuildContext context, {
    bool showTitleActions: true,
    DateTime? minTime,
    DateTime? maxTime,
    DateChangedCallback? onChanged,
    DateChangedCallback? onConfirm,
    DateCancelledCallback? onCancel,
    DateTime? currentTime,
    DatePickerTheme? theme,
    String? title,
  }) async {
    return await Navigator.push(
        context,
        new _DatePickerRoute(
            showTitleActions: showTitleActions,
            onChanged: onChanged,
            onConfirm: onConfirm,
            onCancel: onCancel,
            theme: theme,
            title: title,
            barrierLabel:
                MaterialLocalizations.of(context).modalBarrierDismissLabel,
            pickerModel: DatePickerModel(
                currentTime: currentTime, maxTime: maxTime, minTime: minTime)));
  }
}

class _DatePickerRoute<T> extends PopupRoute<T> {
  _DatePickerRoute({
    this.showTitleActions,
    this.onChanged,
    this.onConfirm,
    this.onCancel,
    this.title,
    theme,
    this.barrierLabel,
    RouteSettings? settings,
    pickerModel,
  })  : this.pickerModel = pickerModel ?? DatePickerModel(),
        this.theme = theme ?? DatePickerTheme(),
        super(settings: settings);

  final bool? showTitleActions;
  final DateChangedCallback? onChanged;
  final DateChangedCallback? onConfirm;
  final DateCancelledCallback? onCancel;
  final DatePickerTheme theme;
  final BasePickerModel pickerModel;
  final String? title;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 200);

  @override
  bool get barrierDismissible => true;

  @override
  final String? barrierLabel;

  @override
  Color get barrierColor => Colors.black54;

  AnimationController? _animationController;

  @override
  AnimationController createAnimationController() {
    assert(_animationController == null);
    _animationController =
        BottomSheet.createAnimationController(navigator!.overlay!);
    return _animationController!;
  }

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    Widget bottomSheet = new MediaQuery.removePadding(
      context: context,
      removeTop: true,
      child: _DatePickerComponent(
        onChanged: onChanged,
        route: this,
        pickerModel: pickerModel,
        title: title,
      ),
    );
    ThemeData inheritTheme = Theme.of(context);
    bottomSheet = new Theme(data: inheritTheme, child: bottomSheet);
    return bottomSheet;
  }
}

class _DatePickerComponent extends StatefulWidget {
  _DatePickerComponent(
      {Key? key,
      required this.route,
      this.onChanged,
      this.pickerModel,
      this.title});

  final DateChangedCallback? onChanged;

  final _DatePickerRoute route;

  final BasePickerModel? pickerModel;

  final String? title;

  @override
  State<StatefulWidget> createState() {
    return _DatePickerState();
  }
}

class _DatePickerState extends State<_DatePickerComponent> {
  FixedExtentScrollController? leftScrollCtrl,
      middleScrollCtrl,
      rightScrollCtrl,
      hourScrollCtrl,
      minuteScrollCtrl;

  @override
  void initState() {
    super.initState();
    refreshScrollOffset();
  }

  void refreshScrollOffset() {
    leftScrollCtrl = new FixedExtentScrollController(
        initialItem: widget.pickerModel!.currentLeftIndex()!);
    middleScrollCtrl = new FixedExtentScrollController(
        initialItem: widget.pickerModel!.currentMiddleIndex()!);
    rightScrollCtrl = new FixedExtentScrollController(
        initialItem: widget.pickerModel!.currentRightIndex()!);
    hourScrollCtrl = new FixedExtentScrollController(
        initialItem: widget.pickerModel!.currentHourIndex()!);
    minuteScrollCtrl = new FixedExtentScrollController(
        initialItem: widget.pickerModel!.currentMinuteIndex()!);
  }

  @override
  Widget build(BuildContext context) {
    DatePickerTheme theme = widget.route.theme;
    return GestureDetector(
      child: AnimatedBuilder(
        animation: widget.route.animation!,
        builder: (BuildContext context, Widget? child) {
          final double bottomPadding = MediaQuery.of(context).padding.bottom;
          return ClipRect(
            child: CustomSingleChildLayout(
              delegate: _BottomPickerLayout(widget.route.animation!.value, theme,
                  showTitleActions: widget.route.showTitleActions,
                  bottomPadding: bottomPadding),
              child: GestureDetector(
                child: Material(
                  color: theme.backgroundColor,
                  child: _renderPickerView(theme, widget.title),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _notifyDateChanged() {
    if (widget.onChanged != null) {
      widget.onChanged!(widget.pickerModel!.finalTime());
    }
  }

  Widget _renderPickerView(DatePickerTheme theme, String? title) {
    Widget itemView = _renderItemView(theme);
    if (widget.route.showTitleActions!) {
      return Column(
        children: <Widget>[
          _renderTitleActionsView(theme, title),
          itemView,
        ],
      );
    }
    return itemView;
  }

  Widget _renderColumnView(
      ValueKey key,
      DatePickerTheme theme,
      StringAtIndexCallBack stringAtIndexCB,
      ScrollController? scrollController,
      int layoutProportion,
      ValueChanged<int> selectedChangedWhenScrolling,
      ValueChanged<int> selectedChangedWhenScrollEnd) {
    return Expanded(
      flex: layoutProportion,
      child: Container(
          padding: EdgeInsets.only(top: 8.0, bottom: 8.0),
          height: theme.containerHeight,
          decoration:
              BoxDecoration(color: theme.backgroundColor ),
          child: NotificationListener(
              onNotification: (ScrollNotification notification) {
                if (notification.depth == 0 &&
                    notification is ScrollEndNotification &&
                    notification.metrics is FixedExtentMetrics) {
                  final FixedExtentMetrics metrics = notification.metrics as FixedExtentMetrics;
                  final int currentItemIndex = metrics.itemIndex;
                  selectedChangedWhenScrollEnd(currentItemIndex);
                }
                return false;
              },
              child: CupertinoPicker.builder(
                  key: key,
                  backgroundColor: theme.backgroundColor ,
                  scrollController: scrollController as FixedExtentScrollController?,
                  itemExtent: theme.itemHeight,
                  onSelectedItemChanged: (int index) {
                    selectedChangedWhenScrolling(index);
                  },
                  useMagnifier: true,
                  itemBuilder: (BuildContext context, int index) {
                    final content = stringAtIndexCB(index);
                    if (content == null) {
                      return null;
                    }
                    return Container(
                      height: theme.itemHeight,
                      alignment: Alignment.center,
                      child: Text(
                        content,
                        style: theme.itemStyle,
                        textAlign: TextAlign.start,
                      ),
                    );
                  }))),
    );
  }

  Widget _renderItemView(DatePickerTheme theme) {
    return Container(
      color: theme.backgroundColor,
      padding: EdgeInsets.only(left: 12, right: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            child: widget.pickerModel!.layoutProportions()[0] > 0
                ? _renderColumnView(
                    ValueKey(widget.pickerModel!.currentLeftIndex()),
                    theme,
                    widget.pickerModel!.leftStringAtIndex,
                    leftScrollCtrl,
                    widget.pickerModel!.layoutProportions()[0], (index) {
                    widget.pickerModel!.setLeftIndex(index);
                  }, (index) {
                    setState(() {
                      refreshScrollOffset();
                      _notifyDateChanged();
                    });
                  })
                : null,
          ),
          Container(
            child: widget.pickerModel!.layoutProportions()[1] > 0
                ? _renderColumnView(
                    ValueKey(widget.pickerModel!.currentLeftIndex()),
                    theme,
                    widget.pickerModel!.middleStringAtIndex,
                    middleScrollCtrl,
                    widget.pickerModel!.layoutProportions()[1], (index) {
                    widget.pickerModel!.setMiddleIndex(index);
                  }, (index) {
                    setState(() {
                      refreshScrollOffset();
                      _notifyDateChanged();
                    });
                  })
                : null,
          ),
          Container(
            child: widget.pickerModel!.layoutProportions()[2] > 0
                ? _renderColumnView(
                    ValueKey(widget.pickerModel!.currentMiddleIndex()! * 100 +
                        widget.pickerModel!.currentLeftIndex()!),
                    theme,
                    widget.pickerModel!.rightStringAtIndex,
                    rightScrollCtrl,
                    widget.pickerModel!.layoutProportions()[2], (index) {
                    widget.pickerModel!.setRightIndex(index);
                  }, (index) {
                    refreshScrollOffset();
                    _notifyDateChanged();
                  })
                : null,
          ),
          Container(
            child: widget.pickerModel!.layoutProportions()[3] > 0
                ? _renderColumnView(
                    ValueKey(widget.pickerModel!.currentMiddleIndex()! * 1000 +
                        widget.pickerModel!.currentLeftIndex()! * 100 +
                        widget.pickerModel!.currentHourIndex()!),
                    theme,
                    widget.pickerModel!.hourStringAtIndex,
                    hourScrollCtrl,
                    widget.pickerModel!.layoutProportions()[3], (index) {
                    widget.pickerModel!.setHourIndex(index);
                  }, (index) {
                    setState(() {
                      refreshScrollOffset();
                      _notifyDateChanged();
                    });
                  })
                : null,
          ),
          Container(
            child: widget.pickerModel!.layoutProportions()[4] > 0
                ? _renderColumnView(
                    ValueKey(widget.pickerModel!.currentMiddleIndex()! * 1000 +
                        widget.pickerModel!.currentLeftIndex()! * 100 +
                        widget.pickerModel!.currentHourIndex()! * 10 +
                        widget.pickerModel!.currentMiddleIndex()!),
                    theme,
                    widget.pickerModel!.minuteStringAtIndex,
                    minuteScrollCtrl,
                    widget.pickerModel!.layoutProportions()[4], (index) {
                    widget.pickerModel!.setMinuteIndex(index);
                  }, (index) {
                    setState(() {
                      refreshScrollOffset();
                      _notifyDateChanged();
                    });
                  })
                : null,
          ),
        ],
      ),
    );
  }

  // Title View
  Widget _renderTitleActionsView(DatePickerTheme theme, String? title) {
    String done = _localeDone();
    String cancel = _localeCancel();
    return Container(
      height: theme.titleHeight,
      decoration: BoxDecoration(
        color: theme.headerColor ?? theme.backgroundColor ,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            height: theme.titleHeight,
            child: CupertinoButton(
              pressedOpacity: 0.3,
              padding: EdgeInsets.only(left: 15, top: 0),
              child: Text(
                '$cancel',
                style: theme.cancelStyle,
              ),
              onPressed: () {
                Navigator.pop(context);
                if (widget.route.onCancel != null) {
                  widget.route.onCancel!();
                }
              },
            ),
          ),
          Expanded(
              child: Container(
                  height: theme.titleHeight,
                  alignment: Alignment.center,
                  child: Text(
                    '$title',
                    style: theme.titleStyle,
                  ))),
          Container(
            height: theme.titleHeight,
            child: CupertinoButton(
              pressedOpacity: 0.3,
              padding: EdgeInsets.only(right: 15, top: 0),
              child: Text(
                '$done',
                style: theme.doneStyle,
              ),
              onPressed: () {
                Navigator.pop(context, widget.pickerModel!.finalTime());
                if (widget.route.onConfirm != null) {
                  widget.route.onConfirm!(widget.pickerModel!.finalTime());
                  print("选中的时间为 ==  ${widget.pickerModel!.finalTime()}");
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  String _localeDone() {
    return "确定";
  }

  String _localeCancel() {
    return "取消";
  }
}

class _BottomPickerLayout extends SingleChildLayoutDelegate {
  _BottomPickerLayout(this.progress, this.theme,
      {this.itemCount, this.showTitleActions, this.bottomPadding = 0});

  final double progress;
  final int? itemCount;
  final bool? showTitleActions;
  final DatePickerTheme theme;
  final double bottomPadding;

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    double maxHeight = theme.containerHeight;
    if (showTitleActions!) {
      maxHeight += theme.titleHeight;
    }

    return new BoxConstraints(
        minWidth: constraints.maxWidth,
        maxWidth: constraints.maxWidth,
        minHeight: 0.0,
        maxHeight: maxHeight + bottomPadding);
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    double height = size.height - childSize.height * progress;
    return new Offset(0.0, height);
  }

  @override
  bool shouldRelayout(_BottomPickerLayout oldDelegate) {
    return progress != oldDelegate.progress;
  }
}
