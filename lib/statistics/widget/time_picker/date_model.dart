//interface for picker data model
import 'datetime_util.dart';

abstract class BasePickerModel {
  //a getter method for left column data, return null to end list
  String? leftStringAtIndex(int index);

  //a getter method for middle column data, return null to end list
  String? middleStringAtIndex(int index);

  //a getter method for right column data, return null to end list
  String? rightStringAtIndex(int index);

  String? hourStringAtIndex(int index);

  String? minuteStringAtIndex(int index);

  //return current left index
  int? currentLeftIndex();

  //return current middle index
  int? currentMiddleIndex();

  //return current right index
  int? currentRightIndex();

  int? currentHourIndex();

  int? currentMinuteIndex();

//set selected left index
  void setLeftIndex(int index);

  //set selected middle index
  void setMiddleIndex(int index);

  //set selected right index
  void setRightIndex(int index);

  void setHourIndex(int index);

  void setMinuteIndex(int index);

  //return final time
  DateTime? finalTime();

  //layout proportions for 3 columns
  List<int> layoutProportions();
}

//a base class for picker data model
class CommonPickerModel extends BasePickerModel {
  late List<String> leftList;
  late List<String> middleList;
  late List<String> rightList;
  late List<String> hourList;
  late List<String> minuteList;
  DateTime? currentTime;
  int? _currentLeftIndex;
  int? _currentMiddleIndex;
  int? _currentRightIndex;
  int? _currentHourIndex;
  int? _currentMinuteIndex;

  CommonPickerModel({this.currentTime});

  @override
  String? leftStringAtIndex(int index) {
    return null;
  }

  @override
  String? middleStringAtIndex(int index) {
    return null;
  }

  @override
  String? rightStringAtIndex(int index) {
    return null;
  }

  @override
  String? hourStringAtIndex(int index) {
    return null;
  }

  @override
  String? minuteStringAtIndex(int index) {
    return null;
  }

  @override
  int? currentLeftIndex() {
    return _currentLeftIndex;
  }

  @override
  int? currentMiddleIndex() {
    return _currentMiddleIndex;
  }

  @override
  int? currentRightIndex() {
    return _currentRightIndex;
  }

  @override
  int? currentHourIndex() {
    return _currentHourIndex;
  }

  @override
  int? currentMinuteIndex() {
    return _currentMinuteIndex;
  }

  @override
  void setLeftIndex(int index) {
    _currentLeftIndex = index;
  }

  @override
  void setMiddleIndex(int index) {
    _currentMiddleIndex = index;
  }

  @override
  void setRightIndex(int index) {
    _currentRightIndex = index;
  }

  @override
  void setHourIndex(int index) {
    _currentHourIndex = index;
  }

  @override
  void setMinuteIndex(int index) {
    _currentMinuteIndex = index;
  }

  @override
  List<int> layoutProportions() {
    return [1, 1, 1, 1, 1];
  }

  @override
  DateTime? finalTime() {
    return null;
  }
}

//a date picker model
class DatePickerModel extends CommonPickerModel {
  DateTime? maxTime;
  DateTime? minTime;

  DatePickerModel({DateTime? currentTime, DateTime? maxTime, DateTime? minTime}) {
    this.maxTime = maxTime ?? DateTime(2049, 12, 31, 0, 0);
    this.minTime = minTime ?? DateTime(1970, 1, 1, 0, 0);

    currentTime = currentTime ?? DateTime.now();
    if (currentTime.compareTo(this.maxTime!) > 0) {
      currentTime = this.maxTime;
    } else if (currentTime.compareTo(this.minTime!) < 0) {
      currentTime = this.minTime;
    }
    this.currentTime = currentTime;

    _fillLeftLists();
    _fillMiddleLists();
    _fillRightLists();
    _fillHourLists();
    _fillMinuteList();
    int minMonth = _minMonthOfCurrentYear();
    int minDay = _minDayOfCurrentMonth();
    _currentLeftIndex = this.currentTime!.year - this.minTime!.year;
    _currentMiddleIndex = this.currentTime!.month - minMonth;
    _currentRightIndex = this.currentTime!.day - minDay;
    _currentHourIndex = this.currentTime!.hour;
    _currentMinuteIndex = this.currentTime!.minute;
  }

  void _fillLeftLists() {
    this.leftList = List.generate(maxTime!.year - minTime!.year + 1, (int index) {
      return '${minTime!.year + index}年';
    });
  }

  void _fillMiddleLists() {
    int minMonth = _minMonthOfCurrentYear();
    int maxMonth = _maxMonthOfCurrentYear();

    this.middleList = List.generate(maxMonth - minMonth + 1, (int index) {
      return '${minMonth + index}月';
    });
  }

  void _fillRightLists() {
    int maxDay = _maxDayOfCurrentMonth();
    int minDay = _minDayOfCurrentMonth();
    this.rightList = List.generate(maxDay - minDay + 1, (int index) {
      return '${minDay + index}日';
    });
  }

  void _fillHourLists() {
    this.hourList = List.generate(24, (index) {
      return '$index时';
    });
  }

  void _fillMinuteList() {
    this.minuteList = List.generate(60, (index) {
      return '$index分';
    });
  }

  int _maxMonthOfCurrentYear() {
    return currentTime!.year == maxTime!.year ? maxTime!.month : 12;
  }

  int _minMonthOfCurrentYear() {
    return currentTime!.year == minTime!.year ? minTime!.month : 1;
  }

  int _maxDayOfCurrentMonth() {
    int dayCount = calcDateCount(currentTime!.year, currentTime!.month);
    return currentTime!.year == maxTime!.year &&
            currentTime!.month == maxTime!.month
        ? maxTime!.day
        : dayCount;
  }

  int _minDayOfCurrentMonth() {
    return currentTime!.year == minTime!.year &&
            currentTime!.month == minTime!.month
        ? minTime!.day
        : 1;
  }

  @override
  void setLeftIndex(int index) {
    super.setLeftIndex(index);
    //adjust middle
    int destYear = index + minTime!.year;
    int minMonth = _minMonthOfCurrentYear();
    DateTime newTime;
    //change date time
    if (currentTime!.month == 2 && currentTime!.day == 29) {
      newTime = currentTime!.isUtc
          ? DateTime.utc(
              destYear,
              currentTime!.month,
              calcDateCount(destYear, 2),
              currentTime!.hour,
              currentTime!.minute,
            )
          : DateTime(
              destYear,
              currentTime!.month,
              calcDateCount(destYear, 2),
        currentTime!.hour,
        currentTime!.minute,
            );
    } else {
      newTime = currentTime!.isUtc
          ? DateTime.utc(
              destYear,
              currentTime!.month,
              currentTime!.day,
        currentTime!.hour,
        currentTime!.minute,
            )
          : DateTime(
              destYear,
              currentTime!.month,
              currentTime!.day,
        currentTime!.hour,
        currentTime!.minute,
            );
    }
    //min/max check
    if (newTime.isAfter(maxTime!)) {
      currentTime = maxTime;
    } else if (newTime.isBefore(minTime!)) {
      currentTime = minTime;
    } else {
      currentTime = newTime;
    }

    _fillMiddleLists();
    _fillRightLists();
    _fillHourLists();
    _fillMinuteList();
    minMonth = _minMonthOfCurrentYear();
    int minDay = _minDayOfCurrentMonth();
    _currentMiddleIndex = currentTime!.month - minMonth;
    _currentRightIndex = currentTime!.day - minDay;
    print("年$currentTime");
  }

  @override
  void setMiddleIndex(int index) {
    super.setMiddleIndex(index);
    //adjust right
    int minMonth = _minMonthOfCurrentYear();
    int destMonth = minMonth + index;
    DateTime newTime;
    //change date time
    int dayCount = calcDateCount(currentTime!.year, destMonth);
    newTime = currentTime!.isUtc
        ? DateTime.utc(
            currentTime!.year,
            destMonth,
            currentTime!.day <= dayCount ? currentTime!.day : dayCount,
      currentTime!.hour,
      currentTime!.minute,
          )
        : DateTime(
            currentTime!.year,
            destMonth,
            currentTime!.day <= dayCount ? currentTime!.day : dayCount,
      currentTime!.hour,
      currentTime!.minute,
          );
    //min/max check
    if (newTime.isAfter(maxTime!)) {
      currentTime = maxTime;
    } else if (newTime.isBefore(minTime!)) {
      currentTime = minTime;
    } else {
      currentTime = newTime;
    }

    _fillRightLists();
    _fillHourLists();
    _fillMinuteList();
    int minDay = _minDayOfCurrentMonth();
    _currentRightIndex = currentTime!.day - minDay;
    print("月$currentTime");
  }

  @override
  void setRightIndex(int index) {
    super.setRightIndex(index);
    int minDay = _minDayOfCurrentMonth();
    currentTime = currentTime!.isUtc
        ? DateTime.utc(
            currentTime!.year,
            currentTime!.month,
            minDay + index,
      currentTime!.hour,
      currentTime!.minute,
          )
        : DateTime(
            currentTime!.year,
            currentTime!.month,
            minDay + index,
      currentTime!.hour,
      currentTime!.minute,
          );
    print("日$currentTime");
  }

  @override
  void setHourIndex(int index) {
    super.setHourIndex(index);
    currentTime = currentTime!.isUtc
        ? DateTime.utc(currentTime!.year, currentTime!.month, currentTime!.day,
            index, currentTime!.minute)
        : DateTime(currentTime!.year, currentTime!.month, currentTime!.day, index,
            currentTime!.minute);
    _fillMinuteList();
  }

  @override
  void setMinuteIndex(int index) {
    super.setMinuteIndex(index);
    currentTime = currentTime!.isUtc
        ? DateTime.utc(currentTime!.year, currentTime!.month, currentTime!.day,
            currentTime!.hour, index)
        : DateTime(currentTime!.year, currentTime!.month, currentTime!.day,
            currentTime!.hour, index);
  }

  @override
  String? leftStringAtIndex(int index) {
    if (index >= 0 && index < leftList.length) {
      return leftList[index];
    } else {
      return null;
    }
  }

  @override
  String? middleStringAtIndex(int index) {
    if (index >= 0 && index < middleList.length) {
      return middleList[index];
    } else {
      return null;
    }
  }

  @override
  String? rightStringAtIndex(int index) {
    if (index >= 0 && index < rightList.length) {
      return rightList[index];
    } else {
      return null;
    }
  }

  @override
  String? hourStringAtIndex(int index) {
    if (index >= 0 && index < hourList.length) {
      return hourList[index];
    } else {
      return null;
    }
  }

  @override
  String? minuteStringAtIndex(int index) {
    if (index >= 0 && index < minuteList.length) {
      return minuteList[index];
    } else {
      return null;
    }
  }

  @override
  DateTime? finalTime() {
    return currentTime;
  }
}
