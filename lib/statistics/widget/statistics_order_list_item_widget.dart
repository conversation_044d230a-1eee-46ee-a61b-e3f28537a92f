import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_order_list_data.dart';
import 'package:flutter/material.dart';
import "package:intl/intl.dart";

class StatisticsOrderListItemWidget extends StatelessWidget {
  final StatisticsOrderItemData itemData;

  StatisticsOrderListItemWidget(this.itemData);

  @override
  Widget build(BuildContext context) {
    var moneyText = formatMoneyText(double.tryParse(itemData.amount));
    return Container(
      constraints: BoxConstraints(minHeight: 168),
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(15, 5, 15, 5),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(2)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 40,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Text(
                    "单据编号：${itemData.docNo.toString()}",
                    style: TextStyle(
                        color: Color(0xFF676773),
                        fontWeight: FontWeight.normal,
                        fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Text(
                  itemData.statusText ?? "",
                  style: TextStyle(
                      color: getStatusTextColor(),
                      fontSize: 12,
                      fontWeight: FontWeight.normal),
                ),
                SizedBox(
                  width: 10,
                )
              ],
            ),
          ),
          Divider(height: 1, color: Color(0xFFF6F6F6)),
          SizedBox(
            height: 10,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 10,
              ),
              SizedBox(
                width: 90,
                height: 90,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    ImageWidget(
                        url: itemData.imgUrl,
                        w: 90,
                        h: 90,
                        defImagePath:
                            "assets/images/order/icon_load_failed.png"),
                    Container(
                      color: Color(0xFFF6F6F6),
                      height: 19,
                      alignment: Alignment.center,
                      child: Text(
                        "${itemData.productNum?.toString() ?? "-"}件商品",
                        style: TextStyle(
                            color: Color(0xFF676773),
                            fontSize: 12,
                            fontWeight: FontWeight.normal),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  constraints: BoxConstraints(minHeight: 127),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        itemData.merchantName ?? "",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: TextStyle(
                            color: Color(0xFF292933),
                            fontSize: 15,
                            fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        "申请时间:  ${itemData.dateTime?.toString() ?? ""}",
                        style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.normal,
                            color: Color(0xFF676773)),
                      ),
                      SizedBox(
                        height: 4,
                      ),
                      Text(
                        "订单来源:  ${itemData.source?.toString() ?? "-"}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      SizedBox(
                        height: 4,
                      ),
                      Text.rich(TextSpan(
                          text: "实付总额：",
                          style: TextStyle(
                              color: Color(0xFF333333),
                              fontSize: 12,
                              fontWeight: FontWeight.normal),
                          children: [
                            TextSpan(
                                text: "¥",
                                style: TextStyle(
                                    color: Color(0xFF292933),
                                    fontSize: 13,
                                    fontWeight: FontWeight.w700)),
                            TextSpan(
                              text: moneyText.substring(
                                  0, moneyText.indexOf(".")),
                              style: TextStyle(
                                  color: Color(0xFF292933),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700),
                            ),
                            TextSpan(
                                text:
                                    moneyText.substring(moneyText.indexOf(".")),
                                style: TextStyle(
                                    color: Color(0xFF292933),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w700))
                          ])),
                      SizedBox(
                        height: 10,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 订单状态 0-未审核，1-审核中，2-配送中，3-已完成，4-已取消，5-已删除,6-已拆单,7-出库中,9-审核结束，10-未支付,11-已支付，90-退款审核中,91-已退款,20-已送达,21-已拒签；
  /// 状态颜色：
  /// 红色：未审核、未支付、已拒签 ，
  /// 绿色：审核中、配送中、出库中、退款审核中、已支付、已送达、 已拆单、审核结束，
  /// 灰色：已完成、已取消、删除、已退款
  Color getStatusTextColor() {
    return Color(0xFF0C112A);
  }

  String formatMoneyText(double? money) {
    return NumberFormat("0.00").format(money);
  }
}
