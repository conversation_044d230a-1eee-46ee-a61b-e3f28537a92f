import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'statistics_item_data.g.dart';

@JsonSerializable()
class StatisticsItemData extends BaseModel<StatisticsItemData> {
  String? key;
  String? value;

  StatisticsItemData();

  @override
  StatisticsItemData fromJsonMap(Map<String, dynamic>? json) {
    return StatisticsItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$StatisticsItemDataToJson(this);
  }

  factory StatisticsItemData.fromJson(Map<String, dynamic> json) =>
      _$StatisticsItemDataFromJson(json);
}
