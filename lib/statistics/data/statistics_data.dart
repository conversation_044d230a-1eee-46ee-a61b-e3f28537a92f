import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

import 'package:json_annotation/json_annotation.dart';

part 'statistics_data.g.dart';

@JsonSerializable()
class StatisticsData extends BaseModel<StatisticsData> {
  String? title;
  List<StatisticsItemData>? content;

  StatisticsData();

  @override
  StatisticsData fromJsonMap(Map<String, dynamic>? json) {
    return StatisticsData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$StatisticsDataToJson(this);
  }

  factory StatisticsData.fromJson(Map<String, dynamic> json) =>
      _$StatisticsDataFromJson(json);
}
