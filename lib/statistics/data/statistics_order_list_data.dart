import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'statistics_order_list_data.g.dart';

@JsonSerializable()
class StatisticsOrderListData extends BaseModelV2<StatisticsOrderListData> {

  dynamic isLastPage;

  List<StatisticsOrderItemData>? list;

  StatisticsOrderListData();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return StatisticsOrderListData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$StatisticsOrderListDataToJson(this);
  }

  factory StatisticsOrderListData.fromJson(Map<String, dynamic> json) =>
      _$StatisticsOrderListDataFromJson(json);
}

@JsonSerializable()
class StatisticsOrderItemData extends BaseModelV2<StatisticsOrderItemData> {

  dynamic docNo;
  dynamic merchantName;
  dynamic dateTime;
  dynamic statusText;
  dynamic source;
  dynamic imgUrl;
  dynamic productNum;
  dynamic amount;
  dynamic docId;
  dynamic merchantId;

  StatisticsOrderItemData();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return StatisticsOrderItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$StatisticsOrderItemDataToJson(this);
  }

  factory StatisticsOrderItemData.fromJson(Map<String, dynamic> json) =>
      _$StatisticsOrderItemDataFromJson(json);
}

