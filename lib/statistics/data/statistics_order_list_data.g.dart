// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'statistics_order_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StatisticsOrderListData _$StatisticsOrderListDataFromJson(
    Map<String, dynamic> json) {
  return StatisticsOrderListData()
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map(
            (e) => StatisticsOrderItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$StatisticsOrderListDataToJson(
        StatisticsOrderListData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };

StatisticsOrderItemData _$StatisticsOrderItemDataFromJson(
    Map<String, dynamic> json) {
  return StatisticsOrderItemData()
    ..docNo = json['docNo']
    ..merchantName = json['merchantName']
    ..dateTime = json['dateTime']
    ..statusText = json['statusText']
    ..source = json['source']
    ..imgUrl = json['imgUrl']
    ..productNum = json['productNum']
    ..amount = json['amount']
    ..docId = json['docId']
    ..merchantId = json['merchantId'];
}

Map<String, dynamic> _$StatisticsOrderItemDataToJson(
        StatisticsOrderItemData instance) =>
    <String, dynamic>{
      'docNo': instance.docNo,
      'merchantName': instance.merchantName,
      'dateTime': instance.dateTime,
      'statusText': instance.statusText,
      'source': instance.source,
      'imgUrl': instance.imgUrl,
      'productNum': instance.productNum,
      'amount': instance.amount,
      'docId': instance.docId,
      'merchantId': instance.merchantId,
    };
