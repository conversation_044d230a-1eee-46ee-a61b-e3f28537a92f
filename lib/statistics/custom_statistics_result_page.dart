import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_data.dart';
import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_item_data.dart';
import 'package:flutter/material.dart';

class CustomStatisticsResultPage extends BasePage {
  final List<StatisticsData>? listData;

  CustomStatisticsResultPage(this.listData);

  @override
  BaseState<StatefulWidget> initState() {
    return CustomStatisticsResultPageState();
  }
}

class CustomStatisticsResultPageState
    extends BaseState<CustomStatisticsResultPage> {
  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xfff7f7f8),
      child: widget.listData == null || widget.listData!.isEmpty
          ? getErrorWidget()
          : ListView.builder(
              itemBuilder: (context, index) {
                return _buildGroupItem(widget.listData![index]);
              },
              itemCount: widget.listData?.length ?? 0,
            ),
    );
  }

  @override
  String getTitleName() {
    return '查询结果';
  }

  Widget _buildGroupItem(StatisticsData listItem) {
    return Container(
      margin: EdgeInsets.fromLTRB(15, 15, 15, 0),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(2)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            listItem.title ?? "-",
            style: TextStyle(
                fontSize: 17,
                color: Color(0xff292933),
                fontWeight: FontWeight.w500),
          ),
          SizedBox(
            height: 15,
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(listItem.content!.length, (index) {
              return _buildItem(listItem.content![index]);
            }),
          )
        ],
      ),
    );
  }

  Widget _buildItem(StatisticsItemData itemData) {
    String? keyText = "-";
    String? valueText = "-";
    if (itemData.key != null && itemData.key!.isNotEmpty) {
      keyText = itemData.key;
    }
    if (itemData.value != null && itemData.value!.isNotEmpty) {
      valueText = itemData.value;
    }

    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          Text(
            keyText ?? "-",
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.normal,
                color: Color(0xff292933)),
            strutStyle: StrutStyle(leading: 0.2),
          ),
          SizedBox(
            width: 10,
          ),
          Expanded(child: Container()),
          Text(
            valueText ?? "-",
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.normal,
                color: Color(0xff292933)),
            strutStyle: StrutStyle(leading: 0.2),
          )
        ],
      ),
    );
  }

  Widget getErrorWidget() {
    return PageStateWidget.pageEmpty(PageState.Empty);
  }
}
