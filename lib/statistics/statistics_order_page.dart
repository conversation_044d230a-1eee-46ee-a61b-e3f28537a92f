import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/statistics/statistics_order_tab_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:collection/collection.dart';

class StatisticsOrderPage extends BasePage {
  final String? period;
  final String? drugstoreType;
  final String? targetId;
  final String? gmvType;
  final String? total;

  StatisticsOrderPage(
      {this.total,
      this.period,
      this.drugstoreType,
      this.targetId,
      this.gmvType});

  @override
  BaseState<StatefulWidget> initState() {
    return StatisticsOrderPageState();
  }
}

class StatisticsOrderPageState extends BaseState<StatisticsOrderPage>
    with SingleTickerProviderStateMixin {
  List<String> _tabTitles = [
    "支付订单",
    "退款订单",
  ];
  TabController? _tabController;

  @override
  void onCreate() {
    super.onCreate();

    _tabController = new TabController(
        initialIndex: 0, length: _tabTitles.length, vsync: this);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: TabBarView(
        controller: _tabController,
        children: [
          StatisticsOrderTabPage(
            period: widget.period,
            drugstoreType: widget.drugstoreType,
            targetId: widget.targetId,
            orderType: "1",
            gmvType: widget.gmvType,
            total: widget.total,
          ),
          StatisticsOrderTabPage(
            period: widget.period,
            drugstoreType: widget.drugstoreType,
            targetId: widget.targetId,
            orderType: "2",
            gmvType: widget.gmvType,
            total: widget.total,
          ),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      titleWidget: buildTitleWidget(),
      rightButtons: [
        SizedBox(
          width: 80,
        )
      ],
    );
  }

  Widget buildTitleWidget() {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.bottomCenter,
      margin: EdgeInsets.only(left: 25),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Color(0xFF292933),
        indicator: TabCustomIndicator(
            wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        unselectedLabelColor: Color(0xFF676733),
        unselectedLabelStyle:
            TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
        tabs: _tabTitles.mapIndexed((index, e) => Tab(text: e)).toList(),
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }
}
