import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_manage_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_list_marquee.dart';
import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_order_list_data.dart';
import 'package:XyyBeanSproutsFlutter/statistics/widget/statistics_order_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:collection/collection.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class StatisticsOrderTabPage extends BasePage {
  final String? period;
  final String? drugstoreType;
  final String? targetId;
  final String? orderType;
  final String? gmvType;
  final String? total;

  StatisticsOrderTabPage(
      {this.total,
      this.period,
      this.drugstoreType,
      this.targetId,
      this.gmvType,
      this.orderType});

  @override
  BaseState<StatefulWidget> initState() {
    return StatisticsOrderTabPageState();
  }
}

class StatisticsOrderTabPageState extends BaseState<StatisticsOrderTabPage>
    with HomeORGChooseManagerObserver {
  bool? isSuccess;
  List<StatisticsOrderItemData>? list;
  bool? isLastPage = false;
  int pageNum = 1;
  EasyRefreshController _refreshController = EasyRefreshController();

  bool forceRefresh = false;

  @override
  void onCreate() {
    super.onCreate();
    requestListModel(true);
  }

  @override
  bool isSubPage() {
    return true;
  }

  bool isRefund() {
    return widget.orderType == "2";
  }

  requestListModel(bool isRefresh) async {
    if (isRefresh) {
      pageNum = 1;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    var paramsMap = buildParamsMap();
    NetworkV2<StatisticsOrderListData>(StatisticsOrderListData())
        .requestDataV2("index/v2/order",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(value.isSuccess, value.getData());
    });
  }

  void handleResult(bool? isSuccess, StatisticsOrderListData? data) {
    EasyLoading.dismiss();
    this.isSuccess = isSuccess;
    if (mounted && (isSuccess ?? false)) {
      if (data != null) {
        var tempList;
        isLastPage = data.isLastPage;
        tempList = data.list;
        if (forceRefresh) {
          list = tempList;
        } else {
          if (list == null) {
            list = tempList;
          } else {
            list?.addAll(tempList ?? []);
          }
        }
      } else {
        isLastPage = true;
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage ?? true);
    } else {
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: false);
    }
    setState(() {});
  }

  buildParamsMap() {
    var params = (this.chooseManager.paramModel ?? HomeORGParamModel()).toMap();
    //分页参数
    params["offset"] = pageNum.toString();
    params["limit"] = "10";
    params["period"] = widget.period;
    if (widget.drugstoreType != "-1") {
      params['drugstoreType'] = widget.drugstoreType;
    }
    params["targetId"] = widget.targetId;
    params["orderType"] = widget.orderType;
    params["gmvType"] = widget.gmvType;
    return params;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeaderView(),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
          Container(
            color: Colors.white,
            height: 44,
            padding: EdgeInsets.only(right: 15),
            alignment: Alignment.centerRight,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text.rich(
                  TextSpan(
                    text: "总计 ¥",
                    style: TextStyle(
                      fontSize: 13,
                      color: Color(0xff292933),
                      fontWeight: FontWeight.w500,
                    ),
                    children: getFormatMoneyTextSpan(widget.total ?? "-"),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (isSuccess == null) {
      return null;
    }
    if (isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        requestListModel(true);
      });
    }
    if ((list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  buildListView() {
    var emptyWidget = getEmptyWidget();
    return Container(
      color: emptyWidget == null ? Colors.transparent : Colors.white,
      child: EasyRefresh.custom(
          controller: _refreshController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            requestListModel(true);
          },
          onLoad: !(isLastPage ?? false) && list?.isNotEmpty == true
              ? () async {
                  requestListModel(false);
                }
              : null,
          slivers: [
            SliverPadding(padding: EdgeInsets.only(top: 5)),
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  //创建列表项
                  return buildOrderItem(index);
                },
                childCount: list?.length ?? 0,
              ),
            ),
            SliverPadding(
              padding: EdgeInsets.only(top: 5),
            ),
          ],
          emptyWidget: emptyWidget),
    );
  }

  buildOrderItem(int index) {
    if (list == null || index >= (list?.length ?? 0)) {
      return Container();
    }
    var itemData = list![index];
    return GestureDetector(
      onTap: () {
        if (isRefund()) {
          Navigator.of(context).pushNamed("/OrderDetailRefundPage", arguments: {
            "orderId": itemData.docId,
            "merchantId": itemData.merchantId
          });
        } else {
          Navigator.of(context).pushNamed("/order_detail_page", arguments: {
            "orderId": itemData.docId,
            "merchantId": itemData.merchantId
          });
        }
      },
      child: StatisticsOrderListItemWidget(itemData),
    );
  }

  List<TextSpan> getFormatMoneyTextSpan(String? moneyText) {
    String integerPlace = "";
    String decimalPlace = "";
    if (moneyText != null) {
      var indexOf = moneyText.indexOf(".");
      if (indexOf != -1) {
        integerPlace = moneyText.substring(0, indexOf);
        decimalPlace = moneyText.substring(indexOf);
      } else {
        integerPlace = moneyText;
        decimalPlace = "";
      }
    }
    return [
      TextSpan(
        text: integerPlace,
        style: TextStyle(
          fontSize: 16,
          color: Color(0xff292933),
          fontWeight: FontWeight.w500,
        ),
      ),
      TextSpan(
        text: decimalPlace,
        style: TextStyle(
          fontSize: 13,
          color: Color(0xff292933),
          fontWeight: FontWeight.w500,
        ),
      )
    ];
  }

  buildHeaderView() {
    return Container(
        height: 30,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Color(0xfffff7ef),
        ),
        child: OrderListMarquee(
          tips: "实付金额/商品数量为实际业绩归属数量，可能与订单明细中不一致",
        ));
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }
}
