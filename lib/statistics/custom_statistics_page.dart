import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/statistics/data/statistics_data.dart';
import 'package:XyyBeanSproutsFlutter/statistics/widget/time_picker/flutter_datetime_picker.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class CustomStatisticsPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return CustomStatisticsPageState();
  }
}

class CustomStatisticsPageState extends BaseState<CustomStatisticsPage> {
  var isManagerLevel = false;
  var scopeText = '全部';
  DateTime? startTime;
  DateTime? endTime;
  String? groupId;
  String? searchUserId;

  @override
  void initState() {
    super.initState();
    _requestRoleType();
    EasyLoading.dismiss();
    // startTime = getDefaultStartTime();
    // endTime = getDefaultEndTime();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
              visible: isManagerLevel,
              child: _buildItem("部门范围", scopeText, "请选择部门范围", () {
                XYYContainer.open(
                    "xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false",
                    callback: (resultData) {
                  if (resultData != null &&
                      resultData is Map &&
                      resultData.containsKey("id")) {
                    setState(() {
                      scopeText = resultData["name"]?.toString() ?? "";
                      String id = resultData["id"]?.toString() ?? "";
                      if (resultData["isgroup"]?.toString() == "true") {
                        groupId = id;
                        searchUserId = null;
                      } else {
                        searchUserId = id;
                        groupId = null;
                      }
                    });
                  }
                });
              })),
          _buildItem("开始时间", _formatTime(startTime), "请选择开始时间", () {
            DatePicker.showDatePicker(context,
                currentTime: startTime ?? getDefaultStartTime(),
                minTime: getMinStartTime(),
                maxTime: getMaxStartTime(),
                showTitleActions: true,
                title: "开始时间", onConfirm: (time) {
              print("guan:${time.toString()}");
              setState(() {
                startTime = time;
              });
            });
          }),
          _buildItem("结束时间", _formatTime(endTime), "请选择结束时间", () {
            DatePicker.showDatePicker(context,
                currentTime: endTime ?? getDefaultEndTime(),
                minTime: getMinEndTime(),
                maxTime: getMaxEndTime(),
                showTitleActions: true,
                title: "结束时间", onConfirm: (time) {
              setState(() {
                endTime = time;
              });
            });
          }),
          SizedBox(
            height: 25,
          ),
          Text(
            "温馨提示：",
            style: TextStyle(
                color: Color(0xFF99664D),
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
          Text(
            "1.2020年6月6日以及之前的数据，未进行业务拆分，之后的数据是进行了业务"
            "拆分的。\n2.如果选择的时间段包含2020年6月6日之前和之后的日期，数据"
            "计算时间会比较长，耐心等待。",
            style: TextStyle(
                color: Color(0xFF99664D),
                fontSize: 14,
                fontWeight: FontWeight.normal),
          ),
          Expanded(child: Container()),
          GestureDetector(
            onTap: () {
              submitQuery();
            },
            child: Container(
              height: 44,
              margin: EdgeInsets.only(bottom: 15),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Color(0xff00b377)),
              alignment: Alignment.center,
              child: Text(
                "查询",
                style: TextStyle(
                    color: Color(0xffffffff),
                    fontSize: 16,
                    fontWeight: FontWeight.w500),
              ),
            ),
          )
        ],
      ),
    );
  }

  _requestRoleType() {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      setState(() {
        isManagerLevel = value;
      });
    });
  }

  @override
  String getTitleName() {
    return '自定义时间查询';
  }

  Widget _buildItem(
      String label, String content, String hint, VoidCallback onPress) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        onPress();
      },
      child: Column(
        children: [
          Container(
            height: 50,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: TextStyle(
                      fontSize: 15,
                      color: Color(0xff292933),
                      fontWeight: FontWeight.normal),
                ),
                SizedBox(
                  width: 20,
                ),
                Expanded(
                    child: Text(
                  content.isNotEmpty ? content : hint,
                  style: TextStyle(
                      color:
                          Color(content.isNotEmpty ? 0xff292933 : 0xff9494a6),
                      fontSize: 15,
                      fontWeight: FontWeight.normal),
                )),
                Image.asset(
                  "assets/images/item_arrow.png",
                  width: 15,
                  height: 15,
                )
              ],
            ),
          ),
          Divider(
            height: 1,
            color: Color(0xfff0f0f2),
          ),
        ],
      ),
    );
  }

  submitQuery() {
    if (startTime == null) {
      showToast("请选择开始时间");
      return;
    }
    if (endTime == null) {
      showToast("请选择结束时间");
      return;
    }
    if (startTime!.isAfter(endTime!)) {
      showToast("开始时间必须早于结束时间");
      return;
    }
    var after31 = startTime!.add(Duration(days: 31));
    if (!isSameDay(after31, endTime) && after31.isBefore(endTime!)) {
      showToast("查询跨度最大只能为31天");
      return;
    }
    requestCustomStatistics();
  }

  requestCustomStatistics() {
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    Network(StatisticsData())
        .requestListData('dashboard/query',
            method: RequestMethod.GET, parameters: buildQueryParams())
        .then((value) {
      if (mounted) {
        EasyLoading.dismiss();
        if (value.isSuccess) {
          Navigator.of(this.context).pushNamed("/custom_statistics_result",
              arguments: {"result": value.data});
        } else {
          showToast(value.errorMsg ?? "网络请求错误，请稍后重试!");
        }
      }
    });
  }

  String _formatTime(DateTime? time) {
    if (time != null) {
      String y = (time.year >= -9999 && time.year <= 9999)
          ? _fourDigits(time.year)
          : _sixDigits(time.year);
      String m = _twoDigits(time.month);
      String d = _twoDigits(time.day);
      String h = _twoDigits(time.hour);
      String min = _twoDigits(time.minute);
      return "$y-$m-$d $h:$min";
    }
    return "";
  }

  static String _fourDigits(int n) {
    int absN = n.abs();
    String sign = n < 0 ? "-" : "";
    if (absN >= 1000) return "$n";
    if (absN >= 100) return "${sign}0$absN";
    if (absN >= 10) return "${sign}00$absN";
    return "${sign}000$absN";
  }

  static String _sixDigits(int n) {
    assert(n < -9999 || n > 9999);
    int absN = n.abs();
    String sign = n < 0 ? "-" : "+";
    if (absN >= 100000) return "$sign$absN";
    return "${sign}0$absN";
  }

  static String _threeDigits(int n) {
    if (n >= 100) return "$n";
    if (n >= 10) return "0$n";
    return "00$n";
  }

  static String _twoDigits(int n) {
    if (n >= 10) return "$n";
    return "0$n";
  }

  getMinStartTime() {
    var nowTime = DateTime.now();

    int year = nowTime.year - 1 - ((14 - nowTime.month) ~/ 12);
    int month = 12 - ((14 - nowTime.month) % 12);
    print("guan min now:${nowTime.month} start:$year,$month");
    return DateTime(year, month, nowTime.day, 0, 0, 0, 0, 0)
        .add(Duration(days: -1));
  }

  getMaxStartTime() {
    var nowTime = DateTime.now();
    return DateTime(nowTime.year, nowTime.month, nowTime.day, 23, 59, 59, 0, 0);
  }

  getDefaultStartTime() {
    var nowTime = DateTime.now();
    return DateTime(nowTime.year, nowTime.month, nowTime.day, 0, 0, 0, 0, 0);
  }

  getDefaultEndTime() {
    return getMaxStartTime();
  }

  getMinEndTime() {
    return getMinStartTime();
  }

  getMaxEndTime() {
    return getMaxStartTime();
  }

  bool isSameDay(DateTime dateTime1, DateTime? dateTime2) {
    return dateTime2 != null &&
        dateTime1.year == dateTime2.year &&
        dateTime1.month == dateTime2.month &&
        dateTime1.day == dateTime2.day;
  }

  buildQueryParams() {
    var params = Map<String, dynamic>();
    params.remove("groupId");
    params.remove("searchUserId");
    if (groupId != null) {
      params["groupId"] = groupId;
    }
    if (searchUserId != null) {
      params["searchUserId"] = searchUserId;
    }
    params["startTime"] = startTime!.millisecondsSinceEpoch;
    params["endTime"] = endTime!.millisecondsSinceEpoch;
    return params;
  }
}
