import 'package:qt_common_sdk/qt_common_sdk.dart';
import 'package:XYYContainer/XYYContainer.dart';

/// Quick Tracking SDK工具类
/// 用于统一管理阿里云Quick Tracking统计分析SDK的初始化和使用
class QuickTrackingUtils {
  static bool _isInitialized = false;
  
  /// 初始化Quick Tracking SDK
  /// 注意：这里的appkey和渠道参数必须和Android端App.java中预初始化的参数严格一致
  static Future<void> initSDK() async {
    if (_isInitialized) {
      print('Quick Tracking SDK already initialized');
      return;
    }
    
    try {
      // 获取当前环境信息来决定使用哪个AppKey
      String androidAppKey = await _getAndroidAppKey();
      String iosAppKey = await _getIOSAppKey();
      String channel = await _getChannel();
      
      // 设置日志开关（正式发布时应该关闭）
      await QTCommonSdk.setLogEnabled(true);
      
      // 初始化SDK
      await QTCommonSdk.initCommon(androidAppKey, iosAppKey, channel);
      
      _isInitialized = true;
      print('Quick Tracking SDK initialized successfully');
      print('Android AppKey: $androidAppKey');
      print('iOS AppKey: $iosAppKey');
      print('Channel: $channel');
      
    } catch (e) {
      print('Quick Tracking SDK initialization failed: $e');
    }
  }
  
  /// 获取Android AppKey
  /// 根据当前环境返回对应的AppKey
  static Future<String> _getAndroidAppKey() async {
    try {
      // 通过XYYContainer获取当前环境信息
      String? env = await XYYContainer.storageChannel.getValue('current_env');
      
      if (env == 'prod' || env == 'production') {
        // 生产环境AppKey - 请替换为您在阿里云控制台申请的生产环境AppKey
        return "your_production_android_appkey_here";
      } else {
        // 测试环境AppKey - 请替换为您在阿里云控制台申请的测试环境AppKey
        return "your_test_android_appkey_here";
      }
    } catch (e) {
      print('Failed to get environment info, using test AppKey: $e');
      return "your_test_android_appkey_here";
    }
  }
  
  /// 获取iOS AppKey
  /// 根据当前环境返回对应的AppKey
  static Future<String> _getIOSAppKey() async {
    try {
      // 通过XYYContainer获取当前环境信息
      String? env = await XYYContainer.storageChannel.getValue('current_env');
      
      if (env == 'prod' || env == 'production') {
        // 生产环境AppKey - 请替换为您在阿里云控制台申请的生产环境AppKey
        return "your_production_ios_appkey_here";
      } else {
        // 测试环境AppKey - 请替换为您在阿里云控制台申请的测试环境AppKey
        return "your_test_ios_appkey_here";
      }
    } catch (e) {
      print('Failed to get environment info, using test AppKey: $e');
      return "your_test_ios_appkey_here";
    }
  }
  
  /// 获取渠道信息
  static Future<String> _getChannel() async {
    try {
      String? env = await XYYContainer.storageChannel.getValue('current_env');
      
      if (env == 'prod' || env == 'production') {
        return "official"; // 生产环境渠道
      } else {
        return "test"; // 测试环境渠道
      }
    } catch (e) {
      print('Failed to get environment info, using test channel: $e');
      return "test";
    }
  }
  
  /// 自定义事件统计
  static Future<void> trackEvent(String eventName, Map<String, dynamic>? properties) async {
    if (!_isInitialized) {
      print('Quick Tracking SDK not initialized, skipping event: $eventName');
      return;
    }
    
    try {
      await QTCommonSdk.onEvent(eventName, properties ?? {});
      print('Event tracked: $eventName with properties: $properties');
    } catch (e) {
      print('Failed to track event $eventName: $e');
    }
  }
  
  /// 带页面信息的自定义事件统计
  static Future<void> trackEventWithPage(String eventName, String pageName, Map<String, dynamic>? properties) async {
    if (!_isInitialized) {
      print('Quick Tracking SDK not initialized, skipping event: $eventName');
      return;
    }
    
    try {
      await QTCommonSdk.onEventWithPage(eventName, pageName, properties ?? {});
      print('Event tracked: $eventName on page: $pageName with properties: $properties');
    } catch (e) {
      print('Failed to track event $eventName on page $pageName: $e');
    }
  }
  
  /// 页面开始统计
  static Future<void> onPageStart(String pageName) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onPageStart(pageName);
      print('Page started: $pageName');
    } catch (e) {
      print('Failed to track page start $pageName: $e');
    }
  }
  
  /// 页面结束统计
  static Future<void> onPageEnd(String pageName) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onPageEnd(pageName);
      print('Page ended: $pageName');
    } catch (e) {
      print('Failed to track page end $pageName: $e');
    }
  }
  
  /// 用户登录统计
  static Future<void> onUserLogin(String userId) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onProfileSignIn(userId);
      print('User login tracked: $userId');
    } catch (e) {
      print('Failed to track user login $userId: $e');
    }
  }
  
  /// 用户登出统计
  static Future<void> onUserLogout() async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.onProfileSignOff();
      print('User logout tracked');
    } catch (e) {
      print('Failed to track user logout: $e');
    }
  }
  
  /// 注册全局属性
  static Future<void> registerGlobalProperties(Map<String, dynamic> properties) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.registerGlobalProperties(properties);
      print('Global properties registered: $properties');
    } catch (e) {
      print('Failed to register global properties: $e');
    }
  }
  
  /// 设置页面属性
  static Future<void> setPageProperty(String pageName, Map<String, dynamic> properties) async {
    if (!_isInitialized) {
      return;
    }
    
    try {
      await QTCommonSdk.setPageProperty(pageName, properties);
      print('Page property set for $pageName: $properties');
    } catch (e) {
      print('Failed to set page property for $pageName: $e');
    }
  }
}
