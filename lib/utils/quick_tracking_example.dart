import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/utils/quick_tracking_utils.dart';

/// Quick Tracking SDK使用示例
/// 展示如何在实际业务中使用统计功能
class QuickTrackingExample {
  
  /// 示例：用户登录事件统计
  static Future<void> trackUserLogin(String userId, String loginMethod) async {
    // 记录用户登录
    await QuickTrackingUtils.onUserLogin(userId);
    
    // 记录登录方式等额外信息
    await QuickTrackingUtils.trackEvent('user_login', {
      'user_id': userId,
      'login_method': loginMethod, // 如：'phone', 'wechat', 'password'
      'login_time': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  /// 示例：用户登出事件统计
  static Future<void> trackUserLogout(String userId) async {
    // 记录用户登出
    await QuickTrackingUtils.onUserLogout();
    
    // 记录登出相关信息
    await QuickTrackingUtils.trackEvent('user_logout', {
      'user_id': userId,
      'logout_time': DateTime.now().millisecondsSinceEpoch,
    });
  }
  
  /// 示例：页面访问统计
  static Future<void> trackPageView(String pageName, {Map<String, dynamic>? extraData}) async {
    // 记录页面开始
    await QuickTrackingUtils.onPageStart(pageName);
    
    // 记录页面访问事件
    Map<String, dynamic> properties = {
      'page_name': pageName,
      'visit_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (extraData != null) {
      properties.addAll(extraData);
    }
    
    await QuickTrackingUtils.trackEvent('page_view', properties);
  }
  
  /// 示例：页面离开统计
  static Future<void> trackPageLeave(String pageName, {int? stayDuration}) async {
    // 记录页面结束
    await QuickTrackingUtils.onPageEnd(pageName);
    
    // 记录页面离开事件
    await QuickTrackingUtils.trackEvent('page_leave', {
      'page_name': pageName,
      'leave_time': DateTime.now().millisecondsSinceEpoch,
      'stay_duration': stayDuration ?? 0, // 停留时长（毫秒）
    });
  }
  
  /// 示例：按钮点击事件统计
  static Future<void> trackButtonClick(String buttonName, String pageName, {Map<String, dynamic>? extraData}) async {
    Map<String, dynamic> properties = {
      'button_name': buttonName,
      'page_name': pageName,
      'click_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (extraData != null) {
      properties.addAll(extraData);
    }
    
    await QuickTrackingUtils.trackEventWithPage('button_click', pageName, properties);
  }
  
  /// 示例：商品相关事件统计
  static Future<void> trackProductEvent(String eventType, String productId, String productName, {
    double? price,
    String? category,
    int? quantity,
    String? pageName,
  }) async {
    Map<String, dynamic> properties = {
      'product_id': productId,
      'product_name': productName,
      'event_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (price != null) properties['price'] = price;
    if (category != null) properties['category'] = category;
    if (quantity != null) properties['quantity'] = quantity;
    
    if (pageName != null) {
      await QuickTrackingUtils.trackEventWithPage(eventType, pageName, properties);
    } else {
      await QuickTrackingUtils.trackEvent(eventType, properties);
    }
  }
  
  /// 示例：搜索事件统计
  static Future<void> trackSearch(String keyword, String searchType, {
    int? resultCount,
    String? pageName,
  }) async {
    Map<String, dynamic> properties = {
      'keyword': keyword,
      'search_type': searchType, // 如：'product', 'customer', 'order'
      'search_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (resultCount != null) properties['result_count'] = resultCount;
    
    if (pageName != null) {
      await QuickTrackingUtils.trackEventWithPage('search', pageName, properties);
    } else {
      await QuickTrackingUtils.trackEvent('search', properties);
    }
  }
  
  /// 示例：订单相关事件统计
  static Future<void> trackOrderEvent(String eventType, String orderId, {
    double? amount,
    String? status,
    String? paymentMethod,
    String? pageName,
  }) async {
    Map<String, dynamic> properties = {
      'order_id': orderId,
      'event_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (amount != null) properties['amount'] = amount;
    if (status != null) properties['status'] = status;
    if (paymentMethod != null) properties['payment_method'] = paymentMethod;
    
    if (pageName != null) {
      await QuickTrackingUtils.trackEventWithPage(eventType, pageName, properties);
    } else {
      await QuickTrackingUtils.trackEvent(eventType, properties);
    }
  }
  
  /// 示例：错误事件统计
  static Future<void> trackError(String errorType, String errorMessage, {
    String? pageName,
    String? stackTrace,
  }) async {
    Map<String, dynamic> properties = {
      'error_type': errorType,
      'error_message': errorMessage,
      'error_time': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (stackTrace != null) properties['stack_trace'] = stackTrace;
    
    if (pageName != null) {
      await QuickTrackingUtils.trackEventWithPage('error', pageName, properties);
    } else {
      await QuickTrackingUtils.trackEvent('error', properties);
    }
  }
  
  /// 示例：性能事件统计
  static Future<void> trackPerformance(String performanceType, int duration, {
    String? pageName,
    Map<String, dynamic>? extraData,
  }) async {
    Map<String, dynamic> properties = {
      'performance_type': performanceType, // 如：'page_load', 'api_call', 'image_load'
      'duration': duration, // 耗时（毫秒）
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    if (extraData != null) {
      properties.addAll(extraData);
    }
    
    if (pageName != null) {
      await QuickTrackingUtils.trackEventWithPage('performance', pageName, properties);
    } else {
      await QuickTrackingUtils.trackEvent('performance', properties);
    }
  }
}

/// 页面统计Mixin
/// 可以被页面Widget混入，自动处理页面统计
mixin QuickTrackingPageMixin<T extends StatefulWidget> on State<T> {
  String get pageName;
  DateTime? _pageStartTime;
  
  @override
  void initState() {
    super.initState();
    _pageStartTime = DateTime.now();
    QuickTrackingExample.trackPageView(pageName);
  }
  
  @override
  void dispose() {
    if (_pageStartTime != null) {
      int stayDuration = DateTime.now().difference(_pageStartTime!).inMilliseconds;
      QuickTrackingExample.trackPageLeave(pageName, stayDuration: stayDuration);
    }
    super.dispose();
  }
}

/// 使用示例Widget
class QuickTrackingExamplePage extends StatefulWidget {
  @override
  _QuickTrackingExamplePageState createState() => _QuickTrackingExamplePageState();
}

class _QuickTrackingExamplePageState extends State<QuickTrackingExamplePage> 
    with QuickTrackingPageMixin {
  
  @override
  String get pageName => 'quick_tracking_example_page';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Quick Tracking 示例'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton(
              onPressed: () {
                QuickTrackingExample.trackButtonClick('test_button', pageName, extraData: {
                  'button_type': 'primary',
                  'button_position': 'top',
                });
              },
              child: Text('测试按钮点击统计'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                QuickTrackingExample.trackSearch('测试商品', 'product', 
                  resultCount: 10, pageName: pageName);
              },
              child: Text('测试搜索统计'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                QuickTrackingExample.trackProductEvent('product_view', 'P001', '测试商品',
                  price: 99.99, category: '测试分类', pageName: pageName);
              },
              child: Text('测试商品事件统计'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                QuickTrackingExample.trackOrderEvent('order_create', 'O001',
                  amount: 199.99, status: 'pending', paymentMethod: 'alipay', pageName: pageName);
              },
              child: Text('测试订单事件统计'),
            ),
          ],
        ),
      ),
    );
  }
}
